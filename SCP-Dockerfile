FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/node:18 AS build-stage
WORKDIR /app

COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY ./packages ./packages
COPY apps/scp-ui/ ./apps/scp-ui/

RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm

# 清理 pnpm store
# RUN pnpm store prune
# RUN pnpm cache clean

RUN pnpm config set registry https://registry.npmmirror.com

RUN pnpm install --reporter=append-only  --ignore-scripts

COPY . .
RUN pnpm run build --filter=scp-ui

# 生产阶段
FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/openresty/openresty:alpine AS production-stage
RUN echo "https://mirrors.aliyun.com/alpine/v3.10/main/" > /etc/apk/repositories \
    && echo "https://mirrors.aliyun.com/alpine/v3.10/community/" >> /etc/apk/repositories
RUN apk update

# 安装 gettext 包
RUN apk add gettext

# 复制构建的文件到生产镜像
COPY --from=build-stage /app/apps/scp-ui/dist /usr/share/nginx/html

# 设置工作目录
WORKDIR /usr/local/openresty/nginx/conf

# 复制 Nginx 配置文件
COPY --from=build-stage /app/apps/scp-ui/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf.template

# 设置 entrypoint
ENTRYPOINT envsubst '$SCP_GO_UI_HOST $SCP_GO_UI_PORT $UI_START_PORT' < nginx.conf.template > nginx.conf && cat nginx.conf && nginx -g 'daemon off;'

EXPOSE 80

LABEL jw_version="1.1"