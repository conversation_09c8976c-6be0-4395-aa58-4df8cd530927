{"name": "scmify-ui", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^17.8.0", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "husky": "^8.0.0", "lint-staged": "^13.0.0", "prettier": "^3.2.5", "turbo": "^2.3.3", "typescript": "5.5.4"}, "packageManager": "pnpm@10.11.1", "engines": {"node": ">=18"}, "resolutions": {"styled-components": "^6.1.12", "react": "^18.2.0", "esbuild": "0.19.2"}, "lint-staged": {"**/*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "**/*.{md,json}": ["prettier --write"]}}