stages:
  - check
  - build
  - trigger

variables:
  TURBO_TOKEN: $CI_JOB_TOKEN
  TURBO_TEAM: $CI_PROJECT_PATH_SLUG
  HARBOR_REGISTRY: jianweisoft.cn

.build_template: &build_template
  stage: build
  variables:
    HARBOR_PROJECT: ''
    IMAGE_NAME: ''
    DOCKERFILE_PATH: ''
  before_script:
    - docker login -u admin -p Harbor12345 jianweisoft.cn
    - docker buildx use multi-builder

.build_variables: &build_variables
  TAG_COMMIT: $HARBOR_REGISTRY/$HARBOR_PROJECT/$IMAGE_NAME:$CI_COMMIT_SHORT_SHA
  TAG_LATEST: $HARBOR_REGISTRY/$HARBOR_PROJECT/$IMAGE_NAME:latest

build_scp_ui:
  <<: *build_template
  tags:
    - newBigserver
  variables:
    <<: *build_variables
    APP_NAME: scp-ui
    HARBOR_PROJECT: scp
    IMAGE_NAME: ui-js-i
    DOCKERFILE_PATH: SCP-Dockerfile
  script:
    - docker buildx build -f $DOCKERFILE_PATH --label hash=$CI_COMMIT_SHORT_SHA --platform linux/arm64,linux/amd64 --tag $TAG_COMMIT --tag $TAG_LATEST . --push
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/scp-ui/**/*
        - packages/**/*
        - SCP-Dockerfile

build_int_ui:
  <<: *build_template
  variables:
    <<: *build_variables
    APP_NAME: int-ui
    HARBOR_PROJECT: int
    IMAGE_NAME: ui-int-i
    DOCKERFILE_PATH: INT-Dockerfile
  script:
    - docker buildx build -f $DOCKERFILE_PATH --label hash=$CI_COMMIT_SHORT_SHA --platform linux/arm64,linux/amd64 --tag $TAG_COMMIT --tag $TAG_LATEST . --push
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/int-ui/**/*
        - packages/**/*
        - SCP-Dockerfile

build_deepE:
  <<: *build_template
  tags:
    - newBigserver
  variables:
    <<: *build_variables
    APP_NAME: deepE
    HARBOR_PROJECT: deep_e
    IMAGE_NAME: deep_e_ui_image
    DOCKERFILE_PATH: DeepE-Dockerfile
  script:
    - docker buildx build -f $DOCKERFILE_PATH --label hash=$CI_COMMIT_SHORT_SHA --platform linux/arm64,linux/amd64 --tag $TAG_COMMIT --tag $TAG_LATEST . --push
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/deep-e/**/*
        - packages/**/*
        - DeepE-Dockerfile
        - .gitlab-ci.yml

.trigger_template: &trigger_template
  stage: trigger
  variables:
    IMAGE_NAME: ''
    CUSTOM_IMAGE_NAME: ''
    CUSTOM_IMAGE_TAG: ''
    FILE: ''
    TRIGGER_PATH: ''
    TOKEN: ''
  script:
    - |
      if [ "$CI_COMMIT_BRANCH" == "dev" ]; then
        export REF=dev
            elif [ "$CI_COMMIT_BRANCH" == "devpre" ] && [ "$FILE" == "deep_e" ]; then
        export REF=devpre
            else
        export REF=devpre-1029
            fi
    - 'curl -X POST --fail -F token=$TOKEN -F ref=$REF -F "variables[IMAGE_NAME]=$IMAGE_NAME" -F "variables[CUSTOM_IMAGE_NAME]=$CUSTOM_IMAGE_NAME" -F "variables[IMAGE_TAG]=$CI_COMMIT_SHORT_SHA" -F "variables[FILE]=$FILE" -F "variables[CUSTOM_IMAGE_TAG]=$CUSTOM_IMAGE_TAG" $TRIGGER_PATH'

trigger_scp_ui:
  tags:
    - newBigserver
  needs:
    - job: build_scp_ui
      artifacts: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/scp-ui/**/*
        - packages/**/*
        - SCP-Dockerfile
  <<: *trigger_template
  variables:
    TOKEN: glptt-0a646b01326e226553d14cb8cb07f199ecae1434
    IMAGE_NAME: ui-js-i
    CUSTOM_IMAGE_NAME: CUSTOM_UI_IMAGE_NAME
    FILE: scp
    CUSTOM_IMAGE_TAG: CUSTOM_UI_IMAGE_VERSION
    TRIGGER_PATH: http://gitlab.scmify.com:8929/api/v4/projects/20/trigger/pipeline

trigger_int_ui:
  stage: trigger
  tags:
    - newBigserver
  needs:
    - job: build_int_ui
      artifacts: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/int-ui/**/*
        - packages/**/*
        - SCP-Dockerfile
  <<: *trigger_template
  variables:
    TOKEN: glptt-f7f0109291b795f16813d4c42c39291dd6257282
    IMAGE_NAME: ui-int-i
    CUSTOM_IMAGE_NAME: CUSTOM_UI_IMAGE_NAME
    FILE: int
    CUSTOM_IMAGE_TAG: CUSTOM_UI_IMAGE_VERSION
    TRIGGER_PATH: http://gitlab.scmify.com:8929/api/v4/projects/29/trigger/pipeline
# curl -X POST --fail -F token=glptt-f7f0109291b795f16813d4c42c39291dd6257282 -F ref=devpre-1029 -F "variables[IMAGE_NAME]=ui-int-i" -F "variables[CUSTOM_IMAGE_NAME]=CUSTOM_UI_IMAGE_NAME" -F "variables[IMAGE_TAG]=$CI_COMMIT_SHORT_SHA" -F "variables[FILE]=int" -F "variables[CUSTOM_IMAGE_TAG]=CUSTOM_UI_IMAGE_VERSION" http://gitlab.scmify.com:8929/api/v4/projects/29/trigger/pipeline

trigger_deep_e:
  tags:
    - newBigserver
  needs:
    - job: build_deepE
      artifacts: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "devpre" || $CI_COMMIT_BRANCH == "dev")'
      changes:
        - apps/deep-e/**/*
        - packages/**/*
        - DeepE-Dockerfile
        - .gitlab-ci.yml
  <<: *trigger_template
  variables:
    TOKEN: glptt-1d1b54865b285d15ed20f27e3b6319001c830835
    IMAGE_NAME: deep_e_ui_image
    CUSTOM_IMAGE_NAME: CUSTOM_UI_AI_IMAGE_NAME
    FILE: deep_e
    REF: devpre
    CUSTOM_IMAGE_TAG: CUSTOM_UI_AI_IMAGE_VERSION
    TRIGGER_PATH: http://gitlab.scmify.com:8929/api/v4/projects/55/trigger/pipeline
