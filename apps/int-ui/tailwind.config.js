/** @type {import('tailwindcss').Config} */
import sharedConfig from '@repo/tailwindcss-config/tailwind.config.js';

export default {
  ...sharedConfig,
  content: [
    ...sharedConfig.content,
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
    './node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        white: '#FFFFFF',
        black: '#000000',
        zinc: {
          50: '#FAFAFA',
          100: '#F4F4F5',
          200: '#E4E4E7',
          300: '#D4D4D8',
          400: '#A1A1AA',
          500: '#71717A',
          600: '#52525B',
          700: '#3F3F46',
          800: '#27272A',
          900: '#18181B',
        },
      },
      spacing: {
        // 60:'18.25rem',
        76: '19rem',
        100: '25rem',
        101: '25.5rem',
        130: '32.5rem',
        152: '338rem',
        159: '39.5rem',
      },
      lineHeight: {
        12: '3rem',
        20: '5rem',
      },
      fontSize: {
        normal: [
          '14px',
          {
            fontStyle: 'normal',
            fontWeight: 400,
            lineHeight: '20px',
            letterSpacing: '0.56px',
            fontFamily: 'PingFang SC',
          },
        ],
        h1: [
          '24px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.96px',
            fontWeight: 600,
          },
        ],
        h2: [
          '22px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.88px',
            fontWeight: 600,
          },
        ],
        h3: [
          '20px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.64px',
            fontWeight: 600,
          },
        ],
        h4: [
          '18px',
          {
            lineHeight: '24px',
            letterSpacing: '0.72px',
            fontWeight: 500,
          },
        ],
        s1: [
          '16px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.96px',
            fontWeight: 500,
          },
        ],
        s2: [
          '16px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.64px',
            fontWeight: 500,
          },
        ],
        b1: [
          '14px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.56px',
            fontWeight: 600,
          },
        ],
        b2: [
          '14px',
          {
            lineHeight: '20px',
            letterSpacing: '0.56px',
            fontWeight: 400,
          },
        ],
        st1: [
          '12px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.48px',
            fontWeight: 400,
          },
        ],
        st2: [
          '10px',
          {
            lineHeight: 'normal',
            fontWeight: 500,
          },
        ],
      },
    },
  },
  darkMode: 'class',
  corePlugins: {
    preflight: false,
  },
};
