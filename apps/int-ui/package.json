{"name": "int-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "prod": "vite --mode production", "test": "vite --mode test", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "build:prod": "tsc && vite build --mode production", "build:test": "tsc && vite build --mode test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "translate": "voerkai18n extract && voerkai18n translate --appkey $KEY --appid $ID", "translate:compile": "voerkai18n compile", "translate:auto": "KEY=0cIMpshCtxohtlSJKlSM ID=20230302001582986 pnpm translate && pnpm translate:compile"}, "dependencies": {"@ant-design/charts": "^2.0.3", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.1.5", "@antv/g2": "^5.2.7", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.0.1", "@repo/configuration": "workspace:*", "@repo/ui": "workspace:*", "@voerkai18n/react": "^2.1.10", "@voerkai18n/runtime": "^2.1.9", "@voerkai18n/vite": "^2.1.13", "ahooks": "^3.8.1", "antd": "^5.11.2", "antd-style": "^3.6.2", "axios": "^1.6.2", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "esbuild": "0.19.2", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "path": "^0.12.7", "qs": "^6.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.19.0", "scmify-components": "^0.1.5", "terser": "^5.27.0", "timezone": "link:dayjs/plugin/timezone"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@repo/eslint-config": "workspace:*", "@repo/tailwindcss-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.17.6", "@types/node": "^20.11.14", "@types/qs": "^6.9.14", "@types/react": "^18.2.37", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}