import { Steps as AntdSteps, Button } from 'antd';
import { t } from '@/languages';

export type optionsType = {
  steps: any;
  current: number;
  items: any;
};
export type setpsOptionType = {
  options: optionsType;
  next?: any;
  prev?: any;
  goBackFirstPage?: any;
};

const Steps: React.FC<setpsOptionType> = (props) => {
  const { options, next, prev, goBackFirstPage } = props;
  return (
    <div className='mt-5'>
      <AntdSteps
        current={options.current}
        items={options.items}
        style={{ padding: '0 2.5rem' }}
      />
      <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
        <Button
          disabled={!(options.current > 0)}
          htmlType='reset'
          className='h-12 mr-2'
          onClick={() => prev()}>
          {t('上一步')}
        </Button>

        {options.current < options.steps.length - 1 && (
          <Button
            htmlType='reset'
            color='primary'
            className='h-12 mr-2'
            onClick={() => next()}>
            {t('下一步')}
          </Button>
        )}
        {options.current === options.steps.length - 1 && (
          <Button
            htmlType='reset'
            color='primary'
            className='h-12 mr-2'
            onClick={() => goBackFirstPage()}>
            {t('返回第一页')}
          </Button>
        )}
      </div>
    </div>
  );
};
export default Steps;
