import { DatePicker, Form, Col } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { stringToDayjs } from './formUtils';
import { getFirstValue } from '@/utils';
interface FormDateProps {
  field: FormField;
  formName: string;
}
const FormDate: React.FC<FormDateProps> = ({ field, formName }) => {
  const defaultValue = getFirstValue<string>(field.DefaultValue) || '';
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        rules={[{ required: field.required }]}
        initialValue={stringToDayjs(defaultValue)}>
        <DatePicker className='w-full' showTime />
      </Form.Item>
    </Col>
  );
  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};
export default FormDate;
