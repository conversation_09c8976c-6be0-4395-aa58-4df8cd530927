import { Input, Form, Col } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { getFirstValue } from '@/utils';
interface FormInputProps {
  field: FormField;
  formName: string;
}

const FormInput: React.FC<FormInputProps> = ({ field, formName }) => {
  const defaultValue = getFirstValue(field.DefaultValue);
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        rules={[{ required: field.required }]}
        initialValue={defaultValue}>
        <Input />
      </Form.Item>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormInput;
