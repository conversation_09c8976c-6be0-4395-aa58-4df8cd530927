import {
  Upload,
  Form,
  Col,
  Button,
  type UploadProps,
  message,
  type UploadFile,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { useState } from 'react';

interface FormUploadProps {
  field: FormField;
  formName: string;
}

const SingleFileUpload: React.FC<
  UploadProps & { onChange?: (fileName: string) => void }
> = (props) => {
  const { onChange, ...restProps } = props;
  const [fileList, setFileList] = useState<UploadFile<any>[]>([]);

  return (
    <Upload
      {...restProps}
      fileList={fileList}
      onChange={(info) => {
        console.log(info);
        const newFileList = info.fileList.slice(-1); // 只保留最新上传的一个文件
        setFileList(newFileList);
        if (info.file.status === 'done' && info.file.response) {
          onChange && onChange(info.file.response.data.result);
          message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 上传失败`);
        }
      }}>
      {props.children}
    </Upload>
  );
};

const FormUpload: React.FC<FormUploadProps> = ({ field, formName }) => {
  const form = Form.useFormInstance();
  const intToken = localStorage.getItem('int-token') as string;

  const config: UploadProps = {
    name: 'file',
    action: '/file/uploadFile',
    headers: {
      Authorization: intToken,
    },
    beforeUpload: (file) => {
      const isYML =
        file.type === 'application/x-yaml' || file.type === 'application/x-yml';
      if (!isYML) {
        message.error(`${file.name} 不是yml/yaml文件`);
        return false;
      }
      return true;
    },
  };

  const formItem = (
    <Col span={8}>
      <Form.Item
        name={[formName, field.field]}
        rules={[{ required: field.required }]}>
        <SingleFileUpload
          {...config}
          onChange={(fileName) => {
            form.setFieldValue([formName, field.field], fileName);
          }}>
          <Button icon={<UploadOutlined />}>Click to Upload</Button>
        </SingleFileUpload>
      </Form.Item>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }

  return formItem;
};

export default FormUpload;
