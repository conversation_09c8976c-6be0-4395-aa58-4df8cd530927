import { Form } from 'antd';
import React from 'react';

const ShouldVisible: React.FC<
  React.PropsWithChildren & {
    visibilityCondition: {
      field: string;
      operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
      value: any;
    };
    formName: string;
    fieldName: string;
  }
> = ({ children, visibilityCondition, formName, fieldName }) => {
  const form = Form.useFormInstance();
  const shouldUpdateFunction = (
    prev: any,
    option: '==' | '!=' | '>' | '<' | '>=' | '<=',
    next: any,
  ) => {
    switch (option) {
      case '==':
        return prev === next;
      case '!=':
        return prev !== next;
      case '>':
        return prev > next;
      case '<':
        return prev < next;
      case '>=':
        return prev >= next;
      case '<=':
        return prev <= next;
      default:
        return false;
    }
  };

  const checkVisibility = (getFieldValue: any) => {
    const value = getFieldValue([formName, visibilityCondition.field]);
    return shouldUpdateFunction(
      value,
      visibilityCondition.operator,
      visibilityCondition.value,
    );
  };

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prev, next) => {
        const prevValue = prev[formName]?.[visibilityCondition.field];
        const nextValue = next[formName]?.[visibilityCondition.field];
        if (prevValue !== nextValue) {
          form.setFieldValue([formName, fieldName], undefined);
        }
        return prevValue !== nextValue || nextValue === null;
      }}>
      {({ getFieldValue }) => {
        const isVisible = checkVisibility(getFieldValue);
        if (isVisible) {
          return (
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const isVisible = checkVisibility(getFieldValue);
                return isVisible ? children : null;
              }}
            </Form.Item>
          );
        } else {
          return null;
        }
      }}
    </Form.Item>
  );
};

export default ShouldVisible;
