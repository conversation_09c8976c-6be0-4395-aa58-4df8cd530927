import { Select, Form, Col } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { getFirstValue } from '@/utils';
interface FormSelectProps {
  field: FormField;
  formName: string;
}

const FormSelect: React.FC<FormSelectProps> = ({ field, formName }) => {
  const defaultValue = getFirstValue(field.DefaultValue);
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        initialValue={defaultValue}
        rules={[{ required: field.required }]}>
        <Select options={field.options?.select || []} />
      </Form.Item>
    </Col>
  );
  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormSelect;
