import { InputNumber, Form, Col } from 'antd';
import ShouldVisible from './ShouldVisible';
import { FormField } from '../types';
import { getFirstValue } from '@/utils';
interface FormNumberProps {
  field: FormField;
  formName: string;
}

const FormNumber: React.FC<FormNumberProps> = ({ field, formName }) => {
  const defaultValue = getFirstValue(field.DefaultValue);
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        initialValue={defaultValue}
        rules={[{ required: field.required }]}>
        <InputNumber className='w-full' />
      </Form.Item>
    </Col>
  );
  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormNumber;
