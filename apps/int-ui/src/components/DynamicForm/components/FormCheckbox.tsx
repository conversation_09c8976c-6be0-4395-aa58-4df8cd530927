import { Checkbox, Form } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { getFirstValue } from '@/utils';
interface FormCheckboxProps {
  field: FormField;
  formName: string;
}

const FormCheckbox: React.FC<FormCheckboxProps> = ({ field, formName }) => {
  const defaultValue =
    getFirstValue<string>(field.DefaultValue)?.split(':')[1].split('|') || [];
  const formItem = (
    <Form.Item
      label={field.label}
      name={[formName, field.field]}
      rules={[{ required: field.required }]}
      initialValue={defaultValue}>
      <Checkbox.Group options={field.options?.checkbox || []} />
    </Form.Item>
  );
  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormCheckbox;
