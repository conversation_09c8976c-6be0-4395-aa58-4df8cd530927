import {
  FormInput,
  FormNumber,
  FormSelect,
  FormDate,
  FormCheckbox,
  FormUpload,
} from './components';
import { Row } from 'antd';
import { FormField } from './types';

export interface DynamicFormProps {
  fields: FormField[];
  formName: string;
}

const DynamicForm: React.FC<DynamicFormProps> = ({ fields, formName }) => {
  const renderFormItem = (field: FormField) => {
    switch (field.type) {
      case 'text':
        return (
          <FormInput field={field} formName={formName} key={field.field} />
        );
      case 'number':
        return (
          <FormNumber field={field} formName={formName} key={field.field} />
        );
      case 'select':
        return (
          <FormSelect field={field} formName={formName} key={field.field} />
        );
      case 'date':
        return <FormDate field={field} formName={formName} key={field.field} />;
      case 'checkbox':
        return (
          <FormCheckbox field={field} formName={formName} key={field.field} />
        );
      case 'upload':
        return (
          <FormUpload field={field} formName={formName} key={field.field} />
        );
    }
  };

  return <Row gutter={[16, 16]}>{fields.map(renderFormItem)}</Row>;
};

export default DynamicForm;
