export interface ConditionalDisplay {
  field: string;
  value: any;
  component: string;
}

export interface FieldAction {
  type: 'setValue' | 'clearField' | 'toggleComponent';
  target: string;
  value?: any;
  toggleOptions?: {
    components: string[];
    conditions: { field: string; value: any }[];
  };
}

export interface FormFieldOptions {
  select?: { value: string; label: string }[];
  checkbox?: { value: string; label: string }[];
  date?: {
    disabledDate?: string | string[];
    showTime?: boolean;
    format?: string;
  };
  [key: string]: any; // This allows for additional custom options
}

export interface FormField {
  field: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'select'
    | 'date'
    | 'switch'
    | 'button'
    | 'checkbox'
    | 'upload';
  options?: FormFieldOptions;
  required?: boolean;
  visibilityCondition?: {
    field: string;
    operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
    value: any;
  };
  conditionalDisplay?: ConditionalDisplay[];
  actions?: FieldAction[];
  DefaultValue?: any;
  events?: {
    [key: string]: FieldAction[];
  };
}

export interface DynamicFormProps {
  fields: FormField[];
  onSubmit: (formId: string, values: any) => void;
  onChange?: (formId: string, values: any) => void;
  formId: string;
}

export interface MultipleDynamicFormsProps {
  formConfigs: {
    id: string;
    fields: FormField[];
  }[];
  onSubmit: (allFormData: Record<string, any>) => void;
}
