/* eslint-disable prettier/prettier */
import { memo, useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select } from 'antd';
import { t } from '@/languages';

type propsType = any;

const FormContent: React.FC<propsType> = memo((props) => {
    const { data, fun } = props;
    const [form] = Form.useForm();
    const [empowerFormData, setEmpowerFormData] = useState<any[]>([]);
    const onValuesChange = (_: any, allValues: any) => {
        fun(allValues);
    };
    useEffect(() => {
        const _empowerFormData = [...data.auth]
        _empowerFormData.push({
            desc: "",
            field: "systemIp",
            specificType: "string",
            title: t("系统地址"),
            type: "input",
            value: data.systemIp || "",
        })
        setEmpowerFormData([..._empowerFormData])
    }, []);

    return (
        <div>
            <div
                className='p-2 mb-4 bg-slate-100 rounded-large overflow-hidden'
                style={{ display: 'inline-block' }}>
                {t('当前系统') + ': '}
                <span style={{ fontWeight: '600', color: '#0070f0' }}>
                    {data.systemTypeName}
                </span>
            </div>
            <Form
                onValuesChange={onValuesChange}
                form={form}
                className='flex flex-wrap overflow-hidden overflow-y-auto'
                style={{ height: 'calc(100vh - 334px)' }}>
                {empowerFormData.map((item: any, index: number) => {
                    let formChild: React.ReactNode = null;
                    switch (item.type) {
                        case 'input':
                            formChild = (
                                <Input
                                    key={index}
                                    className='h-12 w-full'
                                    type={item.field === 'password' ? 'password' : 'text'}
                                />
                            );
                            break;
                        case 'select':
                            formChild = (
                                <Select
                                    key={index}
                                    size={"small"}
                                    className='h-12 w-full'
                                    defaultValue={[(item.value).toString()]}
                                >
                                    {item.options?.map((a: any) => {
                                        return (
                                            <Select.Option key={a.value} value={a.label}>
                                                {a.label}
                                            </Select.Option>
                                        );
                                    })}
                                </Select>
                            );
                            break;
                        case 'date':
                            formChild = (
                                <DatePicker
                                    key={index}
                                    className=' border-0 bg-zinc-100 h-12 w-full'
                                    placeholder={item.title}
                                />
                            );
                            break;
                        default:
                            break;
                    }
                    return (
                        <>
                            <Form.Item
                                label={item.title + ': ' + item.desc}
                                key={index}
                                name={item.field}
                                style={{ width: '42%', margin: '0 4%' }}
                                className='text-zinc-400'
                                initialValue={item.value}>
                                {formChild}
                            </Form.Item>
                        </>
                    );
                })}
            </Form>
        </div>
    );
});

export default FormContent;
