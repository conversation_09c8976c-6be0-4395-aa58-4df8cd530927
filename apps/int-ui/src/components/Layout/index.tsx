import { useEffect, useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { Layout as AntdLayout, ConfigProvider, Flex } from 'antd';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('int');
import locale from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import Head from './Header';
import Icon from '@ant-design/icons';
import MyMenu from './Menu';

dayjs.locale('zh-cn');
const { Sider, Content } = AntdLayout;

const CollapsedIcon = (props: any) => (
  <Icon
    component={() => (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='24'
        height='24'
        viewBox='0 0 24 24'
        fill='none'>
        <path
          d='M2 2V22M12.4897 20.4577L20.656 13.7848C21.4026 13.1747 21.776 12.8696 21.9111 12.5011C22.0296 12.178 22.0296 11.822 21.9111 11.4988C21.776 11.1303 21.4026 10.8252 20.656 10.2151L12.4897 3.54226C11.3258 2.59121 10.7439 2.11568 10.2541 2.11514C9.82817 2.11467 9.42523 2.31248 9.15951 2.6525C8.85397 3.04346 8.85397 3.80468 8.85397 5.3271V18.6728C8.85397 20.1952 8.85397 20.9565 9.15951 21.3474C9.42523 21.6874 9.82817 21.8853 10.2541 21.8848C10.7439 21.8842 11.3258 21.4087 12.4897 20.4577Z'
          stroke='#B9BCC6'
          strokeWidth='2.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </svg>
    )}
    {...props}></Icon>
);

const Layout: React.FC = () => {
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const navigate = useNavigate();
  //获取int-user
  const user = localStorage.getItem('int-user') || null;
  const token = localStorage.getItem('int-token') || null;
  useEffect(() => {
    console.log(location.pathname);
    if (location.pathname === '/') {
      if (user === null || token === null) {
        navigate('/login');
      } else {
        navigate('/home');
      }
    }
  }, []);

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        token: {
          colorBgContainer: '#fff',
          colorTextPlaceholder: '#717179',
        },
        components: {
          Layout: {
            headerBg: '#fff',
            bodyBg: '#fff',
            // headerHeight: 88,
          },
          DatePicker: {
            activeShadow: 'none',
          },
          Menu: {
            itemColor: '#b9bcc6',
            itemHeight: 40,
            itemHoverColor: '#1890ff',
            itemHoverBg: 'transparent',
            itemSelectedBg: 'transparent',
            iconMarginInlineEnd: '20px',
            collapsedIconSize: 16,
            iconSize: 16,
          },
        },
      }}>
      <Flex
        style={{
          height: '100vh',
          width: '100%',
        }}>
        <Sider
          width={240}
          className='border border-solid border-r-neutral-5'
          collapsed={collapsed}
          collapsedWidth={108}
          style={{
            backgroundColor: '#fff',
          }}>
          <div
            className='flex flex-col justify-between'
            style={{
              height: '100%',
            }}>
            <div className='h-full'>
              <div
                className='h-20 flex justify-center items-center cursor-pointer text-brand font-bold text-[32px]'
                onClick={() => {
                  navigate('/home');
                }}>
                <img
                  className='h-[50px] mt-[20px]'
                  src={
                    !collapsed
                      ? '/scmify_logo/PNG/colorful-logo-font.png'
                      : '/scmify_logo/PNG/colorful-logo.png'
                  }
                />
              </div>
              <div>
                <MyMenu />
              </div>
            </div>
            <div className='w-full flex justify-center pb-4'>
              <CollapsedIcon
                onClick={() => setCollapsed(!collapsed)}
                style={{ transform: !collapsed ? 'rotate(180deg)' : '' }}
              />
            </div>
          </div>
        </Sider>
        <Flex
          vertical={true}
          flex={1}
          style={{
            width: 1,
          }}>
          <Head />
          <Content className='p-5 bg-zinc-200'>
            <Outlet />
          </Content>
        </Flex>
      </Flex>
    </ConfigProvider>
  );
};
export default Layout;
