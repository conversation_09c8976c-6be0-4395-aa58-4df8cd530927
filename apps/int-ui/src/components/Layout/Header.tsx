import { useEffect, useState } from 'react';
import { i18nScope, t } from '@/languages';
import { Select, Avatar, Dropdown, Flex } from 'antd';
import { MenuItemType } from 'antd/es/menu/interface';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux/es/hooks/useSelector';
import { setCurrentLanguage } from '@/store/reducers/globalSlice';
import store from '@/store';

const Header: React.FC = () => {
  const avatarClick = () => {
    console.log(123);
  };
  const navigate = useNavigate();
  const user: any = JSON.parse(localStorage.getItem('user') || '{}') || 'jinxu';

  const originOptions = [
    { label: t('中文'), value: 'zh' },
    { label: t('英文'), value: 'en' },
  ];

  const [options, setOptions] = useState(originOptions);

  useEffect(() => {
    setOptions(originOptions);
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  return (
    <Flex
      style={{
        width: '100%',
        height: '68px',
        paddingRight: '20px',
      }}
      justify='flex-end'
      align='center'
      gap={10}>
      <Select
        style={{
          width: '200px',
        }}
        value={useSelector((state: any) => state.global.currentLanguage)}
        options={options}
        onChange={async (value) => {
          await i18nScope.change(value);
          store.dispatch(setCurrentLanguage(value));
        }}
      />
      <Dropdown
        trigger={['click']}
        placement='bottomRight'
        menu={{
          items: [
            {
              label: (
                <div
                  onClick={() => {
                    navigate('/login');
                    localStorage.removeItem('scp-token');
                  }}>
                  {t('登出')}
                </div>
              ),
            },
          ] as MenuItemType[],
        }}>
        <Flex vertical={true} align='center' justify='center'>
          <Avatar
            onClick={avatarClick}
            shape='circle'
            className='cursor-pointer'
          />
          <span>{user?.username}</span>
        </Flex>
      </Dropdown>
    </Flex>
  );
};

export default Header;
