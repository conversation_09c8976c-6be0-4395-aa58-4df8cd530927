import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Menu } from 'antd';
import { t } from '@/languages';
import {
  ContainerOutlined,
  HomeOutlined,
  SafetyCertificateOutlined,
  ScheduleOutlined,
  ReadOutlined,
  DiffOutlined,
} from '@ant-design/icons';
import store from '@/store';
import { setPareaId } from '@/store/reducers/layoutSlice';

const MyMenu = () => {
  const [currentNav, setCurrentNav] = useState<string>(() => {
    const path = location.pathname;
    return path;
  });
  const navigate = useNavigate();
  const originMenuItems = [
    {
      icon: <HomeOutlined />,
      name: t('首页'),
      path: '/home',
    },
    {
      icon: <ScheduleOutlined />,
      name: t('日志'),
      path: '/logpage',
    },
    {
      icon: <ContainerOutlined />,
      name: t('项目'),
      path: '/projection',
    },
    {
      icon: <SafetyCertificateOutlined />,
      name: t('授权'),
      path: '/empower',
    },
  ];

  const [menuItems, setMenuItems] = useState(originMenuItems);

  useEffect(() => {
    if (location.search) {
      const searchParams = new URLSearchParams(window.location.search);
      const paramsObj: any = {};
      for (const [key, value] of searchParams.entries()) {
        paramsObj[key] = value;
      }
      // 检查并设置 'scp-token'
      const scpTokenParam = paramsObj['scpToken'];
      if (scpTokenParam) {
        localStorage.setItem('scp-token', scpTokenParam);
      }

      // 检查并设置 'int-token'
      const intTokenParam = paramsObj['intToken'];
      if (intTokenParam) {
        localStorage.setItem('int-token', 'Bearer ' + intTokenParam);
      }

      // 检查并设置 'int-user'
      const intUserParam = paramsObj['intUser'];
      if (intTokenParam) {
        localStorage.setItem('int-user', JSON.stringify(intUserParam));
      }

      // 检查并设置 'pareaId'
      const pareaIdParam = paramsObj['pareaId'];
      if (pareaIdParam) {
        store.dispatch(setPareaId(pareaIdParam));
        console.log(pareaIdParam.substring(8));
        localStorage.setItem('pareaId', pareaIdParam.substring(8));
      }

      // 检查并设置 'scene'
      if (paramsObj['scene']) {
        localStorage.setItem('scene', paramsObj['scene']);
      }
    }
    setMenuItems(originMenuItems);
    const sence = localStorage.getItem('scene');
    if (sence === 'scp-int') {
      setMenuItems((prevState) => {
        const newState = [...prevState];
        newState.push({
          icon: <ReadOutlined />,
          name: t('运行计划任务'),
          path: '/applicationJob',
        });
        newState.push({
          icon: <DiffOutlined />,
          name: t('任务模版'),
          path: '/jobTemplate',
        });
        return newState;
      });
    } else {
      setMenuItems((prevState) => {
        const newState = [...prevState];
        newState.push({
          icon: <SafetyCertificateOutlined />,
          name: t('运行计划详情'),
          path: '/applicationJob',
        });
        return newState;
      });
    }
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  return (
    <Menu
      mode='inline'
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        // marginTop: '20px',
        fontSize: '16px',
        overflow: 'auto',
        // lineHeight: '20px',
      }}
      defaultSelectedKeys={['/home']}
      items={menuItems.map((item: any, index) => {
        return {
          key: item.path || index,
          label: (
            // <Tooltip title={item.name as string}>
            <div className='truncate max-w-xs'>{item.name as string}</div>
            // </Tooltip>
          ),
          icon: item.icon,
        };
      })}
      selectedKeys={[currentNav]}
      onClick={({ key }) => {
        navigate(key);
        setCurrentNav(key);
      }}
    />
  );
};

export default MyMenu;
