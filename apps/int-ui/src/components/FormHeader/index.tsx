import { memo } from 'react';
import {
  Form,
  DatePicker,
  FormInstance,
  Input,
  Select,
  Button,
  ConfigProvider,
  Flex,
} from 'antd';
import { t } from '@/languages';

export type optionType = {
  type: string;
  key: string;
  value: string;
  label: string | string[];
  initialValue?: any;
  otherprops?: Map<string, any>;
};

export type FormHeaderProps = {
  options: optionType[];
  onSearch: (values: any) => void;
  form: FormInstance;
};
const FormHeader: React.FC<FormHeaderProps> = memo(
  ({ options, onSearch, form }) => {
    const onFinish = (values: any) => {
      onSearch(values);
    };
    return (
      <ConfigProvider
        theme={{
          components: {
            Form: {
              itemMarginBottom: 10,
            },
          },
        }}>
        <Form
          form={form}
          onFinish={onFinish}
          style={{
            width: '100%',
            height: '100%',
            overflow: 'hidden',
          }}>
          <Form.Item noStyle>
            <div
              style={{
                display: 'ruby',
              }}>
              {options.map((item: optionType) => {
                let formChild: React.ReactNode = null;
                switch (item.type) {
                  case 'input':
                    formChild = (
                      <Input
                        key={item.key}
                        size='small'
                        className='w-40'
                        style={{ height: '32px' }}
                      />
                    );
                    break;
                  case 'select':
                  case 'multipleSelect':
                    formChild = (
                      <Select
                        style={{
                          width: '160px',
                          height: '32px',
                        }}
                        key={item.key}
                        mode={
                          item.type === 'multipleSelect'
                            ? 'multiple'
                            : undefined
                        }
                        size='small'
                        className='w-40'>
                        {item.otherprops?.get('options').map((item: any) => {
                          return (
                            <Select.Option key={item.value} value={item.value}>
                              {item.label}
                            </Select.Option>
                          );
                        })}
                      </Select>
                    );
                    break;
                  case 'date':
                    formChild = (
                      <DatePicker
                        className='w-40 border-0 bg-zinc-100'
                        placeholder={item.label as string}
                      />
                    );
                    break;
                  case 'dateRange':
                    formChild = (
                      <DatePicker.RangePicker
                        className='w-70 border-0 bg-zinc-100'
                        placeholder={item.label as [string, string]}
                      />
                    );
                    break;
                  default:
                    break;
                }
                return (
                  <Form.Item
                    key={item.key}
                    name={item.key}
                    label={item.label}
                    initialValue={item.initialValue}
                    style={{
                      marginRight: 10,
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    {formChild}
                  </Form.Item>
                );
              })}
            </div>
          </Form.Item>
          <Form.Item noStyle>
            <Flex
              align='center'
              style={{
                height: '40px',
                float: 'right',
              }}>
              <Button
                htmlType='reset'
                style={{
                  marginRight: 10,
                }}>
                {t('重置')}
              </Button>
              <Button htmlType='submit' color='primary'>
                {t('搜索')}
              </Button>
            </Flex>
          </Form.Item>
        </Form>
      </ConfigProvider>
    );
  },
);

export default FormHeader;
