import React from 'react';
const Home = React.lazy(() => import('./Home'));
const LogPage = React.lazy(() => import('./LogPage'));
const Projection = React.lazy(() => import('./Projection'));
const Empower = React.lazy(() => import('./Empower'));
const Login = React.lazy(() => import('./Login'));
const Error = React.lazy(() => import('./Error'));
const Test = React.lazy(() => import('./Test'));
const JobTemplate = React.lazy(() => import('./JobTemplate'));
const SubJobTemplate = React.lazy(
  () => import('./JobTemplate/JobTemplate.tsx'),
);
const EditJobTemplate = React.lazy(
  () => import('./JobTemplate/EditJobTemplate'),
);
const ApplicationJob = React.lazy(() => import('./ApplicationJob'));
const SubApplicationJob = React.lazy(
  () => import('./ApplicationJob/ApplicationJob.tsx'),
);
const RunApplicationJob = React.lazy(
  () => import('./ApplicationJob/RunApplicationJob'),
);
const CheckApplication = React.lazy(
  () => import('./ApplicationJob/checkApplication'),
);

const pages = {
  JobTemplate,
  Home,
  LogPage,
  Projection,
  Empower,
  Login,
  Error,
  Test,
  ApplicationJob,
  SubApplicationJob,
  SubJobTemplate,
  EditJobTemplate,
  RunApplicationJob,
  CheckApplication,
};

export default pages;
