import React, { useState, useRef } from 'react';
import ajax from '@/api';
import { useEffect } from 'react';
import { Layout, Table, Drawer, Form } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import FormHeader, { optionType } from '@/components/FormHeader';
import DrawerContent from './DrawerContent';
import { useSize } from 'ahooks';
import { t } from '@/languages';
import { useSelector } from 'react-redux';
const { Header, Content } = Layout;
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('int');

export type TableDataType = {
  key: string;
  time: string;
  args: string;
  level: string;
  traceId: string;
  caller: string;
  detail: string;
};
type tableOptionType = {
  columns: any; // title
  dataSource: any; // data
  scroll: any; // height
  isExpand?: boolean; // 是否有二级表
  onExpand?: any; //展开二级表触发的函数
  childrenInfo?: any; // 二级表所需data
};
const LogPage: React.FC = () => {
  const [form] = Form.useForm();
  const originTableColumns: ColumnsType<TableDataType> = [
    {
      title: t('序号'),
      dataIndex: 'key',
      key: 'key',
      width: 80,
      render: (_, _2, index) => `${index + 1}`,
    },
    {
      title: t('服务名称'),
      dataIndex: 'serviceName',
      key: 'serviceName',
      render: (text) => <a>{text}</a>,
    },
    {
      title: t('时间'),
      dataIndex: 'time',
      key: 'time',
    },
    {
      title: t('日志等级'),
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: t('日志信息'),
      key: 'msg',
      dataIndex: 'msg',
    },
    {
      title: t('接口标识'),
      key: 'api',
      dataIndex: 'api',
    },
    {
      title: t('跟踪ID'),
      key: 'traceId',
      dataIndex: 'traceId',
    },
    {
      title: t('操作'),
      dataIndex: 'do',
      key: 'do',
      render: (_, record, index) => {
        const optionClick = () => {
          setDrawerTitle(record.traceId);
          setDrawerContent(record);
          showDrawer();
          setSelectedIndex(index); // 设置选中的traceId
        };
        return (
          <a className='text-blue-400' onClick={optionClick}>
            {t('详情')}
          </a>
        );
      },
    },
  ];
  const [tableColumns, setTableColumns] = useState(originTableColumns);
  const originInputOption: optionType[] = [
    {
      key: 'date',
      value: 'date',
      type: 'dateRange',
      initialValue: [dayjs().subtract(7, 'day'), dayjs()],
      label: [t('操作时间')],
    },
    {
      key: 'level',
      value: 'level',
      type: 'multipleSelect',
      label: t('日志等级'),
      otherprops: new Map().set('options', [
        // { value: 'all', label: '全部' },
        { value: 'info', label: 'info' },
        { value: 'error', label: 'error' },
        { value: 'debug', label: 'debug' },
      ]),
    },
    {
      key: 'msg',
      value: 'msg',
      type: 'input',
      label: t('日志信息'),
    },
    {
      key: 'api',
      value: 'api',
      type: 'input',
      label: t('接口标识'),
    },
    {
      key: 'serviceName',
      value: 'serviceName',
      type: 'input',
      label: t('服务名称'),
    },
    {
      key: 'traceId',
      value: 'traceId',
      type: 'input',
      label: t('跟踪ID'),
    },
  ];
  const [inputOption, setInputOption] = useState(originInputOption);

  useEffect(() => {
    setTableColumns(originTableColumns);
    setInputOption(originInputOption);
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  const [tableData, setTableData] = useState<TableDataType[]>([]);
  //抽屉开关的控制变量
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  //抽屉的标题
  const [drawerTitle, setDrawerTitle] = useState<string>('');
  const [drawerContent, setDrawerContent] = useState<TableDataType>(
    {} as TableDataType,
  );
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  //搜索loading
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  //分页器
  const [pageData, setPageData] = useState({
    page: 1,
    page_size: 20,
  });
  //数据的总条数
  const [total, setTotal] = useState(0);

  const contentRef = useRef<HTMLDivElement>(null);
  const contentSize = useSize(contentRef);

  const [scrollY, setScrollY] = useState(0);
  useEffect(() => {
    if (contentSize) {
      setScrollY(contentSize.height - 158);
    }
  }, [contentSize]);

  const tableOption: tableOptionType = {
    columns: tableColumns,
    dataSource: tableData,
    scroll: { y: scrollY },
  };

  const getLogInfo = async (values: any) => {
    try {
      setSearchLoading(true);
      const res: any = await ajax.getLogInfo(values);
      if (res.code == 200) {
        setTimeout(() => {
          setTableData(res.data.res);
          setTotal(res.data.total);
        }, 200);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSearchLoading(false);
    }
  };

  const formValues = (values: any) => {
    const date = values.date ? dayjs(values.date).format('YYYY-MM-DD') : '';
    values.date = date;
    setPageData({ ...pageData, page: 1 });
  };

  //触发搜索的事件
  const search = () => {
    setSelectedIndex(null);
    const requestValues: Record<string, any> = {};
    for (const key in form.getFieldsValue()) {
      if (
        Object.prototype.hasOwnProperty.call(form.getFieldsValue(true), key) &&
        form.getFieldsValue(true)[key] !== undefined
      ) {
        requestValues[key] = form.getFieldsValue()[key];
      }
    }
    const requestParams = {
      page: pageData.page,
      pageSize: pageData.page_size,
      level: requestValues['level']?.split(','),
      startDate: requestValues['date'][0].format('YYYY-MM-DD'),
      endDate: requestValues['date'][1].format('YYYY-MM-DD'),
      msg: requestValues['msg'],
      api: requestValues['api'],
      traceId: requestValues['traceId'],
      serviceName: requestValues['serviceName'],
    };
    if (
      requestParams['level']?.length === 1 &&
      requestParams['level'][0] === ''
    ) {
      delete requestParams['level'];
    }
    getLogInfo(requestParams);
  };

  //展示Drawer
  const showDrawer = () => {
    setDrawerOpen(true);
  };
  //关闭drawer
  const closeDrawer = () => {
    setSelectedIndex(null);
    setDrawerOpen(false);
  };

  useEffect(() => {
    search();
  }, [pageData]);

  return (
    <Layout
      className='rounded-large overflow-hidden'
      style={{
        height: '100%',
        borderRadius: '0.75rem',
      }}>
      <Header
        style={{
          height: 'auto',
          paddingTop: '20px',
          borderRadius: '0.75rem',
        }}>
        <FormHeader options={inputOption} onSearch={formValues} form={form} />
      </Header>
      <Content
        style={{
          padding: '20px',
          paddingTop: '10px',
        }}
        ref={contentRef}>
        <Table
          rowClassName={(_, index) =>
            index === selectedIndex ? 'selected-row' : ''
          }
          {...tableOption}
          loading={searchLoading}
          pagination={{
            position: ['bottomCenter'],
            pageSize: pageData.page_size,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: any) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPageData({ ...pageData, page, page_size: pageSize });
            },
          }}
        />
      </Content>
      <Drawer
        open={drawerOpen}
        title={drawerTitle}
        onClose={closeDrawer}
        width={600}>
        <DrawerContent content={drawerContent} />
      </Drawer>
    </Layout>
  );
};

export default LogPage;
