import { useState, useEffect } from 'react';
import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
import type { TableDataType } from './index';
import { t } from '@/languages';
import { useSelector } from 'react-redux';

type DrawerContentProps = {
  content: TableDataType;
};

const DrawerContent: React.FC<DrawerContentProps> = ({ content }) => {
  const originItems: DescriptionsProps['items'] = [
    {
      label: t('跟踪ID'),
      span: 1,
      children: content.traceId,
    },
    {
      label: t('日志等级'),
      span: 1,
      children: content.level,
    },
    {
      label: t('时间'),
      span: 1,
      children: content.time,
    },
    {
      label: t('日志位置'),
      span: 1,
      children: content.caller,
    },
    {
      label: t('接口参数'),
      span: 2,
      children: content.args,
    },
    {
      label: t('详细信息'),
      span: 2,
      children: content.detail,
    },
  ];
  const [items, setItems] = useState(originItems);
  useEffect(() => {
    setItems(originItems);
  }, [useSelector((state: any) => state.global.currentLanguage)]);
  return (
    <Descriptions title={t('详细信息')} bordered column={2} items={items} />
  );
};

export default DrawerContent;
