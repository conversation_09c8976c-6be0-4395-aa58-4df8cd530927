import { useEffect, useState } from 'react';
import {
  Modal,
  TableColumnsType,
  Select,
  Form,
  Button,
  Popconfirm,
  Input,
} from 'antd';
import { TemplateFrameValue } from './types';
import ajax from '@/api';
import _ from 'lodash';
import DraggableTable from './components/DragTable';
import { t } from '@/languages';

interface StepModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  templateFrameValue: TemplateFrameValue[] | null;
  addTemplateByUuid: (uuid: string, remark: string) => void;
  deleteTemplateByUuid: (uuid: string, index: number) => void;
  changeTemplateSort: (dragIndex: number, hoverIndex: number) => void;
}

type DataType = {
  id: React.Key;
  key: string;
  templateName: string;
  isOrigin: boolean;
};

const StepModal: React.FC<StepModalProps> = ({
  templateFrameValue,
  addTemplateByUuid,
  deleteTemplateByUuid,
  changeTemplateSort,
  ...modalProps
}) => {
  const [form] = Form.useForm();
  const [tableData, setTableData] = useState<DataType[]>([]);
  const [selectedTemplateOptions, setSelectedTemplateOptions] = useState([]);
  const getJobSelectedTemplate = async () => {
    try {
      const res = await ajax.getJobSelectedTemplate();
      const list = _.get(res, 'data.sysJobTemplates', []);
      const options = list.map((item: any) => ({
        label: item.templateName,
        value: item.templateUuid,
      }));
      setSelectedTemplateOptions(options);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const _tableData = (templateFrameValue || []).map((item, index) => ({
      id: item.key,
      key: item.key + index,
      remark: item.remark,
      templateName: item.title,
      isOrigin: true,
    }));
    setTableData(_tableData);
    getJobSelectedTemplate();
  }, [templateFrameValue]);
  const columns: TableColumnsType<DataType> = [
    {
      key: 'sortnumber',
      title: t('排序'),
      dataIndex: 'sortnumber',
      render: (_, __, index) => index + 1,
    },
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
    },
    {
      key: 'remark',
      title: t('模版别名'),
      dataIndex: 'remark',
    },
    {
      key: 'templateName',
      title: t('模版名称'),
      dataIndex: 'templateName',
    },
    {
      key: 'isOrigin',
      title: t('来源'),
      dataIndex: 'isOrigin',
      render: (text) => (text === 'global' ? 'global' : 'share'),
    },
    {
      key: 'delete',
      title: t('操作'),
      dataIndex: 'delete',
      render: (_, record, index) => (
        <Popconfirm
          title={t('确定删除这个模版吗?(参数不可恢复)')}
          // description='确定删除这个模版吗？（参数不可恢复）'
          onConfirm={() => deleteTemplateByUuid(record.id as string, index)}
          okText={t('确定')}
          cancelText={t('取消')}>
          <Button danger>{t('删除')}</Button>
        </Popconfirm>
      ),
    },
  ];
  return (
    <Modal {...modalProps} title={t('选择模版')} width={1200}>
      <header>
        <Form layout='inline' form={form}>
          <Form.Item label={t('选择模版')} name='templateSelectedkUuid'>
            <Select style={{ width: 200 }} options={selectedTemplateOptions} />
          </Form.Item>
          <Form.Item label={t('模版别名')} name='templateAka'>
            <Input />
          </Form.Item>
          <Form.Item>
            <Button
              onClick={() => {
                addTemplateByUuid(
                  form.getFieldValue('templateSelectedkUuid'),
                  form.getFieldValue('templateAka'),
                );
              }}>
              {t('添加')}
            </Button>
          </Form.Item>
        </Form>
      </header>
      <br />
      <main>
        <label className='text-h4'>{t('已选择模版')}</label>
        <DraggableTable<DataType>
          columns={columns}
          initialData={tableData}
          onChangeSort={changeTemplateSort}
        />
      </main>
    </Modal>
  );
};

export default StepModal;
