import { useState } from 'react';
import { App } from 'antd';
import type { FormField } from '@repo/ui';
import { TemplateFrameValue } from './types';
import SaveAsModal, { SaveAsModalProps } from './components/SaveAsModal.tsx';
import ajax from '@/api';
import { useSearchParams, useNavigate } from 'react-router-dom';
import StepModal from './StepModal';
import _ from 'lodash';
import { EditJobTemplatePage } from '@repo/ui';
import { t } from '@/languages';

export default function JobTemplate() {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const templateUuid = searchParams.get('id');
  const templateName = searchParams.get('name');
  const pageType = searchParams.get('type');
  const isCreated = searchParams.get('isCreated');
  const { message } = App.useApp();

  const [stepModalOpen, setStepModalOpen] = useState(false);
  const openStepModal = () => setStepModalOpen(true);
  const closeStepModal = () => setStepModalOpen(false);

  const [jobFrameValue, setJobFrameValue] = useState<FormField[]>([]);
  const [templateFrameValue, setTemplateFrameValue] = useState<
    TemplateFrameValue[]
  >([]);

  const [formData, setFormData] = useState<any>({});

  const [saveAsModalData, setSaveAsModalData] = useState<SaveAsModalProps>(
    () => ({
      isOpen: false,
      onClose: () => setSaveAsModalData((prev) => ({ ...prev, isOpen: false })),
      onSaveAs: (name: string) => {
        setFormData((prevFormData: any) => {
          const updatedFormData = {
            ...prevFormData,
            templateName: name,
            templateUuid: null,
          };
          console.log(updatedFormData);
          createJobTemplate(updatedFormData).then((res) => {
            if (res === 0) {
              // navigate('/jobTemplate');
            }
          });
          return updatedFormData;
        });
      },
    }),
  );

  const onFinish = () => {
    if (templateUuid === null) {
      createJobTemplate(formData).then((res) => {
        if (res === 0) {
          navigate('/jobTemplate');
        }
      });
    } else {
      updateJobTemplate(formData).then((res) => {
        if (res === 0) {
          navigate('/jobTemplate');
        }
      });
    }
  };

  //创建模版
  const createJobTemplate = async (formData: any) => {
    try {
      const res: any = await ajax.createJobTemplate(formData);
      if (res.code === 0) {
        message.success(t('创建成功'));
      }
      return 0;
    } catch (error) {
      message.error(t('创建失败'));
      console.error(error);
    }
    return 1;
  };

  //修改模版
  const updateJobTemplate = async (formData: any) => {
    try {
      const res: any = await ajax.updateJobTemplate(formData);
      if (res.code === 0) {
        message.success(t('修改成功'));
      }
      return 0;
    } catch (error) {
      message.error(t('修改失败'));
      console.error(error);
    }
    return 1;
  };

  //获取template详细信息
  const getJobTemplateFrameAndValue = async (templateUuid: string) => {
    try {
      const res = await ajax.getJobTemplateFrameAndValue({ templateUuid });
      const jobFrameValue = _.get(res, 'data.jobFrameValue', []) || [];
      const templateFrameValue =
        _.get(res, 'data.templateFrameValue', []) || [];
      jobFrameValue.forEach((field: any) => {
        field.required = false;
      });
      setJobFrameValue(jobFrameValue);
      setTemplateFrameValue(templateFrameValue);
    } catch (error) {
      console.error(error);
    }
  };

  //打开另存为模态框
  const openSaveAsModal = () => {
    setSaveAsModalData((prev) => ({ ...prev, isOpen: true }));
  };

  //通过templateUuid添加模版
  const addTemplateByUuid = async (templateUuid: string, remark: string) => {
    if (!templateUuid) {
      return;
    }
    try {
      const res = await ajax.getJobTemplateFrameAndValue({ templateUuid });
      const templateFrameValue = _.get(res, 'data.templateFrameValue', []);
      templateFrameValue.forEach((item: any) => {
        item.remark = remark;
      });
      setTemplateFrameValue((prev) => {
        return [...prev, ...templateFrameValue];
      });
    } catch (error) {
      console.error(error);
    }
  };

  //删除模版
  const deleteTemplateByUuid = (templateUuid: string, index: number) => {
    setTemplateFrameValue((prev) => {
      return prev.filter(
        (item, idx) => item.key !== templateUuid || idx !== index,
      );
    });
  };

  const changeTemplateSort = (dragIndex: number, hoverIndex: number) => {
    const dragItem = templateFrameValue[dragIndex];
    setTemplateFrameValue((prev) => {
      const _prev = [...prev];
      _prev.splice(dragIndex, 1);
      _prev.splice(hoverIndex, 0, dragItem);
      return _prev;
    });
  };

  const getJobTemplateFrame = async () => {
    try {
      const res = await ajax.getJobTemplateFrameAndValue({});
      const jobFrameValue = _.get(res, 'data.jobFrameValue', []) || [];
      jobFrameValue.forEach((field: any) => {
        field.required = false;
      });
      setJobFrameValue(jobFrameValue);
    } catch (error) {
      console.error(error);
    }
  };

  // useEffect(() => {
  //   form?.resetFields();
  //   setJobFrameValue([]);
  //   setTemplateFrameValue([]);
  //   if (templateUuid !== '' && templateUuid !== null) {
  //     getJobTemplateFrameAndValue(templateUuid);
  //   } else {
  //     getJobTemplateFrame();
  //   }
  // }, [templateUuid, templateName]);

  return (
    <EditJobTemplatePage
      badgeCount={templateFrameValue.length}
      returnFunc={() => navigate(-1)}
      isCreated={isCreated || ''}
      jobFrameValue={jobFrameValue}
      pageType={pageType || ''}
      templateFrameValue={templateFrameValue}
      onFormFinish={onFinish}
      updateJobTemplate={updateJobTemplate}
      templateName={templateName || ''}
      templateUuid={templateUuid || ''}
      changeFormData={setFormData}
      token={localStorage.getItem('int-token') || ''}
      openSaveAsModal={openSaveAsModal}
      getJobTemplateFrameAndValue={getJobTemplateFrameAndValue}
      getJobTemplateFrame={getJobTemplateFrame}
      stepModalData={{
        open: stepModalOpen,
        openStepModal: openStepModal,
      }}>
      <StepModal
        open={stepModalOpen}
        onCancel={closeStepModal}
        onOk={closeStepModal}
        addTemplateByUuid={addTemplateByUuid}
        deleteTemplateByUuid={deleteTemplateByUuid}
        templateFrameValue={templateFrameValue}
        changeTemplateSort={changeTemplateSort}
      />
      <SaveAsModal {...saveAsModalData} />
    </EditJobTemplatePage>
  );
}
