import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, App, Popconfirm } from 'antd';
import { type FormItemType } from './components';
import { useNavigate } from 'react-router-dom';
import ajax from '@/api';
import _ from 'lodash';
import type { TableColumnsType } from 'antd';
import { JobTemplatePage as JobTemplateComponent } from '@repo/ui';
import { t } from '@/languages';
import { useSelector } from 'react-redux';

interface DataType {
  id: React.Key;
  templateName: string;
  templateUuid: string;
  stepsNum: number;
  isOrigin: boolean;
  isSystem: boolean;
  isSystemName: string;
  sourceCode: number;
  sourceName: string;
}

const JobTemplatePage = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [tableData, setTableData] = useState<DataType[]>([]);
  const [shareUserList, setShareUserList] = useState([]);
  const [shareUserId, setShareUserId] = useState<number>(-1);
  const [shareTemplateUuid, setShareTemplateUuid] = useState<string>('');
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [searchParams, setSearchParams] = useState({
    name: '',
    stepNums: 0,
  });

  const closeShareModal = () => setShareModalOpen(false);
  const openShareModal = () => setShareModalOpen(true);

  const [pageData, setPageData] = useState({
    page: 1,
    pageSize: 10,
  });
  const [pageTotal, setPageTotal] = useState(0);
  const originInputFormItems: FormItemType[] = [
    {
      type: 'text',
      field: 'name',
      label: t('名称'),
      required: false,
      value: '',
    },
    {
      type: 'number',
      field: 'stepNums',
      label: t('步数'),
      required: false,
      min: 0,
      max: 100,
    },
  ];
  const [InputFormItems, setInputFormItems] = useState(originInputFormItems);

  const getUserListByCoId = async () => {
    try {
      const res = await ajax.getUserListByCoId();
      const list = _.get(res, 'data.items', []) || [];
      setShareUserList(
        list.map((item: any) => ({
          ...item,
          label: item.username,
          value: item.id,
        })),
      );
    } catch (error) {
      console.log(error);
    }
  };

  const shareJobTemplateTo = async (templateUuid: string, sharedTo: number) => {
    try {
      const res: any = await ajax.shareJobTemplateTo({
        templateUuid,
        sharedTo,
      });
      if (res.code === 200) {
        message.success(t('分享成功'));
        closeShareModal();
      }
    } catch (error) {
      message.error(t('分享失败'));
      console.log(error);
    }
  };

  //判断这个id的模版是否可以进行编辑
  const isCanEdit = async (id: string) => {
    try {
      const res = await ajax.getJobTemplateByUserUpdate({});
      const list = _.get(res, 'data.sysJobTemplates', []);
      const idList = list.map((item: any) => item.templateUuid);
      return !!idList.includes(id);
    } catch (error) {
      console.log(error);
    }
  };

  const deleteJobTemplate = async (templateUuid: string) => {
    try {
      const res: any = await ajax.deleteJobTemplate({ templateUuid });
      if (res.code === 200) {
        message.success(t('删除成功'));
        getJobTemplateByUserOnlyRead();
      }
    } catch (error) {
      message.error(t('删除失败'));
      console.log(error);
    }
  };

  const originColumns: TableColumnsType<DataType> = [
    // { title: t('ID'), dataIndex: 'id', key: 'id', width: 80 },
    { title: t('模版名称'), dataIndex: 'templateName', key: 'templateName' },
    { title: t('步数'), dataIndex: 'stepsNum', key: 'stepsNum' },
    {
      title: t('来源'),
      dataIndex: 'isOrigin',
      key: 'isOrigin',
      render: (text) => (text ? 'global' : 'share'),
    },
    {
      title: t('系统内置'),
      dataIndex: 'isSystem',
      key: 'isSystem',
      render: (text) => (text ? '是' : '否'),
    },
    {
      title: t('操作'),
      align: 'center',
      key: 'operation',
      width: 300,
      render: (_: any, record: DataType) => (
        <div>
          <Button
            type={'link'}
            onClick={() => {
              isCanEdit(record.templateUuid).then((res) => {
                let type: string = 'view';
                if (res) {
                  type = 'edit';
                }
                navigate(
                  `/jobTemplate/editJobTemplate?id=${
                    record.templateUuid
                  }&name=${record.templateName}&type=${type}&isCreated=${
                    record.sourceCode === 4
                  }`,
                );
              });
            }}>
            {t('查看')}
          </Button>
          <Button
            type='link'
            onClick={() => {
              setShareTemplateUuid(record.templateUuid);
              openShareModal();
            }}>
            {t('分享')}
          </Button>
          <Popconfirm
            title={t('确定删除这个模版吗?')}
            onConfirm={() => deleteJobTemplate(record.templateUuid)}
            okText={t('确定')}
            cancelText={t('取消')}>
            <Button type='link' danger>
              {t('删除')}
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];
  const [columns, setColumns] = useState(originColumns);
  useEffect(() => {
    setInputFormItems(originInputFormItems);
    setColumns(originColumns);
  }, [useSelector((state: any) => state.global.currentLanguage)]);
  //获取可读的jobTemplate
  const getJobTemplateByUserOnlyRead = async (data?: any) => {
    try {
      const res: any = await ajax.getJobTemplateByUserOnlyRead(data);
      const list = _.get(res, 'data.sysJobTemplates', []);
      setTableData(list);
      setPageTotal(res.data.total || 0);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getUserListByCoId();
  }, []);

  useEffect(() => {
    getJobTemplateByUserOnlyRead({
      page: pageData.page,
      pageSize: pageData.pageSize,
      steps: searchParams.stepNums,
      templateName: searchParams.name,
    });
  }, [pageData, searchParams]);

  return (
    <JobTemplateComponent
      InputFormItems={InputFormItems}
      onFinish={(values: any) => {
        setSearchParams({
          name: values.name,
          stepNums: values.stepNums,
        });
      }}
      onClickNewTemplate={() =>
        navigate(`/jobTemplate/editJobTemplate?type=edit`)
      }
      mainTableData={{
        columns,
        dataSource: tableData,
        loading: false,
      }}
      pageData={{
        current: pageData.page,
        total: pageTotal,
        pageSize: pageData.pageSize,
        onPageChange: (page: number) => {
          setPageData({ ...pageData, page });
        },
      }}
      modalData={{
        title: t('分享'),
        open: shareModalOpen,
        onCancel: closeShareModal,
        onFinish: () => {
          shareJobTemplateTo(shareTemplateUuid, shareUserId);
        },
        select: {
          placeholder: t('请选择'),
          options: shareUserList,
          onChange: (value: any) => {
            setShareUserId(value);
          },
        },
      }}
    />
  );
};

export default JobTemplatePage;
