import { Outlet } from 'react-router-dom';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const JobTemplate: React.FC = () => {
  const navigate = useNavigate();
  const sence = localStorage.getItem('scene');
  useEffect(() => {
    if (sence === 'gen-int') {
      navigate('/404');
    }
  }, []);
  return (
    <div className='w-full h-full bg-white rounded-xl p-[30px]'>
      <header></header>
      <main className='mt-[10px] h-full'>
        <Outlet />
      </main>
    </div>
  );
};

export default JobTemplate;
