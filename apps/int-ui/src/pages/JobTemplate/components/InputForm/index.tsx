import {
  Form,
  Input,
  Checkbox,
  Select,
  InputN<PERSON>ber,
  DatePicker,
  Row,
  Col,
  Button,
} from 'antd';
import type { FormItemType } from './types';

type InputFormProps = {
  data: FormItemType[];
  onFinish: (values: any) => void;
};
const InputForm: React.FC<InputFormProps> = ({ data, onFinish }) => {
  const renderInputComponents = (
    inputFormItem: FormItemType,
  ): React.ReactNode => {
    const { type } = inputFormItem;
    let component: React.ReactNode;
    switch (type) {
      case 'number':
        component = (
          <InputNumber
            min={inputFormItem.min}
            max={inputFormItem.max}
            className='w-full'
          />
        );
        break;
      case 'select':
        component = <Select options={inputFormItem.options} />;
        break;

      case 'date':
        component = <DatePicker className='w-full' />;
        break;

      case 'checkbox-group':
        component = <Checkbox.Group options={inputFormItem.options} />;
        break;

      case 'text':
        component = <Input className='w-full' />;
        break;
      default:
        component = null;
        break;
    }
    return component;
  };
  return (
    <div className='w-full bg-white h-full'>
      <Form labelAlign='right' layout='inline' onFinish={onFinish}>
        <Row className='w-full' gutter={[32, 16]}>
          {data.map((item, index) => {
            return (
              <Col span={8} key={index + item.type}>
                <Form.Item
                  {...item}
                  initialValue={(item.type === 'text' && item?.value) || ''}
                  name={item.field}
                  rules={[{ required: item.required }]}>
                  {renderInputComponents(item)}
                </Form.Item>
              </Col>
            );
          })}
          <Col span={4} className='flex gap-2'>
            <Button htmlType='reset'>{'重置'}</Button>
            <Button htmlType='submit' type='primary'>
              {'查询'}
            </Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
export default InputForm;
