import { useState } from 'react';
import { App } from 'antd';
import { ExpandTableContent } from '@repo/ui';
import ajax from '@/api';
import _ from 'lodash';

const ExpandTableContent_ = ({ record }: { record: any }) => {
  const { message } = App.useApp();

  const [currentLogs, setCurrentLogs] = useState([]);
  const [isInit, setIsInit] = useState(true);
  const [detailProgressData, setDetailProgressData] = useState([]);

  const getLogsScpLogger = async (seq: number, taskUuid: string) => {
    const res: any = await ajax.getLogsScpLogger({
      taskUuid,
      seq,
    });
    if (res.code === 200) {
      const list = _.get(res, 'data.list', []);
      setCurrentLogs(list);

      setIsInit(false);
    } else {
      message.error(res.msg);
    }
  };

  /**
   * @description 获取日志信息
   */

  const getCronTaskDetail = async (taskUuid: string) => {
    try {
      const res: any = await ajax.getCronTaskDetail({ taskUuid });
      if (res.code === 200) {
        const list = _.get(res, 'data.taskDetailList', []);
        setDetailProgressData(list);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <ExpandTableContent
      record={record}
      getLogsScpLogger={getLogsScpLogger}
      getCronTaskDetail={getCronTaskDetail}
      currentLogs={currentLogs}
      isInit={isInit}
      detailProgressData={detailProgressData}
    />
  );
};

export default ExpandTableContent_;
