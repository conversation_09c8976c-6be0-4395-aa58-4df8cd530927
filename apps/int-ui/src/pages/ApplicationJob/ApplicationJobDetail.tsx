import { useState } from 'react';
import { removeUndefinedValues } from '@/utils';
import { ApplicationJobDetail } from '@repo/ui';
import { SelectProps, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import ExpandTableContent from './components/ExpandTableContent';
import dayjs from 'dayjs';
import ajax from '@/api';
import _ from 'lodash';

export default function ApplicationJobDetail_() {
  const navigate = useNavigate();
  const intUser = localStorage.getItem('int-user');
  const user = JSON.parse(intUser ? intUser : '{}');

  const preferPaPvId = user?.preferPaPvId || -1;
  const [pageData, setPageData] = useState<{ page: number; pageSize: number }>({
    page: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState<number>(pageData.pageSize);
  const [formData, setFormData] = useState<any>({
    ...pageData,
    startTime: dayjs().startOf('day').format(),
    endTime: dayjs().endOf('day').format(),
    paPvId: preferPaPvId,
  });
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [userList, setUserList] = useState<SelectProps['options']>([]);

  /**
   * @description 搜索接口
   */
  const getCornJobTask = async () => {
    const params = {
      ...removeUndefinedValues(formData),
      ...pageData,
    };
    const res: any = await ajax.getCronJobTask({
      endTime: '',
      // paPvId: 0,
      startTime: '',
      ...params,
    });
    if (res.code === 200) {
      const list = _.get(res, 'data.cronTaskResponseList', []) || [];
      if (list.length === 0) {
        // messageApi.info('暂无数据');
      }
      setTotal(res.data.total || pageData.pageSize);
      if (list.length === 1000)
        messageApi.warning('结果可能已经超过1000条，只显示前1000条数据');
      setDataSource(list);
    } else {
      messageApi.error(res.msg);
    }
  };

  /**
   * @description 获取所有用户的信息
   */
  const getUserListByCoIdContainsSelf = async () => {
    try {
      // const res: any = await ajax.getUserListByCoIdContainsSelf();
      // if (res.code === 200) {
      //   const list = _.get(res, 'data.list', []) || [];
      //   setUserList(
      //     list.map((item: any) => ({
      //       label: item.nickName,
      //       value: item.id,
      //     })),
      //   );
      setUserList([]);
      // } else {
      //   message.error(res.msg);
      // }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <ApplicationJobDetail
      navigate={navigate}
      ExpandTableContent={ExpandTableContent}
      userList={userList}
      total={total}
      pageData={pageData}
      dataSource={dataSource}
      contextHolder={contextHolder}
      getCornJobTask={getCornJobTask}
      getUserListByCoIdContainsSelf={getUserListByCoIdContainsSelf}
      setFormData={setFormData}
      setPageData={setPageData}
    />
  );
}
