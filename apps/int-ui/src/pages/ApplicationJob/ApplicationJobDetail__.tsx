import React, { useEffect, useState, useCallback } from 'react';
import { Pagination } from 'scmify-components';
import { RightOutlined, DownOutlined, CopyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import ExpandTableContent from '@/pages/ApplicationJob/components/ExpandTableContent.tsx';
import {
  Table,
  Tag,
  DatePicker,
  Input,
  Form,
  Select,
  Button,
  message,
  ConfigProvider,
  Empty,
  Flex,
} from 'antd';
import ajax from '@/api';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { t } from '@/languages';
import _ from 'lodash';
import { useSelector } from 'react-redux';

type GetCronJobTaskReq = {
  startTime: string;
  endTime: string;
  jobId?: Array<string>;
  taskState?: Array<number>;
  taskTag?: string;
  jobName?: string;
  jobMode?: string;
  page: number;
  pageSize: number;
};

type GetCronJobTaskRes = {
  jobId: string;
  jobName: string;
  jobMode: string;
  taskStartTime: number;
  taskEndTime: number;
  taskState: number;
  taskTag: string;
  taskUuid: string;
};

const ApplicationJobDetail: React.FC = () => {
  const navigate = useNavigate();
  const [pageData, setPageData] = useState<{ page: number; pageSize: number }>({
    page: 1,
    pageSize: 20,
  });
  const [total, setTotal] = useState<number>(pageData.pageSize);
  const [formData, setFormData] = useState<GetCronJobTaskReq>({
    ...pageData,
    startTime: dayjs().startOf('day').format(),
    endTime: dayjs().endOf('day').format(),
  });
  const [dataSource, setDataSource] = useState<GetCronJobTaskRes[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const [messageApi, contextHolder] = message.useMessage();

  const originColumns: ColumnsType<GetCronJobTaskRes> = [
    {
      title: t('定时任务名称'),
      key: 'jobName',
      width: 200,
      dataIndex: 'jobName',
      render: (text: string, record: any) => {
        return (
          <a
            className='text-black'
            onClick={() => {
              const params = {
                taskUuid: record.taskUuid,
                jobId: record.jobId,
                taskName: text,
                taskStateCode: record.taskStateCode,
              };
              const queryString = new URLSearchParams(params).toString();
              navigate(`/applicationjob/checkApplication?${queryString}`);
            }}>
            {text}
            <CopyOutlined className={'ml-[10px]'} />
          </a>
        );
      },
    },
    {
      title: t('执行任务状态'),
      key: 'taskState',
      dataIndex: 'taskState',
      render: (value: number) => {
        return (
          <Tag color={value === 1 ? 'blue' : value === 2 ? 'green' : 'red'}>
            {value === 1 ? t('已完成') : value === 2 ? t('执行中') : t('失败')}
          </Tag>
        );
      },
    },
    {
      title: t('定时任务模式'),
      key: 'jobMode',
      dataIndex: 'jobMode',
    },
    {
      title: t('开始时间'),
      key: 'taskStartTime',
      width: 200,
      dataIndex: 'taskStartTime',
      render: (value: number) =>
        dayjs(value * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: t('结束时间'),
      key: 'taskEndTime',
      width: 200,
      dataIndex: 'taskEndTime',
      render: (value: number) => {
        if (value === 0) {
          return '';
        } else return dayjs(value * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ].map((item) => ({ ...item, align: 'center' }));
  const [columns, setColumns] = useState(originColumns);
  useEffect(() => {
    setColumns(originColumns);
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  /**
   * @description 搜索接口
   */
  const getCornJobTask = async () => {
    const res: any = await ajax.getCronJobTask({ ...formData, ...pageData });
    if (res.code === 200) {
      const list = _.get(res, 'data.cronTaskResponseList', []) || [];
      if (list.length === 0) {
        messageApi.info(t('暂无数据'));
      }
      setTotal(res.data.total || pageData.pageSize);
      if (list.length === 1000)
        messageApi.warning(t('结果可能已经超过1000条, 只显示前1000条数据'));
      setDataSource(list);
    } else {
      messageApi.error(res.msg);
    }
  };

  const changePageData = useCallback((page: number, pageSize: number) => {
    setPageData({ page, pageSize });
  }, []);

  useEffect(() => {
    setLoading(true);
    getCornJobTask();
    setLoading(false);
  }, [pageData]);

  return (
    <div>
      {contextHolder}
      <HeaderForm
        onSearch={(values: Omit<GetCronJobTaskReq, 'page' | 'pageSize'>) => {
          setFormData({ ...values, ...pageData, page: 1 } as GetCronJobTaskReq);
          setPageData({ ...pageData, page: 1 });
        }}
      />
      <ConfigProvider
        renderEmpty={() => (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t(t('暂无数据'))}
          />
        )}
        theme={{
          components: {
            Table: {
              rowExpandedBg: '#fff',
            },
          },
        }}>
        <Table
          expandable={{
            expandedRowRender: (record) => (
              <ExpandTableContent record={record} />
            ),
            expandRowByClick: true,
            expandIcon: ({ expanded, onExpand, record }) =>
              expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              ),
          }}
          loading={loading}
          columns={columns}
          rowKey={(record) => record.jobId + record.taskStartTime}
          dataSource={dataSource}
          pagination={false}
          scroll={{ y: 'calc(100vh - 500px)' }}
        />
      </ConfigProvider>
      <div className='flex items-center justify-center mt-8'>
        <Pagination
          current={pageData.page}
          pageSize={pageData.pageSize}
          total={total}
          onPageChange={changePageData}
        />
      </div>
    </div>
  );
};
type HeaderFormProps = {
  onSearch: (values: any) => void;
};
const HeaderForm: React.FC<HeaderFormProps> = ({ onSearch }) => {
  const navigate = useNavigate();
  const isGenSence = localStorage.getItem('scene') === 'gen-int';
  type FilterType = {
    pareaId: string;
    timeRange: [dayjs.Dayjs, dayjs.Dayjs];
    taskState?: Array<number>;
    taskTag?: string;
    jobName?: string;
    jobMode?: string;
  };
  const [form] = Form.useForm();
  const [envList, setEnvList] = useState<{ label: string; value: string }[]>(
    [],
  );

  const onFinish = (values: FilterType) => {
    const { timeRange, ...rest } = values;
    const startTime = timeRange[0].format(); // format without any arguments will use the default format
    const endTime = timeRange[1].endOf('day').format();
    onSearch({ ...rest, startTime, endTime });
  };
  const getEnvList = async () => {
    const res: any = await ajax.getAllEnvList();
    if (res.code === 200) {
      const list = res.data.list.map((item: any) => {
        return {
          label: item.envName,
          value: item.id,
        };
      });
      setEnvList(list);
      console.log(list);
    }
  };
  useEffect(() => {
    getEnvList();
  }, []);

  const styles = {
    formLabel: {
      flex: 1,
    },
  };

  return (
    <div>
      <Form<FilterType>
        form={form}
        layout='vertical'
        labelAlign={'right'}
        onFinish={onFinish}>
        <Form.Item noStyle>
          <Flex vertical={false} gap={8}>
            <Form.Item
              label={t('执行环境')}
              name='pareaId'
              style={styles.formLabel}>
              <Select options={envList} />
            </Form.Item>
            <Form.Item
              label={t('定时任务名称')}
              name='jobName'
              style={styles.formLabel}>
              <Input />
            </Form.Item>
            <Form.Item
              label={t('执行任务标识')}
              name='taskTag'
              style={styles.formLabel}>
              <Input />
            </Form.Item>
            <Form.Item
              label={t('定时任务模式')}
              name='jobMode'
              style={styles.formLabel}>
              <Select
                allowClear
                options={[
                  { label: t('小时单位'), value: 'time' },
                  { label: t('天单位'), value: 'day' },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={t('任务状态')}
              name='taskState'
              style={styles.formLabel}>
              <Select
                allowClear
                mode='multiple'
                maxTagCount={2}
                options={[
                  { label: t('已完成'), value: 1 },
                  { label: t('执行中'), value: 2 },
                  { label: t('待执行'), value: 3 },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={t('时间范围')}
              name='timeRange'
              rules={[{ required: true }]}
              initialValue={[dayjs().startOf('day'), dayjs().endOf('day')]}
              style={styles.formLabel}>
              <DatePicker.RangePicker className='w-72' />
            </Form.Item>
          </Flex>
        </Form.Item>
        <Form.Item noStyle>
          <Flex vertical={false} gap={10} justify='flex-end'>
            <Button type='primary' htmlType='submit'>
              {t('查询')}
            </Button>
            <Button htmlType='reset'>{t('重置')}</Button>
            {!isGenSence && (
              <Button
                type='primary'
                onClick={() => {
                  navigate('/applicationjob/runApplicationJob');
                }}>
                {t('运行计划')}
              </Button>
            )}
          </Flex>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ApplicationJobDetail;
