import { Button, Form, Input, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ajax from '@/api';

type CaptchaDataType = {
  captchaId: string;
  picPath: string;
  msg: string;
};

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [captchaData, setCaptchaData] = useState<CaptchaDataType>({
    captchaId: '',
    picPath: '',
    msg: '',
  });

  // 获取验证码
  const getCaptcha = async () => {
    const res: any = await ajax.getCaptcha();
    if (res.code === 200) {
      setCaptchaData({
        msg: res.message,
        captchaId: res.data.captchaId,
        picPath: res.data.picPath,
      });
    }
  };

  // 登陆
  const login = async (values: any) => {
    setLoading(true);
    const loginParams: object = {
      username: values.username,
      password: values.password,
      coCode: values.coCode,
      captcha: values.captcha,
      captchaId: captchaData.captchaId,
    };
    try {
      const res: any = await ajax.login(loginParams);
      setLoading(false);
      if (res.code === 200) {
        localStorage.setItem('int-token', res.data.token);
        localStorage.setItem('int-user', JSON.stringify(values.username));
        localStorage.setItem('scene', res.data.scene);
        localStorage.removeItem('scp-token');
        navigate('/home');
      } else {
        getCaptcha();
      }
    } catch {
      getCaptcha();
    }
  };

  // 登陆提交表单
  const onFinish = (values: any) => {
    login(values);
  };

  useEffect(() => {
    getCaptcha();
    localStorage.removeItem('int-token');
    localStorage.removeItem('int-user');
  }, []);

  return (
    <div className='flex justify-center items-center h-screen'>
      <Form
        form={form}
        name='login'
        initialValues={{ remember: true }}
        onFinish={onFinish}
        className='w-96'>
        <img
          src='scmify_logo/PNG/colorful-logo-font.png'
          alt='logo'
          className=' w-[250px] mx-auto mb-10'
        />
        <Form.Item
          name='username'
          rules={[{ required: true, message: '请输入用户名' }]}>
          <Input placeholder='用户名' onChange={() => {}} />
        </Form.Item>
        <Form.Item
          name='password'
          rules={[{ required: true, message: '请输入密码' }]}>
          <Input.Password placeholder='密码' onChange={() => {}} />
        </Form.Item>
        <Form.Item
          name='coCode'
          rules={[{ required: true, message: '请输入公司名称或代码' }]}>
          <Input placeholder='公司名称或代码' onChange={() => {}} />
        </Form.Item>
        <Form.Item name='captcha' dependencies={['username', 'password']}>
          <Space.Compact>
            <Input placeholder='验证码' style={{ width: '350px' }} />
            <img
              src={captchaData.picPath}
              alt='验证码'
              className='h-8'
              onClick={getCaptcha}
            />
          </Space.Compact>
        </Form.Item>
        <Form.Item>
          <Button htmlType='submit' loading={loading} className='w-full mb-4'>
            登录
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Login;
