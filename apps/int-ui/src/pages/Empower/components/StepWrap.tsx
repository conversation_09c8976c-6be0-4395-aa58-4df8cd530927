/* eslint-disable prettier/prettier */
import { Layout, message } from 'antd';
import React, { useState, useEffect } from 'react';
import Steps from '../../../components/Steps';
import AddSystem from '../components/AddSystem';
import AddEmpower from '../components/AddEmpower';
import SuccessAdd from '../components/SuccessAdd';
import store from '@/store';
import {
    setTagListDataAction,
    setFormDataAction,
} from '@/store/reducers/empowerSlice';
import ajax from '../../../api/Empower';
import { t } from '@/languages';
import { useSelector } from 'react-redux';

const {Content} = Layout;

type propsType = {
    envId: number;
    getPageNum: any;
};

const StepWrap: React.FC<propsType> = (props) => {
    const {envId, getPageNum} = props;
    const [current, setCurrent] = useState(0);
    const originSteps = [
        {
            title: t('第三方应用授权'),
        },
        {
            title: t('更新系统登陆信息'),
        },
        {
            title: t('连接测试'),
        },
    ];
    const [steps, setSteps] = useState(originSteps);
    useEffect(() => {
        setSteps(originSteps);
    }, [useSelector((state: any) => state.global.currentLanguage)]);
    const items = steps.map((item) => ({key: item.title, title: item.title}));
    const stepsOption = {
        steps,
        current,
        items,
    };

    let isproceed: boolean = false;

    function firstNext(value: any) {
        value.map((item: any) => {
            if (item.isChoose === true) {
                isproceed = true;
            }
        });
        store.dispatch(setTagListDataAction(value));
    }

    function secondNext(value: any) {
        store.dispatch(setFormDataAction(value));
    }

    const updateThirdpartySystem = async () => {
        console.log(store.getState().empower.FORM_DATA);
        if (!store.getState().empower.FORM_DATA) {
            return await ajax.updateThirdpartySystem(
                store.getState().empower.CHOOSE_SYSTEM_INFO,
            );
        } else {
            const systemInfo = store.getState().empower.CHOOSE_SYSTEM_INFO as any;
            const formData = store.getState().empower.FORM_DATA as any;
            const formDataArr = Object.entries(formData);
            const auth = [...systemInfo.auth,];
            const newAuth: any[] = [];
            for (let i = 0; i < auth.length; i++) {
                for (let j = 0; j < formDataArr.length; j++) {
                    if (auth[i].field === formDataArr[j][0]) {
                        newAuth.push({
                            ...auth[i],
                            value: formDataArr[j][1],
                        });
                    }
                }
            }
            const params = {
                ...store.getState().empower.CHOOSE_SYSTEM_INFO,
                auth: newAuth,
                systemIp: formDataArr[formDataArr.length-1][1]
            };
            console.log(params,"params")
            return await ajax.updateThirdpartySystem(params);
        }
    };

    const next = async () => {
        if (current === 0) {
            if (isproceed) {
                setCurrent(current + 1);
                getPageNum(current + 1);
                isproceed = false;
            } else {
                message.error(t('请选择第三方应用'));
            }
        } else if (current === 1) {
            const submitRes = ((await updateThirdpartySystem()) as any) || {};
            if (submitRes.code === 200) {
                setCurrent(current + 1);
                getPageNum(current + 1);
                isproceed = false;
            } else {
                message.error(t('错误'));
            }
        }
    };
    const prev = () => {
        setCurrent(current - 1);
        getPageNum(current - 1);
    };
    const goBackFirstPage = () => {
        setCurrent(0);
        getPageNum(0);
    };

    useEffect(() => {
        getPageNum(0);
    }, []);

    return (
        <Layout className='h-full'
        style={{
            backgroundColor: 'transparent',
        }}>
            <Content className='p-5 w-full'>
                <div style={{height: 'calc(100vh - 336px)', position: 'relative'}}>
                    {current === 0 ? (
                        <AddSystem envId={envId} firstNext={firstNext}/>
                    ) : current === 1 ? (
                        <AddEmpower secondNext={secondNext}/>
                    ) : (
                        <SuccessAdd/>
                    )}
                </div>
                <Steps
                    options={stepsOption}
                    next={next}
                    prev={prev}
                    goBackFirstPage={goBackFirstPage}
                />
            </Content>
        </Layout>
    );
};

export default StepWrap;
