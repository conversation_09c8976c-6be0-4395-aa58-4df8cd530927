import React, { useEffect, useState } from 'react';
// import FormHeader, { optionType } from '../../../components/FormHeader';
import { Tag } from 'antd';
import ajax from '../../../api/Empower';
import store from '@/store';
import { SET_CHOOSE_SYSTEM_INFO } from '@/store/reducers/empowerSlice';

type propsType = {
  envId: number;
  firstNext: (value: any) => void;
};

const AddSystem: React.FC<propsType> = (props) => {
  // const [form] = Form.useForm();
  const { envId, firstNext } = props;
  const [thirdpartySystemList, setThirdpartySystemList] = useState<any[]>([]);
  // const inputOption: optionType[] = [
  //   {
  //     key: 'projectName',
  //     value: 'projectName',
  //     type: 'input',
  //     label: '应用名称',
  //   },
  // ];

  const getThirdpartySystemListByEnv = async (projectName?: string) => {
    const params = {
      envId: envId,
      systemTypeName: projectName || '',
    };
    const _thirdpartySystemList: any[] = [];
    const res = (await ajax.getThirdpartySystemListByEnv(params)) as any;
    if (res.code === 200) {
      res.data.list.map((item: any, index: number) => {
        if (item.auth) {
          _thirdpartySystemList.push({
            key: index,
            id: item.id,
            systemName: item.systemName,
            coId: item.coId,
            systemIp: item.systemIp,
            systemTypeName: item.systemTypeName,
            type: 'initialization',
            isChoose: false,
            auth: item.auth,
          });
        }
      });
      setThirdpartySystemList(_thirdpartySystemList);
    }
  };
  const chooseTag = (value: any) => {
    const newThirdpartySystemList: any[] = [];
    thirdpartySystemList.map((item: any) => {
      if (item.systemName == value.systemName) {
        newThirdpartySystemList.push({
          ...item,
          isChoose: true,
        });
      } else {
        newThirdpartySystemList.push({
          ...item,
          isChoose: false,
        });
      }
    });
    setThirdpartySystemList(newThirdpartySystemList);
    store.dispatch(SET_CHOOSE_SYSTEM_INFO(value));
    firstNext(newThirdpartySystemList);
  };

  // const search = (values: any) => {
  //   getThirdpartySystemListByEnv(values.projectName);
  // };

  useEffect(() => {
    getThirdpartySystemListByEnv();
  }, []);

  return (
    <div className='h-full'>
      {/* <FormHeader options={inputOption} onSearch={search} form={form} /> */}
      <div
        className='mt-5 overflow-hidden overflow-y-auto'
        style={{ height: 'calc(100vh - 404px)' }}>
        {thirdpartySystemList.map((item: any) => {
          let tagClassName: string = '';
          switch (item.type) {
            case 'success':
              if (item.isChoose === true) {
                tagClassName =
                  'text-white bg-green-500 border-green-500  m-0 text-center leading-20 text-base font-bold  rounded-large ';
              } else {
                tagClassName =
                  'text-green-500 bg-white hover:bg-green-400 hover:text-white border-green-500  m-0 text-center leading-20 text-base font-bold  rounded-large  ';
              }
              break;
            case 'error':
              if (item.isChoose === true) {
                tagClassName =
                  'text-white bg-red-400  border-red-400  m-0 text-center leading-20 text-base font-bold  rounded-large';
              } else {
                tagClassName =
                  'text-red-400 bg-white hover:bg-red-300 hover:text-white border-red-400  m-0 text-center leading-20 text-base font-bold  rounded-large ';
              }
              break;
            case 'initialization':
              if (item.isChoose === true) {
                tagClassName =
                  'text-white  bg-orange-400 border-orange-400  m-0 text-center leading-20 text-base font-bold  rounded-large ';
              } else {
                tagClassName =
                  'text-orange-400 bg-white hover:bg-orange-300 hover:text-white border-orange-400  m-0 text-center leading-20 text-base font-bold  rounded-large ';
              }
              break;
            default:
              break;
          }

          return item.systemName ? (
            <Tag
              id={item.systemName}
              key={item.key}
              onClick={() => {
                chooseTag(item);
              }}
              className={tagClassName}
              style={{ width: '19%', margin: '0.5%', cursor: 'pointer' }}>
              {item.systemTypeName}
            </Tag>
          ) : null;
        })}
      </div>
    </div>
  );
};

export default AddSystem;
