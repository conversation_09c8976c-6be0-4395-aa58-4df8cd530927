import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import ajax from '../../api';
import StepWrap from './components/StepWrap';
import { useSize } from 'ahooks';

type AllEnvListType = {
  id: number;
  envName: string;
  coId: number;
};

const Empower: React.FC = () => {
  const [allEnvList, setAllEnvList] = useState<any[]>([]);
  const [pageNum, setPageNum] = useState<number>(0);
  const getAllEnvList = async () => {
    const r = (await ajax.getCurCoId()) as any;
    if (r.code === 200) {
      const res = (await ajax.getAllEnvList()) as any;
      const _allEnvList: AllEnvListType[] = [];
      if (res.code === 200) {
        res.data.list.map((item: any) => {
          _allEnvList.push({
            id: item.id,
            envName: item.envName,
            coId: item.coId,
          });
        });
        setAllEnvList(_allEnvList);
      }
    }
  };

  const getPageNum = (value: number) => {
    setPageNum(value);
  };
  useEffect(() => {
    getAllEnvList();
  }, []);

  const contentHeight = useSize(
    document.querySelector('.ant-tabs-content-holder'),
  );

  return (
    <div
      className='bg-white h-full rounded-large'
      style={{
        borderRadius: '0.75rem',
      }}>
      <Tabs
        style={{
          height: '100%',
        }}
        tabBarStyle={{
          padding: '0 1.25rem',
        }}
        aria-label='Options'
        color='primary'
        items={allEnvList.map((item: any) => {
          return {
            key: item.id,
            label: item.envName,
            children: <StepWrap envId={item.id} getPageNum={getPageNum} />,
            disabled: pageNum !== 0,
            style: {
              height: contentHeight?.height || '100%',
            },
          };
        })}></Tabs>
    </div>
  );
};

export default Empower;
