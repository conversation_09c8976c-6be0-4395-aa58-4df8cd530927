import React, { useState, useEffect } from 'react';
// import timezone from 'dayjs/plugin/timezone';
import ajax from '@/api';
import type {
  DataMapType,
  ApiListType,
  PropsType,
  FormDataType,
} from './Model';
import {
  Select,
  // message,
  Form,
  Modal,
  Input,
  Button,
  Space,
  DatePicker,
  Radio,
  message,
} from 'antd';
// import ajax from '@/api';
// dayjs.extend(timezone);
//TODO:目前只能前端写死
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('int');

const DataMap: DataMapType[] = [
  {
    apiId: 1,
    category: 'PRICE',
    dataType: 'td',
  },
  {
    apiId: 2,
    category: 'SALOUTSTOCK',
    dataType: 'td',
  },
  {
    apiId: 3,
    category: 'PRDINSTOCK',
    dataType: 'td',
  },
  //获取工序业务数据
  {
    apiId: 4,
    category: 'SFC_OperationReport',
    dataType: 'td',
  },
  //获取工序主数据
  {
    apiId: 5,
    category: 'ENG_Route',
    dataType: 'md',
  },
];
const GetDataBtn: React.FC<PropsType> = (props) => {
  const [form] = Form.useForm();
  const { record, systemAuth } = props;
  const [systemAuthData, setsystemAuthData] = useState<ApiListType[]>([]);
  const [openModal, setOpenModal] = useState<boolean>(false);
  //false:使用当前系统时间，true:自定义时间
  const [timeType, setTimeType] = useState<boolean>(true);
  const openModalFn = () => {
    setOpenModal(true);
  };
  const closeModal = () => {
    setOpenModal(false);
    form.resetFields();
  };

  // 获取当前时间
  const now = dayjs();

  // 禁用当前日期之前的日期
  const disabledDate = (current: any) => {
    return current && current < now.startOf('minute');
  };

  const startCrone = async (values: any) => {
    const res = (await ajax.startCornJob(values)) as any;
    if (res.code === 0) {
      message.success(res.msg);
    } else {
      if (res.code === 7) {
        if (res.data.expire) {
          const getTokenParams = {
            scpToken: localStorage.getItem('scp-token'),
          };
          const tokenRes = (await ajax.getNewScpToken(getTokenParams)) as any;
          if (tokenRes.code === 200) {
            localStorage.setItem('isNeedSCPToken', 'true');
            localStorage.setItem('scp-token', tokenRes.data.scpToken);
            const newOnloadFormRes = (await ajax.startCornJob(values)) as any;
            if (newOnloadFormRes.code === 0) {
              message.success(res.msg);
              return;
            }
          }
        }
      }
      message.error(res.msg);
      return;
    }
    closeModal();
  };

  const onFinish = (values: FormDataType) => {
    const { jobType, jobName } = values;
    let params: any = {
      // dataOrigin: 'KingDee',
      paId: localStorage.getItem('pareaId'),
      category: record.apiTag,
      dataType: 'td',
      authInfo: JSON.stringify(systemAuthData),
    };
    for (const item of DataMap) {
      if (item.apiId === record.apiId) {
        params = {
          ...params,
          dataType: item.dataType,
        };
      }
    }
    const taskParameters = JSON.stringify(params);

    if (jobType === 0) {
      const props = {
        jobName,
        jobType,
        taskParameters,
        taskTag: params.category,
        taskType: record.apiType,
        pversion: 'Base',
      };
      console.log(props);
      startCrone(props);
    } else {
      const props = {
        jobName,
        jobType,
        taskParameters,
        jobMode: values.frequency.jobMode === 'day' ? 'day' : 'time',
        jobStartTime: values.jobStartTime?.format() || '',
        jobLimitTimes: Number(values.jobEndTime.times) || 0,
        isCurTime: timeType,
        jobEndTime: values.jobEndTime.date?.format() || '',
        jobParameters: '',
        taskTag: params.category,
        taskType: record.apiType,
        pversion: 'Base',
      };
      let jobParameters = {};
      if (props.jobMode === 'time') {
        jobParameters = {
          timeNum: Number(values.frequency.jobLimitTimes),
          timeLevel: values.frequency.jobMode,
        };
      } else {
        jobParameters = {
          interval: Number(values.frequency.jobLimitTimes),
          dateTimes: [dayjs().add(-1, 'day').format()],
        };
      }
      props.jobParameters = JSON.stringify(jobParameters);
      startCrone(props);
    }
  };

  useEffect(() => {
    setsystemAuthData(systemAuth);
  }, [systemAuth]);

  return (
    <>
      <Button className='h-12 w-36 overflow-hidden' onClick={openModalFn}>
        获取数据
      </Button>
      <Modal
        title='执行任务'
        width={1000}
        open={openModal}
        onCancel={closeModal}
        footer={false}>
        <div>
          <Form labelCol={{ span: 4 }} form={form} onFinish={onFinish}>
            <Form.Item
              label={<span className='text-lg'>{'任务名称'}</span>}
              name='jobName'
              rules={[{ required: true, message: '请输入任务名称' }]}>
              <Input placeholder='请输入' style={{ width: 200 }} />
            </Form.Item>
            <Form.Item
              label={<span className='text-lg'>{'执行方式'}</span>}
              name='jobType'
              initialValue={0}>
              <Select
                style={{ width: 200 }}
                options={[
                  { label: '立即执行', value: 0 },
                  { label: '重复执行', value: 1 },
                ]}
              />
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const operationType = getFieldValue('jobType');
                if (operationType === 1) {
                  return (
                    <>
                      <Form.Item
                        label={<span className='text-lg'>{'重复频率'}</span>}
                        rules={[{ required: true }]}>
                        <Space.Compact>
                          <Form.Item
                            name={['frequency', 'jobLimitTimes']}
                            rules={[{ required: true, message: '请输入次数' }]}>
                            <Input style={{ width: 100 }} addonBefore={'每'} />
                          </Form.Item>
                          <Form.Item
                            name={['frequency', 'jobMode']}
                            initialValue={'day'}
                            rules={[{ required: true }]}>
                            <Select
                              style={{ width: 100 }}
                              options={[
                                { label: '天', value: 'day' },
                                { label: '小时', value: 'hour' },
                                { label: '分钟', value: 'minute' },
                              ]}
                            />
                          </Form.Item>
                        </Space.Compact>
                      </Form.Item>
                      <Form.Item
                        label={<span className='text-lg'>{'开始时间'}</span>}>
                        {!timeType ? (
                          <Form.Item
                            name='jobStartTime'
                            initialValue={dayjs()}
                            noStyle>
                            <DatePicker
                              style={{ width: 200 }}
                              format='YYYY-MM-DD HH:mm'
                              showTime={{ format: 'HH:mm' }}
                              disabledDate={disabledDate}
                            />
                          </Form.Item>
                        ) : (
                          <span style={{ width: 200, display: 'inline-block' }}>
                            {'使用当前系统时间'}
                          </span>
                        )}
                        <Button
                          onClick={() => setTimeType(!timeType)}
                          className='ml-4'>
                          {timeType ? '使用自定义时间' : '当前系统时间'}
                        </Button>
                      </Form.Item>
                      <Form.Item
                        label={<span className='text-lg'>{'结束时间'}</span>}>
                        <Form.Item
                          name={['jobEndTime', 'type']}
                          noStyle
                          rules={[
                            { required: true, message: '请选择结束时间' },
                          ]}
                          initialValue={1}>
                          <Radio.Group className='mb-4'>
                            <Radio value={1}>永不</Radio>
                            <Radio value={2}>按日期</Radio>
                            <Radio value={3}>按重复次数</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item noStyle shouldUpdate>
                          {({ getFieldValue }) => {
                            const endTimeType = getFieldValue([
                              'jobEndTime',
                              'type',
                            ]);
                            if (endTimeType === 2) {
                              return (
                                <Form.Item
                                  name={['jobEndTime', 'date']}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请选择结束时间',
                                    },
                                  ]}>
                                  <DatePicker
                                    style={{ width: 200 }}
                                    format='YYYY-MM-DD HH:mm'
                                    showTime={{
                                      format: 'HH:mm',
                                    }}
                                    disabledDate={disabledDate}
                                  />
                                </Form.Item>
                              );
                            } else if (endTimeType === 3) {
                              return (
                                <Form.Item
                                  name={['jobEndTime', 'times']}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请选择重复次数',
                                    },
                                  ]}>
                                  <Input
                                    placeholder='请输入'
                                    suffix={'次'}
                                    style={{ width: 200 }}
                                  />
                                </Form.Item>
                              );
                            }
                          }}
                        </Form.Item>
                      </Form.Item>
                    </>
                  );
                }
              }}
            </Form.Item>
            <Button htmlType='submit'>提交</Button>
          </Form>
        </div>
      </Modal>
    </>
  );
};
export default GetDataBtn;
