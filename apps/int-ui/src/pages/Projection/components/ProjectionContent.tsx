import { Layout, Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import ajax from '../../../api/Projection';
import ConfigurationTable, { tableOptionType } from './ConfigurationTable';
import FormHeader, { optionType } from '../../../components/FormHeader';
import { t } from '@/languages';
import { useSelector } from 'react-redux';

const { Header, Content } = Layout;

type IntegratorListType = {
  key: React.Key;
  intId: number;
  intName: string;
  description: string;
  definitionTime: string;
  intState: string;
  operate: string;
  relType?: number | undefined;
  iecId: string;
};

type PropsType = {
  envId: any;
};

const ProjectionContent: React.FC<PropsType> = (props) => {
  useEffect(() => {
    setColumns(originCulumns);
    setInputOption(originInputOption);
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  const [form] = Form.useForm();
  const { envId } = props;
  const [integratorList, setIntegratorList] = useState<IntegratorListType[]>(
    [],
  );
  const [integratorInfo, setIntegratorInfo] = useState<any>({});
  const originCulumns: ColumnsType<IntegratorListType> = [
    {
      title: t('集成器名称'),
      dataIndex: 'intName',
      key: 'intName',
      align: 'center',
    },
    {
      title: t('集成器描述'),
      dataIndex: 'description',
      key: 'description',
      align: 'center',
    },
    {
      title: t('定义时间'),
      dataIndex: 'definitionTime',
      key: 'definitionTime',
      align: 'center',
    },
    {
      title: t('集成器状态'),
      dataIndex: 'intState',
      key: 'intState',
      align: 'center',
    },
  ];
  const [columns, setColumns] = useState(originCulumns);
  const otherpropsMap = new Map();
  otherpropsMap.set('options', [
    { value: 1, label: t('开启') },
    { value: 2, label: t('关闭') },
  ]);
  const originInputOption: optionType[] = [
    {
      key: 'integratorName',
      value: 'integratorName',
      type: 'input',
      label: t('集成器名称'),
    },
    {
      key: 'integratorStatus',
      value: 'integratorStatus',
      type: 'select',
      label: t('集成器状态'),
      otherprops: otherpropsMap,
    },
  ];
  const [inputOption, setInputOption] = useState(originInputOption);
  const tableOption: tableOptionType = {
    columns: columns,
    dataSource: integratorList,
    scroll: { y: 'calc(100vh - 427px' },
    isExpand: true,
    childrenInfo: integratorInfo,
  };

  const onExpand = async (value: any) => {
    setIntegratorInfo(value);
  };

  const search = (values: any) => {
    getIntegratorList(values.integratorName, values.integratorStatus);
  };
  const getIntegratorList = async (
    intName?: string | undefined,
    intState?: boolean | undefined,
  ) => {
    const params = {
      intName: intName,
      intState: intState,
      envId: envId,
      pageNum: '',
      pageSize: '',
    };
    const res = (await ajax.getIntEnvSchemeList(params)) as any;
    let _integratorList: IntegratorListType[] = [];
    if (res.code === 200) {
      const list = res.data.list || [];
      _integratorList = list.map((item: any, index: number) => {
        return {
          key: index,
          intId: item.intId,
          intName: item.intName,
          description: t('集成器描述'),
          definitionTime: t('定义时间'),
          intState: item.intState === 1 ? t('开启') : t('关闭'),
          operate: t('配置'),
          relType: item.relType,
          iecId: item.iecId,
        };
      });
      setIntegratorList(_integratorList);
    }
  };
  useEffect(() => {
    getIntegratorList();
  }, []);

  return (
    <Layout className='h-full'>
      <Header
        className='flex items-center justify-between'
        style={{
          height: 'auto',
          lineHeight: 'normal',
        }}>
        <FormHeader options={inputOption} onSearch={search} form={form} />
      </Header>
      <Content className='bg-white p-5'>
        <ConfigurationTable options={tableOption} onExpand={onExpand} />
      </Content>
    </Layout>
  );
};
export default ProjectionContent;
