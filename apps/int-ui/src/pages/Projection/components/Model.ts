import dayjs from 'dayjs';

export type PropsType = {
  record: ApiListType;
  systemAuth: any;
};
export type ApiListType = {
  id: number;
  key: React.Key;
  apiId: number;
  apiName: string;
  apiTag: string;
  apiType: number;
  apiVersion: string;
  apiRoute: string;
  enabled: boolean;
  isGreenChannel: boolean;
};

export type DataMapType = {
  apiId: number;
  category: string;
  dataType: string;
};

export type FormDataType = {
  jobName: string;
  jobType: number;
  operationType: number;
  frequency: { jobMode: string; jobLimitTimes: number };
  jobStartTime: dayjs.Dayjs;
  jobEndTime: { type: number; date: dayjs.Dayjs; times: number };
};
