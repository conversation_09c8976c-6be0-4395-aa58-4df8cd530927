import { Table } from 'antd';
import ExpandedRow from './ExpandTable';
import ajax from '@/api/Projection';
import { useState } from 'react';

export type tableOptionType = {
  columns: any; // title
  dataSource: any; // data
  scroll: any; // height
  isExpand?: boolean; // 是否有二级表
  onExpand?: any; //展开二级表触发的函数
  childrenInfo?: any; // 二级表所需data
};
export type tableOption = {
  options: tableOptionType;
  onExpand?: (value: any) => void;
};

const defaultPagination = {
  position: ['bottomCenter'],
  defaultPageSize: 20,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20', '50'],
  showQuickJumper: true,
} as any;

const ConfigurationTable: React.FC<tableOption> = (props) => {
  const { options, onExpand } = props as any;
  const [systemAuth, setSystemAuth] = useState();
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([]);
  return (
    <>
      <Table
        columns={options.columns}
        dataSource={options.dataSource}
        scroll={options.scroll}
        expandable={
          options.isExpand
            ? {
                expandedRowRender: () => (
                  <ExpandedRow
                    integratorInfo={options}
                    systemAuth={systemAuth}
                  />
                ),
                rowExpandable: (record) => record.name !== 'Not Expandable',
                onExpand: async (expand, record) => {
                  onExpand(record);
                  if (expand) {
                    setExpandedRowKeys([record.key]);
                  } else {
                    setExpandedRowKeys(
                      expandedRowKeys.filter((key: any) => key !== record.key),
                    );
                  }
                  const res = (await ajax.getThirdpartySystemAuth(
                    record.iecId,
                  )) as any;
                  if (res.code === 200) {
                    setSystemAuth(res.data.systemAuth);
                  }
                },
                expandRowByClick: true,
                expandedRowKeys: expandedRowKeys,
              }
            : {}
        }
        pagination={{
          ...defaultPagination,
          showTotal: (total) => `共 ${total} 条`,
        }}
      />
    </>
  );
};

export default ConfigurationTable;
