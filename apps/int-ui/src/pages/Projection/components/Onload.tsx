import { Upload, message, Modal, Form, Button, Select, Flex } from 'antd';
import { CloudUploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { useState, useEffect } from 'react';
import { FileTwoTone, DeleteTwoTone } from '@ant-design/icons';
import ajax from '@/api';
import { PropsType } from './Model';
import { useSelector } from 'react-redux';

const Onload: React.FC<PropsType> = (props) => {
  const { record, systemAuth } = props;
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState<string>('');
  const [fileList, setFileList] = useState<any[]>([]);
  const [isHasFile, setIsHasFile] = useState(false);
  const [pversionList, setPversionList] = useState(null);
  const pareaId = useSelector((state: any) => state.layout.pareaId);
  const intToken = localStorage.getItem('int-token') as any;

  const getPversionList = async () => {
    const res: any = await ajax.getPversionList({
      pareaId: pareaId,
    });
    if (res.code !== 0 && res.code !== 200) return;
    const list = res.data.pversionList;
    setPversionList(list);
  };

  useEffect(() => {
    getPversionList();
  }, []);

  const config: UploadProps = {
    name: 'file',
    action: '/file/uploadFile',
    headers: {
      Authorization: intToken,
    },
    showUploadList: false,
    beforeUpload: (file) => {
      const isYML =
        file.type === 'application/x-yaml' || file.type === 'application/x-yml';
      if (!isYML) {
        message.error(`${file.name} 不是yml/yaml文件`);
        return;
      }
    },
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        setFileList([...fileList, { fileName: info.file.name }]);
        setIsHasFile(true);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  const showModal = () => {
    setOpen(true);
    if (record.apiName === '文件导入主数据') {
      setTitle('上传文件生成主数据');
    } else if (record.apiName === '文件导入业务数据') {
      setTitle('上传文件生成业务数据');
    } else {
      setTitle('上传文件');
    }
  };
  const handleOk = async () => {
    localStorage.setItem('isNeedSCPToken', 'true');
    let params: any = {
      dataOrigin: 'YmlFile',
      fileName: fileList[0].fileName,
      filePath: fileList[0].fileName,
      typeId: form.getFieldsValue().id,
      paId: localStorage.getItem('pareaId'),
      category: record.apiTag,
      authInfo: JSON.stringify(systemAuth),
      paPvId: form.getFieldValue('paPvId'),
    };
    if (record.apiName === '文件导入主数据') {
      params = {
        ...params,
        dataType: 'md',
      };
    } else if (record.apiName === '文件导入业务数据') {
      params = {
        ...params,
        dataType: 'td',
      };
    }
    const taskParameters = JSON.stringify(params);
    const props = {
      taskParameters,
      jobType: 0,
      jobName: params.fileName,
      taskTag: params.category,
      pversion: 'Base',
      taskType: record.apiType,
    };
    const res = (await ajax.startCornJob(props)) as any;
    if (res.code === 0) {
      message.success(res.msg);
    } else if (res.code === 7) {
      if (res.data.expire) {
        const getTokenParams = {
          scpToken: localStorage.getItem('scp-token'),
        };
        const tokenRes = (await ajax.getNewScpToken(getTokenParams)) as any;
        if (tokenRes.code === 200) {
          localStorage.setItem('isNeedSCPToken', 'true');
          localStorage.setItem('scp-token', tokenRes.data.scpToken);
          const newOnloadFormRes = (await ajax.onloadForm(params)) as any;
          if (newOnloadFormRes.code === 0) {
            message.success(res.msg);
          }
        } else {
          message.error(tokenRes.message);
        }
      } else {
        message.error(res.msg);
      }
    } else {
      message.error(res.msg);
    }
    localStorage.removeItem('isNeedSCPToken');
    setFileList([]);
    setIsHasFile(false);
    setOpen(false);
  };
  const handleCancel = () => {
    setFileList([]);
    setIsHasFile(false);
    setOpen(false);
  };
  const deleteFile = (index: number) => {
    console.log(index, 'index');
    console.log(fileList.splice(index, 1));
    const newFileList = [...fileList.splice(index, 1)];
    console.log(newFileList, 'newFileList');
    if (newFileList.length === 0) {
      setIsHasFile(false);
    }
    setFileList([...newFileList]);
  };

  return (
    <div>
      <Button className='h-12 w-36 overflow-hidden' onClick={showModal}>
        <CloudUploadOutlined />
        上传
      </Button>
      <Modal
        title={title}
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
        footer={[
          <Button onClick={handleCancel} className='mr-2'>
            取消
          </Button>,
          <Button onClick={handleOk} color='primary' disabled={!isHasFile}>
            提交
          </Button>,
        ]}>
        <Form form={form}>
          <Form.Item noStyle>
            <Flex gap={20} justify='center' align='center' vertical={false}>
              <Form.Item noStyle name='file'>
                <Button
                  className='h-12 w-56 overflow-hidden'
                  disabled={isHasFile}>
                  <Upload {...config}>
                    <div className='h-12 w-48 leading-12'>
                      <CloudUploadOutlined className='mr-2' />
                      上传文件
                    </div>
                  </Upload>
                </Button>
              </Form.Item>
              {record.apiType === 3 ? (
                <Form.Item noStyle name={'paPvId'}>
                  <Flex gap={5} align='center'>
                    <span>{'paPvId:'}</span>
                    <Select
                      options={pversionList || []}
                      style={{
                        width: '120px',
                      }}></Select>
                  </Flex>
                </Form.Item>
              ) : null}
            </Flex>
          </Form.Item>
        </Form>
        {fileList.map((item: any, index: number) => {
          return (
            <div className='flex justify-between bg-slate-200 py-0.5 pl-4 rounded-large mb-0.5 overflow-hidden'>
              <div key={index}>
                文件{index + 1}: <FileTwoTone className='mx-2' />
                {item.fileName}
              </div>
              <div
                className='text-center w-12 hover:bg-slate-300'
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  deleteFile(index);
                }}>
                <DeleteTwoTone />
              </div>
            </div>
          );
        })}
      </Modal>
    </div>
  );
};
export default Onload;
