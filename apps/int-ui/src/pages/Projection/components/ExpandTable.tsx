import { useEffect, useState } from 'react';
import { Switch, Table } from 'antd';
import type { TableColumnsType } from 'antd';
import ajax from '@/api';
import Onload from './Onload';
import GetDataBtn from './GetDataBtn';
import _ from 'lodash';
import type { ApiListType } from './Model';

type PropsType = {
  integratorInfo: any;
  systemAuth: any;
};

const ExpandedRow: React.FC<PropsType> = (props) => {
  const { integratorInfo, systemAuth } = props;
  const [intefacesList, setIntefacesList] = useState<ApiListType[]>([]);
  const columns: TableColumnsType<ApiListType> = [
    {
      title: '接口名称',
      dataIndex: 'apiName',
      key: 'apiName',
      align: 'center',
    },
    {
      title: '接口版本',
      dataIndex: 'apiVersion',
      key: 'apiVersion',
      align: 'center',
    },
    {
      title: '接口路由地址',
      dataIndex: 'apiRoute',
      key: 'apiRoute',
      align: 'center',
    },
    {
      title: '接口状态',
      dataIndex: 'enabled',
      key: 'enabled',
      align: 'center',
      render: (enabled, data) => {
        return (
          <Switch
            key={data.apiId}
            checked={enabled}
            onChange={(event) => updateIntApiState(event, data.id)}
          />
        );
      },
    },
    {
      title: '绿色通道',
      dataIndex: 'isGreenChannel',
      key: 'isGreenChannel',
      align: 'center',
      render: (isGreenChannel, data) => {
        return (
          <Switch
            key={data.apiId}
            checked={isGreenChannel}
            onChange={(event) => UpdateIntApiGreenChannel(event, data.id)}
          />
        );
      },
    },
    {
      title:
        integratorInfo.childrenInfo.relType === 1 ? '上传数据' : '获取数据',
      dataIndex: 'onload',
      key: 'onload',
      align: 'center',
      render: (_, record: any) => {
        return integratorInfo.childrenInfo.relType === 1 ? (
          <Onload record={record} systemAuth={systemAuth} />
        ) : (
          <GetDataBtn record={record} systemAuth={systemAuth} />
        );
      },
    },
  ];
  if (
    integratorInfo.childrenInfo.relType === undefined ||
    integratorInfo.childrenInfo.relType === null
  ) {
    columns.splice(5, 1);
  }
  //获取接口
  let _intefacesList: ApiListType[] = [];
  const getIntApiListByIecId = async (iecId: number) => {
    const params = {
      iecId: iecId,
    };
    const res = await ajax.getIntApiListByIecId(params);
    if (res.code === 200) {
      _intefacesList = [];
      const list = _.get(res, 'data.list', []);
      list.map((item: any, index: number) => {
        _intefacesList.push({
          id: item.id,
          key: index,
          apiId: item.apiId,
          apiName: item.apiName,
          apiTag: item.apiTag,
          apiVersion: 'v1',
          apiType: item.apiType,
          apiRoute: '192.168.0.0',
          enabled: item.apiState === 1 ? true : false,
          isGreenChannel: item.isGreenChannel ? true : false,
        });
      });
      setIntefacesList(_intefacesList);
    }
  };
  // 更新接口状态
  const updateIntApiState = async (enabled: any, key: any) => {
    const params = {
      id: key,
      apiState: enabled ? 1 : 2,
    };
    const res = (await ajax.updateIntApiState(params)) as any;
    if (res.code === 200) {
      getIntApiListByIecId(integratorInfo.childrenInfo.iecId);
    } else {
      console.log('更新失败');
    }
  };
  // 更新绿色通道
  const UpdateIntApiGreenChannel = async (
    isGreenChannel: boolean,
    key: any,
  ) => {
    const params = {
      id: key,
      isGreenChannel: isGreenChannel,
    };
    const res = (await ajax.UpdateIntApiGreenChannel(params)) as any;
    if (res.code === 200) {
      getIntApiListByIecId(integratorInfo.childrenInfo.iecId);
    } else {
      console.log('更新失败');
    }
  };

  useEffect(() => {
    getIntApiListByIecId(integratorInfo.childrenInfo.iecId).then();
  }, []);

  return (
    <Table columns={columns} dataSource={intefacesList} pagination={false} />
  );
};
export default ExpandedRow;
