import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import ProjectionContent from './components/ProjectionContent';
import ajax from '../../api';

type AllEnvListType = {
  id: number;
  envName: string;
  coId: number;
};

const Projection: React.FC = () => {
  const [allEnvList, setAllEnvList] = useState<any[]>([]);
  const getAllEnvList = async () => {
    const r = (await ajax.getCurCoId()) as any;
    if (r.code === 200) {
      const res = (await ajax.getAllEnvList()) as any;
      const _allEnvList: AllEnvListType[] = [];
      if (res.code === 200) {
        res.data.list.map((item: any) => {
          _allEnvList.push({
            id: item.id,
            envName: item.envName,
            coId: item.coId,
          });
        });
        setAllEnvList(_allEnvList);
      }
    }
  };
  useEffect(() => {
    getAllEnvList();
  }, []);

  return (
    <div
      className='bg-white h-full rounded-large'
      style={{
        borderRadius: '0.75rem',
      }}>
      <Tabs
        style={{
          height: '100%',
        }}
        tabBarStyle={{
          padding: '0 1.25rem',
        }}
        aria-label='Options'
        color='primary'
        items={allEnvList.map((item: any) => {
          return {
            key: item.id,
            label: item.envName,
            children: <ProjectionContent envId={item.id} />,
          };
        })}></Tabs>
    </div>
  );
};
export default Projection;
