import { Card } from 'antd';
import { Line } from '@ant-design/charts';

type PropsType = {
  cardData: any;
  index: number;
  config: any;
};
const HomeCard: React.FC<PropsType> = (props) => {
  const { cardData, index, config } = props;
  return (
    <Card
      key={index}
      title={cardData.title}
      style={{
        marginRight: `${index === cardData.length - 1 ? '' : '0.75rem'}`,
      }}
      className='w-72  overflow-hidden'>
      <p>{cardData.number}</p>
      <div className='h-20'>
        <Line {...config} />
      </div>
      <div>
        <span>{cardData.subtitle}</span>
        <span>{cardData.value}</span>
      </div>
    </Card>
  );
};

export default HomeCard;
