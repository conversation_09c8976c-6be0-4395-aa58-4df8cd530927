import { useState, useEffect } from 'react';
import { Card } from 'antd';
import { Pie } from '@ant-design/plots';
import { t } from '@/languages';
import { useSelector } from 'react-redux';

const InterfacesCard: React.FC = () => {
  const currentLanguage = useSelector(
    (state: any) => state.global.currentLanguage,
  );
  useEffect(() => {
    setData1([
      {
        type: t('分类一'),
        value: 25,
      },
      {
        type: t('分类二'),
        value: 75,
      },
    ]);
    setData2([
      {
        type: t('分类一'),
        value: 75,
      },
      {
        type: t('分类二'),
        value: 25,
      },
    ]);
    setData3([
      {
        type: t('分类一'),
        value: 50,
      },
      {
        type: t('分类二'),
        value: 50,
      },
    ]);
  }, [currentLanguage]);

  const [data1, setData1] = useState([
    {
      type: t('分类一'),
      value: 25,
    },
    {
      type: t('分类二'),
      value: 75,
    },
  ]);
  const [data2, setData2] = useState([
    {
      type: t('分类一'),
      value: 75,
    },
    {
      type: t('分类二'),
      value: 25,
    },
  ]);
  const [data3, setData3] = useState([
    {
      type: t('分类一'),
      value: 50,
    },
    {
      type: t('分类二'),
      value: 50,
    },
  ]);

  const config1 = {
    data: data1,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.6,
    legend: false,
    fitView: true,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '{value}',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: 'AntV\nG2Plot',
      },
    },
  };
  const config2 = {
    data: data2,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.6,
    legend: false,
    fitView: true,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '{value}',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: 'AntV\nG2Plot',
      },
    },
  };
  const config3 = {
    data: data3,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.6,
    legend: false,
    fitView: true,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '{value}',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: 'AntV\nG2Plot',
      },
    },
  };

  return (
    <Card title={t('系统接口健康占比')} style={{ height: '32%' }}>
      <div className='flex h-28 justify-between'>
        <div className='flex'>
          <div className='h-28 w-28'>
            <Pie {...config1} />
          </div>
          <div>{t('系统1')}</div>
        </div>
        <div className='flex'>
          <div className='h-28 w-28'>
            <Pie {...config2} />
          </div>
          <div>{t('系统2')}</div>
        </div>{' '}
        <div className='flex'>
          <div className='h-28 w-28'>
            <Pie {...config3} />
          </div>
          <div>{t('系统3')}</div>
        </div>
      </div>
    </Card>
  );
};

export default InterfacesCard;
