import { Card, Avatar, List } from 'antd';
import { t } from '@/languages';

type PropsType = {
  trendsData: any;
};
const TrendsCard: React.FC<PropsType> = (props) => {
  const { trendsData } = props;
  return (
    <div className=' overflow-hidden h-full ' style={{ width: '34%' }}>
      <Card title={t('发布日志')} className='w-full overflow-hidden'>
        <div
          className='w-full overflow-hidden  overflow-y-auto'
          style={{ height: 'calc(100vh - 210px)' }}>
          <List
            itemLayout='horizontal'
            dataSource={trendsData}
            renderItem={(item: any, index: number) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar src={item.avatarAddress + `&key=${index}`} />}
                  title={<a href='#'>{item.title}</a>}
                  description={item.desc}
                />
              </List.Item>
            )}
          />
        </div>
      </Card>
    </div>
  );
};

export default TrendsCard;
