import { Card } from 'antd';
import React, { useEffect, useState } from 'react';
import { Layout } from 'antd';
import { Line } from '@ant-design/charts';
// import ajax from '../../api/Home';
import HomeCard from './components/HomeCard';
import InterfacesCard from './components/InterfacesCard';
import TrendsCard from './components/TrendsCard';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSelector } from 'react-redux/es/hooks/useSelector';
// import { cardResType, trendsResType } from './home';
const { Content } = Layout;

const Home: React.FC = () => {
  const { t } = useVoerkaI18n();
  const data = [
    { year: '23年1月', value: 3 },
    { year: '23年2月', value: 4 },
    { year: '23年3月', value: 3.5 },
    { year: '23年4月', value: 5 },
    { year: '23年5月', value: 4.9 },
    { year: '23年6月', value: 6 },
    { year: '23年7月', value: 7 },
    { year: '23年8月', value: 9 },
    { year: '23年9月', value: 13 },
  ];
  const config = {
    data,
    xField: 'year',
    yField: 'value',
    point: {
      size: 5,
      shape: 'diamond',
    },
  };

  const [cardData, setCardData] = useState<any[]>([]);
  const [trendsData, setCrendsData] = useState<any[]>([]);
  const getData: any = async () => {
    // const cardParams = {
    //   cardNumber: 4,
    //   cardTitle: ['接口访问次数', '访问量', '接口成功率', '采购金额'],
    // };
    // const cardRes: cardResType = (await ajax.getCardList(cardParams)) as any;
    const cardRes: any = {
      code: 200,
      cardNumber: 4,
      data: [
        {
          title: t('接口访问次数'),
          number: 100,
          subtitle: 'CPU占用率',
          value: '60%',
          chartsData: {
            type: 'year',
            value: '1999',
          },
        },
        {
          title: t('访问量'),
          number: 120000,
          subtitle: '日访问量',
          value: '1500',
          chartsData: {
            type: 'year',
            value: '1999',
          },
        },
        {
          title: t('接口成功率'),
          number: 90,
          subtitle: '周同比',
          value: '60%',
          chartsData: {
            type: 'year',
            value: '1999',
          },
        },
        {
          title: t('采购金额'),
          number: 10000,
          subtitle: '日均采购金额',
          value: '3000',
          chartsData: {
            type: 'year',
            value: '1999',
          },
        },
      ],
    };
    // const trendsRes: trendsResType = (await ajax.getTrendsList()) as any;
    const trendsRes: any = {
      code: 200,
      data: [
        {
          title: t('v0.0.1发布'),
          desc: '2023/9/16',
          avatarAddress: 'https://xsgames.co/randomusers/avatar.php?g=pixel',
        },
      ],
    };
    if (cardRes.code === 200) {
      if (trendsRes.code === 200) {
        const _cardData: any[] = [];
        const _trendsData: any[] = [];
        cardRes.data.map((item: any) => {
          _cardData.push({ ...item });
        });
        setCardData(_cardData);
        trendsRes.data.map((item: any) => {
          _trendsData.push({ ...item });
        });
        setCrendsData(_trendsData);
      }
    }
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    getData();
  }, [useSelector((state: any) => state.global.currentLanguage)]);

  return (
    <Layout
      className='rounded-large '
      style={{ backgroundColor: 'rgb(228 228 231 / var(--tw-bg-opacity))' }}>
      <Content className='flex justify-between'>
        <div
          className=' flex flex-col justify-between h-full'
          style={{ height: 'calc(100vh - 104px)', width: '65%' }}>
          <div
            className='flex  overflow-hidden overflow-x-auto rounded-large'
            style={{ height: '31%' }}>
            {cardData.map((item: any, index: number) => {
              return (
                <HomeCard
                  cardData={item}
                  index={index}
                  config={config}
                  key={index}
                />
              );
            })}
          </div>
          <InterfacesCard />
          <Card title={t('集成平台接口访问频次')} style={{ height: '32%' }}>
            <div className='h-28'>
              <Line {...config} />
            </div>
          </Card>
        </div>
        <TrendsCard trendsData={trendsData} />
      </Content>
    </Layout>
  );
};
export default Home;
