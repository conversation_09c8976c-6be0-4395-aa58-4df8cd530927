type cardResType = {
  cardNumber: number;
  code: number;
  data: cardDatumType[];
};
type cardDatumType = {
  chartsData: cardChartsDataType;
  number: number;
  subtitle: string;
  title: string;
  value: string;
};
type cardChartsDataType = {
  type: string;
  value: string;
};
type trendsResType = {
  code: number;
  data: trendsChartsDataType[];
};
type trendsChartsDataType = {
  avatarAddress: string;
  desc: string;
  title: string;
};

export { cardResType, trendsResType };
