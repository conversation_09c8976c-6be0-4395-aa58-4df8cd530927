import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';
type Props = {
  title?: string;
  subTitle?: string;
};

const Error: React.FC<Props> = ({ title, subTitle }) => {
  return (
    <div style={{ height: '100%', padding: '20px' }}>
      <Result
        status='error'
        title={title || '403'}
        subTitle={
          subTitle || 'Sorry, you are not authorized to access this page.'
        }
        style={{ backgroundColor: '#fff', height: '100%' }}
        extra={
          <Link to='/home'>
            <Button>回主页</Button>
          </Link>
        }
      />
      <Link to='/'>
        <Button type='primary'>返回主页</Button>
      </Link>
    </div>
  );
};

export default Error;
