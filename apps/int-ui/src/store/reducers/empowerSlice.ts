import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  SYSTEM_TAG_LIST: [],
  FORM_DATA: {},
  CHOOSE_SYSTEM_INFO: [],
};

const empowerSlice = createSlice({
  name: 'empower',
  initialState,
  reducers: {
    setTagListDataAction(state: any, action: any) {
      state.SYSTEM_TAG_LIST = action.payload;
    },
    setFormDataAction(state: any, action: any) {
      state.FORM_DATA = action.payload as any;
    },
    SET_CHOOSE_SYSTEM_INFO(state: any, action: any) {
      state.CHOOSE_SYSTEM_INFO = action.payload;
    },
  },
});

export const {
  setTagListDataAction,
  setFormDataAction,
  SET_CHOOSE_SYSTEM_INFO,
} = empowerSlice.actions;

export default empowerSlice.reducer;
