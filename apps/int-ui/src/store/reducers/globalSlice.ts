import { createSlice } from '@reduxjs/toolkit';
import { i18nScope } from '@/languages';

const initialState = {
  currentLanguage: i18nScope.activeLanguage,
};

const globalSlice = createSlice({
  name: 'global',
  initialState,
  reducers: {
    setCurrentLanguage: (state, action) => {
      state.currentLanguage = action.payload;
    },
  },
});

export const { setCurrentLanguage } = globalSlice.actions;

export default globalSlice.reducer;
