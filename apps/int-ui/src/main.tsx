// import React from 'react';
import ReactDOM from 'react-dom/client';
import { i18nScope } from './languages';
import { VoerkaI18nProvider } from '@voerkai18n/react';
import App from './App.tsx';
import './index.css';
import { Provider } from 'react-redux';
// const { Provider } = require('react-redux')
import store from './store';
import './preflight.css';
import { App as AntdApp } from 'antd';

ReactDOM.createRoot(document.getElementById('root')!).render(
  // <React.StrictMode>
  <VoerkaI18nProvider scope={i18nScope} fallback={<div>正在加载语言包...</div>}>
    <Provider store={store}>
      <AntdApp>
        <App />
      </AntdApp>
    </Provider>
  </VoerkaI18nProvider>,
  // </React.StrictMode>,
);
