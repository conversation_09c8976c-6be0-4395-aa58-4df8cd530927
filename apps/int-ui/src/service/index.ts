import axios, {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosResponse,
} from 'axios';
/** 封装axios的实例，方便多个url时的封装 */
export interface returnData<T> extends AxiosResponse {
  code: number;
  reason: string;
  data: {
    list: T;
    page?: number;
    pageSize?: number;
    total?: number;
    [key: string]: any;
  };
  message: string;
}
export const createAxiosIntance = (): AxiosInstance => {
  const request = axios.create({ timeout: 1000 * 60 * 5 });
  // 请求拦截器器
  request.interceptors.request.use((config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('int-token');
    config.headers['Accept-Language'] = 'zh';
    if (token) {
      config.headers.Authorization = token;
    }
    if (config.url?.includes('/scp')) {
      const scp_token = localStorage.getItem('scp-token');
      console.log('scp_token', scp_token);
      config.headers['X-Token'] = scp_token;
    }
    // if (localStorage.getItem('isNeedSCPToken') === 'true') {
    // 判断是否为scp请求 是则传scp的token
    const scp_token = localStorage.getItem('scp-token');
    config.headers['X-Token'] = scp_token;
    // }/
    return config;
  });
  // 响应拦截器
  request.interceptors.response.use(
    (response) => {
      const code = response.data.code;
      switch (code) {
        case 200:
          return response.data;
        default:
          return response.data || {};
      }
    },
    (error) => {
      // 全局接口报错处理
      console.error(error, 'error');
    },
  );
  return request;
};

export const request = createAxiosIntance();
