export default {
  '1': 'Interface access times',
  '2': 'Traffic volume',
  '3': 'Interface success rate',
  '4': 'Purchase amount',
  '5': 'V0.0.1 release',
  '6': 'Integration platform interface access frequency',
  '7': 'ID',
  '8': 'Chinese',
  '9': 'English',
  '10': 'logout',
  '11': 'Log Release',
  '12': 'Type 1',
  '13': 'Type 2',
  '14': 'Healthy Interface Percentage',
  '15': 'System 1',
  '16': 'System 2',
  '17': 'System 3',
  '18': 'Home',
  '19': 'Log',
  '20': 'Projection',
  '21': 'Empower',
  '22': 'Application Job',
  '23': 'Job Template',
  '24': 'Application Detail',
  '25': 'Order',
  '26': 'Service Name',
  '27': 'Time',
  '28': 'Log Level',
  '29': 'Log Info',
  '30': 'Interface Singnature',
  '31': 'Track ID',
  '32': 'Operation',
  '33': 'detail',
  '34': 'Operation Time',
  '35': 'Reset',
  '36': 'Search',
  '37': 'Log Position',
  '38': 'Interface Params',
  '39': 'Detailed Information',
  '40': 'Intergration Name',
  '41': 'Intergration Description',
  '42': 'Defination Time',
  '43': 'Intergration Status',
  '44': 'Open',
  '45': 'Close',
  '46': 'Configuration',
  '47': 'Third-party application authorization',
  '48': 'Update system login information',
  '49': 'Connection test',
  '50': 'Plese choose the thrid-party application',
  '51': 'error',
  '52': 'Previous step',
  '53': 'Next step',
  '54': 'Return to the first step',
  '55': 'Run ApplicationJob',
  '56': 'Sechuled Job Name',
  '57': 'Execution Job Status',
  '58': 'Complete',
  '59': 'Executing',
  '60': 'Failed',
  '61': 'Sechuled Job Mode',
  '62': 'Start Time',
  '63': 'End Time',
  '64': 'No Data',
  '65': 'The result may over 1000 entries, and only the first 1000 entries are displayed',
  '66': 'Execution Environment',
  '67': 'Execution Job Signature',
  '68': 'Job Status',
  '69': 'Hourly unit',
  '70': 'Daily unit',
  '71': 'Waiting',
  '72': 'Time Range',
  '73': 'Query',
  '74': 'Name',
  '75': 'Steps Num',
  '76': 'Share Success',
  '77': 'Share Failed',
  '78': 'Delete Success',
  '79': 'Delete Failed',
  '80': 'Template Name',
  '81': 'Oringin',
  '82': 'System build-in',
  '83': 'Check',
  '84': 'Share',
  '85': 'Sure to delete the template?',
  '86': 'Sure',
  '87': 'Cancel',
  '88': 'Delete',
  '89': 'Please Choose',
  '90': 'Create Success',
  '91': 'Create Failed',
  '92': 'Modify Success',
  '93': 'Modify Failed',
  '94': 'Check whether the template name already exists',
  '95': 'Save',
  '96': 'Sort',
  '97': 'Template Alias',
  '98': 'Sure to delete the template?(Params are not recoverable)',
  '99': 'Choose Template',
  '100': 'Add',
  '101': 'Chosen Templates',
  '102': 'System IP',
  '103': 'Current System',
  '104': 'Empower Success',
};
