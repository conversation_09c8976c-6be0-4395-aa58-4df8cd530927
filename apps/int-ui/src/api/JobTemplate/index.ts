import { request } from '@/service';

const JobTempalteApi = {
  /**
   * @description 获取任务列表(可被作为任务链)
   * */
  ///api/plan/getJobSelectedTemplate
  getJobSelectedTemplate: () => request.get('/api/plan/getJobSelectedTemplate'),

  ///api/plan/getJobTemplateByUserOnlyRead
  /**
   * @description 获取任务列表（当前登录用户可查看/使用）
   * */
  getJobTemplateByUserOnlyRead: (data: any) =>
    request.get('/api/plan/getJobTemplateByUserOnlyRead', {
      params: data,
    }),

  /**
   * @description  获取任务列表（当前登录用户可修改/删除)
   * @api /api/plan/getJobTemplateByUserUpdate
   * */
  getJobTemplateByUserUpdate: (data: any) =>
    request.get('/api/plan/getJobTemplateByUserUpdate', {
      params: data,
    }),

  /**
   * @description  获取模版框架和默认值
   * @api /api/job/getJobTemplateFrameAndValue
   * */
  getJobTemplateFrameAndValue: (data: any) =>
    request.get('/api/job/getJobTemplateFrameAndValue', {
      params: data,
    }),

  getJobTemplateFrameAndValueForCopy: (data: any) =>
    request.get('/api/plan/getJobTemplateFrameAndValueForCopy', {
      params: data,
    }),

  /**
   * @description 删除任务模版
   * @api  /api/plan/deleteJobTemplate
   * */
  deleteJobTemplate: (data: any) =>
    request.post('/api/plan/deleteJobTemplate', data),

  /**
   * @description 检查模板名称
   * @api /api/job/checkTemplateName
   * */
  checkTemplateName: (data: any) =>
    request.get('/api/job/checkTemplateName', { params: data }),

  /**
   * @description 查询用户列表（含分页)
   * @api /api/users
   * */

  getUsers: (data: any) => request.get('/api/users', { params: data }),

  createJobTemplate: (data: any) =>
    request.post('/api/plan/createJobTemplate', data),
  updateJobTemplate: (data: any) =>
    request.post('/api/plan/updateJobTemplate', data),
  getJobTemplateFrame: () =>
    request.get('/api/jobTemplate/getJobTemplateFrame'),

  getUserListByCoId: () => request.get('/api/users/getUserListNotSelf'),
  shareJobTemplateTo: (data: any) =>
    request.post('/api/plan/shareJobTemplateTo', data),
};

export default JobTempalteApi;
