import { request, returnData } from '@/service';
import { GetIntApiListByIecIdReq, GetIntApiListByIecIdRes } from './model';
const Projection = {
  // 获取环境信息
  getAllEnvList: () => request.get('/api/apihandle/getAllEnvList'),
  // 获取当前环境下的集成器列表
  getIntEnvSchemeList: (data: any) =>
    request.get('/api/apihandle/getIntEnvSchemeList', {
      params: {
        intName: data.intName,
        intState: data.intState,
        pageNum: data.pageNum,
        pageSize: data.pageSize,
        envId: data.envId,
      },
    }),
  // 根据集成器id获取这个集成器下的接口列表
  getIntApiListByIecId: (
    data: GetIntApiListByIecIdReq,
  ): Promise<returnData<GetIntApiListByIecIdRes[]>> =>
    request.get('/api/apihandle/getIntApiListByIecId', {
      params: data,
    }),

  // 更改接口状态
  updateIntApiState: (data: any) =>
    request.post('/api/apihandle/updateIntApiState', data),
  // 更新绿色通道
  UpdateIntApiGreenChannel: (data: any) =>
    request.post('/api/apihandle/updateIntApiGreenChannel', data),
  getIdsByUser: () => request.get('/mdDataView/getIdsByUser'),
  upload: (data: any) => request.post('/api/file/uploadFile', data),
  onloadForm: (data: any) => request.get('/int/transfer', { params: data }),
  getNewScpToken: (data: any) => request.post('/api/jump/getNewScpToken', data),
  getThirdpartySystemAuth: (data: any) =>
    request.get('/api/thirdparty/getThirdpartySystemAuth?iecId=' + `${data}`),
  //新获取数据接口，定时任务
  startCornJob: (data: any) => request.post('/scp/cron/startCronJob', data),
  //test
  testJob: (data: any) => request.get('/scp/cron/testJob', { params: data }),
};

export default Projection;
