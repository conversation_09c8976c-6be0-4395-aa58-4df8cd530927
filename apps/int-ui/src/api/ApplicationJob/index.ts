import { request } from '@/service';

const ApplicationJobApi = {
  /**
   * @description 查看具体task的日志
   * @api /api/cron/getScpTaskLog
   * */
  getLogsScpLogger: (data: any) =>
    request.get('/api/cron/getScpTaskLog', { params: data }),

  /**
   * @description 启动定时任务
   * @api /api/cron/startCronJob/api/cron/startCronJob
   */
  startCronJob: (data: any) => request.post('/api/cron/startCronJob', data),

  /**
   * @description  获取一个时间段内的已执行任务和模拟数据
   * @api /api/cron/getCronJobTask
   * */
  getCronJobTask: (data: any) =>
    request.get('/api/cron/getCronJobTask', { params: data }),

  /**
   * @description 获取任务详情
   * @api /api/cron/getCronTaskDetail
   * */

  getCronTaskDetail: (data: any) =>
    request.get('/api/cron/getCronTaskDetail', { params: data }),

  getCronTaskDetailContainsParent: (taskUuid: string, jobId: string) =>
    request.get('/api/cron/getCronTaskDetailContainsParent', {
      params: { taskUuid, jobId },
    }),

  deleteUnExecutedJob: (jobId: string) =>
    request.post('/api/cron/deleteUnExecutedJob', { jobId: jobId }),

  /**
   * @description
   * @api /user/getUserListByCoIdContainsSelf
   */
  getUserListByCoIdContainsSelf: () =>
    request.get('/api/user/getUserListByCoIdContainsSelf'),

  getPversionList: (data: any) =>
    request.get('/api/users/getPverisonList', {
      params: data,
    }),
};
export default ApplicationJobApi;
