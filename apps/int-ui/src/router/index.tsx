import pages from '@/pages';
import React from 'react';
import Loading from '@/components/Loading';
import Layout from '@/components/Layout';
import { type RouteObject } from 'react-router-dom';
const routes: RouteObject[] = [
  {
    path: '/',
    element: <Layout />, // 默认展示的组件
    children: [
      {
        path: '/home',
        ErrorBoundary: pages.Error,
        element: <pages.Home />,
      },
      {
        path: '/logpage',
        element: <pages.LogPage />,
        ErrorBoundary: pages.Error,
      },
      {
        path: '/projection',
        element: <pages.Projection />,
        ErrorBoundary: pages.Error,
      },
      {
        path: '/Empower',
        element: <pages.Empower />,
        ErrorBoundary: pages.Error,
      },
      {
        path: '/test',
        element: <pages.Test />,
      },
      {
        path: '/applicationJob',
        element: <pages.ApplicationJob />,
        children: [
          {
            index: true,
            element: <pages.SubApplicationJob />,
          },

          {
            path: 'checkApplication',
            element: <pages.CheckApplication />,
          },
        ],
      },
      {
        path: '/runApplicationJob',
        element: <pages.RunApplicationJob />,
      },
      {
        path: '/jobTemplate',
        element: <pages.JobTemplate />,
        children: [
          {
            index: true,
            element: <pages.SubJobTemplate />,
          },
          {
            path: 'editJobTemplate',
            element: <pages.EditJobTemplate />,
          },
        ],
      },
      {
        path: '/error',
        element: <pages.Error />,
        ErrorBoundary: pages.Error,
      },
    ],
  },
  {
    path: '/login',
    element: <pages.Login />,
    ErrorBoundary: pages.Error,
  },
  {
    path: '*',
    element: <pages.Error />,
  },
];

const processRoutes = (routes: RouteObject[]): RouteObject[] => {
  return routes.map((route: RouteObject) => {
    if (route.children) {
      return {
        ...route,
        children: processRoutes(route.children),
        element: (
          <React.Suspense fallback={<Loading />}>
            {route.element}
          </React.Suspense>
        ),
      };
    }
    return {
      ...route,
      element: (
        <React.Suspense fallback={<Loading />}>{route.element}</React.Suspense>
      ),
    };
  });
};

const after_routes: RouteObject[] = processRoutes(routes);
export default after_routes;
