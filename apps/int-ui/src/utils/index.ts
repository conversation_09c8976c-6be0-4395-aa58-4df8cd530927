/**
 * Get the first value of an object
 * @param obj
 * @returns The first value of the object, or undefined if the object is empty
 * @example
 * ```ts
 * getFirstValue({ a: 1, b: 2 }); // 1
 * getFirstValue({}); // undefined
 * ```
 */

function getFirstValue<T>(obj: Record<string, T> | null | undefined): T | null {
  if (obj == null) {
    return null;
  }
  const keys = Object.keys(obj);
  if (keys.length === 0) {
    return null;
  }
  return obj[keys[0]];
}

/**
 * @description 移除对象中值为undefined的键值对
 * */
function removeUndefinedValues(obj: any) {
  return Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    Object.entries(obj).filter(([_, value]) => value !== undefined),
  );
}

export { getFirstValue, removeUndefinedValues };
