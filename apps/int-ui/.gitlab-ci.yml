stages:
  - build
  - trigger

build:
  stage: build
  tags:
    - newBigserver
  before_script:
    - docker login -u admin -p Harbor12345 jianweisoft.cn
    # - docker login registry.gitlab.com -u jinxuliu -p **************************
    - docker buildx use multi-builder
  script:
    - docker buildx build --label hash=$CI_COMMIT_SHORT_SHA --platform linux/arm64,linux/amd64 -t jianweisoft.cn/int/ui-int-i:latest -t jianweisoft.cn/int/ui-int-i:$CI_COMMIT_SHORT_SHA . --push
    # - docker build --label hash=$CI_COMMIT_SHORT_SHA --tag registry.gitlab.com/hzjwsoft/int-ui:$CI_COMMIT_SHORT_SHA --tag registry.gitlab.com/hzjwsoft/int-ui:latest  . --push
trigger:
  stage: trigger
  tags:
    - newBigserver
  only:
    - main
    - develop
  script:
    - 'curl -X POST --fail -F token=glptt-f7f0109291b795f16813d4c42c39291dd6257282 -F ref=devpre -F "variables[IMAGE_NAME]=ui-int-i" -F "variables[CUSTOM_IMAGE_NAME]=CUSTOM_UI_IMAGE_NAME" -F "variables[IMAGE_TAG]=$CI_COMMIT_SHORT_SHA" -F "variables[CUSTOM_IMAGE_TAG]=CUSTOM_UI_IMAGE_VERSION"  http://gitlab.scmify.com:8929/api/v4/projects/29/trigger/pipeline'
