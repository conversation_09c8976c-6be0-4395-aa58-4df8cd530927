lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@ant-design/charts':
    specifier: ^2.0.3
    version: 2.0.3(lodash-es@4.17.21)(react-dom@18.2.0)(react@18.2.0)
  '@ant-design/icons':
    specifier: ^5.2.6
    version: 5.2.6(react-dom@18.2.0)(react@18.2.0)
  '@ant-design/plots':
    specifier: ^2.1.5
    version: 2.1.5(lodash-es@4.17.21)(react-dom@18.2.0)(react@18.2.0)
  '@antv/g2':
    specifier: ^5.2.7
    version: 5.2.7
  '@dnd-kit/core':
    specifier: ^6.1.0
    version: 6.1.0(react-dom@18.2.0)(react@18.2.0)
  '@dnd-kit/modifiers':
    specifier: ^7.0.0
    version: 7.0.0(@dnd-kit/core@6.1.0)(react@18.2.0)
  '@dnd-kit/sortable':
    specifier: ^8.0.0
    version: 8.0.0(@dnd-kit/core@6.1.0)(react@18.2.0)
  '@dnd-kit/utilities':
    specifier: ^3.2.2
    version: 3.2.2(react@18.2.0)
  '@nextui-org/input':
    specifier: ^2.1.16
    version: 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)
  '@nextui-org/react':
    specifier: ^2.2.9
    version: 2.2.9(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)(tailwindcss@3.3.5)
  '@reduxjs/toolkit':
    specifier: ^2.0.1
    version: 2.0.1(react-redux@8.1.3)(react@18.2.0)
  '@voerkai18n/react':
    specifier: ^2.1.10
    version: 2.1.10(@types/react@18.2.38)(@voerkai18n/runtime@2.1.10)(react@18.2.0)
  ahooks:
    specifier: ^3.8.1
    version: 3.8.1(react@18.2.0)
  antd:
    specifier: ^5.11.2
    version: 5.11.2(react-dom@18.2.0)(react@18.2.0)
  axios:
    specifier: ^1.6.2
    version: 1.6.2
  dayjs:
    specifier: ^1.11.10
    version: 1.11.10
  dotenv:
    specifier: ^16.3.1
    version: 16.3.1
  framer-motion:
    specifier: ^10.16.16
    version: 10.16.16(react-dom@18.2.0)(react@18.2.0)
  lodash:
    specifier: ^4.17.21
    version: 4.17.21
  path:
    specifier: ^0.12.7
    version: 0.12.7
  qs:
    specifier: ^6.12.0
    version: 6.12.0
  react:
    specifier: ^18.2.0
    version: 18.2.0
  react-dom:
    specifier: ^18.2.0
    version: 18.2.0(react@18.2.0)
  react-redux:
    specifier: ^8.1.3
    version: 8.1.3(@types/react-dom@18.2.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)(redux@5.0.1)
  react-router-dom:
    specifier: ^6.19.0
    version: 6.19.0(react-dom@18.2.0)(react@18.2.0)
  scmify-components:
    specifier: ^0.1.5
    version: 0.1.5(react-dom@18.2.0)(react@18.2.0)
  terser:
    specifier: ^5.27.0
    version: 5.27.0
  timezone:
    specifier: link:dayjs/plugin/timezone
    version: link:dayjs/plugin/timezone

devDependencies:
  '@commitlint/cli':
    specifier: ^18.4.3
    version: 18.4.3(typescript@5.3.2)
  '@commitlint/config-conventional':
    specifier: ^18.4.3
    version: 18.4.3
  '@types/lodash':
    specifier: ^4.17.6
    version: 4.17.6
  '@types/node':
    specifier: ^20.11.14
    version: 20.11.14
  '@types/qs':
    specifier: ^6.9.14
    version: 6.9.14
  '@types/react':
    specifier: ^18.2.37
    version: 18.2.38
  '@types/react-beautiful-dnd':
    specifier: ^13.1.8
    version: 13.1.8
  '@types/react-dom':
    specifier: ^18.2.15
    version: 18.2.17
  '@typescript-eslint/eslint-plugin':
    specifier: ^6.10.0
    version: 6.12.0(@typescript-eslint/parser@6.12.0)(eslint@8.54.0)(typescript@5.3.2)
  '@typescript-eslint/parser':
    specifier: ^6.10.0
    version: 6.12.0(eslint@8.54.0)(typescript@5.3.2)
  '@vitejs/plugin-react-swc':
    specifier: ^3.5.0
    version: 3.5.0(vite@5.0.2)
  autoprefixer:
    specifier: ^10.4.16
    version: 10.4.16(postcss@8.4.31)
  eslint:
    specifier: ^8.53.0
    version: 8.54.0
  eslint-config-prettier:
    specifier: ^9.0.0
    version: 9.0.0(eslint@8.54.0)
  eslint-plugin-prettier:
    specifier: ^5.0.1
    version: 5.0.1(eslint-config-prettier@9.0.0)(eslint@8.54.0)(prettier@3.1.0)
  eslint-plugin-react-hooks:
    specifier: ^4.6.0
    version: 4.6.0(eslint@8.54.0)
  eslint-plugin-react-refresh:
    specifier: ^0.4.4
    version: 0.4.4(eslint@8.54.0)
  husky:
    specifier: ^8.0.0
    version: 8.0.3
  lint-staged:
    specifier: ^15.2.0
    version: 15.2.0
  postcss:
    specifier: ^8.4.31
    version: 8.4.31
  prettier:
    specifier: ^3.1.0
    version: 3.1.0
  tailwindcss:
    specifier: ^3.3.5
    version: 3.3.5
  typescript:
    specifier: ^5.2.2
    version: 5.3.2
  vite:
    specifier: ^5.0.0
    version: 5.0.2(@types/node@20.11.14)(terser@5.27.0)

packages:

  /@aashutoshrathi/word-wrap@1.2.6:
    resolution: {integrity: sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /@alloc/quick-lru@5.2.0:
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  /@ant-design/charts-util@0.0.1-alpha.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EwTUjRPhU2CUyI2e11pAi9aOQg9oEWdFEWxv1f4j+Ta4doYXd1yTl94Zy9JvigiEj4qv8dPGl0PUd3r+qMVrAg==}
    peerDependencies:
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/charts@2.0.3(lodash-es@4.17.21)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-CDyDRoMLtUcZ6ZU92nmW23Yt9a9KJU3Z/i5RPMasDuBMShTPNY3i/sOQgv5nTho+7FfBMVrdAax+cKAYcAz3jQ==}
    peerDependencies:
      lodash-es: ^4.17.21
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@ant-design/plots': 2.1.5(lodash-es@4.17.21)(react-dom@18.2.0)(react@18.2.0)
      lodash-es: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/colors@7.0.0:
    resolution: {integrity: sha512-iVm/9PfGCbC0dSMBrz7oiEXZaaGH7ceU40OJEfKmyuzR9R5CRimJYPlRiFtMQGQcbNMea/ePcoIebi4ASGYXtg==}
    dependencies:
      '@ctrl/tinycolor': 3.6.1
    dev: false

  /@ant-design/colors@7.1.0:
    resolution: {integrity: sha512-MMoDGWn1y9LdQJQSHiCC20x3uZ3CwQnv9QMz6pCmJOrqdgM9YxsoVVY0wtrdXbmfSgnV0KNk6zi09NAhMR2jvg==}
    dependencies:
      '@ctrl/tinycolor': 3.6.1
    dev: false

  /@ant-design/cssinjs-utils@1.1.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-2HAiyGGGnM0es40SxdszeQAU5iWp41wBIInq+ONTCKjlSKOrzQfnw4JDtB8IBmqE6tQaEKwmzTP2LGdt5DSwYQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/cssinjs': 1.21.1(react-dom@18.2.0)(react@18.2.0)
      '@babel/runtime': 7.26.0
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/cssinjs@1.17.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ed2rruHMxZTVYGPec6QBZkrh00Df5K1FAufmBpONai5iGYxoxIkg1sRD8YdCw0NvPRAa4r1nJP5LbwICGHzGiQ==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@emotion/hash': 0.8.0
      '@emotion/unitless': 0.7.5
      classnames: 2.3.2
      csstype: 3.1.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      stylis: 4.3.0
    dev: false

  /@ant-design/cssinjs@1.21.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-tyWnlK+XH7Bumd0byfbCiZNK43HEubMoCcu9VxwsAwiHdHTgWa+tMN0/yvxa+e8EzuFP1WdUNNPclRpVtD33lg==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@emotion/hash': 0.8.0
      '@emotion/unitless': 0.7.5
      classnames: 2.3.2
      csstype: 3.1.3
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      stylis: 4.3.4
    dev: false

  /@ant-design/fast-color@2.0.6:
    resolution: {integrity: sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA==}
    engines: {node: '>=8.x'}
    dependencies:
      '@babel/runtime': 7.26.0
    dev: false

  /@ant-design/icons-svg@4.3.1:
    resolution: {integrity: sha512-4QBZg8ccyC6LPIRii7A0bZUk3+lEDCLnhB+FVsflGdcWPPmV+j3fire4AwwoqHV/BibgvBmR9ZIo4s867smv+g==}
    dev: false

  /@ant-design/icons-svg@4.4.2:
    resolution: {integrity: sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==}
    dev: false

  /@ant-design/icons@5.2.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-4wn0WShF43TrggskBJPRqCD0fcHbzTYjnaoskdiJrVHg86yxoZ8ZUqsXvyn4WUqehRiFKnaclOhqk9w4Ui2KVw==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 7.0.0
      '@ant-design/icons-svg': 4.3.1
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/icons@5.5.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-0UrM02MA2iDIgvLatWrj6YTCYe0F/cwXvVE0E2SqGrL7PZireQwgEKTKBisWpZyal5eXZLvuM98kju6YtYne8w==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 7.0.0
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.26.0
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/plots@2.1.5(lodash-es@4.17.21)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Rj2Qp21l/I71qGW1HR+Odkk3BTMb/XNKoPVoq0btZZQ+35cT7XYn12sCNQ4O3VZgksA1COsZN4pNp+k2deBQRQ==}
    peerDependencies:
      lodash-es: ^4.17.21
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@ant-design/charts-util': 0.0.1-alpha.5(react-dom@18.2.0)(react@18.2.0)
      '@antv/event-emitter': 0.1.3
      '@antv/g2': 5.2.7
      '@antv/g2-extension-plot': 0.1.1
      lodash-es: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/react-slick@1.0.2(react@18.2.0):
    resolution: {integrity: sha512-Wj8onxL/T8KQLFFiCA4t8eIRGpRR+UPgOdac2sYzonv+i0n3kXHmvHLLiOYL655DQx2Umii9Y9nNgL7ssu5haQ==}
    peerDependencies:
      react: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      json2mq: 0.2.0
      react: 18.2.0
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 5.0.0
    dev: false

  /@ant-design/react-slick@1.1.2(react@18.2.0):
    resolution: {integrity: sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA==}
    peerDependencies:
      react: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      json2mq: 0.2.0
      react: 18.2.0
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 5.0.2
    dev: false

  /@antv/component@2.1.1:
    resolution: {integrity: sha512-V0UCq3Bekqtjw5WedexT1tHM/9x5BY0UAaU7G/5A2NhRfp9GuaQ8xGWLMSWlCQiJSRZWhPIA7RoOSw4Y/W+7UA==}
    dependencies:
      '@antv/g': 6.1.9
      '@antv/scale': 0.4.15
      '@antv/util': 3.3.5
      svg-path-parser: 1.1.0
    dev: false

  /@antv/coord@0.4.7:
    resolution: {integrity: sha512-UTbrMLhwJUkKzqJx5KFnSRpU3BqrdLORJbwUbHK2zHSCT3q3bjcFA//ZYLVfIlwqFDXp/hzfMyRtp0c77A9ZVA==}
    dependencies:
      '@antv/scale': 0.4.15
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
    dev: false

  /@antv/event-emitter@0.1.3:
    resolution: {integrity: sha512-4ddpsiHN9Pd4UIlWuKVK1C4IiZIdbwQvy9i7DUSI3xNJ89FPUFt8lxDYj8GzzfdllV0NkJTRxnG+FvLk0llidg==}
    dev: false

  /@antv/g-camera-api@1.2.21:
    resolution: {integrity: sha512-5qdqhRV8fYPsOiQkRWusVLWV8z2DycFKXShjrLkIhoZIXW0dpRwLs4ixc0BW0Gbj6Y37DwNx54dggRihhVyujQ==}
    dependencies:
      '@antv/g-lite': 1.2.20
      '@antv/util': 3.3.5
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-camera-api@2.0.23:
    resolution: {integrity: sha512-tDoABx5HF+xAYsgWCFsVOj7qaC+XXM6l38ne2dxGuox9PGjzjAel1yMbWa0t97a4S1YWtnvgg4OpH+9HPTm2iQ==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-canvas@2.0.27:
    resolution: {integrity: sha512-3qqI6pfjfhZrcNqK9LARFuaxEJLO4b9LgL6gr1Wx8ByX9fNWXHxiXD4gRB2zv+RzVqq9n7PaYCtRlypODvl/oA==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/g-plugin-canvas-path-generator': 2.1.4
      '@antv/g-plugin-canvas-picker': 2.1.6
      '@antv/g-plugin-canvas-renderer': 2.2.6
      '@antv/g-plugin-dom-interaction': 2.1.9
      '@antv/g-plugin-html-renderer': 2.1.9
      '@antv/g-plugin-image-loader': 2.1.6
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      tslib: 2.6.2
    dev: false

  /@antv/g-dom-mutation-observer-api@1.2.20:
    resolution: {integrity: sha512-J3EuEtWmIKGC44XWgJXG/nFnsjFY/oH9aAj1YaDVMtYREcyQOjb/aY9jdCfDmZBrNcMYeUHcsZEVhYwpc0wBcw==}
    dependencies:
      '@antv/g-lite': 1.2.20
    dev: false

  /@antv/g-dom-mutation-observer-api@2.0.20:
    resolution: {integrity: sha512-/KntumSsYt5LFrtRBcFB4AJrskWgp0iGPVZKkKmFCp38qufaxxDijiqewJRe1I1YHUeRQclP5DpLttv3o5woZQ==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@babel/runtime': 7.26.0
    dev: false

  /@antv/g-lite@1.2.20:
    resolution: {integrity: sha512-h/0Chyp87e4gEGQw9pn5n3489rGEgttlMVwuhRsYXcOGVAYR92+7/jkDYEArf2eVtSdko11pg+kJKhr0yWk7gA==}
    dependencies:
      '@antv/g-math': 2.0.2
      '@antv/util': 3.3.5
      d3-color: 1.4.1
      eventemitter3: 5.0.1
      gl-matrix: 3.4.3
      rbush: 3.0.1
      tslib: 2.6.2
    dev: false

  /@antv/g-lite@2.2.4:
    resolution: {integrity: sha512-61gfrqqErnmoRR2wtbHAAxx38U9VEFz2goYloNFQ3XDYJQJUAf3UCSBZ0kpsSKRSoWEMiY7n8m70nO6F/Q9kBg==}
    dependencies:
      '@antv/g-math': 3.0.0
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      d3-color: 3.1.0
      eventemitter3: 5.0.1
      gl-matrix: 3.4.3
      rbush: 3.0.1
      tslib: 2.6.2
    dev: false

  /@antv/g-math@2.0.2:
    resolution: {integrity: sha512-uqGU1C+70orjeSUoIzD3TuXjL5dRQCIyjZrBrTmm0FWd6VQJMWHyG5ypuZ2lMiI5MrRajVSE1w+3J4hiNBYSJg==}
    dependencies:
      '@antv/util': 3.3.5
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-math@3.0.0:
    resolution: {integrity: sha512-AkmiNIEL1vgqTPeGY2wtsMdBBqKFwF7SKSgs+D1iOS/rqYMsXdhp/HvtuQ5tx/HdawE/ZzTiicIYopc520ADZw==}
    dependencies:
      '@antv/util': 3.3.5
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-canvas-path-generator@2.1.4:
    resolution: {integrity: sha512-bSNKEAGoUyo+zps7RpD/9f66HNkXIW9QgjYit68n8APmlq7Du0xKE++ryR7DH8WQNUyx75DedwMNcXxemcV8NQ==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/g-math': 3.0.0
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-canvas-picker@2.1.6:
    resolution: {integrity: sha512-3sXowCAnwU6eeN1KUMN4CCeuEy/vFWaQRVFXPGl8HDxCJkh9wua2K8J89tU4EuQx5CIIzfGYz/WFBHGQ+Ly8ug==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/g-math': 3.0.0
      '@antv/g-plugin-canvas-path-generator': 2.1.4
      '@antv/g-plugin-canvas-renderer': 2.2.6
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-canvas-renderer@2.2.6:
    resolution: {integrity: sha512-da0MakWZkUMqaDs1UR3MOtVfkUq3z2LWMeQkfdCQOO9WxTxxj75A+edPGT19hZ3YJIBujur7ZtbN6N0W1HZ34w==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/g-math': 3.0.0
      '@antv/g-plugin-canvas-path-generator': 2.1.4
      '@antv/g-plugin-image-loader': 2.1.6
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-dom-interaction@2.1.9:
    resolution: {integrity: sha512-84FqlejIJqH3yTivBLeQHEywtjNqeuLNvI5RAgg3xEPc93qUR4UWtB6ks6ETuJDwRyf5UZheFCy3E+JfmlEbLA==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@babel/runtime': 7.26.0
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-dragndrop@2.0.20:
    resolution: {integrity: sha512-q74KLu5kh7YYW3oEcx6ZNSi5cmo4g+88MuVSKCWXrbzxYhFMRTBsT787Fkf2l6av9PTxdBgD9Ot30N5IyqgGtQ==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-html-renderer@2.1.9:
    resolution: {integrity: sha512-OzTaswRchzNBdtxf9rByE7XS5m/xoohrDrn/nw+Q0X9JIdkEJCPfC2CecLsmreF1XuN58VUiKd0pXzdxmphigA==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-plugin-image-loader@2.1.6:
    resolution: {integrity: sha512-MkOjC/o4frh4Vx2WG2QUslMP+z7l19JyFXy4tf2sgYjADb1fwED/UQ/lxvtDiApEb5yR11yrG0JCtT+lhMgEqA==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@antv/g-web-animations-api@1.2.21:
    resolution: {integrity: sha512-3LkPDOjpMjQ9b/mYoGgsoocWEOvDIUW2GpoLClbyd0vcIc97EujXVUobUIu7jvFQgvucgTnaxDLkAGDzuPaFvg==}
    dependencies:
      '@antv/g-lite': 1.2.20
      '@antv/util': 3.3.5
      tslib: 2.6.2
    dev: false

  /@antv/g-web-animations-api@2.1.9:
    resolution: {integrity: sha512-Lz9J0g4AQZNwmeflg+PPzdDhWrmyZlNJ3rHVHH8rQuzYdxFw+pOoamgbm+ImpmS5pBJFp2xtdbQtSDP12XTe2w==}
    dependencies:
      '@antv/g-lite': 2.2.4
      '@antv/util': 3.3.5
      '@babel/runtime': 7.26.0
      tslib: 2.6.2
    dev: false

  /@antv/g2-extension-plot@0.1.1:
    resolution: {integrity: sha512-yruGQudJYL/3/2/mHA0+WRW6oJ+YX4ZWMSqGu6eY5+ooqvTgeeF91KHixrodiPfiZZ+zDFrNeZ0HsOcLuHB+YA==}
    dependencies:
      '@antv/g': 5.18.23
      '@antv/g2': 5.2.7
      '@antv/util': 3.3.5
      d3-array: 3.2.4
      d3-hierarchy: 3.1.2
    dev: false

  /@antv/g2@5.2.7:
    resolution: {integrity: sha512-bOU7ZJfa735KCqIsWWwlFtn3pc8TwJIckBhy7X8PFcxTuMIXzgqOt7vbMMdF4psBHMyIIOCDAo8zf9rGhgjEzA==}
    dependencies:
      '@antv/component': 2.1.1
      '@antv/coord': 0.4.7
      '@antv/event-emitter': 0.1.3
      '@antv/g': 6.1.9
      '@antv/g-canvas': 2.0.27
      '@antv/g-plugin-dragndrop': 2.0.20
      '@antv/scale': 0.4.15
      '@antv/util': 3.3.5
      d3-array: 3.2.4
      d3-dsv: 3.0.1
      d3-force: 3.0.0
      d3-format: 3.1.0
      d3-geo: 3.1.0
      d3-hierarchy: 3.1.2
      d3-path: 3.1.0
      d3-scale-chromatic: 3.0.0
      d3-shape: 3.2.0
      flru: 1.0.2
      fmin: 0.0.2
      pdfast: 0.2.0
    dev: false

  /@antv/g@5.18.23:
    resolution: {integrity: sha512-6O+ODt1w0FC4w98J00Ao7rtqztkV7AWqKF69kog3RqQWh40rYpM7JIfR0cFUAdHf8GLRyr4m+RQR+TZlt+QVEw==}
    dependencies:
      '@antv/g-camera-api': 1.2.21
      '@antv/g-dom-mutation-observer-api': 1.2.20
      '@antv/g-lite': 1.2.20
      '@antv/g-web-animations-api': 1.2.21
    dev: false

  /@antv/g@6.1.9:
    resolution: {integrity: sha512-JwZkjU6t8Q8X43TetRFxmEAyH51COHx3mHUJppmMDmXOtHdSD0nzMyZb9i8UvRPVLzhQOht818cGy3Fu6IHt0Q==}
    dependencies:
      '@antv/g-camera-api': 2.0.23
      '@antv/g-dom-mutation-observer-api': 2.0.20
      '@antv/g-lite': 2.2.4
      '@antv/g-web-animations-api': 2.1.9
      '@babel/runtime': 7.26.0
    dev: false

  /@antv/scale@0.4.15:
    resolution: {integrity: sha512-b3b2U1L55fcJg0fB/BloVvcngPo/QxhosWgjn6JmYLVLezxDYMO+zdQou8hxpsLMUVhH15NqRDcM/1n4U6G04g==}
    dependencies:
      '@antv/util': 2.0.17
      color-string: 1.9.1
      fecha: 4.2.3
    dev: false

  /@antv/util@2.0.17:
    resolution: {integrity: sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q==}
    dependencies:
      csstype: 3.1.3
      tslib: 2.6.2
    dev: false

  /@antv/util@3.3.5:
    resolution: {integrity: sha512-bVv1loamL/MgUEN9dNt7VKAsghO4Wgb+kzr8B9TgkM5tHgKk++xiTwi3pejIdgU8DDkzcyaRsO+VTOXJt8jLng==}
    dependencies:
      fast-deep-equal: 3.1.3
      flru: 1.0.2
      gl-matrix: 3.4.3
      tslib: 2.6.2
    dev: false

  /@babel/code-frame@7.23.5:
    resolution: {integrity: sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.23.4
      chalk: 2.4.2
    dev: true

  /@babel/helper-validator-identifier@7.22.20:
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/highlight@7.23.4:
    resolution: {integrity: sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/runtime@7.23.4:
    resolution: {integrity: sha512-2Yv65nlWnWlSpe3fXEyX5i7fx5kIKo4Qbcj+hMO0odwaneFjfXw5fdum+4yL20O0QiaHpia0cYQ9xpNMqrBwHg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0
    dev: false

  /@babel/runtime@7.26.0:
    resolution: {integrity: sha512-FDSOghenHTiToteC/QRlv2q3DhPZ/oOXTBoirfWNx1Cx3TMVcGWQtMMmQcSvb/JjpNeGzx8Pq/b4fKEJuWm1sw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0
    dev: false

  /@commitlint/cli@18.4.3(typescript@5.3.2):
    resolution: {integrity: sha512-zop98yfB3A6NveYAZ3P1Mb6bIXuCeWgnUfVNkH4yhIMQpQfzFwseadazOuSn0OOfTt0lWuFauehpm9GcqM5lww==}
    engines: {node: '>=v18'}
    hasBin: true
    dependencies:
      '@commitlint/format': 18.4.3
      '@commitlint/lint': 18.4.3
      '@commitlint/load': 18.4.3(typescript@5.3.2)
      '@commitlint/read': 18.4.3
      '@commitlint/types': 18.4.3
      execa: 5.1.1
      lodash.isfunction: 3.0.9
      resolve-from: 5.0.0
      resolve-global: 1.0.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - typescript
    dev: true

  /@commitlint/config-conventional@18.4.3:
    resolution: {integrity: sha512-729eRRaNta7JZF07qf6SAGSghoDEp9mH7yHU0m7ff0q89W97wDrWCyZ3yoV3mcQJwbhlmVmZPTkPcm7qiAu8WA==}
    engines: {node: '>=v18'}
    dependencies:
      conventional-changelog-conventionalcommits: 7.0.2
    dev: true

  /@commitlint/config-validator@18.4.3:
    resolution: {integrity: sha512-FPZZmTJBARPCyef9ohRC9EANiQEKSWIdatx5OlgeHKu878dWwpyeFauVkhzuBRJFcCA4Uvz/FDtlDKs008IHcA==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/types': 18.4.3
      ajv: 8.12.0
    dev: true

  /@commitlint/ensure@18.4.3:
    resolution: {integrity: sha512-MI4fwD9TWDVn4plF5+7JUyLLbkOdzIRBmVeNlk4dcGlkrVA+/l5GLcpN66q9LkFsFv6G2X31y89ApA3hqnqIFg==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/types': 18.4.3
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1
    dev: true

  /@commitlint/execute-rule@18.4.3:
    resolution: {integrity: sha512-t7FM4c+BdX9WWZCPrrbV5+0SWLgT3kCq7e7/GhHCreYifg3V8qyvO127HF796vyFql75n4TFF+5v1asOOWkV1Q==}
    engines: {node: '>=v18'}
    dev: true

  /@commitlint/format@18.4.3:
    resolution: {integrity: sha512-8b+ItXYHxAhRAXFfYki5PpbuMMOmXYuzLxib65z2XTqki59YDQJGpJ/wB1kEE5MQDgSTQWtKUrA8n9zS/1uIDQ==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/types': 18.4.3
      chalk: 4.1.2
    dev: true

  /@commitlint/is-ignored@18.4.3:
    resolution: {integrity: sha512-ZseOY9UfuAI32h9w342Km4AIaTieeFskm2ZKdrG7r31+c6zGBzuny9KQhwI9puc0J3GkUquEgKJblCl7pMnjwg==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/types': 18.4.3
      semver: 7.5.4
    dev: true

  /@commitlint/lint@18.4.3:
    resolution: {integrity: sha512-18u3MRgEXNbnYkMOWoncvq6QB8/90m9TbERKgdPqVvS+zQ/MsuRhdvHYCIXGXZxUb0YI4DV2PC4bPneBV/fYuA==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/is-ignored': 18.4.3
      '@commitlint/parse': 18.4.3
      '@commitlint/rules': 18.4.3
      '@commitlint/types': 18.4.3
    dev: true

  /@commitlint/load@18.4.3(typescript@5.3.2):
    resolution: {integrity: sha512-v6j2WhvRQJrcJaj5D+EyES2WKTxPpxENmNpNG3Ww8MZGik3jWRXtph0QTzia5ZJyPh2ib5aC/6BIDymkUUM58Q==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/config-validator': 18.4.3
      '@commitlint/execute-rule': 18.4.3
      '@commitlint/resolve-extends': 18.4.3
      '@commitlint/types': 18.4.3
      '@types/node': 18.19.3
      chalk: 4.1.2
      cosmiconfig: 8.3.6(typescript@5.3.2)
      cosmiconfig-typescript-loader: 5.0.0(@types/node@18.19.3)(cosmiconfig@8.3.6)(typescript@5.3.2)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
      resolve-from: 5.0.0
    transitivePeerDependencies:
      - typescript
    dev: true

  /@commitlint/message@18.4.3:
    resolution: {integrity: sha512-ddJ7AztWUIoEMAXoewx45lKEYEOeOlBVWjk8hDMUGpprkuvWULpaXczqdjwVtjrKT3JhhN+gMs8pm5G3vB2how==}
    engines: {node: '>=v18'}
    dev: true

  /@commitlint/parse@18.4.3:
    resolution: {integrity: sha512-eoH7CXM9L+/Me96KVcfJ27EIIbA5P9sqw3DqjJhRYuhaULIsPHFs5S5GBDCqT0vKZQDx0DgxhMpW6AQbnKrFtA==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/types': 18.4.3
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0
    dev: true

  /@commitlint/read@18.4.3:
    resolution: {integrity: sha512-H4HGxaYA6OBCimZAtghL+B+SWu8ep4X7BwgmedmqWZRHxRLcX2q0bWBtUm5FsMbluxbOfrJwOs/Z0ah4roP/GQ==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/top-level': 18.4.3
      '@commitlint/types': 18.4.3
      fs-extra: 11.2.0
      git-raw-commits: 2.0.11
      minimist: 1.2.8
    dev: true

  /@commitlint/resolve-extends@18.4.3:
    resolution: {integrity: sha512-30sk04LZWf8+SDgJrbJCjM90gTg2LxsD9cykCFeFu+JFHvBFq5ugzp2eO/DJGylAdVaqxej3c7eTSE64hR/lnw==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/config-validator': 18.4.3
      '@commitlint/types': 18.4.3
      import-fresh: 3.3.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0
      resolve-global: 1.0.0
    dev: true

  /@commitlint/rules@18.4.3:
    resolution: {integrity: sha512-8KIeukDf45BiY+Lul1T0imSNXF0sMrlLG6JpLLKolkmYVQ6PxxoNOriwyZ3UTFFpaVbPy0rcITaV7U9JCAfDTA==}
    engines: {node: '>=v18'}
    dependencies:
      '@commitlint/ensure': 18.4.3
      '@commitlint/message': 18.4.3
      '@commitlint/to-lines': 18.4.3
      '@commitlint/types': 18.4.3
      execa: 5.1.1
    dev: true

  /@commitlint/to-lines@18.4.3:
    resolution: {integrity: sha512-fy1TAleik4Zfru1RJ8ZU6cOSvgSVhUellxd3WZV1D5RwHZETt1sZdcA4mQN2y3VcIZsUNKkW0Mq8CM9/L9harQ==}
    engines: {node: '>=v18'}
    dev: true

  /@commitlint/top-level@18.4.3:
    resolution: {integrity: sha512-E6fJPBLPFL5R8+XUNSYkj4HekIOuGMyJo3mIx2PkYc3clel+pcWQ7TConqXxNWW4x1ugigiIY2RGot55qUq1hw==}
    engines: {node: '>=v18'}
    dependencies:
      find-up: 5.0.0
    dev: true

  /@commitlint/types@18.4.3:
    resolution: {integrity: sha512-cvzx+vtY/I2hVBZHCLrpoh+sA0hfuzHwDc+BAFPimYLjJkpHnghQM+z8W/KyLGkygJh3BtI3xXXq+dKjnSWEmA==}
    engines: {node: '>=v18'}
    dependencies:
      chalk: 4.1.2
    dev: true

  /@ctrl/tinycolor@3.6.1:
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}
    dev: false

  /@dnd-kit/accessibility@3.1.0(react@18.2.0):
    resolution: {integrity: sha512-ea7IkhKvlJUv9iSHJOnxinBcoOI3ppGnnL+VDJ75O45Nss6HtZd8IdN8touXPDtASfeI2T2LImb8VOZcL47wjQ==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /@dnd-kit/core@6.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-J3cQBClB4TVxwGo3KEjssGEXNJqGVWx17aRTZ1ob0FliR5IjYgTxl5YJbKTzA6IzrtelotH19v6y7uoIRUZPSg==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@dnd-kit/accessibility': 3.1.0(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tslib: 2.6.2
    dev: false

  /@dnd-kit/modifiers@7.0.0(@dnd-kit/core@6.1.0)(react@18.2.0):
    resolution: {integrity: sha512-BG/ETy3eBjFap7+zIti53f0PCLGDzNXyTmn6fSdrudORf+OH04MxrW4p5+mPu4mgMk9kM41iYONjc3DOUWTcfg==}
    peerDependencies:
      '@dnd-kit/core': ^6.1.0
      react: '>=16.8.0'
    dependencies:
      '@dnd-kit/core': 6.1.0(react-dom@18.2.0)(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /@dnd-kit/sortable@8.0.0(@dnd-kit/core@6.1.0)(react@18.2.0):
    resolution: {integrity: sha512-U3jk5ebVXe1Lr7c2wU7SBZjcWdQP+j7peHJfCspnA81enlu88Mgd7CC8Q+pub9ubP7eKVETzJW+IBAhsqbSu/g==}
    peerDependencies:
      '@dnd-kit/core': ^6.1.0
      react: '>=16.8.0'
    dependencies:
      '@dnd-kit/core': 6.1.0(react-dom@18.2.0)(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /@dnd-kit/utilities@3.2.2(react@18.2.0):
    resolution: {integrity: sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /@emotion/hash@0.8.0:
    resolution: {integrity: sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==}
    dev: false

  /@emotion/is-prop-valid@0.8.8:
    resolution: {integrity: sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA==}
    requiresBuild: true
    dependencies:
      '@emotion/memoize': 0.7.4
    dev: false
    optional: true

  /@emotion/memoize@0.7.4:
    resolution: {integrity: sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw==}
    requiresBuild: true
    dev: false
    optional: true

  /@emotion/unitless@0.7.5:
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}
    dev: false

  /@esbuild/android-arm64@0.19.7:
    resolution: {integrity: sha512-YEDcw5IT7hW3sFKZBkCAQaOCJQLONVcD4bOyTXMZz5fr66pTHnAet46XAtbXAkJRfIn2YVhdC6R9g4xa27jQ1w==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.19.7:
    resolution: {integrity: sha512-YGSPnndkcLo4PmVl2tKatEn+0mlVMr3yEpOOT0BeMria87PhvoJb5dg5f5Ft9fbCVgtAz4pWMzZVgSEGpDAlww==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.19.7:
    resolution: {integrity: sha512-jhINx8DEjz68cChFvM72YzrqfwJuFbfvSxZAk4bebpngGfNNRm+zRl4rtT9oAX6N9b6gBcFaJHFew5Blf6CvUw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.19.7:
    resolution: {integrity: sha512-dr81gbmWN//3ZnBIm6YNCl4p3pjnabg1/ZVOgz2fJoUO1a3mq9WQ/1iuEluMs7mCL+Zwv7AY5e3g1hjXqQZ9Iw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.19.7:
    resolution: {integrity: sha512-Lc0q5HouGlzQEwLkgEKnWcSazqr9l9OdV2HhVasWJzLKeOt0PLhHaUHuzb8s/UIya38DJDoUm74GToZ6Wc7NGQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.19.7:
    resolution: {integrity: sha512-+y2YsUr0CxDFF7GWiegWjGtTUF6gac2zFasfFkRJPkMAuMy9O7+2EH550VlqVdpEEchWMynkdhC9ZjtnMiHImQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.19.7:
    resolution: {integrity: sha512-CdXOxIbIzPJmJhrpmJTLx+o35NoiKBIgOvmvT+jeSadYiWJn0vFKsl+0bSG/5lwjNHoIDEyMYc/GAPR9jxusTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.19.7:
    resolution: {integrity: sha512-inHqdOVCkUhHNvuQPT1oCB7cWz9qQ/Cz46xmVe0b7UXcuIJU3166aqSunsqkgSGMtUCWOZw3+KMwI6otINuC9g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.19.7:
    resolution: {integrity: sha512-Y+SCmWxsJOdQtjcBxoacn/pGW9HDZpwsoof0ttL+2vGcHokFlfqV666JpfLCSP2xLxFpF1lj7T3Ox3sr95YXww==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.19.7:
    resolution: {integrity: sha512-2BbiL7nLS5ZO96bxTQkdO0euGZIUQEUXMTrqLxKUmk/Y5pmrWU84f+CMJpM8+EHaBPfFSPnomEaQiG/+Gmh61g==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.19.7:
    resolution: {integrity: sha512-BVFQla72KXv3yyTFCQXF7MORvpTo4uTA8FVFgmwVrqbB/4DsBFWilUm1i2Oq6zN36DOZKSVUTb16jbjedhfSHw==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.19.7:
    resolution: {integrity: sha512-DzAYckIaK+pS31Q/rGpvUKu7M+5/t+jI+cdleDgUwbU7KdG2eC3SUbZHlo6Q4P1CfVKZ1lUERRFP8+q0ob9i2w==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.19.7:
    resolution: {integrity: sha512-JQ1p0SmUteNdUaaiRtyS59GkkfTW0Edo+e0O2sihnY4FoZLz5glpWUQEKMSzMhA430ctkylkS7+vn8ziuhUugQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.19.7:
    resolution: {integrity: sha512-xGwVJ7eGhkprY/nB7L7MXysHduqjpzUl40+XoYDGC4UPLbnG+gsyS1wQPJ9lFPcxYAaDXbdRXd1ACs9AE9lxuw==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.19.7:
    resolution: {integrity: sha512-U8Rhki5PVU0L0nvk+E8FjkV8r4Lh4hVEb9duR6Zl21eIEYEwXz8RScj4LZWA2i3V70V4UHVgiqMpszXvG0Yqhg==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.19.7:
    resolution: {integrity: sha512-ZYZopyLhm4mcoZXjFt25itRlocKlcazDVkB4AhioiL9hOWhDldU9n38g62fhOI4Pth6vp+Mrd5rFKxD0/S+7aQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.19.7:
    resolution: {integrity: sha512-/yfjlsYmT1O3cum3J6cmGG16Fd5tqKMcg5D+sBYLaOQExheAJhqr8xOAEIuLo8JYkevmjM5zFD9rVs3VBcsjtQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.19.7:
    resolution: {integrity: sha512-MYDFyV0EW1cTP46IgUJ38OnEY5TaXxjoDmwiTXPjezahQgZd+j3T55Ht8/Q9YXBM0+T9HJygrSRGV5QNF/YVDQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.19.7:
    resolution: {integrity: sha512-JcPvgzf2NN/y6X3UUSqP6jSS06V0DZAV/8q0PjsZyGSXsIGcG110XsdmuWiHM+pno7/mJF6fjH5/vhUz/vA9fw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.19.7:
    resolution: {integrity: sha512-ZA0KSYti5w5toax5FpmfcAgu3ZNJxYSRm0AW/Dao5up0YV1hDVof1NvwLomjEN+3/GMtaWDI+CIyJOMTRSTdMw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.19.7:
    resolution: {integrity: sha512-CTOnijBKc5Jpk6/W9hQMMvJnsSYRYgveN6O75DTACCY18RA2nqka8dTZR+x/JqXCRiKk84+5+bRKXUSbbwsS0A==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.19.7:
    resolution: {integrity: sha512-gRaP2sk6hc98N734luX4VpF318l3w+ofrtTu9j5L8EQXF+FzQKV6alCOHMVoJJHvVK/mGbwBXfOL1HETQu9IGQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@eslint-community/eslint-utils@4.4.0(eslint@8.54.0):
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 8.54.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@eslint-community/regexpp@4.10.0:
    resolution: {integrity: sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@eslint/eslintrc@2.1.3:
    resolution: {integrity: sha512-yZzuIG+jnVu6hNSzFEN07e8BxF3uAzYtQb6uDkaYZLo6oYZDCq454c5kB8zxnzfCYyP4MIuyBn10L0DqwujTmA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4
      espree: 9.6.1
      globals: 13.23.0
      ignore: 5.3.0
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@eslint/js@8.54.0:
    resolution: {integrity: sha512-ut5V+D+fOoWPgGGNj83GGjnntO39xDy6DWxO0wb7Jp3DcMX0TfIqdzHF85VTQkerdyGmuuMD9AKAo5KiNlf/AQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /@formatjs/ecma402-abstract@1.18.0:
    resolution: {integrity: sha512-PEVLoa3zBevWSCZzPIM/lvPCi8P5l4G+NXQMc/CjEiaCWgyHieUoo0nM7Bs0n/NbuQ6JpXEolivQ9pKSBHaDlA==}
    dependencies:
      '@formatjs/intl-localematcher': 0.5.2
      tslib: 2.6.2
    dev: false

  /@formatjs/fast-memoize@2.2.0:
    resolution: {integrity: sha512-hnk/nY8FyrL5YxwP9e4r9dqeM6cAbo8PeU9UjyXojZMNvVad2Z06FAVHyR3Ecw6fza+0GH7vdJgiKIVXTMbSBA==}
    dependencies:
      tslib: 2.6.2
    dev: false

  /@formatjs/icu-messageformat-parser@2.7.3:
    resolution: {integrity: sha512-X/jy10V9S/vW+qlplqhMUxR8wErQ0mmIYSq4mrjpjDl9mbuGcCILcI1SUYkL5nlM4PJqpc0KOS0bFkkJNPxYRw==}
    dependencies:
      '@formatjs/ecma402-abstract': 1.18.0
      '@formatjs/icu-skeleton-parser': 1.7.0
      tslib: 2.6.2
    dev: false

  /@formatjs/icu-skeleton-parser@1.7.0:
    resolution: {integrity: sha512-Cfdo/fgbZzpN/jlN/ptQVe0lRHora+8ezrEeg2RfrNjyp+YStwBy7cqDY8k5/z2LzXg6O0AdzAV91XS0zIWv+A==}
    dependencies:
      '@formatjs/ecma402-abstract': 1.18.0
      tslib: 2.6.2
    dev: false

  /@formatjs/intl-localematcher@0.5.2:
    resolution: {integrity: sha512-txaaE2fiBMagLrR4jYhxzFO6wEdEG4TPMqrzBAcbr4HFUYzH/YC+lg6OIzKCHm8WgDdyQevxbAAV1OgcXctuGw==}
    dependencies:
      tslib: 2.6.2
    dev: false

  /@humanwhocodes/config-array@0.11.13:
    resolution: {integrity: sha512-JSBDMiDKSzQVngfRjOdFXgFfklaXI4K9nLF49Auh21lmBWRLIK3+xTErTWD4KU54pb6coM6ESE7Awz/FNU3zgQ==}
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': 2.0.1
      debug: 4.3.4
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/module-importer@1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}
    dev: true

  /@humanwhocodes/object-schema@2.0.1:
    resolution: {integrity: sha512-dvuCeX5fC9dXgJn9t+X5atfmgQAzUOWqS1254Gh0m6i8wKd10ebXkfNKiRK+1GWi/yTvvLDHpoxLr0xxxeslWw==}
    dev: true

  /@internationalized/date@3.5.1:
    resolution: {integrity: sha512-LUQIfwU9e+Fmutc/DpRTGXSdgYZLBegi4wygCWDSVmUdLTaMHsQyASDiJtREwanwKuQLq0hY76fCJ9J/9I2xOQ==}
    dependencies:
      '@swc/helpers': 0.5.3
    dev: false

  /@internationalized/message@3.1.1:
    resolution: {integrity: sha512-ZgHxf5HAPIaR0th+w0RUD62yF6vxitjlprSxmLJ1tam7FOekqRSDELMg4Cr/DdszG5YLsp5BG3FgHgqquQZbqw==}
    dependencies:
      '@swc/helpers': 0.5.3
      intl-messageformat: 10.5.8
    dev: false

  /@internationalized/number@3.5.0:
    resolution: {integrity: sha512-ZY1BW8HT9WKYvaubbuqXbbDdHhOUMfE2zHHFJeTppid0S+pc8HtdIxFxaYMsGjCb4UsF+MEJ4n2TfU7iHnUK8w==}
    dependencies:
      '@swc/helpers': 0.5.3
    dev: false

  /@internationalized/string@3.2.0:
    resolution: {integrity: sha512-Xx3Sy3f2c9ctT+vh8c7euEaEHQZltp0euZ3Hy4UfT3E13r6lxpUS3kgKyumEjboJZSnaZv7JhqWz3D75v+IxQg==}
    dependencies:
      '@swc/helpers': 0.5.3
    dev: false

  /@isaacs/cliui@8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi@7.0.0
    dev: false

  /@jridgewell/gen-mapping@0.3.3:
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.20

  /@jridgewell/resolve-uri@3.1.1:
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array@1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/source-map@0.3.5:
    resolution: {integrity: sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20

  /@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  /@jridgewell/trace-mapping@0.3.20:
    resolution: {integrity: sha512-R8LcPeWZol2zR8mmH3JeKQ6QRCFb7XgUhV9ZlGhHLGyg4wpPiPZNQOOWhFZhxKw8u//yTbNGI42Bx/3paXEQ+Q==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15

  /@ljharb/resumer@0.0.1:
    resolution: {integrity: sha512-skQiAOrCfO7vRTq53cxznMpks7wS1va95UCidALlOVWqvBAzwPVErwizDwoMqNVMEn1mDq0utxZd02eIrvF1lw==}
    engines: {node: '>= 0.4'}
    dependencies:
      '@ljharb/through': 2.3.11
    dev: false

  /@ljharb/through@2.3.11:
    resolution: {integrity: sha512-ccfcIDlogiXNq5KcbAwbaO7lMh3Tm1i3khMPYpxlK8hH/W53zN81KM9coerRLOnTGu3nfXIniAmQbRI9OxbC0w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
    dev: false

  /@nextui-org/accordion@2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-WzD7sscL+4K0TFyUutTn1AhU0wcS68TqNCTNv7KgON6ODdwieydilMxAyXvwo3RgXeWG+8BbdxJC/6W+/iLBTg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/divider': 2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-accordion': 2.0.2(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/button': 3.9.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tree': 3.7.5(react@18.2.0)
      '@react-types/accordion': 3.0.0-alpha.17(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/aria-utils@2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-4M4jeJ/ghGaia9064yS+mEZ3sFPH80onmjNGWJZkkZDmUV4R88lNkqe/XYBK1tbxfl4Kxa8jc/ALsZkUkkvR5w==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.10
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - tailwind-variants
    dev: false

  /@nextui-org/autocomplete@2.0.9(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-ViPXrZnP35k7LF+TBA4w8nqu0OEj9p1z9Rt7rwrACmY2VmDGY6h6a6nDCMjhuTVXptftRvzxfIPsIyzBYqxb0g==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/button': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/input': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/listbox': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/popover': 2.1.14(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/scroll-shadow': 2.1.12(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/spinner': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@react-aria/combobox': 3.8.1(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/combobox': 3.8.1(react@18.2.0)
      '@react-types/combobox': 3.10.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/avatar@2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-3QUn8v61iNvAYogUbEDVnhDjBK6WBxxFYLp95a0H52zN0p2LHXe+UNwdGZYFo5QNWx6CHGH3vh2AHlLLy3WFSQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-image': 2.0.4(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/badge@2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-FA3XgqEbyKWepMXqMZg7D+1IRf7flrb2LzFvTbkmsbvWQ4yYz1LqJXZ/HDmoCydvh2pOnc+1zPK3BpB7vGrrwA==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/breadcrumbs@2.0.4(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-SAE0+QRgA7vxUHPL65TKz3MRj7u2mbSwk8Eifkwo6hPcF0d34zv2QDupTGyphIjoGCSrQHFIq/CPAkXyaOXZxw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/breadcrumbs': 3.5.9(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/breadcrumbs': 3.7.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/button@2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-mDrSII1oneY4omwDdxUhl5oLa3AhoWCchwV/jt7egunnAFie32HbTqfFYGpLGiJw3JMMh3WDUthrI1islVTRKA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/ripple': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/spinner': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@react-aria/button': 3.9.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/card@2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-16uAS0i6+EO+u8aqtmaCXatjovsyuTq51JwCLBlB67OldfgXoYcYl3GaE2VoZdEwxVu1G/qypDfXv29k46nZuA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/ripple': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@react-aria/button': 3.9.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/checkbox@2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-X6WkwPbZlDvioEcXF6HhKH21wD6OK+3+FSroKkzMPQLJrj2KYUIYGbiuw9rT9aCtdjbT+6HUCv+FA8/cBQr7cA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/checkbox': 3.13.0(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/checkbox': 3.6.1(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/chip@2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-hfVSaq5JWzGn97s3K2Ac/xOopHWelaUW3eus0O0wns/6+NCI0QUjgwNt2bAQSNvnE6vjvYLJTqGG/jFHyFJjOg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/code@2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-Kw/uOQtdytRWY99zMQuGHqMAAGXWBAxHlyMMge1OCckpadCDfX6plPjqoS18SGM0orJ4fox+a1FM8VhnRQ2kQw==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/divider@2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-yEvHqYlhNBwmF68pfjJKdzC8gVQtL+txxD5COBGF9uFyfxA5hVw2D6GmYgOH514bxrFBuWOLcQX6gyljgcN3bA==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.10
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/dropdown@2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-3KINNvC7Cz+deQltCM8gaB7iJCfU4Qsp1fwnoy1wUEjeZhEtPOPR59oTyqT+gPaPIisP1+LLOfcqRl4jNQoVXw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/menu': 2.0.17(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/popover': 2.1.14(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/menu': 3.12.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/menu': 3.6.0(react@18.2.0)
      '@react-types/menu': 3.9.6(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/framer-transitions@2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-UlWMCAFdrq8wKrYFGwc+O4kFhKCkL4L9ZadBkP0PqjmfyAC2gA3ygRbNqtKhFMWeKbBAiC8qQ9aTBEA/+0r/EA==}
    peerDependencies:
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - tailwind-variants
    dev: false

  /@nextui-org/image@2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-bps5D5ki7PoLldb8wcJEf6C4EUFZm3PocLytNaGa7dNxFfaCOD78So+kq+K+0IRusK3yn94K8r31qMvpI3Gg2Q==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-image': 2.0.4(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/input@2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-nUTlAvsXj5t88ycvQdICxf78/pko6Wznx2OomvYjb3E45eb77twQcWUDhydkJCWIh3b4AhGHSMM6GYxwWUgMDA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/textfield': 3.14.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/textfield': 3.9.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-textarea-autosize: 8.5.3(@types/react@18.2.38)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@nextui-org/kbd@2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-cYwbEjp/+/tjtOdmiRy2UHjfBhP3bqd5e+JFTa5sY1HotckUZrCintATyBcg9bPa3iSPUI44M6Cb9e0oAUUeMA==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/link@2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-X8zX3U5MWfiStOCd45oIZ2YKZG0GoUio6PcMFYjpOPsEG7wV58CuhUSxpyx3QTF8JavVSO/p/cl4Pc9pukVDUg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-link': 2.0.15(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/link': 3.6.3(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/link': 3.5.2(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/listbox@2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-5PmUCoHFgAr+1nAU3IlqPFTgyHo7zsTcNeja4wcErD/KseCF2h7Uk5OqUX5hQDN9B9fZuGjPrkG4yoK/6pqcUQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/divider': 2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@nextui-org/use-is-mobile': 2.0.6(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/listbox': 3.11.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-types/menu': 3.9.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/menu@2.0.17(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-qr/BPDbBvg5tpAZZLkLx8eNnvYwJYM3Q72fmRYbzwmG3upNtdjln0QYxSwPXUz7RYqTKEFWc9JPxq2pgPM15Wg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/divider': 2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@nextui-org/use-is-mobile': 2.0.6(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/menu': 3.12.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/menu': 3.6.0(react@18.2.0)
      '@react-stately/tree': 3.7.5(react@18.2.0)
      '@react-types/menu': 3.9.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/modal@2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-unfP0EMF3FDg5CkRqou03s4/BopWbaBTeVIMZeA2A1WF5teHUOmpLdp44Z1KOoWB1RVMDVd4JeoauNHNhJMp0g==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@nextui-org/use-aria-modal-overlay': 2.0.6(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/use-disclosure': 2.0.6(react@18.2.0)
      '@react-aria/dialog': 3.5.9(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.7(@types/react@18.2.38)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/navbar@2.0.27(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-iP4Pn4ItQkAW1nbu1Jmrh5l9pMVG43lDxq9rbx6DbLjLnnZOOrE6fURb8uN5NVy3ooV5dF02zKAoxlkE5fN/xw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-toggle-button': 2.0.6(react@18.2.0)
      '@nextui-org/use-scroll-position': 2.0.4(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.7(@types/react@18.2.38)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/pagination@2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OVpkpXqUKRuMRIcYESBAL95d3pqZ17SKAyNINMiJ/DwWnrzJu/LXGmFwTuYRoBdqHFlm7guGqZbHmAkcS/Fgow==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@nextui-org/use-pagination': 2.0.4(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.0.10
    dev: false

  /@nextui-org/popover@2.1.14(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-fqqktFQ/chIBS9Y3MghL6KX6qAy3hodtXUDchnxLa1GL+oi6TCBLUjo+wgI5EMJrTTbqo/eFLui/Ks00JfCj+A==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/button': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@react-aria/dialog': 3.5.9(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.7(@types/react@18.2.38)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/progress@2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-RPVsFCF8COFClS/8PqEepzryhDFtIcJGQLu/P+qAr7jIDlXizXaBDrp0X34GVtQsapNeE9ExxX9Kt+QIspuHHQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-is-mounted': 2.0.4(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/progress': 3.4.9(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/progress': 3.5.1(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/radio@2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-vRX0ppM5Tlzu0HoqTG6LdmQnMjk8RRl66BH1+QaosvZRXA1iIdA3BduqQYqn5ZZHBBlJ2u9QzaD3lTAlWIHvNg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/radio': 3.10.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/radio': 3.10.1(react@18.2.0)
      '@react-types/radio': 3.7.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/react-rsc-utils@2.0.10:
    resolution: {integrity: sha512-LNePDEThUF9PAbJW4T8k7EgSfqwlvGku5fIqJ1IA9+OpVy5LqhrUQehjvgXe63N1RupC7Pt+XvaaxkGu9U2FiQ==}
    dev: false

  /@nextui-org/react-utils@2.0.10(react@18.2.0):
    resolution: {integrity: sha512-bcA+k7ZdcgcK+r/8nrCtbdgHo0SD6jicbazWIokknFwjb97JQ7ooaMwxnLt5E5sswCAv0XeLwybOmrgm7JA5TA==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.10
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/react@2.2.9(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)(tailwindcss@3.3.5):
    resolution: {integrity: sha512-QHkUQTxI9sYoVjrvTpYm5K68pMDRqD13+DVzdsrkJuETGhbvE2c2CCGc4on9EwXC3JsOxuP/OyqaAmOIuHhYkA==}
    peerDependencies:
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/accordion': 2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/autocomplete': 2.0.9(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/avatar': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/badge': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/breadcrumbs': 2.0.4(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/button': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/card': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/checkbox': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/chip': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/code': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/divider': 2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/dropdown': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/image': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/input': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/kbd': 2.0.25(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/link': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/listbox': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/menu': 2.0.17(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/modal': 2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/navbar': 2.0.27(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/pagination': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/popover': 2.1.14(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/progress': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/radio': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/ripple': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/scroll-shadow': 2.1.12(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/select': 2.1.20(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/skeleton': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/slider': 2.2.5(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/snippet': 2.0.30(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/spacer': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/spinner': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/switch': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/table': 2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/tabs': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/tooltip': 2.0.29(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/user': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
      - tailwindcss
    dev: false

  /@nextui-org/ripple@2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PCvAk9ErhmPX46VRmhsg8yMxw3Qd9LY7BDkRRfIF8KftgRDyOpG2vV8DxvSOxQu1/aqBWkkHNUuEjM/EvSEung==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/scroll-shadow@2.1.12(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-uxT8D+WCWeBy4xaFDfqVpBgjjHZUwydXsX5HhbzZCBir/1eRG5GMnUES3w98DSwcUVadG64gAVsyGW4HmSZw1Q==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-data-scroll-overflow': 2.1.2(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/select@2.1.20(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-GCO9uzyYnFIdJTqIe6aDe2NnYlclcdYfZnECFAze/R2MW0jpoysk5ysGBDjVDmZis6tLu+BOFXJbIlYEi+LoUQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/listbox': 2.1.16(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/popover': 2.1.14(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(@types/react@18.2.38)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/scroll-shadow': 2.1.12(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/spinner': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@nextui-org/use-aria-multiselect': 2.1.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - tailwind-variants
    dev: false

  /@nextui-org/shared-icons@2.0.6(react@18.2.0):
    resolution: {integrity: sha512-Mw5utPJAclFaeKAZowznEgabI5gdhXrW0iMaMA18Y4zcZRTidAc0WFeGYUlX876NxYLPc1Zk4bZUhQvMe+7uWg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/shared-utils@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-Ms7A6UCvo/SZt/9Nmb7cZwHe9fZFw+EPsieTnC1vtpvDNCasxrTB0hj9VWFoYfWOaCzzqxl1AL9maIz/gMvckQ==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/skeleton@2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-bsb+lYugSfQV3RHrEHLbHhkkeslaxybnnT4z485Y/GBYTENOiHIOnWFWntfxCbjZ6vCewGlfgnphj6zeqlk20g==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/slider@2.2.5(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-dC6HHMmtn2WvxDmbY/Dq51XJjQ7cAnjZsuYVIvhwIiCLDG8QnEIhmYN0DQp/6oeZsCHnyMHC4DmtgOiJL0eXrQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/tooltip': 2.0.29(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/slider': 3.7.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/slider': 3.5.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - framer-motion
      - tailwind-variants
    dev: false

  /@nextui-org/snippet@2.0.30(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-8hKxqKpbJIMqFVedzYj90T4td+TkWdOdyYD9+VjywMdezAjsWdr8tqQj7boaMFjVNVSG+Pnw55Pgg/vkpc21aw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/button': 2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/tooltip': 2.0.29(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/use-clipboard': 2.0.4(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/spacer@2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-bLnhPRnoyHQXhLneHjbRqZNxJWMFOBYOZkuX83uy59/FFUY07BcoNsb2s80tN3GoVxsaZ2jB6NxxVbaCJwoPog==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/spinner@2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-s/q2FmxGPNEqA0ifWfc7xgs5a5D9c3xKkxL3n7jDoRnWo0NPlRsa6QRJGiSL5dHNoUqspRf/lNw2V94Bxk86Pg==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/switch@2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-U7g68eReMSkgG0bBOSdzRLK+npv422YK6WYHpYOSkEBDqGwQ7LCeMRQreT/KxN0QFxIKmafebdLHAbuKc/X+5Q==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/switch': 3.6.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/system-rsc@2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-1QqZ+GM7Ii0rsfSHXS6BBjzKOoLIWwb72nm4h4WgjlMXbRKLZcCQasRHVe5HMSBMvN0JUo7qyGExchfDFl/Ubw==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      tailwind-variants: '>=0.1.13'
    dependencies:
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      clsx: 1.2.1
      react: 18.2.0
      tailwind-variants: 0.1.19(tailwindcss@3.3.5)
    dev: false

  /@nextui-org/system@2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-WFDq+Rx6D+gmK1YGEG2RBARPK9EOYonQDt5Tq2tUchzOOqj3kXXcM5Z0F3fudM59eIncLa/tX/ApJSTLry+hsw==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/system-rsc': 2.0.11(@nextui-org/theme@2.1.17)(react@18.2.0)(tailwind-variants@0.1.19)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - tailwind-variants
    dev: false

  /@nextui-org/table@2.0.28(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-qH/7jdV5+tiMDDvBfMrUZN4jamds0FsL5Ak+ighoKIUYRFTSXOroi+63ZzzAh/mZAsUALCPPcfbXt4r4aBFDzg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/checkbox': 2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-icons': 2.0.6(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/spacer': 2.0.24(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/table': 3.13.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/table': 3.11.4(react@18.2.0)
      '@react-stately/virtualizer': 3.6.6(react@18.2.0)
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/table': 3.9.2(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/tabs@2.0.26(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-GjERgBYUAY1KD4GqNVy0cRi6GyQnf62q0ddcN4je3sEM6rsq3PygEXhkN5pxxFPacoYM/UE6rBswHSKlbjJjgw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@nextui-org/use-is-mounted': 2.0.4(react@18.2.0)
      '@nextui-org/use-update-effect': 2.0.4(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/tabs': 3.8.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tabs': 3.6.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/tabs': 3.3.4(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.0.10
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/theme@2.1.17(tailwindcss@3.3.5):
    resolution: {integrity: sha512-/WeHcMrAcWPGsEVn9M9TnvxKkaYkCocBH9JrDYCEFQoJgleUzHd4nVk7MWtpSOYJXLUzUMY1M9AqAK3jBkw+5g==}
    peerDependencies:
      tailwindcss: '*'
    dependencies:
      color: 4.2.3
      color2k: 2.0.3
      deepmerge: 4.3.1
      flat: 5.0.2
      lodash.foreach: 4.5.0
      lodash.get: 4.4.2
      lodash.kebabcase: 4.1.1
      lodash.mapkeys: 4.6.0
      lodash.omit: 4.5.0
      tailwind-variants: 0.1.19(tailwindcss@3.3.5)
      tailwindcss: 3.3.5
    dev: false

  /@nextui-org/tooltip@2.0.29(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19):
    resolution: {integrity: sha512-LaFyS5bXhcZFXP9rnh6pTKsYX6siWjzEe5z72FIOyAV2yvv2yhkRiO/mEHKI8moo+/tScW/6muFXsvbEalPefg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/aria-utils': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/framer-transitions': 2.0.15(@nextui-org/theme@2.1.17)(framer-motion@10.16.16)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/tooltip': 3.7.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tooltip': 3.4.6(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/tooltip': 3.4.6(react@18.2.0)
      framer-motion: 10.16.16(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - tailwind-variants
    dev: false

  /@nextui-org/use-aria-accordion@2.0.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ebYr4CdvWifuTM/yyhQLKCa7aUqbVrWyR0SB6VNCGDID/kvRUW52puWnY9k24xdwY0cKbW3JRciKtQkrokRQwg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@react-aria/button': 3.9.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tree': 3.7.5(react@18.2.0)
      '@react-types/accordion': 3.0.0-alpha.17(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    transitivePeerDependencies:
      - react-dom
    dev: false

  /@nextui-org/use-aria-button@2.0.6(react@18.2.0):
    resolution: {integrity: sha512-38DZ3FK/oPZ3sppfM5EtgJ4DITOajNwSKkAMePBmuSZl+bsW7peP8g5JNd9uPOEz3edCOppT60AQSICsYiH3cg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-aria-link@2.0.15(react@18.2.0):
    resolution: {integrity: sha512-znzOeTZ10o3O5F2nihi8BR8rAhRHgrRWcEBovV7OqJeFzvTQwsHl9/xy45zBfwJQksBtfcBfQf+GEHXeDwfigA==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-aria-press': 2.0.1(react@18.2.0)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/link': 3.5.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-aria-modal-overlay@2.0.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-JfhXvH2RObWpHeLmxdIBDPF2SDzV4SqBvEh01yRvg/EuZ3HDRfCnTDh+5HD0ziUVdk/kWuy/hZLX59sMX7QHWA==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/use-aria-multiselect@2.1.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OM1lj2jdl0Q2Zme/ds6qyT4IIGsBJSGNjvkM6pEnpdyoej/HwTKsSEpEFTDGJ5t9J9DWWCEt3hz0uJxOPnZ66Q==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/listbox': 3.11.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/menu': 3.12.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-stately/menu': 3.6.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/select': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nextui-org/use-aria-press@2.0.1(react@18.2.0):
    resolution: {integrity: sha512-T3MjHH5TU9qnkf872GmhcfQK16ITMmMW9zir6xsSsz0w6ay9Y0XTSPrI2zRL6ociFyfJjP840XCLtSx6VBfEBQ==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-aria-toggle-button@2.0.6(react@18.2.0):
    resolution: {integrity: sha512-6Sjp7a0HQjmboLKNZu9AtZmyHz8+vhqcDwJDYTZjrrna0udxEXG+6C14YZzQxoJcvuaMimr5E8Aq0AxyRAr0MQ==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-aria-button': 2.0.6(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-callback-ref@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-GF50SzOFU/R0gQT1TmjbEUiS8CQ87qiV5Rp/TD5pqys1xprVgGLUUNQzlh+YDS2JHNu5FGlZc4sJKhtf2xF5aw==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-safe-layout-effect': 2.0.4(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-clipboard@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-rMcaX0QsolOJ1BQbp1T/FVsSPn2m0Ss4Z+bbdS7eM6EFKtJdVJWlpbrST0/kR2UcW1KWeK27NYmtNPF5+hgZMA==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/use-data-scroll-overflow@2.1.2(react@18.2.0):
    resolution: {integrity: sha512-3h9QX+dWkfqnqciQc2KeeR67e77hobjefNHGBTDuB4LhJSJ180ToZH09SQNHaUmKRLTU/RABjGWXxdbORI0r6g==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-disclosure@2.0.6(react@18.2.0):
    resolution: {integrity: sha512-pazzLsAGKjUD4cMVySTivItmIgpsfIf4baP/02K0Xc8tbFAH4K1n7cUnEEjs+MTXy1Bprvz3pfAHDGZRDI1yYg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-callback-ref': 2.0.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-image@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-tomOkrhlhTA45qA/MLh1YmiWVGgJ2KeM0qBSLP1ikVcppc/e9UtkIJjHIGdNCnHZTjoPEh53HzyJeUMlYUM9uw==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/use-safe-layout-effect': 2.0.4(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-is-mobile@2.0.6(react@18.2.0):
    resolution: {integrity: sha512-HeglWUoq6Ln8P5n6s1SZvBRatLYMKsiXQM7Mk2l+6jFByzZh3VWtZ05xmuX8te/1rGmeUxjeXtW6x+F7/f/JoA==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-is-mounted@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-NSQwQjg8+k02GVov9cDwtAdop1Cr90eDgB0MAdvu7QCMgfBZjy88IdQnx3Yo7bG4wP45xC0vLjqDBanaK+11hw==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/use-pagination@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-EETHzhh+LW8u2bm93LkUABbu0pIoWBCeY8hmvgjhhNMkILuwZNGYnp9tdF2rcS2P4KDlHQkIQcoiOGrGMqBUaQ==}
    peerDependencies:
      react: '>=18'
    dependencies:
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      react: 18.2.0
    dev: false

  /@nextui-org/use-safe-layout-effect@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-K7ppEhTfzdVOzbgKaNFEBi4HwRfQ8j+kRBQqsU5yo8bSM+5uv8OUy/mjpEf4i02PUDIBmsgJC4En9S537DXrwg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/use-scroll-position@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-5ugiHqQ1OptBmujOsJGigbUt/rQ826+8RKYSpBp1uax1eF7TlpigXt6mS1PDsJIyEauHi8rjH5B3weOn1//tug==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/use-update-effect@2.0.4(react@18.2.0):
    resolution: {integrity: sha512-HycSl9Eopmy3ypZQxXVR7eov2D0q0zcgldgbIPvlKExbj8OInaIImc9zLMI9oQgfmg/YdvLeFSrfwc5BPrIvlg==}
    peerDependencies:
      react: '>=18'
    dependencies:
      react: 18.2.0
    dev: false

  /@nextui-org/user@2.0.25(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ykh65O0ynJBlstlZowM8KrX6zv/VLfDgYX892Dk0goLwU8gcSILPZE7yGIBZi1XsNN7mE3dmTp/APLFDbkzzXw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      '@nextui-org/avatar': 2.0.24(@nextui-org/system@2.0.15)(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)
      '@nextui-org/react-utils': 2.0.10(react@18.2.0)
      '@nextui-org/shared-utils': 2.0.4(react@18.2.0)
      '@nextui-org/system': 2.0.15(@nextui-org/theme@2.1.17)(react-dom@18.2.0)(react@18.2.0)(tailwind-variants@0.1.19)
      '@nextui-org/theme': 2.1.17(tailwindcss@3.3.5)
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0

  /@pkgjs/parseargs@0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}
    requiresBuild: true
    dev: false
    optional: true

  /@pkgr/utils@2.4.2:
    resolution: {integrity: sha512-POgTXhjrTfbTV63DiFXav4lBHiICLKKwDeaKn9Nphwj7WH6m0hMMCaJkMyRWjgtPFyRKRVoMXXjczsTQRDEhYw==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}
    dependencies:
      cross-spawn: 7.0.3
      fast-glob: 3.3.2
      is-glob: 4.0.3
      open: 9.1.0
      picocolors: 1.0.0
      tslib: 2.6.2
    dev: true

  /@rc-component/async-validator@5.0.4:
    resolution: {integrity: sha512-qgGdcVIF604M9EqjNF0hbUTz42bz/RDtxWdWuU5EQe3hi7M8ob54B6B35rOsvX5eSvIHIzT9iH1R3n+hk3CGfg==}
    engines: {node: '>=14.x'}
    dependencies:
      '@babel/runtime': 7.26.0
    dev: false

  /@rc-component/color-picker@1.4.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-vh5EWqnsayZa/JwUznqDaPJz39jznx/YDbyBuVJntv735tKXKwEUZZb2jYEldOg+NKWZwtALjGMrNeGBmqFoEw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@ctrl/tinycolor': 3.6.1
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/color-picker@2.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-WcZYwAThV/b2GISQ8F+7650r5ZZJ043E57aVBFkQ+kSY4C6wdofXgB0hBx+GPGpIU0Z81eETNoDUJMr7oy/P8Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/fast-color': 2.0.6
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/context@1.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/mini-decimal@1.1.0:
    resolution: {integrity: sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ==}
    engines: {node: '>=8.x'}
    dependencies:
      '@babel/runtime': 7.26.0
    dev: false

  /@rc-component/mutate-observer@1.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/portal@1.1.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/qrcode@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-L+rZ4HXP2sJ1gHMGHjsg9jlYBX/SLN2D6OxP9Zn3qgtpMWtO2vUfxVFwiogHpAIqs54FnALxraUy/BCO1yRIgg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/tour@1.10.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-voV0BKaTJbewB9LLgAHQ7tAGG7rgDkKQkZo82xw2gIk542hY+o7zwoqdN16oHhIKk7eG/xi+mdXrONT62Dt57A==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/tour@1.15.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Tr2t7J1DKZUpfJuDZWHxyxWpfmj8EZrqSgyMZ+BCdvKZ6r1UDsfU46M/iWAAFBy961Ssfom2kv5f3UcjIL2CmQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/trigger@1.18.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-jRLYgFgjLEPq3MvS87fIhcfuywFSRDaDrYw1FLku7Cm4esszvzTbA0JBsyacAyLrK9rF3TiHFcvoEDMzoD3CTA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/trigger@2.2.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-X1oFIpKoXAMXNDYCviOmTfuNuYxE4h5laBsyCqVAVMjNHxoF3/uiyA7XdegK1XbCvBbCZ6P6byWrEoDRpKL8+A==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/breadcrumbs@3.5.9(react@18.2.0):
    resolution: {integrity: sha512-asbXTL5NjeHl1+YIF0K70y8tNHk8Lb6VneYH8yOkpLO49ejyNDYBK0tp0jtI9IZAQiTa2qkhYq58c9LloTwebQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/link': 3.6.3(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/breadcrumbs': 3.7.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/button@3.9.1(react@18.2.0):
    resolution: {integrity: sha512-nAnLMUAnwIVcRkKzS1G2IU6LZSkIWPJGu9amz/g7Y02cGUwFp3lk5bEw2LdoaXiSDJNSX8g0SZFU8FROg57jfQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/checkbox@3.13.0(react@18.2.0):
    resolution: {integrity: sha512-eylJwtADIPKJ1Y5rITNJm/8JD8sXG2nhiZBIg1ko44Szxrpu+Le53NoGtg8nlrfh9vbUrXVvuFtf2jxbPXR5Jw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/form': 3.0.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/toggle': 3.10.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/checkbox': 3.6.1(react@18.2.0)
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/combobox@3.8.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-0Zsy91WC2uhnIjtProL1E5qRjBtRVdsNgpr8T9QCQht4i2sHd8L/srrOx7b6vRIngUMZq7GofOpQcKVdxx4kEA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/listbox': 3.11.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/live-announcer': 3.3.1
      '@react-aria/menu': 3.12.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/textfield': 3.14.0(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/combobox': 3.8.1(react@18.2.0)
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/combobox': 3.10.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/dialog@3.5.9(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Eg5pFJN3b5NitKL60nf30iPpQGCyOcU4YakUVn5+GWKLBlm8ryE8jyoIIO0e0LCM65K+fL+gGHGK01GCZyKrpQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/dialog': 3.5.7(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/focus@3.16.0(react@18.2.0):
    resolution: {integrity: sha512-GP6EYI07E8NKQQcXHjpIocEU0vh0oi0Vcsd+/71fKS0NnTR0TUOEeil0JuuQ9ymkmPDTu51Aaaa4FxVsuN/23A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      clsx: 2.1.0
      react: 18.2.0
    dev: false

  /@react-aria/form@3.0.1(react@18.2.0):
    resolution: {integrity: sha512-6586oODMDR4/ciGRwXjpvEAg7tWGSDrXE//waK0n5e5sMuzlPOo1DHc5SpPTvz0XdJsu6VDt2rHdVWVIC9LEyw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/grid@3.8.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-JlQDkdm5heG1FfRyy5KnB8b6s/hRqSI6Xt2xN2AccLX5kcbfFr2/d5KVxyf6ahfa4Gfd46alN6477ju5eTWJew==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/live-announcer': 3.3.1
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/grid': 3.8.4(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-stately/virtualizer': 3.6.6(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/i18n@3.10.0(react@18.2.0):
    resolution: {integrity: sha512-sviD5Y1pLPG49HHRmVjR+5nONrp0HK219+nu9Y7cDfUhXu2EjyhMS9t/n9/VZ69hHChZ2PnHYLEE2visu9CuCg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@internationalized/date': 3.5.1
      '@internationalized/message': 3.1.1
      '@internationalized/number': 3.5.0
      '@internationalized/string': 3.2.0
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/interactions@3.20.1(react@18.2.0):
    resolution: {integrity: sha512-PLNBr87+SzRhe9PvvF9qvzYeP4ofTwfKSorwmO+hjr3qoczrSXf4LRQlb27wB6hF10C7ZE/XVbUI1lj4QQrZ/g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/label@3.7.4(react@18.2.0):
    resolution: {integrity: sha512-3Y0yyrqpLzZdzHw+TOyzwuyx5wa2ujU5DGfKuL5GFnU9Ii4DtdwBGSYS7Yu7qadU+eQmG4OGhAgFVswbIgIwJw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/link@3.6.3(react@18.2.0):
    resolution: {integrity: sha512-8kPWc4u/lDow3Ll0LDxeMgaxt9Y3sl8UldKLGli8tzRSltYFugNh/n+i9sCnmo4Qv9Tp9kYv+yxBK50Uk9sINw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/link': 3.5.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/listbox@3.11.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PBrnldmyEYUUJvfDeljW8ITvZyBTfGpLNf0b5kfBPK3TDgRH4niEH2vYEcaZvSqb0FrpdvcunuTRXcOpfb+gCQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-types/listbox': 3.4.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/live-announcer@3.3.1:
    resolution: {integrity: sha512-hsc77U7S16trM86d+peqJCOCQ7/smO1cybgdpOuzXyiwcHQw8RQ4GrXrS37P4Ux/44E9nMZkOwATQRT2aK8+Ew==}
    dependencies:
      '@swc/helpers': 0.5.3
    dev: false

  /@react-aria/menu@3.12.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Nsujv3b61WR0gybDKnBjAeyxDVJOfPLMggRUf9SQDfPWnrPXEsAFxaPaVcAkzlfI4HiQs1IxNwsKFNpc3PPZTQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/overlays': 3.20.0(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/menu': 3.6.0(react@18.2.0)
      '@react-stately/tree': 3.7.5(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/menu': 3.9.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/overlays@3.20.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-2m7MpRJL5UucbEuu08lMHsiFJoDowkJV4JAIFBZYK1NzVH0vF/A+w9HRNM7jRwx2DUxE+iIsZnl8yKV/7KY8OQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/button': 3.9.1(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/progress@3.4.9(react@18.2.0):
    resolution: {integrity: sha512-CME1ZLsJHOmSgK8IAPOC/+vYO5Oc614mkEw5MluT/yclw5rMyjAkK1XsHLjEXy81uwPeiRyoQQIMPKG2/sMxFQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/progress': 3.5.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/radio@3.10.0(react@18.2.0):
    resolution: {integrity: sha512-6NaKzdGymdcVWLYgHT0cHsVmNzPOp89o8r41w29OPBQWu8w2c9mxg4366OiIZn/uXIBS4abhQ4nL4toBRLgBrg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/form': 3.0.1(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/radio': 3.10.1(react@18.2.0)
      '@react-types/radio': 3.7.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/selection@3.17.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xl2sgeGH61ngQeE05WOWWPVpGRTPMjQEFmsAWEprArFi4Z7ihSZgpGX22l1w7uSmtXM/eN/v0W8hUYUju5iXlQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/slider@3.7.4(react@18.2.0):
    resolution: {integrity: sha512-OFJWeGSL2duVDFs/kcjlWsY6bqCVKZgM0aFn2QN4wmID+vfBvBnqGHAgWv3BCePTAPS3+GBjMN002TrftorjwQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/slider': 3.5.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/slider': 3.7.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/ssr@3.9.1(react@18.2.0):
    resolution: {integrity: sha512-NqzkLFP8ZVI4GSorS0AYljC13QW2sc8bDqJOkBvkAt3M8gbcAXJWVRGtZBCRscki9RZF+rNlnPdg0G0jYkhJcg==}
    engines: {node: '>= 12'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/switch@3.6.0(react@18.2.0):
    resolution: {integrity: sha512-YNWc5fGLNXE4XlmDAKyqAdllRiClGR7ki4KGFY7nL+xR5jxzjCGU3S3ToMK5Op3QSMGZLxY/aYmC4O+MvcoADQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/toggle': 3.10.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/switch': 3.5.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/table@3.13.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AzmETpyxwNqISTzwHJPs85x9gujG40IIsSOBUdp49oKhB85RbPLvMwhadp4wCVAoHw3erOC/TJxHtVc7o2K1LA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/grid': 3.8.6(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/live-announcer': 3.3.1
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-aria/visually-hidden': 3.8.8(react@18.2.0)
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/flags': 3.0.0
      '@react-stately/table': 3.11.4(react@18.2.0)
      '@react-stately/virtualizer': 3.6.6(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/table': 3.9.2(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/tabs@3.8.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Plw0K/5Qv35vYq7pHZFfQB2BF5OClFx4Abzo9hLVx4oMy3qb7i5lxmLBVbt81yPX/MdjYeP4zO1EHGBl4zMRhA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/i18n': 3.10.0(react@18.2.0)
      '@react-aria/selection': 3.17.3(react-dom@18.2.0)(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tabs': 3.6.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/tabs': 3.3.4(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@react-aria/textfield@3.14.0(react@18.2.0):
    resolution: {integrity: sha512-LtHFcPK/N9m3KWSRM5KdmlIk7cUEk0OF+uBUrfKsGGc1bJKVToimdW7jQusChHmHhslHUR7WQ4KDjXyFjoLXOw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/form': 3.0.1(react@18.2.0)
      '@react-aria/label': 3.7.4(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/textfield': 3.9.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/toggle@3.10.0(react@18.2.0):
    resolution: {integrity: sha512-6cUf4V9TuG2J7AvXUdU/GspEPFCubUOID3mrselSe563RViy+mMZk0vUEOdyoNanDcEXl58W4dE3SGWxFn71vg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/toggle': 3.7.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/tooltip@3.7.0(react@18.2.0):
    resolution: {integrity: sha512-+u9Sftkfe09IDyPEnbbreFKS50vh9X/WTa7n1u2y3PenI9VreLpUR6czyzda4BlvQ95e9jQz1cVxUjxTNaZmBw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/focus': 3.16.0(react@18.2.0)
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-stately/tooltip': 3.4.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/tooltip': 3.4.6(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-aria/utils@3.23.0(react@18.2.0):
    resolution: {integrity: sha512-fJA63/VU4iQNT8WUvrmll3kvToqMurD69CcgVmbQ56V7ZbvlzFi44E7BpnoaofScYLLtFWRjVdaHsohT6O/big==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/ssr': 3.9.1(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      clsx: 2.1.0
      react: 18.2.0
    dev: false

  /@react-aria/visually-hidden@3.8.8(react@18.2.0):
    resolution: {integrity: sha512-Cn2PYKD4ijGDtF0+dvsh8qa4y7KTNAlkTG6h20r8Q+6UTyRNmtE2/26QEaApRF8CBiNy9/BZC/ZC4FK2OjvCoA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/interactions': 3.20.1(react@18.2.0)
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/checkbox@3.6.1(react@18.2.0):
    resolution: {integrity: sha512-rOjFeVBy32edYwhKiHj3ZLdLeO+xZ2fnBwxnOBjcygnw4Neygm8FJH/dB1J0hdYYR349yby86ED2x0wRc84zPw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/collections@3.10.4(react@18.2.0):
    resolution: {integrity: sha512-OHhCrItGt4zB2bSrgObRo0H2SC7QlkH8ReGxo+NVIWchXRLRoiWBP7S+IwleewEo5gOqDVPY3hqA9n4iiI8twg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/combobox@3.8.1(react@18.2.0):
    resolution: {integrity: sha512-FaWkqTXQdWg7ptaeU4iPcqF/kxbRg2ZNUcvW/hiL/enciV5tRCsddvfNqvDvy1L30z9AUwlp9MWqzm/DhBITCw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-stately/select': 3.6.1(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/combobox': 3.10.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/flags@3.0.0:
    resolution: {integrity: sha512-e3i2ItHbIa0eEwmSXAnPdD7K8syW76JjGe8ENxwFJPW/H1Pu9RJfjkCb/Mq0WSPN/TpxBb54+I9TgrGhbCoZ9w==}
    dependencies:
      '@swc/helpers': 0.4.36
    dev: false

  /@react-stately/form@3.0.0(react@18.2.0):
    resolution: {integrity: sha512-C8wkfFmtx1escizibhdka5JvTy9/Vp173CS9cakjvWTmnjYYC1nOlzwp7BsYWTgerCFbRY/BU/Cf/bJDxPiUKQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/grid@3.8.4(react@18.2.0):
    resolution: {integrity: sha512-rwqV1K4lVhaiaqJkt4TfYqdJoVIyqvSm98rKAYfCNzrKcivVpoiCMJ2EMt6WlYCjDVBdEOQ7fMV1I60IV0pntA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/list@3.10.2(react@18.2.0):
    resolution: {integrity: sha512-INt+zofkIg2KN8B95xPi9pJG7ZFWAm30oIm/lCPBqM3K1Nm03/QaAbiQj2QeJcOsG3lb7oqI6D6iwTolwJkjIQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/menu@3.6.0(react@18.2.0):
    resolution: {integrity: sha512-OB6CjNyfOkAuirqx1oTL8z8epS9WDzLyrXjmRnxdiCU9EgRXLGAQNECuO7VIpl58oDry8tgRJiJ8fn8FivWSQA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/menu': 3.9.6(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/overlays@3.6.4(react@18.2.0):
    resolution: {integrity: sha512-tHEaoAGpE9dSnsskqLPVKum59yGteoSqsniTopodM+miQozbpPlSjdiQnzGLroy5Afx5OZYClE616muNHUILXA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/radio@3.10.1(react@18.2.0):
    resolution: {integrity: sha512-MsBYbcLCvjKsqTAKe43T681F2XwKMsS7PLG0eplZgWP9210AMY78GeY1XPYZKHPAau8XkbYiuJqbqTerIJ3DBw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/radio': 3.7.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/select@3.6.1(react@18.2.0):
    resolution: {integrity: sha512-e5ixtLiYLlFWM8z1msDqXWhflF9esIRfroptZsltMn1lt2iImUlDRlOTZlMtPQzUrDWoiHXRX88sSKUM/jXjQQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/form': 3.0.0(react@18.2.0)
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/select': 3.9.1(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/selection@3.14.2(react@18.2.0):
    resolution: {integrity: sha512-mL7OoiUgVWaaF7ks5XSxgbXeShijYmD4G3bkBHhqkpugU600QH6BM2hloCq8KOUupk1y8oTljPtF9EmCv375DA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/slider@3.5.0(react@18.2.0):
    resolution: {integrity: sha512-dOVpIxb7XKuiRxgpHt1bUSlsklciFki100tKIyBPR+Okar9iC/CwLYROYgVfLkGe77jEBNkor9tDLjDGEWcc1w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/slider': 3.7.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/table@3.11.4(react@18.2.0):
    resolution: {integrity: sha512-dWINJIEOKQl4qq3moq+S8xCD3m+yJqBj0dahr+rOkS+t2uqORwzsusTM35D2T/ZHZi49S2GpE7QuDa+edCynPw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/flags': 3.0.0
      '@react-stately/grid': 3.8.4(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/table': 3.9.2(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/tabs@3.6.3(react@18.2.0):
    resolution: {integrity: sha512-Nj+Gacwa2SIzYIvHW40GsyX4Q6c8kF7GOuXESeQswbCjnwqhrSbDBp+ngPcUPUJxqFh6JhDCVwAS3wMhUoyUwA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/list': 3.10.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@react-types/tabs': 3.3.4(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/toggle@3.7.0(react@18.2.0):
    resolution: {integrity: sha512-TRksHkCJk/Xogq4181g3CYgJf+EfsJCqX5UZDSw1Z1Kgpvonjmdf6FAfQfCh9QR2OuXUL6hOLUDVLte5OPI+5g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/checkbox': 3.6.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/tooltip@3.4.6(react@18.2.0):
    resolution: {integrity: sha512-uL93bmsXf+OOgpKLPEKfpDH4z+MK2CuqlqVxx7rshN0vjWOSoezE5nzwgee90+RpDrLNNNWTNa7n+NkDRpI1jA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/overlays': 3.6.4(react@18.2.0)
      '@react-types/tooltip': 3.4.6(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/tree@3.7.5(react@18.2.0):
    resolution: {integrity: sha512-xTJVwvhAeY0N5rui4N/TxN7f8hjXdqApDuGDxMZeFAWoQz8Abf7LFKBVQ3OkT6qVr7P+23dgoisUDBhD5a45Hg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-stately/collections': 3.10.4(react@18.2.0)
      '@react-stately/selection': 3.14.2(react@18.2.0)
      '@react-stately/utils': 3.9.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/utils@3.9.0(react@18.2.0):
    resolution: {integrity: sha512-yPKFY1F88HxuZ15BG2qwAYxtpE4HnIU0Ofi4CuBE0xC6I8mwo4OQjDzi+DZjxQngM9D6AeTTD6F1V8gkozA0Gw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-stately/virtualizer@3.6.6(react@18.2.0):
    resolution: {integrity: sha512-9hWvfITdE/028q4YFve6FxlmA3PdSMkUwpYA+vfaGCXI/4DFZIssBMspUeu4PTRJoV+k+m0z1wYHPmufrq6a3g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-aria/utils': 3.23.0(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      '@swc/helpers': 0.5.3
      react: 18.2.0
    dev: false

  /@react-types/accordion@3.0.0-alpha.17(react@18.2.0):
    resolution: {integrity: sha512-Wsp31bYRu9wy4zAAV2W8FLvVGFF3Vk/JKn2MxqhzaSHwHBw/dfgJTvRRUW+OmBgnqVN97ur893TP9A3odpoZEg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/breadcrumbs@3.7.2(react@18.2.0):
    resolution: {integrity: sha512-esl6RucDW2CNMsApJxNYfMtDaUcfLlwKMPH/loYsOBbKxGl2HsgVLMcdpjEkTRs2HCTNCbBXWpeU8AY77t+bsw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/link': 3.5.2(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/button@3.9.1(react@18.2.0):
    resolution: {integrity: sha512-bf9iTar3PtqnyV9rA+wyFyrskZKhwmOuOd/ifYIjPs56YNVXWH5Wfqj6Dx3xdFBgtKx8mEVQxVhoX+WkHX+rtw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/checkbox@3.6.0(react@18.2.0):
    resolution: {integrity: sha512-vgbuJzQpVCNT5AZWV0OozXCnihqrXxoZKfJFIw0xro47pT2sn3t5UC4RA9wfjDGMoK4frw1K/4HQLsQIOsPBkw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/combobox@3.10.0(react@18.2.0):
    resolution: {integrity: sha512-1IXSNS02TPbguyYopaW2snU6sZusbClHrEyVr4zPeexTV4kpUUBNXOzFQ+eSQRR0r2XW57Z0yRW4GJ6FGU0yCA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/dialog@3.5.7(react@18.2.0):
    resolution: {integrity: sha512-geYoqAyQaTLG43AaXdMUVqZXYgkSifrD9cF7lR2kPAT0uGFv0YREi6ieU+aui8XJ83EW0xcxP+EPWd2YkN4D4w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/grid@3.2.3(react@18.2.0):
    resolution: {integrity: sha512-GQM4RDmYhstcYZ0Odjq+xUwh1fhLmRebG6qMM8OXHTPQ77nhl3wc1UTGRhZm6mzEionplSRx4GCpEMEHMJIU0w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/link@3.5.2(react@18.2.0):
    resolution: {integrity: sha512-/s51/WejmpLiyxOgP89s4txgxYoGaPe8pVDItVo1h4+BhU1Puyvgv/Jx8t9dPvo6LUXbraaN+SgKk/QDxaiirw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/listbox@3.4.6(react@18.2.0):
    resolution: {integrity: sha512-XOQvrTqNh5WIPDvKiWiep8T07RAsMfjAXTjDbnjxVlKACUXkcwpts9kFaLnJ9LJRFt6DwItfP+WMkzvmx63/NQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/menu@3.9.6(react@18.2.0):
    resolution: {integrity: sha512-w/RbFInOf4nNayQDv5c2L8IMJbcFOkBhsT3xvvpTy+CHvJcQdjggwaV1sRiw7eF/PwB81k2CwigmidUzHJhKDg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/overlays@3.8.4(react@18.2.0):
    resolution: {integrity: sha512-pfgNlQnbF6RB/R2oSxyqAP3Uzz0xE/k5q4n5gUeCDNLjY5qxFHGE8xniZZ503nZYw6VBa9XMN1efDOKQyeiO0w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/progress@3.5.1(react@18.2.0):
    resolution: {integrity: sha512-CqsUjczUK/SfuFzDcajBBaXRTW0D3G9S/yqLDj9e8E0ii+lGDLt1PHj24t1J7E88U2rVYqmM9VL4NHTt8o3IYA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/radio@3.7.0(react@18.2.0):
    resolution: {integrity: sha512-EcwGAXzSHjSqpFZha7xn3IUrhPiJLj+0yb1Ip0qPmhWz0VVw2DwrkY7q/jfaKroVvQhTo2TbfGhcsAQrt0fRqg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/select@3.9.1(react@18.2.0):
    resolution: {integrity: sha512-EpKSxrnh8HdZvOF9dHQkjivAcdIp1K81FaxmvosH8Lygqh0iYXxAdZGtKLMyBoPI8YFhA+rotIzTcOqgCCnqWA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/shared@3.22.0(react@18.2.0):
    resolution: {integrity: sha512-yVOekZWbtSmmiThGEIARbBpnmUIuePFlLyctjvCbgJgGhz8JnEJOipLQ/a4anaWfzAgzSceQP8j/K+VOOePleA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /@react-types/slider@3.7.0(react@18.2.0):
    resolution: {integrity: sha512-uyQXUVFfqc9SPUW0LZLMan2n232F/OflRafiHXz9viLFa9tVOupVa7GhASRAoHojwkjoJ1LjFlPih7g5dOZ0/Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/switch@3.5.0(react@18.2.0):
    resolution: {integrity: sha512-/wNmUGjk69bP6t5k2QkAdrNN5Eb9Rz4dOyp0pCPmoeE+5haW6sV5NmtkvWX1NSc4DQz1xL/a5b+A0vxPCP22Jw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/table@3.9.2(react@18.2.0):
    resolution: {integrity: sha512-brw5JUANOzBa2rYNpN8AIl9nDZ9RwRZC6G/wTM/JhtirjC1S42oCtf8Ap5rWJBdmMG/5KOfcGNcAl/huyqb3gg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/grid': 3.2.3(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/tabs@3.3.4(react@18.2.0):
    resolution: {integrity: sha512-4mCTtFrwMRypyGTZCvNYVT9CkknexO/UYvqwDm2jMYb8JgjRvxnomu776Yh7uyiYKWyql2upm20jqasEOm620w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/textfield@3.9.0(react@18.2.0):
    resolution: {integrity: sha512-D/DiwzsfkwlAg3uv8hoIfwju+zhB/hWDEdTvxQbPkntDr0kmN/QfI17NMSzbOBCInC4ABX87ViXLGxr940ykGA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@react-types/tooltip@3.4.6(react@18.2.0):
    resolution: {integrity: sha512-RaZewdER7ZcsNL99RhVHs8kSLyzIBkwc0W6eFZrxST2MD9J5GzkVWRhIiqtFOd5U1aYnxdJ6woq72Ef+le6Vfw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
    dependencies:
      '@react-types/overlays': 3.8.4(react@18.2.0)
      '@react-types/shared': 3.22.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@reduxjs/toolkit@2.0.1(react-redux@8.1.3)(react@18.2.0):
    resolution: {integrity: sha512-fxIjrR9934cmS8YXIGd9e7s1XRsEU++aFc9DVNMFMRTM5Vtsg2DCRMj21eslGtDt43IUf9bJL3h5bwUlZleibA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18
      react-redux: ^7.2.1 || ^8.1.3 || ^9.0.0
    peerDependenciesMeta:
      react:
        optional: true
      react-redux:
        optional: true
    dependencies:
      immer: 10.0.3
      react: 18.2.0
      react-redux: 8.1.3(@types/react-dom@18.2.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)(redux@5.0.1)
      redux: 5.0.1
      redux-thunk: 3.1.0(redux@5.0.1)
      reselect: 5.1.0
    dev: false

  /@remix-run/router@1.12.0:
    resolution: {integrity: sha512-2hXv036Bux90e1GXTWSMfNzfDDK8LA8JYEWfyHxzvwdp6GyoWEovKc9cotb3KCKmkdwsIBuFGX7ScTWyiHv7Eg==}
    engines: {node: '>=14.0.0'}
    dev: false

  /@rollup/rollup-android-arm-eabi@4.5.1:
    resolution: {integrity: sha512-YaN43wTyEBaMqLDYeze+gQ4ZrW5RbTEGtT5o1GVDkhpdNcsLTnLRcLccvwy3E9wiDKWg9RIhuoy3JQKDRBfaZA==}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-android-arm64@4.5.1:
    resolution: {integrity: sha512-n1bX+LCGlQVuPlCofO0zOKe1b2XkFozAVRoczT+yxWZPGnkEAKTTYVOGZz8N4sKuBnKMxDbfhUsB1uwYdup/sw==}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-arm64@4.5.1:
    resolution: {integrity: sha512-QqJBumdvfBqBBmyGHlKxje+iowZwrHna7pokj/Go3dV1PJekSKfmjKrjKQ/e6ESTGhkfPNLq3VXdYLAc+UtAQw==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-x64@4.5.1:
    resolution: {integrity: sha512-RrkDNkR/P5AEQSPkxQPmd2ri8WTjSl0RYmuFOiEABkEY/FSg0a4riihWQGKDJ4LnV9gigWZlTMx2DtFGzUrYQw==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-gnueabihf@4.5.1:
    resolution: {integrity: sha512-ZFPxvUZmE+fkB/8D9y/SWl/XaDzNSaxd1TJUSE27XAKlRpQ2VNce/86bGd9mEUgL3qrvjJ9XTGwoX0BrJkYK/A==}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-gnu@4.5.1:
    resolution: {integrity: sha512-FEuAjzVIld5WVhu+M2OewLmjmbXWd3q7Zcx+Rwy4QObQCqfblriDMMS7p7+pwgjZoo9BLkP3wa9uglQXzsB9ww==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-musl@4.5.1:
    resolution: {integrity: sha512-f5Gs8WQixqGRtI0Iq/cMqvFYmgFzMinuJO24KRfnv7Ohi/HQclwrBCYkzQu1XfLEEt3DZyvveq9HWo4bLJf1Lw==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-gnu@4.5.1:
    resolution: {integrity: sha512-CWPkPGrFfN2vj3mw+S7A/4ZaU3rTV7AkXUr08W9lNP+UzOvKLVf34tWCqrKrfwQ0NTk5GFqUr2XGpeR2p6R4gw==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-musl@4.5.1:
    resolution: {integrity: sha512-ZRETMFA0uVukUC9u31Ed1nx++29073goCxZtmZARwk5aF/ltuENaeTtRVsSQzFlzdd4J6L3qUm+EW8cbGt0CKQ==}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-arm64-msvc@4.5.1:
    resolution: {integrity: sha512-ihqfNJNb2XtoZMSCPeoo0cYMgU04ksyFIoOw5S0JUVbOhafLot+KD82vpKXOurE2+9o/awrqIxku9MRR9hozHQ==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-ia32-msvc@4.5.1:
    resolution: {integrity: sha512-zK9MRpC8946lQ9ypFn4gLpdwr5a01aQ/odiIJeL9EbgZDMgbZjjT/XzTqJvDfTmnE1kHdbG20sAeNlpc91/wbg==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-x64-msvc@4.5.1:
    resolution: {integrity: sha512-5I3Nz4Sb9TYOtkRwlH0ow+BhMH2vnh38tZ4J4mggE48M/YyJyp/0sPSxhw1UeS1+oBgQ8q7maFtSeKpeRJu41Q==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-darwin-arm64@1.3.99:
    resolution: {integrity: sha512-Qj7Jct68q3ZKeuJrjPx7k8SxzWN6PqLh+VFxzA+KwLDpQDPzOlKRZwkIMzuFjLhITO4RHgSnXoDk/Syz0ZeN+Q==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-darwin-x64@1.3.99:
    resolution: {integrity: sha512-wR7m9QVJjgiBu1PSOHy7s66uJPa45Kf9bZExXUL+JAa9OQxt5y+XVzr+n+F045VXQOwdGWplgPnWjgbUUHEVyw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-gnu@1.3.99:
    resolution: {integrity: sha512-gcGv1l5t0DScEONmw5OhdVmEI/o49HCe9Ik38zzH0NtDkc+PDYaCcXU5rvfZP2qJFaAAr8cua8iJcOunOSLmnA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-musl@1.3.99:
    resolution: {integrity: sha512-XL1/eUsTO8BiKsWq9i3iWh7H99iPO61+9HYiWVKhSavknfj4Plbn+XyajDpxsauln5o8t+BRGitymtnAWJM4UQ==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-gnu@1.3.99:
    resolution: {integrity: sha512-fGrXYE6DbTfGNIGQmBefYxSk3rp/1lgbD0nVg4rl4mfFRQPi7CgGhrrqSuqZ/ezXInUIgoCyvYGWFSwjLXt/Qg==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-musl@1.3.99:
    resolution: {integrity: sha512-kvgZp/mqf3IJ806gUOL6gN6VU15+DfzM1Zv4Udn8GqgXiUAvbQehrtruid4Snn5pZTLj4PEpSCBbxgxK1jbssA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-arm64-msvc@1.3.99:
    resolution: {integrity: sha512-yt8RtZ4W/QgFF+JUemOUQAkVW58cCST7mbfKFZ1v16w3pl3NcWd9OrtppFIXpbjU1rrUX2zp2R7HZZzZ2Zk/aQ==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-ia32-msvc@1.3.99:
    resolution: {integrity: sha512-62p5fWnOJR/rlbmbUIpQEVRconICy5KDScWVuJg1v3GPLBrmacjphyHiJC1mp6dYvvoEWCk/77c/jcQwlXrDXw==}
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-x64-msvc@1.3.99:
    resolution: {integrity: sha512-PdppWhkoS45VGdMBxvClVgF1hVjqamtvYd82Gab1i4IV45OSym2KinoDCKE1b6j3LwBLOn2J9fvChGSgGfDCHQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core@1.3.99:
    resolution: {integrity: sha512-8O996RfuPC4ieb4zbYMfbyCU9k4gSOpyCNnr7qBQ+o7IEmh8JCV6B8wwu+fT/Om/6Lp34KJe1IpJ/24axKS6TQ==}
    engines: {node: '>=10'}
    requiresBuild: true
    peerDependencies:
      '@swc/helpers': ^0.5.0
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@swc/counter': 0.1.2
      '@swc/types': 0.1.5
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.3.99
      '@swc/core-darwin-x64': 1.3.99
      '@swc/core-linux-arm64-gnu': 1.3.99
      '@swc/core-linux-arm64-musl': 1.3.99
      '@swc/core-linux-x64-gnu': 1.3.99
      '@swc/core-linux-x64-musl': 1.3.99
      '@swc/core-win32-arm64-msvc': 1.3.99
      '@swc/core-win32-ia32-msvc': 1.3.99
      '@swc/core-win32-x64-msvc': 1.3.99
    dev: true

  /@swc/counter@0.1.2:
    resolution: {integrity: sha512-9F4ys4C74eSTEUNndnER3VJ15oru2NumfQxS8geE+f3eB5xvfxpWyqE5XlVnxb/R14uoXi6SLbBwwiDSkv+XEw==}
    dev: true

  /@swc/helpers@0.4.14:
    resolution: {integrity: sha512-4C7nX/dvpzB7za4Ql9K81xK3HPxCpHMgwTZVyf+9JQ6VUbn9jjZVN7/Nkdz/Ugzs2CSjqnL/UPXroiVBVHUWUw==}
    dependencies:
      tslib: 2.6.2
    dev: false

  /@swc/helpers@0.4.36:
    resolution: {integrity: sha512-5lxnyLEYFskErRPenYItLRSge5DjrJngYKdVjRSrWfza9G6KkgHEXi0vUZiyUeMU5JfXH1YnvXZzSp8ul88o2Q==}
    dependencies:
      legacy-swc-helpers: /@swc/helpers@0.4.14
      tslib: 2.6.2
    dev: false

  /@swc/helpers@0.5.3:
    resolution: {integrity: sha512-FaruWX6KdudYloq1AHD/4nU+UsMTdNE8CKyrseXWEcgjDAbvkwJg2QGPAnfIJLIWsjZOSPLOAykK6fuYp4vp4A==}
    dependencies:
      tslib: 2.6.2
    dev: false

  /@swc/types@0.1.5:
    resolution: {integrity: sha512-myfUej5naTBWnqOCc/MdVOLVjXUXtIA+NpDrDBKJtLLg2shUjBu3cZmB/85RyitKc55+lUUyl7oRfLOvkr2hsw==}
    dev: true

  /@types/hoist-non-react-statics@3.3.5:
    resolution: {integrity: sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==}
    dependencies:
      '@types/react': 18.2.38
      hoist-non-react-statics: 3.3.2
    dev: false

  /@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: true

  /@types/lodash@4.17.6:
    resolution: {integrity: sha512-OpXEVoCKSS3lQqjx9GGGOapBeuW5eUboYHRlHP9urXPX25IKZ6AnP5ZRxtVf63iieUbsHxLn8NQ5Nlftc6yzAA==}
    dev: true

  /@types/minimist@1.2.5:
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}
    dev: true

  /@types/node@18.19.3:
    resolution: {integrity: sha512-k5fggr14DwAytoA/t8rPrIz++lXK7/DqckthCmoZOKNsEbJkId4Z//BqgApXBUGrGddrigYa1oqheo/7YmW4rg==}
    dependencies:
      undici-types: 5.26.5
    dev: true

  /@types/node@20.11.14:
    resolution: {integrity: sha512-w3yWCcwULefjP9DmDDsgUskrMoOy5Z8MiwKHr1FvqGPtx7CvJzQvxD7eKpxNtklQxLruxSXWddyeRtyud0RcXQ==}
    dependencies:
      undici-types: 5.26.5
    dev: true

  /@types/normalize-package-data@2.4.4:
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}
    dev: true

  /@types/prop-types@15.7.11:
    resolution: {integrity: sha512-ga8y9v9uyeiLdpKddhxYQkxNDrfvuPrlFb0N1qnZZByvcElJaXthF1UhvCh9TLWJBEHeNtdnbysW7Y6Uq8CVng==}

  /@types/qs@6.9.14:
    resolution: {integrity: sha512-5khscbd3SwWMhFqylJBLQ0zIu7c1K6Vz0uBIt915BI3zV0q1nfjRQD3RqSBcPaO6PHEF4ov/t9y89fSiyThlPA==}
    dev: true

  /@types/react-beautiful-dnd@13.1.8:
    resolution: {integrity: sha512-E3TyFsro9pQuK4r8S/OL6G99eq7p8v29sX0PM7oT8Z+PJfZvSQTx4zTQbUJ+QZXioAF0e7TGBEcA1XhYhCweyQ==}
    dependencies:
      '@types/react': 18.2.38
    dev: true

  /@types/react-dom@18.2.17:
    resolution: {integrity: sha512-rvrT/M7Df5eykWFxn6MYt5Pem/Dbyc1N8Y0S9Mrkw2WFCRiqUgw9P7ul2NpwsXCSM1DVdENzdG9J5SreqfAIWg==}
    dependencies:
      '@types/react': 18.2.38

  /@types/react-redux@7.1.34:
    resolution: {integrity: sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==}
    dependencies:
      '@types/hoist-non-react-statics': 3.3.5
      '@types/react': 18.2.38
      hoist-non-react-statics: 3.3.2
      redux: 4.2.1
    dev: false

  /@types/react@18.2.38:
    resolution: {integrity: sha512-cBBXHzuPtQK6wNthuVMV6IjHAFkdl/FOPFIlkd81/Cd1+IqkHu/A+w4g43kaQQoYHik/ruaQBDL72HyCy1vuMw==}
    dependencies:
      '@types/prop-types': 15.7.11
      '@types/scheduler': 0.16.8
      csstype: 3.1.2

  /@types/scheduler@0.16.8:
    resolution: {integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==}

  /@types/semver@7.5.6:
    resolution: {integrity: sha512-dn1l8LaMea/IjDoHNd9J52uBbInB796CDffS6VdIxvqYCPSG0V0DzHp76GpaWnlhg88uYyPbXCDIowa86ybd5A==}
    dev: true

  /@types/use-sync-external-store@0.0.3:
    resolution: {integrity: sha512-EwmlvuaxPNej9+T4v5AuBPJa2x2UOJVdjCtDHgcDqitUeOtjnJKJ+apYjVcAoBEMjKW1VVFGZLUb5+qqa09XFA==}
    dev: false

  /@typescript-eslint/eslint-plugin@6.12.0(@typescript-eslint/parser@6.12.0)(eslint@8.54.0)(typescript@5.3.2):
    resolution: {integrity: sha512-XOpZ3IyJUIV1b15M7HVOpgQxPPF7lGXgsfcEIu3yDxFPaf/xZKt7s9QO/pbk7vpWQyVulpJbu4E5LwpZiQo4kA==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^6.0.0 || ^6.0.0-alpha
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@eslint-community/regexpp': 4.10.0
      '@typescript-eslint/parser': 6.12.0(eslint@8.54.0)(typescript@5.3.2)
      '@typescript-eslint/scope-manager': 6.12.0
      '@typescript-eslint/type-utils': 6.12.0(eslint@8.54.0)(typescript@5.3.2)
      '@typescript-eslint/utils': 6.12.0(eslint@8.54.0)(typescript@5.3.2)
      '@typescript-eslint/visitor-keys': 6.12.0
      debug: 4.3.4
      eslint: 8.54.0
      graphemer: 1.4.0
      ignore: 5.3.0
      natural-compare: 1.4.0
      semver: 7.5.4
      ts-api-utils: 1.0.3(typescript@5.3.2)
      typescript: 5.3.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser@6.12.0(eslint@8.54.0)(typescript@5.3.2):
    resolution: {integrity: sha512-s8/jNFPKPNRmXEnNXfuo1gemBdVmpQsK1pcu+QIvuNJuhFzGrpD7WjOcvDc/+uEdfzSYpNu7U/+MmbScjoQ6vg==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/scope-manager': 6.12.0
      '@typescript-eslint/types': 6.12.0
      '@typescript-eslint/typescript-estree': 6.12.0(typescript@5.3.2)
      '@typescript-eslint/visitor-keys': 6.12.0
      debug: 4.3.4
      eslint: 8.54.0
      typescript: 5.3.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/scope-manager@6.12.0:
    resolution: {integrity: sha512-5gUvjg+XdSj8pcetdL9eXJzQNTl3RD7LgUiYTl8Aabdi8hFkaGSYnaS6BLc0BGNaDH+tVzVwmKtWvu0jLgWVbw==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': 6.12.0
      '@typescript-eslint/visitor-keys': 6.12.0
    dev: true

  /@typescript-eslint/type-utils@6.12.0(eslint@8.54.0)(typescript@5.3.2):
    resolution: {integrity: sha512-WWmRXxhm1X8Wlquj+MhsAG4dU/Blvf1xDgGaYCzfvStP2NwPQh6KBvCDbiOEvaE0filhranjIlK/2fSTVwtBng==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/typescript-estree': 6.12.0(typescript@5.3.2)
      '@typescript-eslint/utils': 6.12.0(eslint@8.54.0)(typescript@5.3.2)
      debug: 4.3.4
      eslint: 8.54.0
      ts-api-utils: 1.0.3(typescript@5.3.2)
      typescript: 5.3.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types@6.12.0:
    resolution: {integrity: sha512-MA16p/+WxM5JG/F3RTpRIcuOghWO30//VEOvzubM8zuOOBYXsP+IfjoCXXiIfy2Ta8FRh9+IO9QLlaFQUU+10Q==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dev: true

  /@typescript-eslint/typescript-estree@6.12.0(typescript@5.3.2):
    resolution: {integrity: sha512-vw9E2P9+3UUWzhgjyyVczLWxZ3GuQNT7QpnIY3o5OMeLO/c8oHljGc8ZpryBMIyympiAAaKgw9e5Hl9dCWFOYw==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 6.12.0
      '@typescript-eslint/visitor-keys': 6.12.0
      debug: 4.3.4
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.5.4
      ts-api-utils: 1.0.3(typescript@5.3.2)
      typescript: 5.3.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/utils@6.12.0(eslint@8.54.0)(typescript@5.3.2):
    resolution: {integrity: sha512-LywPm8h3tGEbgfyjYnu3dauZ0U7R60m+miXgKcZS8c7QALO9uWJdvNoP+duKTk2XMWc7/Q3d/QiCuLN9X6SWyQ==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.54.0)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.5.6
      '@typescript-eslint/scope-manager': 6.12.0
      '@typescript-eslint/types': 6.12.0
      '@typescript-eslint/typescript-estree': 6.12.0(typescript@5.3.2)
      eslint: 8.54.0
      semver: 7.5.4
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/visitor-keys@6.12.0:
    resolution: {integrity: sha512-rg3BizTZHF1k3ipn8gfrzDXXSFKyOEB5zxYXInQ6z0hUvmQlhaZQzK+YmHmNViMA9HzW5Q9+bPPt90bU6GQwyw==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': 6.12.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@ungap/structured-clone@1.2.0:
    resolution: {integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==}
    dev: true

  /@vitejs/plugin-react-swc@3.5.0(vite@5.0.2):
    resolution: {integrity: sha512-1PrOvAaDpqlCV+Up8RkAh9qaiUjoDUcjtttyhXDKw53XA6Ve16SOp6cCOpRs8Dj8DqUQs6eTW5YkLcLJjrXAig==}
    peerDependencies:
      vite: ^4 || ^5
    dependencies:
      '@swc/core': 1.3.99
      vite: 5.0.2(@types/node@20.11.14)(terser@5.27.0)
    transitivePeerDependencies:
      - '@swc/helpers'
    dev: true

  /@voerkai18n/react@2.1.10(@types/react@18.2.38)(@voerkai18n/runtime@2.1.10)(react@18.2.0):
    resolution: {integrity: sha512-JZqdx8mZp3e1Li/HmVA4BGhws+2rgLYolNO7bWhUtKwLbGjOzW3OnysdDDIg/Zz++cf94Uu4iAq6bsi3+KHpXA==}
    peerDependencies:
      '@types/react': ^16.9.0
      '@voerkai18n/runtime': ^2.1.10
      react: ^16.9.0
    dependencies:
      '@types/react': 18.2.38
      '@voerkai18n/runtime': 2.1.10
      react: 18.2.0
    dev: false

  /@voerkai18n/runtime@2.1.10:
    resolution: {integrity: sha512-3vniwAyre7+s1cAjm4ZLwCNYkikVkl1N/Qfdew84tdhXzIPvqIqChEIQHaKgBFxaY/9LGnGt3dVAD9Spk2hn7A==}
    dependencies:
      flex-tools: 1.4.9
    dev: false

  /JSONStream@1.3.5:
    resolution: {integrity: sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==}
    hasBin: true
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8
    dev: true

  /acorn-jsx@5.3.2(acorn@8.11.2):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.11.2
    dev: true

  /acorn@5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn@8.11.2:
    resolution: {integrity: sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /ahooks@3.8.1(react@18.2.0):
    resolution: {integrity: sha512-JoP9+/RWO7MnI/uSKdvQ8WB10Y3oo1PjLv+4Sv4Vpm19Z86VUMdXh+RhWvMGxZZs06sq2p0xVtFk8Oh5ZObsoA==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.23.4
      dayjs: 1.11.10
      intersection-observer: 0.12.2
      js-cookie: 3.0.5
      lodash: 4.17.21
      react: 18.2.0
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.6.2
    dev: false

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1
    dev: true

  /align-text@0.1.4:
    resolution: {integrity: sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
      longest: 1.0.1
      repeat-string: 1.6.1
    dev: false

  /amdefine@1.0.1:
    resolution: {integrity: sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==}
    engines: {node: '>=0.4.2'}
    dev: false

  /ansi-escapes@6.2.0:
    resolution: {integrity: sha512-kzRaCqXnpzWs+3z5ABPQiVke+iq0KXkHo8xiWV4RPTi5Yli0l97BEQuhXV1s7+aSU/fu1kUuxgS4MsQ0fRuygw==}
    engines: {node: '>=14.16'}
    dependencies:
      type-fest: 3.13.1
    dev: true

  /ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  /ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  /ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  /antd@5.11.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-7/yqmfXpShHH0MJQOgv3vX9PUFwctyBm/G5L0i/S4AQy20ON6ZZ2UkjmWxgwg3vq2CEHKyVGTHozpH9WwDizgw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/colors': 7.0.0
      '@ant-design/cssinjs': 1.17.5(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/icons': 5.2.6(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/react-slick': 1.0.2(react@18.2.0)
      '@babel/runtime': 7.23.4
      '@ctrl/tinycolor': 3.6.1
      '@rc-component/color-picker': 1.4.1(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/mutate-observer': 1.1.0(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/tour': 1.10.0(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.10
      qrcode.react: 3.1.0(react@18.2.0)
      rc-cascader: 3.20.0(react-dom@18.2.0)(react@18.2.0)
      rc-checkbox: 3.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-collapse: 3.7.1(react-dom@18.2.0)(react@18.2.0)
      rc-dialog: 9.3.4(react-dom@18.2.0)(react@18.2.0)
      rc-drawer: 6.5.2(react-dom@18.2.0)(react@18.2.0)
      rc-dropdown: 4.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-field-form: 1.40.0(react-dom@18.2.0)(react@18.2.0)
      rc-image: 7.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-input: 1.3.6(react-dom@18.2.0)(react@18.2.0)
      rc-input-number: 8.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-mentions: 2.9.1(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.12.2(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-notification: 5.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-pagination: 3.7.0(react-dom@18.2.0)(react@18.2.0)
      rc-picker: 3.14.6(dayjs@1.11.10)(react-dom@18.2.0)(react@18.2.0)
      rc-progress: 3.5.1(react-dom@18.2.0)(react@18.2.0)
      rc-rate: 2.12.0(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-segmented: 2.2.2(react-dom@18.2.0)(react@18.2.0)
      rc-select: 14.10.0(react-dom@18.2.0)(react@18.2.0)
      rc-slider: 10.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-steps: 6.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-switch: 4.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-table: 7.36.0(react-dom@18.2.0)(react@18.2.0)
      rc-tabs: 12.13.1(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.5.3(react-dom@18.2.0)(react@18.2.0)
      rc-tooltip: 6.1.2(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.8.2(react-dom@18.2.0)(react@18.2.0)
      rc-tree-select: 5.15.0(react-dom@18.2.0)(react@18.2.0)
      rc-upload: 4.3.5(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.0
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment
    dev: false

  /antd@5.21.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EviOde/VEu+OsIKH5t6YXTMmmNeg9R85m0W5zXAo+Np8Latg9q10691JvAqOTMpnrRmbdeKUQL1Krp69Bzbe/g==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/colors': 7.1.0
      '@ant-design/cssinjs': 1.21.1(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/cssinjs-utils': 1.1.1(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/icons': 5.5.1(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/react-slick': 1.1.2(react@18.2.0)
      '@babel/runtime': 7.26.0
      '@ctrl/tinycolor': 3.6.1
      '@rc-component/color-picker': 2.0.1(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/mutate-observer': 1.1.0(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/qrcode': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/tour': 1.15.1(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.13
      rc-cascader: 3.28.2(react-dom@18.2.0)(react@18.2.0)
      rc-checkbox: 3.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-collapse: 3.8.0(react-dom@18.2.0)(react@18.2.0)
      rc-dialog: 9.6.0(react-dom@18.2.0)(react@18.2.0)
      rc-drawer: 7.2.0(react-dom@18.2.0)(react@18.2.0)
      rc-dropdown: 4.2.0(react-dom@18.2.0)(react@18.2.0)
      rc-field-form: 2.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-image: 7.11.0(react-dom@18.2.0)(react@18.2.0)
      rc-input: 1.6.3(react-dom@18.2.0)(react@18.2.0)
      rc-input-number: 9.2.0(react-dom@18.2.0)(react@18.2.0)
      rc-mentions: 2.16.1(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.15.1(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-notification: 5.6.2(react-dom@18.2.0)(react@18.2.0)
      rc-pagination: 4.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-picker: 4.6.15(dayjs@1.11.13)(react-dom@18.2.0)(react@18.2.0)
      rc-progress: 4.0.0(react-dom@18.2.0)(react@18.2.0)
      rc-rate: 2.13.0(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-segmented: 2.5.0(react-dom@18.2.0)(react@18.2.0)
      rc-select: 14.15.2(react-dom@18.2.0)(react@18.2.0)
      rc-slider: 11.1.7(react-dom@18.2.0)(react@18.2.0)
      rc-steps: 6.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-switch: 4.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-table: 7.47.5(react-dom@18.2.0)(react@18.2.0)
      rc-tabs: 15.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.8.2(react-dom@18.2.0)(react@18.2.0)
      rc-tooltip: 6.2.1(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-tree-select: 5.23.0(react-dom@18.2.0)(react@18.2.0)
      rc-upload: 4.8.1(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.2
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment
    dev: false

  /any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  /argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: true

  /array-buffer-byte-length@1.0.0:
    resolution: {integrity: sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==}
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.2
    dev: false

  /array-ify@1.0.0:
    resolution: {integrity: sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==}
    dev: true

  /array-tree-filter@2.1.0:
    resolution: {integrity: sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==}
    dev: false

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /arraybuffer.prototype.slice@1.0.2:
    resolution: {integrity: sha512-yMBKppFur/fbHu9/6USUe03bZ4knMYiwFBcyiaXB8Go0qNehwX6inYPzK9U0NeQvGxKthcmHcaR8P5MStSRBAw==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.22.3
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.2
      is-shared-array-buffer: 1.0.2
    dev: false

  /arrify@1.0.1:
    resolution: {integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /art-template@4.13.2:
    resolution: {integrity: sha512-04ws5k+ndA5DghfheY4c8F1304XJKeTcaXqZCLpxFkNMSkaR3ChW1pX2i9d3sEEOZuLy7de8lFriRaik1jEeOQ==}
    engines: {node: '>= 1.0.0'}
    dependencies:
      acorn: 5.7.4
      escodegen: 1.14.3
      estraverse: 4.3.0
      html-minifier: 3.5.21
      is-keyword-js: 1.0.3
      js-tokens: 3.0.2
      merge-source-map: 1.1.0
      source-map: 0.5.7
    dev: false

  /async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}
    dev: false

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    dev: false

  /autoprefixer@10.4.16(postcss@8.4.31):
    resolution: {integrity: sha512-7vd3UC6xKp0HLfua5IjZlcXvGAGy7cBAXTg2lyQ/8WpNhd6SiZ8Be+xm3FyBSYJx5GKcpRCzBh7RH4/0dnY+uQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.22.1
      caniuse-lite: 1.0.30001564
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.31
      postcss-value-parser: 4.2.0
    dev: true

  /available-typed-arrays@1.0.5:
    resolution: {integrity: sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==}
    engines: {node: '>= 0.4'}
    dev: false

  /axios@1.6.2:
    resolution: {integrity: sha512-7i24Ri4pmDRfJTR7LDBhsOTtcm+9kjX5WiY1X3wIisx6G9So3pfMkEiU7emUBe46oceVImccTEM3k6C5dbVW8A==}
    dependencies:
      follow-redirects: 1.15.3
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}
    dev: true

  /binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  /bplist-parser@0.2.0:
    resolution: {integrity: sha512-z0M+byMThzQmD9NILRniCUXYsYpjwnlO8N5uCFaCqIOpqRsJCrQL9NK3JsD67CN5a08nF5oIL2bD6loTdHOuKw==}
    engines: {node: '>= 5.10.0'}
    dependencies:
      big-integer: 1.6.52
    dev: true

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: false

  /braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /browserslist@4.22.1:
    resolution: {integrity: sha512-FEVc202+2iuClEhZhrWy6ZiAcRLvNMyYcxZ8raemul1DYVOVdFsbqckWLdsixQZCpJlwe77Z3UTalE7jsjnKfQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001564
      electron-to-chromium: 1.4.590
      node-releases: 2.0.13
      update-browserslist-db: 1.0.13(browserslist@4.22.1)
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  /bundle-name@3.0.0:
    resolution: {integrity: sha512-PKA4BeSvBpQKQ8iPOGCSiell+N8P+Tf1DlwqmYhpe2gAhKPHn8EYOxVT+ShuGmhg8lN8XiSlS80yiExKXrURlw==}
    engines: {node: '>=12'}
    dependencies:
      run-applescript: 5.0.0
    dev: true

  /call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2
    dev: false

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camel-case@3.0.0:
    resolution: {integrity: sha512-+MbKztAYHXPr1jNTSKQF52VpcFjwY5RkR7fxksV8Doo4KAYc5Fl4UJRgthBbTmEx8C54DqahhbLJkDwjI3PI/w==}
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3
    dev: false

  /camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  /camelcase-keys@6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1
    dev: true

  /camelcase@1.2.1:
    resolution: {integrity: sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: true

  /caniuse-lite@1.0.30001564:
    resolution: {integrity: sha512-DqAOf+rhof+6GVx1y+xzbFPeOumfQnhYzVnZD6LAXijR77yPtm9mfOcqOnT3mpnJiZVT+kwLAFnRlZcIz+c6bg==}
    dev: true

  /center-align@0.1.3:
    resolution: {integrity: sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      align-text: 0.1.4
      lazy-cache: 1.0.4
    dev: false

  /chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: false

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk@5.3.0:
    resolution: {integrity: sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}
    dev: true

  /chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /class-variance-authority@0.7.0:
    resolution: {integrity: sha512-jFI8IQw4hczaL4ALINxqLEXQbWcNjoSkloa4IaufXCJr6QawJyw7tuRysRsrE8w2p/4gGaxKIt/hX3qz/IbD1A==}
    dependencies:
      clsx: 2.0.0
    dev: false

  /classnames@2.3.2:
    resolution: {integrity: sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==}
    dev: false

  /classnames@2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}
    dev: false

  /clean-css@4.2.4:
    resolution: {integrity: sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: false

  /cli-cursor@4.0.0:
    resolution: {integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      restore-cursor: 4.0.0
    dev: true

  /cli-truncate@4.0.0:
    resolution: {integrity: sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==}
    engines: {node: '>=18'}
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.0.0
    dev: true

  /cliui@2.1.0:
    resolution: {integrity: sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==}
    dependencies:
      center-align: 0.1.3
      right-align: 0.1.3
      wordwrap: 0.0.2
    dev: false

  /cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}
    dev: false

  /clsx@2.0.0:
    resolution: {integrity: sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==}
    engines: {node: '>=6'}
    dev: false

  /clsx@2.1.0:
    resolution: {integrity: sha512-m3iNNWpd9rl3jvvcBnu70ylMdrXt8Vlq4HYadnU5fwcOtvkSQWPmj7amUcDT2qYI7risszBjI5AUIUox9D16pg==}
    engines: {node: '>=6'}
    dev: false

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: true

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false

  /color2k@2.0.3:
    resolution: {integrity: sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==}
    dev: false

  /color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    dev: false

  /colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: true

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /commander@11.1.0:
    resolution: {integrity: sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==}
    engines: {node: '>=16'}
    dev: true

  /commander@2.17.1:
    resolution: {integrity: sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==}
    dev: false

  /commander@2.19.0:
    resolution: {integrity: sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==}
    dev: false

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  /commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  /commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}
    dev: false

  /compare-func@2.0.0:
    resolution: {integrity: sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==}
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0
    dev: true

  /compute-scroll-into-view@3.1.0:
    resolution: {integrity: sha512-rj8l8pD4bJ1nx+dAkMhV1xB5RuZEyVysfxJqB1pRchh1KVvwOv9b7CGB8ZfjTImVv2oF+sYMUkMZq6Na5Ftmbg==}
    dev: false

  /concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  /contour_plot@0.0.1:
    resolution: {integrity: sha512-Nil2HI76Xux6sVGORvhSS8v66m+/h5CwFkBJDO+U5vWaMdNC0yXNCsGDPbzPhvqOEU5koebhdEvD372LI+IyLw==}
    dev: false

  /conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha512-ROjNchA9LgfNMTTFSIWPzebCwOGFdgkEq45EnvvrmSLvCtAw0HSmrCs7/ty+wAeYUZyNay0YMUNYFTRL72PkBQ==}
    engines: {node: '>=16'}
    dependencies:
      compare-func: 2.0.0
    dev: true

  /conventional-changelog-conventionalcommits@7.0.2:
    resolution: {integrity: sha512-NKXYmMR/Hr1DevQegFB4MwfM5Vv0m4UIxKZTTYuD98lpTknaZlSRrDOG4X7wIXpGkfsYxZTghUN+Qq+T0YQI7w==}
    engines: {node: '>=16'}
    dependencies:
      compare-func: 2.0.0
    dev: true

  /conventional-commits-parser@5.0.0:
    resolution: {integrity: sha512-ZPMl0ZJbw74iS9LuX9YIAiW8pfM5p3yh2o/NbXHbkFuZzY5jvdi5jFycEOkmBW5H5I7nA+D6f3UcsCLP2vvSEA==}
    engines: {node: '>=16'}
    hasBin: true
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0
    dev: true

  /copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /cosmiconfig-typescript-loader@5.0.0(@types/node@18.19.3)(cosmiconfig@8.3.6)(typescript@5.3.2):
    resolution: {integrity: sha512-+8cK7jRAReYkMwMiG+bxhcNKiHJDM6bR9FD/nGBXOWdMLuYawjF5cGrtLilJ+LGd3ZjCXnJjR5DkfWPoIVlqJA==}
    engines: {node: '>=v16'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=8.2'
      typescript: '>=4'
    dependencies:
      '@types/node': 18.19.3
      cosmiconfig: 8.3.6(typescript@5.3.2)
      jiti: 1.21.0
      typescript: 5.3.2
    dev: true

  /cosmiconfig@8.3.6(typescript@5.3.2):
    resolution: {integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
      typescript: 5.3.2
    dev: true

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  /css-box-model@1.2.1:
    resolution: {integrity: sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==}
    dependencies:
      tiny-invariant: 1.3.3
    dev: false

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /csstype@3.1.2:
    resolution: {integrity: sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==}

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: false

  /d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}
    dependencies:
      internmap: 2.0.3
    dev: false

  /d3-color@1.4.1:
    resolution: {integrity: sha512-p2sTHSLCJI2QKunbGb7ocOh7DgTAn8IrLx21QRc/BSnodXM4sv6aLQlnfpvehFMLZEfBc6g9pH9SWQccFYfJ9Q==}
    dev: false

  /d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}
    dev: false

  /d3-dsv@3.0.1:
    resolution: {integrity: sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==}
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      commander: 7.2.0
      iconv-lite: 0.6.3
      rw: 1.3.3
    dev: false

  /d3-force@3.0.0:
    resolution: {integrity: sha512-zxV/SsA+U4yte8051P4ECydjD/S+qeYtnaIyAs9tgHCqfguma/aAQDjo85A9Z6EKhBirHRJHXIgJUlffT4wdLg==}
    engines: {node: '>=12'}
    dependencies:
      d3-dispatch: 3.0.1
      d3-quadtree: 3.0.1
      d3-timer: 3.0.1
    dev: false

  /d3-format@3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}
    dev: false

  /d3-geo@3.1.0:
    resolution: {integrity: sha512-JEo5HxXDdDYXCaWdwLRt79y7giK8SbhZJbFWXqbRTolCHFI5jRqteLzCsq51NKbUoX0PjBVSohxrx+NoOUujYA==}
    engines: {node: '>=12'}
    dependencies:
      d3-array: 3.2.4
    dev: false

  /d3-hierarchy@3.1.2:
    resolution: {integrity: sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==}
    engines: {node: '>=12'}
    dev: false

  /d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-path@3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}
    dev: false

  /d3-quadtree@3.0.1:
    resolution: {integrity: sha512-04xDrxQTDTCFwP5H6hRhsRcb9xxv2RzkcsygFzmkSIOJy3PeRJP7sNk3VRIbKXcog561P9oU0/rVH6vDROAgUw==}
    engines: {node: '>=12'}
    dev: false

  /d3-scale-chromatic@3.0.0:
    resolution: {integrity: sha512-Lx9thtxAKrO2Pq6OO2Ua474opeziKr279P/TKZsMAhYyNDD3EnCffdbgeSYN5O7m2ByQsxtuP2CSDczNUIZ22g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
      d3-interpolate: 3.0.1
    dev: false

  /d3-shape@3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}
    dependencies:
      d3-path: 3.1.0
    dev: false

  /d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}
    dev: false

  /dargs@7.0.0:
    resolution: {integrity: sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg==}
    engines: {node: '>=8'}
    dev: true

  /dayjs@1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==}
    dev: false

  /dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}
    dev: false

  /debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /decamelize-keys@1.1.1:
    resolution: {integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1
    dev: true

  /decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  /deep-equal@1.1.2:
    resolution: {integrity: sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.5
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.1
    dev: false

  /deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  /deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}
    dev: false

  /default-browser-id@3.0.0:
    resolution: {integrity: sha512-OZ1y3y0SqSICtE8DE4S8YOE9UZOJ8wO16fKWVP5J1Qz42kV9jcnMVFrEE/noXb/ss3Q4pZIH79kxofzyNNtUNA==}
    engines: {node: '>=12'}
    dependencies:
      bplist-parser: 0.2.0
      untildify: 4.0.0
    dev: true

  /default-browser@4.0.0:
    resolution: {integrity: sha512-wX5pXO1+BrhMkSbROFsyxUm0i/cJEScyNhA4PPxc41ICuv05ZZB/MX28s8aZx6xjmatvebIapF6hLEKEcpneUA==}
    engines: {node: '>=14.16'}
    dependencies:
      bundle-name: 3.0.0
      default-browser-id: 3.0.0
      execa: 7.2.0
      titleize: 3.0.0
    dev: true

  /define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1
    dev: false

  /define-lazy-prop@3.0.0:
    resolution: {integrity: sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==}
    engines: {node: '>=12'}
    dev: true

  /define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1
    dev: false

  /defined@1.0.1:
    resolution: {integrity: sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q==}
    dev: false

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}
    dev: false

  /detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}
    dev: false

  /didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  /dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  /doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dot-prop@5.3.0:
    resolution: {integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /dotenv@16.3.1:
    resolution: {integrity: sha512-IPzF4w4/Rd94bA9imS68tZBaYyBWSCE47V1RGuMrB94iyTOIEwRmVL2x/4An+6mETpLrKJ5hQkB8W4kFAadeIQ==}
    engines: {node: '>=12'}
    dev: false

  /dotignore@0.1.2:
    resolution: {integrity: sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw==}
    hasBin: true
    dependencies:
      minimatch: 3.1.2
    dev: false

  /eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    dev: false

  /electron-to-chromium@1.4.590:
    resolution: {integrity: sha512-hohItzsQcG7/FBsviCYMtQwUSWvVF7NVqPOnJCErWsAshsP/CR2LAXdmq276RbESNdhxiAq5/vRo1g2pxGXVww==}
    dev: true

  /emoji-regex@10.3.0:
    resolution: {integrity: sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw==}
    dev: true

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  /emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    dev: false

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /es-abstract@1.22.3:
    resolution: {integrity: sha512-eiiY8HQeYfYH2Con2berK+To6GrK2RxbPawDkGq4UiCQQfZHb6wX9qQqkbpPqaxQFcl8d9QzZqo0tGE0VcrdwA==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      arraybuffer.prototype.slice: 1.0.2
      available-typed-arrays: 1.0.5
      call-bind: 1.0.7
      es-set-tostringtag: 2.0.2
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.1
      has-symbols: 1.0.3
      hasown: 2.0.0
      internal-slot: 1.0.6
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.12
      is-weakref: 1.0.2
      object-inspect: 1.13.1
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.1
      safe-array-concat: 1.0.1
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.8
      string.prototype.trimend: 1.0.7
      string.prototype.trimstart: 1.0.7
      typed-array-buffer: 1.0.0
      typed-array-byte-length: 1.0.0
      typed-array-byte-offset: 1.0.0
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.13
    dev: false

  /es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
    dev: false

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}
    dev: false

  /es-set-tostringtag@2.0.2:
    resolution: {integrity: sha512-BuDyupZt65P9D2D2vA/zqcI3G5xRsklm5N3xCwuiy+/vKy8i0ifdsQP1sLgO4tZDSCaQUSnmC48khknGMV3D2Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.0
      hasown: 2.0.0
    dev: false

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: false

  /esbuild@0.19.7:
    resolution: {integrity: sha512-6brbTZVqxhqgbpqBR5MzErImcpA0SQdoKOkcWK/U30HtQxnokIpG3TX2r0IJqbFUzqLjhU/zC1S5ndgakObVCQ==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.19.7
      '@esbuild/android-arm64': 0.19.7
      '@esbuild/android-x64': 0.19.7
      '@esbuild/darwin-arm64': 0.19.7
      '@esbuild/darwin-x64': 0.19.7
      '@esbuild/freebsd-arm64': 0.19.7
      '@esbuild/freebsd-x64': 0.19.7
      '@esbuild/linux-arm': 0.19.7
      '@esbuild/linux-arm64': 0.19.7
      '@esbuild/linux-ia32': 0.19.7
      '@esbuild/linux-loong64': 0.19.7
      '@esbuild/linux-mips64el': 0.19.7
      '@esbuild/linux-ppc64': 0.19.7
      '@esbuild/linux-riscv64': 0.19.7
      '@esbuild/linux-s390x': 0.19.7
      '@esbuild/linux-x64': 0.19.7
      '@esbuild/netbsd-x64': 0.19.7
      '@esbuild/openbsd-x64': 0.19.7
      '@esbuild/sunos-x64': 0.19.7
      '@esbuild/win32-arm64': 0.19.7
      '@esbuild/win32-ia32': 0.19.7
      '@esbuild/win32-x64': 0.19.7
    dev: true

  /escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}
    dev: true

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /escodegen@1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1
    dev: false

  /eslint-config-prettier@9.0.0(eslint@8.54.0):
    resolution: {integrity: sha512-IcJsTkJae2S35pRsRAwoCE+925rJJStOdkKnLVgtE+tEpqU0EVVM7OqrwxqgptKdX29NUwC82I5pXsGFIgSevw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: 8.54.0
    dev: true

  /eslint-plugin-prettier@5.0.1(eslint-config-prettier@9.0.0)(eslint@8.54.0)(prettier@3.1.0):
    resolution: {integrity: sha512-m3u5RnR56asrwV/lDC4GHorlW75DsFfmUcjfCYylTUs85dBRnB7VM6xG8eCMJdeDRnppzmxZVf1GEPJvl1JmNg==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '*'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true
    dependencies:
      eslint: 8.54.0
      eslint-config-prettier: 9.0.0(eslint@8.54.0)
      prettier: 3.1.0
      prettier-linter-helpers: 1.0.0
      synckit: 0.8.5
    dev: true

  /eslint-plugin-react-hooks@4.6.0(eslint@8.54.0):
    resolution: {integrity: sha512-oFc7Itz9Qxh2x4gNHStv3BqJq54ExXmfC+a1NjAta66IAN87Wu0R/QArgIS9qKzX3dXKPI9H5crl9QchNMY9+g==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
    dependencies:
      eslint: 8.54.0
    dev: true

  /eslint-plugin-react-refresh@0.4.4(eslint@8.54.0):
    resolution: {integrity: sha512-eD83+65e8YPVg6603Om2iCIwcQJf/y7++MWm4tACtEswFLYMwxwVWAfwN+e19f5Ad/FOyyNg9Dfi5lXhH3Y3rA==}
    peerDependencies:
      eslint: '>=7'
    dependencies:
      eslint: 8.54.0
    dev: true

  /eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint@8.54.0:
    resolution: {integrity: sha512-NY0DfAkM8BIZDVl6PgSa1ttZbx3xHgJzSNJKYcQglem6CppHyMhRIQkBVSSMaSRnLhig3jsDbEzOjwCVt4AmmA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.54.0)
      '@eslint-community/regexpp': 4.10.0
      '@eslint/eslintrc': 2.1.3
      '@eslint/js': 8.54.0
      '@humanwhocodes/config-array': 0.11.13
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.2.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.23.0
      graphemer: 1.4.0
      ignore: 5.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.3
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.11.2
      acorn-jsx: 5.3.2(acorn@8.11.2)
      eslint-visitor-keys: 3.4.3
    dev: true

  /esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: false

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  /eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}

  /execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa@7.2.0:
    resolution: {integrity: sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==}
    engines: {node: ^14.18.0 || ^16.14.0 || >=18.0.0}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 4.3.1
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.1.0
      onetime: 6.0.0
      signal-exit: 3.0.7
      strip-final-newline: 3.0.0
    dev: true

  /execa@8.0.1:
    resolution: {integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==}
    engines: {node: '>=16.17'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.1.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0
    dev: true

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}
    dev: true

  /fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  /fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}
    dependencies:
      reusify: 1.0.4

  /fecha@4.2.3:
    resolution: {integrity: sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==}
    dev: false

  /file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.2.0
    dev: true

  /fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: true

  /find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.2.9
      keyv: 4.5.4
      rimraf: 3.0.2
    dev: true

  /flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true
    dev: false

  /flatted@3.2.9:
    resolution: {integrity: sha512-36yxDn5H7OFZQla0/jFJmbIKTdZAQHngCedGxiMmpNfEZM0sdEeT+WczLQrjK6D7o2aiyLYDnkw0R3JK0Qv1RQ==}
    dev: true

  /flex-tools@1.4.9:
    resolution: {integrity: sha512-p/EDkun3JKixm4zOo49M7gOGnlKwP4WumRQ37T6pO0+4Ueof9Mq5BN1jby6TR+G2gyOH8PMDTrP6IL3T+6JSjQ==}
    dependencies:
      art-template: 4.13.2
      glob: 10.4.5
    dev: false

  /flru@1.0.2:
    resolution: {integrity: sha512-kWyh8ADvHBFz6ua5xYOPnUroZTT/bwWfrCeL0Wj1dzG4/YOmOcfJ99W8dOVyyynJN35rZ9aCOtHChqQovV7yog==}
    engines: {node: '>=6'}
    dev: false

  /fmin@0.0.2:
    resolution: {integrity: sha512-sSi6DzInhl9d8yqssDfGZejChO8d2bAGIpysPsvYsxFe898z89XhCZg6CPNV3nhUhFefeC/AXZK2bAJxlBjN6A==}
    dependencies:
      contour_plot: 0.0.1
      json2module: 0.0.3
      rollup: 0.25.8
      tape: 4.17.0
      uglify-js: 2.8.29
    dev: false

  /follow-redirects@1.15.3:
    resolution: {integrity: sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7
    dev: false

  /foreground-child@3.3.0:
    resolution: {integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0
    dev: false

  /form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}
    dev: true

  /framer-motion@10.16.16(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-je6j91rd7NmUX7L1XHouwJ4v3R+SO4umso2LUcgOct3rHZ0PajZ80ETYZTajzEXEl9DlKyzjyt4AvGQ+lrebOw==}
    peerDependencies:
      react: ^18.0.0
      react-dom: ^18.0.0
    peerDependenciesMeta:
      react:
        optional: true
      react-dom:
        optional: true
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tslib: 2.6.2
    optionalDependencies:
      '@emotion/is-prop-valid': 0.8.8
    dev: false

  /fs-extra@11.2.0:
    resolution: {integrity: sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==}
    engines: {node: '>=14.14'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /function.prototype.name@1.1.6:
    resolution: {integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.22.3
      functions-have-names: 1.2.3
    dev: false

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: false

  /get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  /get-east-asian-width@1.2.0:
    resolution: {integrity: sha512-2nk+7SIVb14QrgXFHcm84tD4bKQz0RxPuMT8Ag5KPOq7J5fEmAg0UbXdTOSHqNuHSU28k55qnceesxXRZGzKWA==}
    engines: {node: '>=18'}
    dev: true

  /get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.1
      has-symbols: 1.0.3
      hasown: 2.0.0
    dev: false

  /get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}
    dev: false

  /get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: true

  /get-stream@8.0.1:
    resolution: {integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==}
    engines: {node: '>=16'}
    dev: true

  /get-symbol-description@1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
    dev: false

  /git-raw-commits@2.0.11:
    resolution: {integrity: sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      dargs: 7.0.0
      lodash: 4.17.21
      meow: 8.1.2
      split2: 3.2.2
      through2: 4.0.2
    dev: true

  /gl-matrix@3.4.3:
    resolution: {integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==}
    dev: false

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3

  /glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1
    dev: false

  /glob@7.1.6:
    resolution: {integrity: sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /global-dirs@0.1.1:
    resolution: {integrity: sha512-NknMLn7F2J7aflwFOlGdNIuCDpN3VGoSoB+aap3KABFWbHVn1TCgFC+np23J8W2BiZbjfEw3BFBycSMv1AFblg==}
    engines: {node: '>=4'}
    dependencies:
      ini: 1.3.8
    dev: true

  /globals@13.23.0:
    resolution: {integrity: sha512-XAmF0RjlrjY23MA51q3HltdlGxUpXPvg0GioKiD9X6HD28iMjo2dKC8Vqwm7lne4GNr78+RHTfliktR6ZH09wA==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globalthis@1.0.3:
    resolution: {integrity: sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1
    dev: false

  /globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.0
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.4
    dev: false

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: true

  /graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}
    dev: true

  /hard-rejection@2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}
    dev: true

  /has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: false

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: true

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.0
    dev: false

  /has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /has@1.0.4:
    resolution: {integrity: sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ==}
    engines: {node: '>= 0.4.0'}
    dev: false

  /hasown@2.0.0:
    resolution: {integrity: sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: false

  /hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}
    dev: true

  /hosted-git-info@4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==}
    engines: {node: '>=10'}
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /html-minifier@3.5.21:
    resolution: {integrity: sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA==}
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      camel-case: 3.0.0
      clean-css: 4.2.4
      commander: 2.17.1
      he: 1.2.0
      param-case: 2.1.1
      relateurl: 0.2.7
      uglify-js: 3.4.10
    dev: false

  /human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}
    dev: true

  /human-signals@4.3.1:
    resolution: {integrity: sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==}
    engines: {node: '>=14.18.0'}
    dev: true

  /human-signals@5.0.0:
    resolution: {integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==}
    engines: {node: '>=16.17.0'}
    dev: true

  /husky@8.0.3:
    resolution: {integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ignore@5.3.0:
    resolution: {integrity: sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg==}
    engines: {node: '>= 4'}
    dev: true

  /immer@10.0.3:
    resolution: {integrity: sha512-pwupu3eWfouuaowscykeckFmVTpqbzW+rXFCX8rQLkZzM9ftBmU/++Ra+o+L27mz03zJTlyV4UUr+fdKNffo4A==}
    dev: false

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: true

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}
    dev: false

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: true

  /internal-slot@1.0.6:
    resolution: {integrity: sha512-Xj6dv+PsbtwyPpEflsejS+oIZxmMlV44zAhG479uYu89MsjcYOhCFnNyKrkJrihbsiasQyY0afoCl/9BLR65bg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
      hasown: 2.0.0
      side-channel: 1.0.6
    dev: false

  /internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}
    dev: false

  /intersection-observer@0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==}
    dev: false

  /intl-messageformat@10.5.8:
    resolution: {integrity: sha512-NRf0jpBWV0vd671G5b06wNofAN8tp7WWDogMZyaU8GUAsmbouyvgwmFJI7zLjfAMpm3zK+vSwRP3jzaoIcMbaA==}
    dependencies:
      '@formatjs/ecma402-abstract': 1.18.0
      '@formatjs/fast-memoize': 2.2.0
      '@formatjs/icu-messageformat-parser': 2.7.3
      tslib: 2.6.2
    dev: false

  /invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.0
    dev: false

  /is-array-buffer@3.0.2:
    resolution: {integrity: sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      is-typed-array: 1.1.12
    dev: false

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}
    dev: true

  /is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false

  /is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2
    dev: false

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.0
    dev: false

  /is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-core-module@2.13.1:
    resolution: {integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==}
    dependencies:
      hasown: 2.0.0

  /is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true
    dev: true

  /is-docker@3.0.0:
    resolution: {integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dev: true

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  /is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}
    dev: true

  /is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==}
    engines: {node: '>=18'}
    dependencies:
      get-east-asian-width: 1.2.0
    dev: true

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-inside-container@1.0.0:
    resolution: {integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==}
    engines: {node: '>=14.16'}
    hasBin: true
    dependencies:
      is-docker: 3.0.0
    dev: true

  /is-keyword-js@1.0.3:
    resolution: {integrity: sha512-EW8wNCNvomPa/jsH1g0DmLfPakkRCRTcTML1v1fZMLiVCvQ/1YB+tKsRzShBiWQhqrYCi5a+WsepA4Z8TA9iaA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-obj@2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: true

  /is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.0
    dev: false

  /is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.7
    dev: false

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: true

  /is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  /is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /is-text-path@2.0.0:
    resolution: {integrity: sha512-+oDTluR6WEjdXEJMnC2z6A4FRwFoYuvShVVEGsS7ewc0UTi2QtAKMDJuL4BDEVt+5T7MjFo12RP8ghOM75oKJw==}
    engines: {node: '>=8'}
    dependencies:
      text-extensions: 2.4.0
    dev: true

  /is-typed-array@1.1.12:
    resolution: {integrity: sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.13
    dev: false

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.7
    dev: false

  /is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
    dev: true

  /isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}
    dev: false

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  /jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    dev: false

  /jiti@1.21.0:
    resolution: {integrity: sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==}
    hasBin: true

  /js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}
    dev: false

  /js-tokens@3.0.2:
    resolution: {integrity: sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg==}
    dev: false

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}
    dev: true

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: true

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: true

  /json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    dev: true

  /json2module@0.0.3:
    resolution: {integrity: sha512-qYGxqrRrt4GbB8IEOy1jJGypkNsjWoIMlZt4bAsmUScCA507Hbc2p1JOhBzqn45u3PWafUgH2OnzyNU7udO/GA==}
    hasBin: true
    dependencies:
      rw: 1.3.3
    dev: false

  /json2mq@0.2.0:
    resolution: {integrity: sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==}
    dependencies:
      string-convert: 0.2.1
    dev: false

  /jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonparse@1.3.1:
    resolution: {integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==}
    engines: {'0': node >= 0.2.0}
    dev: true

  /keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}
    dependencies:
      json-buffer: 3.0.1
    dev: true

  /kind-of@3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /lazy-cache@1.0.4:
    resolution: {integrity: sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2
    dev: false

  /levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  /lilconfig@3.0.0:
    resolution: {integrity: sha512-K2U4W2Ff5ibV7j7ydLr+zLAkIg5JJ4lPn1Ltsdt+Tz/IjQ8buJ55pZAxoP34lqIiwtF9iAvtLv3JGv7CAyAg+g==}
    engines: {node: '>=14'}

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  /lint-staged@15.2.0:
    resolution: {integrity: sha512-TFZzUEV00f+2YLaVPWBWGAMq7So6yQx+GG8YRMDeOEIf95Zn5RyiLMsEiX4KTNl9vq/w+NqRJkLA1kPIo15ufQ==}
    engines: {node: '>=18.12.0'}
    hasBin: true
    dependencies:
      chalk: 5.3.0
      commander: 11.1.0
      debug: 4.3.4
      execa: 8.0.1
      lilconfig: 3.0.0
      listr2: 8.0.0
      micromatch: 4.0.5
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.3.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /listr2@8.0.0:
    resolution: {integrity: sha512-u8cusxAcyqAiQ2RhYvV7kRKNLgUvtObIbhOX2NCXqvp1UU32xIg5CT22ykS2TPKJXZWJwtK3IKLiqAGlGNE+Zg==}
    engines: {node: '>=18.0.0'}
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.0.0
      rfdc: 1.3.0
      wrap-ansi: 9.0.0
    dev: true

  /locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: true

  /locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}
    dev: false

  /lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}
    dev: true

  /lodash.foreach@4.5.0:
    resolution: {integrity: sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==}
    dev: false

  /lodash.get@4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}
    dev: false

  /lodash.isfunction@3.0.9:
    resolution: {integrity: sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw==}
    dev: true

  /lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}
    dev: true

  /lodash.kebabcase@4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==}

  /lodash.mapkeys@4.6.0:
    resolution: {integrity: sha512-0Al+hxpYvONWtg+ZqHpa/GaVzxuN3V7Xeo2p+bY06EaK/n+Y9R7nBePPN2o1LxmL0TWQSwP8LYZ008/hc9JzhA==}
    dev: false

  /lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash.mergewith@4.6.2:
    resolution: {integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==}
    dev: true

  /lodash.omit@4.5.0:
    resolution: {integrity: sha512-XeqSp49hNGmlkj2EJlfrQFIzQ6lXdNro9sddtQzcJY8QaoC2GO0DT7xaIokHeyM+mIT0mPMlPvkYzg2xCuHdZg==}
    dev: false

  /lodash.snakecase@4.1.1:
    resolution: {integrity: sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw==}
    dev: true

  /lodash.startcase@4.4.0:
    resolution: {integrity: sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg==}
    dev: true

  /lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}
    dev: true

  /lodash.upperfirst@4.3.1:
    resolution: {integrity: sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg==}
    dev: true

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  /log-update@6.0.0:
    resolution: {integrity: sha512-niTvB4gqvtof056rRIrTZvjNYE4rCUzO6X/X+kYjd7WFxXeJ0NwEFnRxX6ehkvv3jTwrXnNdtAak5XYZuIyPFw==}
    engines: {node: '>=18'}
    dependencies:
      ansi-escapes: 6.2.0
      cli-cursor: 4.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0
    dev: true

  /longest@1.0.1:
    resolution: {integrity: sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lower-case@1.1.4:
    resolution: {integrity: sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA==}
    dev: false

  /lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}
    dev: false

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /map-obj@1.0.1:
    resolution: {integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}
    dev: true

  /memoize-one@5.2.1:
    resolution: {integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==}
    dev: false

  /meow@12.1.1:
    resolution: {integrity: sha512-BhXM0Au22RwUneMPwSCnyhTOizdWoIEPU9sp0Aqa1PnDMR5Wv2FGXYDjuzJEIX+Eo2Rb8xuYe5jrnm5QowQFkw==}
    engines: {node: '>=16.10'}
    dev: true

  /meow@8.1.2:
    resolution: {integrity: sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q==}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimist': 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9
    dev: true

  /merge-source-map@1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==}
    dependencies:
      source-map: 0.6.1
    dev: false

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  /micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}
    dev: true

  /min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}
    dev: true

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: false

  /minimist-options@4.1.0:
    resolution: {integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==}
    engines: {node: '>= 6'}
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3
    dev: true

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  /minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}
    dev: false

  /mock-property@1.0.3:
    resolution: {integrity: sha512-2emPTb1reeLLYwHxyVx993iYyCHEiRRO+y8NFXFPL5kl5q14sgTK76cXyEKkeKCHeRw35SfdkUJ10Q1KfHuiIQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      functions-have-names: 1.2.3
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      hasown: 2.0.0
      isarray: 2.0.5
    dev: false

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /no-case@2.3.2:
    resolution: {integrity: sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==}
    dependencies:
      lower-case: 1.1.4
    dev: false

  /node-releases@2.0.13:
    resolution: {integrity: sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==}
    dev: true

  /normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 5.7.2
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-package-data@3.0.3:
    resolution: {integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.13.1
      semver: 7.5.4
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /npm-run-path@5.1.0:
    resolution: {integrity: sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      path-key: 4.0.0
    dev: true

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  /object-inspect@1.12.3:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}
    dev: false

  /object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==}
    dev: false

  /object-is@1.1.5:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
    dev: false

  /object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: false

  /object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: false

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}
    dependencies:
      mimic-fn: 4.0.0
    dev: true

  /open@9.1.0:
    resolution: {integrity: sha512-OS+QTnw1/4vrf+9hh1jc1jnYjzSG4ttTBB8UxOwAnInG3Uo4ssetzC1ihqaIHjLJnA5GGlRl6QlZXOTQhRBUvg==}
    engines: {node: '>=14.16'}
    dependencies:
      default-browser: 4.0.0
      define-lazy-prop: 3.0.0
      is-inside-container: 1.0.0
      is-wsl: 2.2.0
    dev: true

  /optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5
    dev: false

  /optionator@0.9.3:
    resolution: {integrity: sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      '@aashutoshrathi/word-wrap': 1.2.6
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: true

  /p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}
    dev: true

  /package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}
    dev: false

  /param-case@2.1.1:
    resolution: {integrity: sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w==}
    dependencies:
      no-case: 2.3.2
    dev: false

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.23.5
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  /path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}
    dev: true

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    dev: false

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /path@0.12.7:
    resolution: {integrity: sha512-aXXC6s+1w7otVF9UletFkFcDsJeO7lSZBPUQhtb5O0xJe8LtYhj/GxldoL09bBj9+ZmE2hNoHqQSFMN5fikh4Q==}
    dependencies:
      process: 0.11.10
      util: 0.10.4
    dev: false

  /pdfast@0.2.0:
    resolution: {integrity: sha512-cq6TTu6qKSFUHwEahi68k/kqN2mfepjkGrG9Un70cgdRRKLKY6Rf8P8uvP2NvZktaQZNF3YE7agEkLj0vGK9bA==}
    dev: false

  /picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  /pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  /postcss-import@15.1.0(postcss@8.4.31):
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.4.31
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  /postcss-js@4.0.1(postcss@8.4.31):
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.31

  /postcss-load-config@4.0.2(postcss@8.4.31):
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true
    dependencies:
      lilconfig: 3.0.0
      postcss: 8.4.31
      yaml: 2.3.4

  /postcss-nested@6.0.1(postcss@8.4.31):
    resolution: {integrity: sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      postcss: 8.4.31
      postcss-selector-parser: 6.0.13

  /postcss-selector-parser@6.0.13:
    resolution: {integrity: sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  /postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2

  /prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      fast-diff: 1.3.0
    dev: true

  /prettier@3.1.0:
    resolution: {integrity: sha512-TQLvXjq5IAibjh8EpBIkNKxO749UEWABoiIZehEPiY4GNpVdhaFKqSTu+QrlU6D2dPAfubRmtJTi4K4YkQ5eXw==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: false

  /proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}
    dev: false

  /punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: true

  /qrcode.react@3.1.0(react@18.2.0):
    resolution: {integrity: sha512-oyF+Urr3oAMUG/OiOuONL3HXM+53wvuH3mtIWQrYmsXoAq0DkvZp2RYUWFSMFtbdOpuS++9v+WAkzNVkMlNW6Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /qs@6.12.0:
    resolution: {integrity: sha512-trVZiI6RMOkO476zLGaBIzszOdFPnCCXHPG9kn0yuS1uz6xdVxPfZdB3vUig9pxPFDM9BRAgz/YUIVQ1/vuiUg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.6
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  /quick-lru@4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}
    dev: true

  /quickselect@2.0.0:
    resolution: {integrity: sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==}
    dev: false

  /raf-schd@4.0.3:
    resolution: {integrity: sha512-tQkJl2GRWh83ui2DiPTJz9wEiMN20syf+5oKfB03yYP7ioZcJwsIK8FjrtLwH1m7C7e+Tt2yYBlrOpdT+dyeIQ==}
    dev: false

  /rbush@3.0.1:
    resolution: {integrity: sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==}
    dependencies:
      quickselect: 2.0.0
    dev: false

  /rc-cascader@3.20.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lkT9EEwOcYdjZ/jvhLoXGzprK1sijT3/Tp4BLxQQcHDZkkOzzwYQC9HgmKoJz0K7CukMfgvO9KqHeBdgE+pELw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      array-tree-filter: 2.1.0
      classnames: 2.3.2
      rc-select: 14.10.0(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.8.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-cascader@3.28.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-8f+JgM83iLTvjgdkgU7GfI4qY8icXOBP0cGZjOdx2iJAkEe8ucobxDQAVE69UD/c3ehCxZlcgEHeD5hFmypbUw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      array-tree-filter: 2.1.0
      classnames: 2.5.1
      rc-select: 14.15.2(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-checkbox@3.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PAwpJFnBa3Ei+5pyqMMXdcKYKNBMS+TvSDiLdDnARnMJHC8ESxwPfm4Ao1gJiKtWLdmGfigascnCpwrHFgoOBQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-checkbox@3.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ih3ZaAcoAiFKJjifzwsGiT/f/quIkxJoklW4yKGho14Olulwn8gN7hOBve0/WGDg5o/l/5mL0w7ff7/YGvefVw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-collapse@3.7.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-N/7ejyiTf3XElNJBBpxqnZBUuMsQWEOPjB2QkfNvZ/Ca54eAvJXuOD1EGbCWCk2m7v/MSxku7mRpdeaLOCd4Gg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-collapse@3.8.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-YVBkssrKPBG09TGfcWWGj8zJBYD9G3XuTy89t5iUmSXrIXEAnO1M+qjUxRW6b4Qi0+wNWG6MHJF/+US+nmIlzA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dialog@9.3.4(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-975X3018GhR+EjZFbxA2Z57SX5rnu0G0/OxFgMMvZK4/hQWEm3MHaNvP4wXpxYDoJsp+xUvVW+GB9CMMCm81jA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dialog@9.6.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ApoVi9Z8PaCQg6FsUzS8yvBEQy0ZL2PkuvAgrmohPkN3okps5WZ5WQWPc1RNuiOKaAYv8B97ACdsFU5LizzCqg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-drawer@6.5.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-QckxAnQNdhh4vtmKN0ZwDf3iakO83W9eZcSKWYYTDv4qcD2fHhRAZJJ/OE6v2ZlQ2kSqCJX5gYssF4HJFvsEPQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-drawer@7.2.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-9lOQ7kBekEJRdEpScHvtmEtXnAsy+NGDXiRWc2ZVC7QXAazNVbeT4EraQKYwCME8BJLa8Bxqxvs5swwyOepRwg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dropdown@4.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-VZjMunpBdlVzYpEdJSaV7WM7O0jf8uyDjirxXLZRNZ+tAC+NzD3PXPEtliFwGzVwBBdCmGuSqiS9DWcOLxQ9tw==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dropdown@4.2.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-odM8Ove+gSh0zU27DUj5cG1gNKg7mLWBYzB5E4nNLrLwBmYEgYP43vHKDGOVZcJSVElQBI0+jTQgjnq0NfLjng==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-field-form@1.40.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OM3N01X2BYFGJDJcwpk9/BBtlwgveE7eh2SQAKIxVCt9KVWlODYJ9ypTHQdxchfDbeJKJKxMBFXlLAmyvlgPHg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      async-validator: 4.2.5
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-field-form@2.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-XZ/lF9iqf9HXApIHQHqzJK5v2w4mkUMsVqAzOyWVzoiwwXEavY6Tpuw7HavgzIoD+huVff4JghSGcgEfX6eycg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/async-validator': 5.0.4
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-image@7.11.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-aZkTEZXqeqfPZtnSdNUnKQA0N/3MbgR7nUnZ+/4MfSFWPFHZau4p5r5ShaI0KPEMnNjv4kijSCFq/9wtJpwykw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-dialog: 9.6.0(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-image@7.3.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ICEF6SWv9YKhDXxy1vrXcmf0TVvEcQWIww5Yg+f+mn7e4oGX7FNP4+FExwMjNO5UHBEuWrigbGhlCgI6yZZ1jg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-dialog: 9.3.4(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input-number@8.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-B6rziPOLRmeP7kcS5qbdC5hXvvDHYKV4vUxmahevYx2E6crS2bRi0xLDjhJ0E1HtOWo8rTmaE2EBJAkTCZOLdA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.3.2
      rc-input: 1.3.6(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input-number@9.2.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-5XZFhBCV5f9UQ62AZ2hFbEY8iZT/dm23Q1kAg0H8EvOgD3UDbYYJAayoVIkM3lQaCqYAW5gV0yV3vjw1XtzWHg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.5.1
      rc-input: 1.6.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input@1.3.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-/HjTaKi8/Ts4zNbYaB5oWCquxFyFQO4Co1MnMgoCeGJlpe7k8Eir2HN0a0F9IHDmmo+GYiGgPpz7w/d/krzsJA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input@1.6.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-wI4NzuqBS8vvKr8cljsvnTUqItMfG1QbJoxovCgL+DX4eVUcHIjVwharwevIxyy7H/jbLryh+K7ysnJr23aWIA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-mentions@2.16.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-GnhSTGP9Mtv6pqFFGQze44LlrtWOjHNrUUAcsdo9DnNAhN4pwVPEWy4z+2jpjkiGlJ3VoXdvMHcNDQdfI9fEaw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-input: 1.6.3(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.15.1(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.8.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-mentions@2.9.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-cZuElWr/5Ws0PXx1uxobxfYh4mqUw2FitfabR62YnWgm+WAfDyXZXqZg5DxXW+M1cgVvntrQgDDd9LrihrXzew==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-input: 1.3.6(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.12.2(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.5.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-menu@9.12.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-NzloFH2pRUYmQ3S/YbJAvRkgCZaLvq0sRa5rgJtuIHLfPPprNHNyepeSlT64+dbVqI4qRWL44VN0lUCldCbbfg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-menu@9.15.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-UKporqU6LPfHnpPmtP6hdEK4iO5Q+b7BRv/uRpxdIyDGplZy9jwUjsnpev5bs3PQKB0H0n34WAPDfjAfn3kAPA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-motion@2.9.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-XIU2+xLkdIr1/h6ohPZXyPBMvOmuyFZQ/T0xnawz+Rh+gh4FINcnZmMT5UTIj6hgI0VLDjTaPeRd+smJeSPqiQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-motion@2.9.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-rkW47ABVkic7WEB0EKJqzySpvDqwl60/tdkY7hWP7dYnh5pm0SzJpo54oW3TDUGXV5wfxXFmMkxrzRRbotQ0+w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-notification@5.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-WCf0uCOkZ3HGfF0p1H4Sgt7aWfipxORWTPp7o6prA3vxwtWhtug3GfpYls1pnBp4WA+j8vGIi5c2/hQRpGzPcQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-notification@5.6.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Id4IYMoii3zzrG0lB0gD6dPgJx4Iu95Xu0BQrhHIbp7ZnAZbLqdqQ73aIWH0d0UFcElxwaKjnzNovTjo7kXz7g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-overflow@1.3.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-nsUm78jkYAoPygDAcGZeC2VwIg/IBGSodtOY3pMof4W3M9qRJgqaDYm03ZayHlde3I6ipliAxbN0RUcGf5KOzw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.3.2
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-pagination@3.7.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-IxSzKapd13L91/195o1TPkKnCNw8gIR25UP1GCW/7c7n/slhld4npu2j2PB9IWjXm4SssaAaSAt2lscYog7wzg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-pagination@4.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-UubEWA0ShnroQ1tDa291Fzw6kj0iOeF26IsUObxYTpimgj4/qPCWVFl18RLZE+0Up1IZg0IK4pMn6nB3mjvB7g==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-picker@3.14.6(dayjs@1.11.10)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AdKKW0AqMwZsKvIpwUWDUnpuGKZVrbxVTZTNjcO+pViGkjC1EBcjMgxVe8tomOEaIHJL5Gd13vS8Rr3zzxWmag==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      dayjs: 1.11.10
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-picker@4.6.15(dayjs@1.11.13)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OWZ1yrMie+KN2uEUfYCfS4b2Vu6RC1FWwNI0s+qypsc3wRt7g+peuZKVIzXCTaJwyyZruo80+akPg2+GmyiJjw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      dayjs: 1.11.13
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-progress@3.5.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-V6Amx6SbLRwPin/oD+k1vbPrO8+9Qf8zW1T8A7o83HdNafEVvAxPV5YsgtKFP+Ud5HghLj33zKOcEHrcrUGkfw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-progress@4.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-oofVMMafOCokIUIBnZLNcOZFsABaUw8PPrf1/y0ZBvKZNpOiu5h4AO9vv11Sw0p4Hb3D0yGWuEattcQGtNJ/aw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-rate@2.12.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-g092v5iZCdVzbjdn28FzvWebK2IutoVoiTeqoLTj9WM7SjA/gOJIw5/JFZMRyJYYVe1jLAU2UhAfstIpCNRozg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-rate@2.13.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-oxvx1Q5k5wD30sjN5tqAyWTvJfLNNJn7Oq3IeS4HxWfAiC4BOXMITNAsw7u/fzdtO4MS8Ki8uRLOzcnEuoQiAw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-resize-observer@1.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PnMVyRid9JLxFavTjeDXEXo65HCRqbmLBw9xX9gfC4BZiSzbLXKzW3jPz+J0P71pLbD5tBMTT+mkstV5gD0c9Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-segmented@2.2.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Mq52M96QdHMsNdE/042ibT5vkcGcD5jxKp7HgPC2SRofpia99P5fkfHy1pEaajLMF/kj0+2Lkq1UZRvqzo9mSA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-segmented@2.5.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-B28Fe3J9iUFOhFJET3RoXAPFJ2u47QvLSYcZWC4tFYNGPEjug5LAxEasZlA/PpAxhdOPqGWsGbSj7ftneukJnw==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-select@14.10.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-TsIJTYafTTapCA32LLNpx/AD6ntepR1TG8jEVx35NiAAWCPymhUfuca8kRcUNd3WIGVMDcMKn9kkphoxEz+6Ag==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.11.3(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-select@14.15.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-oNoXlaFmpqXYcQDzcPVLrEqS2J9c+/+oJuGrlXeVVX/gVgrbHa5YcyiRUXRydFjyuA7GP3elRuLF7Y3Tfwltlw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.11.3(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-slider@10.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ZlpWjFhOlEf0w4Ng31avFBkXNNBj60NAcTPaIoiCxBkJ29wOtHSPMqv9PZeEoqmx64bpJkgK7kPa47HG4LPzww==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-slider@11.1.7(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ytYbZei81TX7otdC0QvoYD72XSlxvTihNth5OeZ6PMXyEDq/vHdWFulQmfDGyXK1NwKwSlKgpvINOa88uT5g2A==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-steps@6.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-switch@4.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-TI8ufP2Az9oEbvyCeVE4+90PDSljGyuwix3fV58p7HV2o4wBnVToEyomJRVyTaZeqNPAp+vqeo4Wnj5u0ZZQBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-table@7.36.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-3xVcdCC5OLeOOhaCg+5Lps2oPreM/GWXmUXWTSX4p6vF7F76ABM4dfPpMJ9Dnf5yGRyh+8pe7FRyhRVnWw2H/w==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/context': 1.4.0(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.11.3(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-table@7.47.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-fzq+V9j/atbPIcvs3emuclaEoXulwQpIiJA6/7ey52j8+9cJ4P8DGmp4YzfUVDrb3qhgedcVeD6eRgUrokwVEQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/context': 1.4.0(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.14.8(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tabs@12.13.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-83u3l2QkO0UznCzdBLEk9WnNcT+imtmDmMT993sUUEOGnNQAmqOdev0XjeqrcvsAMe9CDpAWDFd7L/RZw+LVJQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-dropdown: 4.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.12.2(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tabs@15.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lzE18r+zppT/jZWOAWS6ntdkDUKHOLJzqMi5UAij1LeKwOaQaupupAoI9Srn73GRzVpmGznkECMRrzkRusC40A==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-dropdown: 4.2.0(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.15.1(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-textarea@1.5.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-oH682ghHx++stFNYrosPRBfwsypywrTXpaD0/5Z8MPkUOnyOQUaY9ueL9tMu6BP1LfsuYQ1VLpg5OtshViLNgA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-input: 1.3.6(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-textarea@1.8.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-UFAezAqltyR00a8Lf0IPAyTd29Jj9ee8wt8DqXyDMal7r/Cg/nDt3e1OOv3Th4W6mKaZijjgwuPXhAfVNTN8sw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-input: 1.6.3(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tooltip@6.1.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-89zwvybvCxGJu3+gGF8w5AXd4HHk6hIN7K0vZbkzjilVaEAIWPqc1fcyeUeP71n3VCcw7pTL9LyFupFbrx8gHw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      '@rc-component/trigger': 1.18.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tooltip@6.2.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-rws0duD/3sHHsD905Nex7FvoUGy2UBQRhTkKxeEvr2FB+r21HsOxcDJI0TzyO8NHhnAA8ILr8pfbSBg5Jj5KBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree-select@5.15.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-YJHfdO6azFnR0/JuNBZLDptGE4/RGfVeHAafUIYcm2T3RBkL1O8aVqiHvwIyLzdK59ry0NLrByd+3TkfpRM+9Q==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-select: 14.10.0(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.8.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree-select@5.23.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-aQGi2tFSRw1WbXv0UVXPzHm09E0cSvUVZMLxQtMv3rnZZpNmdRXWrnd9QkLNlVH31F+X5rgghmdSFF3yZW0N9A==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-select: 14.15.2(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree@5.8.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xH/fcgLHWTLmrSuNphU8XAqV7CdaOQgm4KywlLGNoTMhDAcNR3GVNP6cZzb0GrKmIZ9yae+QLot/cAgUdPRMzg==}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-motion: 2.9.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.11.3(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree@5.9.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-CPrgOvm9d/9E+izTONKSngNzQdIEjMox2PBufWjS1wf7vxtvmCWzK1SlpHbRY6IaBfJIeZ+88RkcIevf729cRg==}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.3(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.11.3(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-upload@4.3.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EHlKJbhkgFSQHliTj9v/2K5aEuFwfUQgZARzD7AmAPOneZEPiCNF3n6PEWIuqz9h7oq6FuXgdR67sC5BWFxJbA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.23.4
      classnames: 2.3.2
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-upload@4.8.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-toEAhwl4hjLAI1u8/CgKWt30BR06ulPa4iGQSMvSXoHzO88gPCslxqV/mnn4gJU7PDoltGIC9Eh+wkeudqgHyw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-util@5.38.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-e4ZMs7q9XqwTuhIK7zBIVFltUtMSjphuPPQXHoHlzRzNdOwUxDejo0Zls5HYaJfRKNURcsS/ceKVULlhjBrxng==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
    dev: false

  /rc-util@5.43.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
    dev: false

  /rc-virtual-list@3.11.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-tu5UtrMk/AXonHwHxUogdXAWynaXsrx1i6dsgg+lOo/KJSF8oBAcprh1z5J3xgnPJD5hXxTL58F8s8onokdt0Q==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.3.2
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.38.1(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-virtual-list@3.14.8(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-8D0KfzpRYi6YZvlOWIxiOm9BGt4Wf2hQyEaM6RXlDDiY2NhLheuYI+RA+7ZaZj1lq+XQqy3KHlaeeXQfzI5fGg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /react-beautiful-dnd@13.1.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-0Lvs4tq2VcrEjEgDXHjT98r+63drkKEgqyxdA7qD3mvKwga6a5SscbdLPO2IExotU1jW8L0Ksdl0Cj2AF67nPQ==}
    deprecated: 'react-beautiful-dnd is now deprecated. Context and options: https://github.com/atlassian/react-beautiful-dnd/issues/2672'
    peerDependencies:
      react: ^16.8.5 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.5 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.26.0
      css-box-model: 1.2.1
      memoize-one: 5.2.1
      raf-schd: 4.0.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-redux: 7.2.9(react-dom@18.2.0)(react@18.2.0)
      redux: 4.2.1
      use-memo-one: 1.1.3(react@18.2.0)
    transitivePeerDependencies:
      - react-native
    dev: false

  /react-dom@18.2.0(react@18.2.0):
    resolution: {integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==}
    peerDependencies:
      react: ^18.2.0
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.0
    dev: false

  /react-fast-compare@3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}
    dev: false

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: false

  /react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}
    dev: false

  /react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}
    dev: false

  /react-redux@7.2.9(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==}
    peerDependencies:
      react: ^16.8.3 || ^17 || ^18
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      '@babel/runtime': 7.26.0
      '@types/react-redux': 7.1.34
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 17.0.2
    dev: false

  /react-redux@8.1.3(@types/react-dom@18.2.17)(@types/react@18.2.38)(react-dom@18.2.0)(react@18.2.0)(redux@5.0.1):
    resolution: {integrity: sha512-n0ZrutD7DaX/j9VscF+uTALI3oUPa/pO4Z3soOBIjuRn/FzVu6aehhysxZCLi6y7duMf52WNZGMl7CtuK5EnRw==}
    peerDependencies:
      '@types/react': ^16.8 || ^17.0 || ^18.0
      '@types/react-dom': ^16.8 || ^17.0 || ^18.0
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
      react-native: '>=0.59'
      redux: ^4 || ^5.0.0-beta.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
      react-dom:
        optional: true
      react-native:
        optional: true
      redux:
        optional: true
    dependencies:
      '@babel/runtime': 7.23.4
      '@types/hoist-non-react-statics': 3.3.5
      '@types/react': 18.2.38
      '@types/react-dom': 18.2.17
      '@types/use-sync-external-store': 0.0.3
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
      redux: 5.0.1
      use-sync-external-store: 1.2.0(react@18.2.0)
    dev: false

  /react-remove-scroll-bar@2.3.4(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-63C4YQBUt0m6ALadE9XV56hV8BgJWDmmTPY758iIJjfQKt2nYwoUrPk0LXRXcB/yIj82T1/Ixfdpdk68LwIB0A==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      react: 18.2.0
      react-style-singleton: 2.2.1(@types/react@18.2.38)(react@18.2.0)
      tslib: 2.6.2
    dev: false

  /react-remove-scroll@2.5.7(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-FnrTWO4L7/Bhhf3CYBNArEG/yROV0tKmTv7/3h9QCFvH6sndeFf1wPqOcbFVu5VAulS5dV1wGT3GZZ/1GawqiA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      react: 18.2.0
      react-remove-scroll-bar: 2.3.4(@types/react@18.2.38)(react@18.2.0)
      react-style-singleton: 2.2.1(@types/react@18.2.38)(react@18.2.0)
      tslib: 2.6.2
      use-callback-ref: 1.3.1(@types/react@18.2.38)(react@18.2.0)
      use-sidecar: 1.1.2(@types/react@18.2.38)(react@18.2.0)
    dev: false

  /react-router-dom@6.19.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-N6dWlcgL2w0U5HZUUqU2wlmOrSb3ighJmtQ438SWbhB1yuLTXQ8yyTBMK3BSvVjp7gBtKurT554nCtMOgxCZmQ==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'
    dependencies:
      '@remix-run/router': 1.12.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-router: 6.19.0(react@18.2.0)
    dev: false

  /react-router@6.19.0(react@18.2.0):
    resolution: {integrity: sha512-0W63PKCZ7+OuQd7Tm+RbkI8kCLmn4GPjDbX61tWljPxWgqTKlEpeQUwPkT1DRjYhF8KSihK0hQpmhU4uxVMcdw==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      '@remix-run/router': 1.12.0
      react: 18.2.0
    dev: false

  /react-style-singleton@2.2.1(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      get-nonce: 1.0.1
      invariant: 2.2.4
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /react-textarea-autosize@8.5.3(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-XT1024o2pqCuZSuBt9FwHlaDeNtVrtCXu0Rnz88t1jUGheCLa3PhjE1GH8Ctm2axEtvdCl5SUHYschyQ0L5QHQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.23.4
      react: 18.2.0
      use-composed-ref: 1.3.0(react@18.2.0)
      use-latest: 1.2.1(@types/react@18.2.38)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /react@18.2.0:
    resolution: {integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0

  /read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: true

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0
    dev: true

  /redux-thunk@3.1.0(redux@5.0.1):
    resolution: {integrity: sha512-NW2r5T6ksUKXCabzhL9z+h206HQw/NJkcLm1GPImRQ8IzfXwRGqjVhKJGauHirT0DAuyy6hjdnMZaRoAcy0Klw==}
    peerDependencies:
      redux: ^5.0.0
    dependencies:
      redux: 5.0.1
    dev: false

  /redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}
    dependencies:
      '@babel/runtime': 7.26.0
    dev: false

  /redux@5.0.1:
    resolution: {integrity: sha512-M9/ELqF6fy8FwmkpnF0S3YKOqMyoWJ4+CS5Efg2ct3oY9daQvd/Pc71FpGZsVsbl3Cpb+IIcjBDUnnyBdQbq4w==}
    dev: false

  /regenerator-runtime@0.14.0:
    resolution: {integrity: sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==}
    dev: false

  /regexp.prototype.flags@1.5.1:
    resolution: {integrity: sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      set-function-name: 2.0.1
    dev: false

  /relateurl@0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==}
    engines: {node: '>= 0.10'}
    dev: false

  /repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}
    dev: false

  /require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /reselect@5.1.0:
    resolution: {integrity: sha512-aw7jcGLDpSgNDyWBQLv2cedml85qd95/iszJjN988zX1t7AVRJi19d9kto5+W7oCfQ94gyo40dVbT6g2k4/kXg==}
    dev: false

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: true

  /resolve-global@1.0.0:
    resolution: {integrity: sha512-zFa12V4OLtT5XUX/Q4VLvTfBf+Ok0SPc1FNGM/z9ctUdiU618qwKpWnd0CHs3+RqROfyEg/DhuHbMWYqcgljEw==}
    engines: {node: '>=8'}
    dependencies:
      global-dirs: 0.1.1
    dev: true

  /resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor@4.0.0:
    resolution: {integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  /rfdc@1.3.0:
    resolution: {integrity: sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==}
    dev: true

  /right-align@0.1.3:
    resolution: {integrity: sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      align-text: 0.1.4
    dev: false

  /rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /rollup@0.25.8:
    resolution: {integrity: sha512-a2S4Bh3bgrdO4BhKr2E4nZkjTvrJ2m2bWjMTzVYtoqSCn0HnuxosXnaJUHrMEziOWr3CzL9GjilQQKcyCQpJoA==}
    hasBin: true
    dependencies:
      chalk: 1.1.3
      minimist: 1.2.8
      source-map-support: 0.3.3
    dev: false

  /rollup@4.5.1:
    resolution: {integrity: sha512-0EQribZoPKpb5z1NW/QYm3XSR//Xr8BeEXU49Lc/mQmpmVVG5jPUVrpc2iptup/0WMrY9mzas0fxH+TjYvG2CA==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.5.1
      '@rollup/rollup-android-arm64': 4.5.1
      '@rollup/rollup-darwin-arm64': 4.5.1
      '@rollup/rollup-darwin-x64': 4.5.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.5.1
      '@rollup/rollup-linux-arm64-gnu': 4.5.1
      '@rollup/rollup-linux-arm64-musl': 4.5.1
      '@rollup/rollup-linux-x64-gnu': 4.5.1
      '@rollup/rollup-linux-x64-musl': 4.5.1
      '@rollup/rollup-win32-arm64-msvc': 4.5.1
      '@rollup/rollup-win32-ia32-msvc': 4.5.1
      '@rollup/rollup-win32-x64-msvc': 4.5.1
      fsevents: 2.3.3
    dev: true

  /run-applescript@5.0.0:
    resolution: {integrity: sha512-XcT5rBksx1QdIhlFOCtgZkB99ZEouFZ1E2Kc2LHqNW13U3/74YGdkQRmThTwxy4QIyookibDKYZOPqX//6BlAg==}
    engines: {node: '>=12'}
    dependencies:
      execa: 5.1.1
    dev: true

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3

  /rw@1.3.3:
    resolution: {integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==}
    dev: false

  /safe-array-concat@1.0.1:
    resolution: {integrity: sha512-6XbUAseYE2KtOuGueyeobCySj9L4+66Tn6KQMOPQJrAJEowYKW/YR/MGJZl7FdydUdaFu4LYyDZjxf4/Nmo23Q==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5
    dev: false

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: true

  /safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      is-regex: 1.1.4
    dev: false

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /scheduler@0.23.0:
    resolution: {integrity: sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /scmify-components@0.1.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-0sGenQ4gaTrsdvgK/virAqafd3fJsyNcC3PVsyF5bG4DdML9pmNZVgVLR4CGCRONh+N2E6KvGtbNbgQHXMm5hQ==}
    dependencies:
      '@ant-design/cssinjs': 1.21.1(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/icons': 5.5.1(react-dom@18.2.0)(react@18.2.0)
      antd: 5.21.6(react-dom@18.2.0)(react@18.2.0)
      class-variance-authority: 0.7.0
      react-beautiful-dnd: 13.1.1(react-dom@18.2.0)(react@18.2.0)
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment
      - react
      - react-dom
      - react-native
    dev: false

  /screenfull@5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /scroll-into-view-if-needed@3.0.10:
    resolution: {integrity: sha512-t44QCeDKAPf1mtQH3fYpWz8IM/DyvHLjs8wUvvwMYxk5moOqCzrMSxK6HQVD0QVmVjXFavoFIPRVrMuJPKAvtg==}
    dependencies:
      compute-scroll-into-view: 3.1.0
    dev: false

  /scroll-into-view-if-needed@3.1.0:
    resolution: {integrity: sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==}
    dependencies:
      compute-scroll-into-view: 3.1.0
    dev: false

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    dev: true

  /semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
    dev: false

  /set-function-name@2.0.1:
    resolution: {integrity: sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2
    dev: false

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  /side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1
    dev: false

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  /simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: false

  /slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi@5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0
    dev: true

  /slice-ansi@7.1.0:
    resolution: {integrity: sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==}
    engines: {node: '>=18'}
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0
    dev: true

  /source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  /source-map-support@0.3.3:
    resolution: {integrity: sha512-9O4+y9n64RewmFoKUZ/5Tx9IHIcXM6Q+RTSw6ehnqybUz4a7iwR3Eaw80uLtqqQ5D0C+5H03D4KKGo9PdP33Gg==}
    dependencies:
      source-map: 0.1.32
    dev: false

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  /source-map@0.1.32:
    resolution: {integrity: sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ==}
    engines: {node: '>=0.8.0'}
    dependencies:
      amdefine: 1.0.1
    dev: false

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.16
    dev: true

  /spdx-exceptions@2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}
    dev: true

  /spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.16
    dev: true

  /spdx-license-ids@3.0.16:
    resolution: {integrity: sha512-eWN+LnM3GR6gPu35WxNgbGl8rmY1AEmoMDvL/QD6zYmPWgywxWqJWNdLGT+ke8dKNWrcYgYjPpG5gbTfghP8rw==}
    dev: true

  /split2@3.2.2:
    resolution: {integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==}
    dependencies:
      readable-stream: 3.6.2
    dev: true

  /split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}
    dev: true

  /string-argv@0.3.2:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-convert@0.2.1:
    resolution: {integrity: sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==}
    dev: false

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: false

  /string-width@7.0.0:
    resolution: {integrity: sha512-GPQHj7row82Hjo9hKZieKcHIhaAIKOJvFSIZXuCU9OASVZrMNUaZuz++SPVrBjnLsnk4k+z9f2EIypgxf2vNFw==}
    engines: {node: '>=18'}
    dependencies:
      emoji-regex: 10.3.0
      get-east-asian-width: 1.2.0
      strip-ansi: 7.1.0
    dev: true

  /string.prototype.trim@1.2.8:
    resolution: {integrity: sha512-lfjY4HcixfQXOfaqCvcBuOIapyaroTXhbkfJN3gcB1OtyupngWK4sEET9Knd0cXd28kTUqu/kHoV4HKSJdnjiQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.22.3
    dev: false

  /string.prototype.trimend@1.0.7:
    resolution: {integrity: sha512-Ni79DqeB72ZFq1uH/L6zJ+DKZTkOtPIHovb3YZHQViE+HDouuU4mBrLOLDn5Dde3RF8qw5qVETEjhu9locMLvA==}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.22.3
    dev: false

  /string.prototype.trimstart@1.0.7:
    resolution: {integrity: sha512-NGhtDFu3jCEm7B4Fy0DpLewdJQOZcQ0rGbwQ/+stjnrp2i+rlKeCvos9hOIeCmqwratM47OBxY7uFZzjxHXmrg==}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.22.3
    dev: false

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1

  /strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.0.1

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: true

  /strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}
    dev: true

  /strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}
    dependencies:
      min-indent: 1.0.1
    dev: true

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /stylis@4.3.0:
    resolution: {integrity: sha512-E87pIogpwUsUwXw7dNyU4QDjdgVMy52m+XEOPEKUn161cCzWjjhPSQhByfd1CcNvrOLnXQ6OnnZDwnJrz/Z4YQ==}
    dev: false

  /stylis@4.3.4:
    resolution: {integrity: sha512-osIBl6BGUmSfDkyH2mB7EFvCJntXDrLhKjHTRj/rK6xLH0yuPrHULDRQzKokSOD4VoorhtKpfcfW1GAntu8now==}
    dev: false

  /sucrase@3.34.0:
    resolution: {integrity: sha512-70/LQEZ07TEcxiU2dz51FKaE6hCTWC6vr7FOk3Gr0U60C3shtAN+H+BFr9XlYe5xqf3RA8nrc+VIwzCfnxuXJw==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      commander: 4.1.1
      glob: 7.1.6
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  /supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}
    dev: false

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /svg-path-parser@1.1.0:
    resolution: {integrity: sha512-jGCUqcQyXpfe38R7RFfhrMyfXcBmpMNJI/B+4CE9/Unkh98UporAc461GTthv+TVDuZXsBx7/WiwJb1Oh4tt4A==}
    dev: false

  /synckit@0.8.5:
    resolution: {integrity: sha512-L1dapNV6vu2s/4Sputv8xGsCdAVlb5nRDMFU/E27D44l5U6cw1g0dGd45uLc+OXjNMmF4ntiMdCimzcjFKQI8Q==}
    engines: {node: ^14.18.0 || >=16.0.0}
    dependencies:
      '@pkgr/utils': 2.4.2
      tslib: 2.6.2
    dev: true

  /tailwind-merge@1.14.0:
    resolution: {integrity: sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==}
    dev: false

  /tailwind-variants@0.1.19(tailwindcss@3.3.5):
    resolution: {integrity: sha512-D9Yf5WqsxodnCtjZt6KifEoKwW8rTURXQV03KRKlojITQM5gV1vPVWufWNiIvd/ptC3QybYFpwmHK9cs4Ei08Q==}
    engines: {node: '>=16.x', pnpm: '>=7.x'}
    peerDependencies:
      tailwindcss: '*'
    dependencies:
      tailwind-merge: 1.14.0
      tailwindcss: 3.3.5
    dev: false

  /tailwindcss@3.3.5:
    resolution: {integrity: sha512-5SEZU4J7pxZgSkv7FP1zY8i2TIAOooNZ1e/OGtxIEv6GltpoiXUqWvLy89+a10qYTB1N5Ifkuw9lqQkN9sscvA==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.5.3
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.0
      lilconfig: 2.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.0
      postcss: 8.4.31
      postcss-import: 15.1.0(postcss@8.4.31)
      postcss-js: 4.0.1(postcss@8.4.31)
      postcss-load-config: 4.0.2(postcss@8.4.31)
      postcss-nested: 6.0.1(postcss@8.4.31)
      postcss-selector-parser: 6.0.13
      resolve: 1.22.8
      sucrase: 3.34.0
    transitivePeerDependencies:
      - ts-node

  /tape@4.17.0:
    resolution: {integrity: sha512-KCuXjYxCZ3ru40dmND+oCLsXyuA8hoseu2SS404Px5ouyS0A99v8X/mdiLqsR5MTAyamMBN7PRwt2Dv3+xGIxw==}
    hasBin: true
    dependencies:
      '@ljharb/resumer': 0.0.1
      '@ljharb/through': 2.3.11
      call-bind: 1.0.7
      deep-equal: 1.1.2
      defined: 1.0.1
      dotignore: 0.1.2
      for-each: 0.3.3
      glob: 7.2.3
      has: 1.0.4
      inherits: 2.0.4
      is-regex: 1.1.4
      minimist: 1.2.8
      mock-property: 1.0.3
      object-inspect: 1.12.3
      resolve: 1.22.8
      string.prototype.trim: 1.2.8
    dev: false

  /terser@5.27.0:
    resolution: {integrity: sha512-bi1HRwVRskAjheeYl291n3JC4GgO/Ty4z1nVs5AAsmonJulGxpSektecnNedrwK9C7vpvVtcX3cw00VSLt7U2A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.5
      acorn: 8.11.2
      commander: 2.20.3
      source-map-support: 0.5.21

  /text-extensions@2.4.0:
    resolution: {integrity: sha512-te/NtwBwfiNRLf9Ijqx3T0nlqZiQ2XrrtBvu+cLL8ZRrGkO0NHTug8MYFKyoSrv/sHTaSKfilUkizV6XhxMJ3g==}
    engines: {node: '>=8'}
    dev: true

  /text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}
    dev: true

  /thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1

  /thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0

  /throttle-debounce@5.0.0:
    resolution: {integrity: sha512-2iQTSgkkc1Zyk0MeVrt/3BvuOXYPl/R8Z0U2xxo9rjwNciaHDG3R+Lm6dh4EeUci49DanvBnuqI6jshoQQRGEg==}
    engines: {node: '>=12.22'}
    dev: false

  /throttle-debounce@5.0.2:
    resolution: {integrity: sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==}
    engines: {node: '>=12.22'}
    dev: false

  /through2@4.0.2:
    resolution: {integrity: sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw==}
    dependencies:
      readable-stream: 3.6.2
    dev: true

  /through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}
    dev: true

  /tiny-invariant@1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}
    dev: false

  /titleize@3.0.0:
    resolution: {integrity: sha512-KxVu8EYHDPBdUYdKZdKtU2aj2XfEx9AfjXxE/Aj0vT06w2icA09Vus1rh6eSu1y01akYg6BjIK/hxyLJINoMLQ==}
    engines: {node: '>=12'}
    dev: true

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}
    dev: false

  /trim-newlines@3.0.1:
    resolution: {integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==}
    engines: {node: '>=8'}
    dev: true

  /ts-api-utils@1.0.3(typescript@5.3.2):
    resolution: {integrity: sha512-wNMeqtMz5NtwpT/UZGY5alT+VoKdSsOOP/kqHFcUW1P/VRhH2wJ48+DN2WwUliNbQ976ETwDL0Ifd2VVvgonvg==}
    engines: {node: '>=16.13.0'}
    peerDependencies:
      typescript: '>=4.2.0'
    dependencies:
      typescript: 5.3.2
    dev: true

  /ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  /tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==}

  /type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
    dev: false

  /type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-fest@0.18.1:
    resolution: {integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}
    dev: true

  /type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}
    dev: true

  /type-fest@3.13.1:
    resolution: {integrity: sha512-tLq3bSNx+xSpwvAJnzrK0Ep5CLNWjvFTOp71URMaAEWBfRb9nnJiBoUe0tF8bI4ZFO3omgBR6NvnbzVUT3Ly4g==}
    engines: {node: '>=14.16'}
    dev: true

  /typed-array-buffer@1.0.0:
    resolution: {integrity: sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      is-typed-array: 1.1.12
    dev: false

  /typed-array-byte-length@1.0.0:
    resolution: {integrity: sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: false

  /typed-array-byte-offset@1.0.0:
    resolution: {integrity: sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.7
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: false

  /typed-array-length@1.0.4:
    resolution: {integrity: sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==}
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      is-typed-array: 1.1.12
    dev: false

  /typescript@5.3.2:
    resolution: {integrity: sha512-6l+RyNy7oAHDfxC4FzSJcz9vnjTKxrLpDG5M2Vu4SHRVNg6xzqZp6LYSR9zjqQTu8DU/f5xwxUdADOkbrIX2gQ==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /uglify-js@2.8.29:
    resolution: {integrity: sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      source-map: 0.5.7
      yargs: 3.10.0
    optionalDependencies:
      uglify-to-browserify: 1.0.2
    dev: false

  /uglify-js@3.4.10:
    resolution: {integrity: sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      commander: 2.19.0
      source-map: 0.6.1
    dev: false

  /uglify-to-browserify@1.0.2:
    resolution: {integrity: sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==}
    requiresBuild: true
    dev: false
    optional: true

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: false

  /undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}
    dev: true

  /universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /untildify@4.0.0:
    resolution: {integrity: sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==}
    engines: {node: '>=8'}
    dev: true

  /update-browserslist-db@1.0.13(browserslist@4.22.1):
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.22.1
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: true

  /upper-case@1.1.3:
    resolution: {integrity: sha512-WRbjgmYzgXkCV7zNVpy5YgrHgbBv126rMALQQMrmzOVC4GM2waQ9x7xtm8VU+1yF2kWyPzI9zbZ48n4vSxwfSA==}
    dev: false

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /use-callback-ref@1.3.1(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-Lg4Vx1XZQauB42Hw3kK7JM6yjVjgFmFC5/Ab797s79aARomD2nEErc4mCgM8EZrARLmmbWpi5DGCadmK50DcAQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /use-composed-ref@1.3.0(react@18.2.0):
    resolution: {integrity: sha512-GLMG0Jc/jiKov/3Ulid1wbv3r54K9HlMW29IWcDFPEqFkSO2nS0MuefWgMJpeHQ9YJeXDL3ZUF+P3jdXlZX/cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /use-isomorphic-layout-effect@1.1.2(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      react: 18.2.0
    dev: false

  /use-latest@1.2.1(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-xA+AVm/Wlg3e2P/JiItTziwS7FK92LWrDB0p+hgXloIMuVCeJJ8v6f0eeHyPZaJrM+usM1FkFfbNCrJGs8A/zw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      react: 18.2.0
      use-isomorphic-layout-effect: 1.1.2(@types/react@18.2.38)(react@18.2.0)
    dev: false

  /use-memo-one@1.1.3(react@18.2.0):
    resolution: {integrity: sha512-g66/K7ZQGYrI6dy8GLpVcMsBp4s17xNkYJVSMvTEevGy3nDxHOfE6z8BVE22+5G5x7t3+bhzrlTDB7ObrEE0cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /use-sidecar@1.1.2(@types/react@18.2.38)(react@18.2.0):
    resolution: {integrity: sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.9.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.38
      detect-node-es: 1.1.0
      react: 18.2.0
      tslib: 2.6.2
    dev: false

  /use-sync-external-store@1.2.0(react@18.2.0):
    resolution: {integrity: sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /util@0.10.4:
    resolution: {integrity: sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1
    dev: true

  /vite@5.0.2(@types/node@20.11.14)(terser@5.27.0):
    resolution: {integrity: sha512-6CCq1CAJCNM1ya2ZZA7+jS2KgnhbzvxakmlIjN24cF/PXhRMzpM/z8QgsVJA/Dm5fWUWnVEsmtBoMhmerPxT0g==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      '@types/node': 20.11.14
      esbuild: 0.19.7
      postcss: 8.4.31
      rollup: 4.5.1
      terser: 5.27.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: false

  /which-typed-array@1.1.13:
    resolution: {integrity: sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
    dev: false

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /window-size@0.1.0:
    resolution: {integrity: sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /wordwrap@0.0.2:
    resolution: {integrity: sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==}
    engines: {node: '>=0.4.0'}
    dev: false

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  /wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: false

  /wrap-ansi@9.0.0:
    resolution: {integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==}
    engines: {node: '>=18'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.0.0
      strip-ansi: 7.1.0
    dev: true

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  /y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: true

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yaml@2.3.4:
    resolution: {integrity: sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==}
    engines: {node: '>= 14'}

  /yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}
    dev: true

  /yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}
    dev: true

  /yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1
    dev: true

  /yargs@3.10.0:
    resolution: {integrity: sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==}
    dependencies:
      camelcase: 1.2.1
      cliui: 2.1.0
      decamelize: 1.2.0
      window-size: 0.1.0
    dev: false

  /yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: true
