import { fileURLToPath } from 'url';
import path from 'path';
import react from '@vitejs/plugin-react-swc';
import { ConfigEnv, defineConfig, loadEnv } from 'vite';
import Voerkai18nPlugin from '@voerkai18n/vite';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig((UserConfig: ConfigEnv) => {
  const env = loadEnv(UserConfig.mode, process.cwd(), '');
  return {
    plugins: [
      react(),
      Voerkai18nPlugin({
        debug: true, // 输出一些调试信息
      }),
    ],
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: env.VITE_DROP_CONSOLE === 'true',
          drop_debugger: true,
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      host: '0.0.0.0',
      hmr: true,
      strictPort: false,
      port: Number(env.VITE_PORT), // 将 env.VITE_PORT 转换为 number 类型
      open: false,
      proxy: {
        '/api/file/uploadFile': {
          target: env.VITE_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        '/api': {
          target: env.VITE_API_URL,
          changeOrigin: true,
        },
        '/scp': {
          target: env.VITE_GET_ID_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/scp/, ''),
        },
        '/mdDataView/getIdsByUser': {
          target: env.VITE_GET_ID_URL,
          changeOrigin: true,
        },
        '/int/transfer': {
          target: env.VITE_GET_ID_URL,
          changeOrigin: true,
        },
        '/mock': {
          target: env.VITE_MOCK_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/mock/, ''),
        },
      },
    },
  };
});
