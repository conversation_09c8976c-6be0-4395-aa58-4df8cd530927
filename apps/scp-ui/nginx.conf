events {
    worker_connections  1024;
}

http {
    include       /usr/local/openresty/nginx/conf/mime.types;
   # 全局设置
    client_body_timeout 120;
    client_header_timeout 120;
    keepalive_timeout 120;
    send_timeout 120;

     types {
        text/javascript js;
        application/javascript js;
        # 添加其他 MIME 类型...
     }
    server {
        listen 80;
        server_name  localhost;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location ^~ /api {
            rewrite ^/api/(.*)$ /$1 break;
            # proxy_pass http://*************:55051;
             proxy_pass http://${SCP_GO_UI_HOST}:${SCP_GO_UI_PORT};
            #proxy_pass *************:50051;
            proxy_read_timeout 120;
            proxy_connect_timeout 120;
            proxy_send_timeout 120;

            # 添加跨域头部
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }

        location ^~ /jump/scpJumpToInt {
            # proxy_pass http://*************:55051;
            proxy_pass http://${SCP_GO_UI_HOST}:${SCP_GO_UI_PORT};
            #proxy_pass *************:50051;
            proxy_read_timeout 120;
            proxy_connect_timeout 120;
            proxy_send_timeout 120;
            # 添加跨域头部
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}