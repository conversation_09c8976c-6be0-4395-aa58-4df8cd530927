//   getAttribute: (data: object) =>
//     request.get('/api/eigenvalue/getAttribute', { params: data }),
//
//   createOrUpdateAttribute: (data: object) =>
//     request.post('/api/eigenvalue/createOrUpdateAttribute', data),
//
//   getMasterDataTypeList: (data: object) =>
//     request.get('/api/eigenvalue/getMasterDataTypeList', { params: data }),
//
//   getMasterDataTypeDetails: (data: object) =>
//     request.get('/api/eigenvalue/getMasterDataTypeDetails', { params: data }),
//
//   getTimeProfileList: (data: object) =>
//     request.get('/api/eigenvalue/getTimeProfileList', { params: data }),
//
//   getPlanningLevelList: (data: object) =>
//     request.get('/api/eigenvalue/getPlanningLevelList', { params: data }),
//
//   getPlanningLevelDetail: (data: object) =>
//     request.get('/api/eigenvalue/getPlanningLevelDetail', { params: data }),
//
//   createOrUpdatePlanLevel: (data: object) =>
//     request.post('/api/eigenvalue/createOrUpdatePlanLevel', data),
//
//   getKeyFigureList: (data: object) =>
//     request.get('/api/eigenvalue/getKeyFigureList', { params: data }),
//   getKeyFigureDetail: (data: object) =>
//     request.get('/api/eigenvalue/getKeyFigureDetail', { params: data }),
//
//   createOrUpdateKeyFigure: (data: object) =>
//     request.post('/eigenvalue/createOrUpdateKeyFigure', data),
// };
const getAttributeData: any = {
  code: 0,
  data: {
    list: [
      {
        attributeId: 1260,
        attributeName: 'tact_time',
        valueType: 'float8',
        queryType: ['='],
        label: '节拍时间',
        isCustom: true,
      },
      {
        attributeId: 1259,
        attributeName: 'standard_working_time',
        valueType: 'float8',
        queryType: ['IN'],
        label: '标准工时',
        isCustom: true,
      },
      {
        attributeId: 1258,
        attributeName: 'auxiliary_column',
        valueType: 'varchar',
        queryType: ['IN'],
        label: '辅助列',
        isCustom: true,
      },
      {
        attributeId: 1257,
        attributeName: 'working_procedure',
        valueType: 'varchar',
        queryType: ['IN'],
        label: '工序',
        isCustom: true,
      },
      {
        attributeId: 1256,
        attributeName: 'tp_level_name',
        valueType: 'varchar',
        queryType: ['in'],
        label: '时间维度名称',
        isCustom: true,
      },
      {
        attributeId: 1255,
        attributeName: 'rate_to_country',
        valueType: 'varchar',
        queryType: ['='],
        label: '汇率-目标国家',
        isCustom: true,
      },
      {
        attributeId: 1254,
        attributeName: 'rate_from_country',
        valueType: 'varchar',
        queryType: ['='],
        label: '汇率-原国家',
        isCustom: true,
      },
      {
        attributeId: 1252,
        attributeName: 'year',
        valueType: 'int4',
        queryType: ['in'],
        label: '年',
        isCustom: false,
      },
      {
        attributeId: 1251,
        attributeName: 'quarter',
        valueType: 'int4',
        queryType: ['in'],
        label: '季度',
        isCustom: false,
      },
      {
        attributeId: 1250,
        attributeName: 'month',
        valueType: 'int4',
        queryType: ['in'],
        label: '月',
        isCustom: false,
      },
    ],
    total: 65,
    page: 1,
    pageSize: 10,
  },
  msg: '查询成功',
};
const getMasterDataTypeListData: any = {
  code: 0,
  data: {
    list: [
      {
        masterDataTypeId: 108,
        name: 'md_GX01_wp',
        label: '工序信息',
        isCustom: true,
      },
      {
        masterDataTypeId: 106,
        name: 'md_GX01_transfer_country',
        label: '转换元素国家信息',
        isCustom: true,
      },
      {
        masterDataTypeId: 105,
        name: 'md_time_level',
        label: '时间配置文件',
        isCustom: false,
      },
      {
        masterDataTypeId: 104,
        name: 'md_GX01_cust_product',
        label: '订阅费用户产品关系信息',
        isCustom: true,
      },
      {
        masterDataTypeId: 103,
        name: 'md_GX01_customer',
        label: '订阅费客户信息',
        isCustom: true,
      },
      {
        masterDataTypeId: 102,
        name: 'md_GX01_product',
        label: '订阅费产品信息',
        isCustom: true,
      },
      {
        masterDataTypeId: 97,
        name: 'md_production_heads',
        label: '生产父项信息',
        isCustom: false,
      },
      {
        masterDataTypeId: 96,
        name: 'md_resources',
        label: '产线信息',
        isCustom: false,
      },
      {
        masterDataTypeId: 95,
        name: 'md_locations',
        label: '地点信息',
        isCustom: false,
      },
      {
        masterDataTypeId: 94,
        name: 'md_materials',
        label: '物料信息',
        isCustom: false,
      },
    ],
    total: 12,
    page: 1,
    pageSize: 10,
  },
  msg: '查询成功',
};
const getTimeProfileListData: any = {
  code: 0,
  data: {
    list: [
      {
        name: 'Default',
        profileDesc: '默认时间配置文件',
        periodStart: '2014-01-01T00:00:00Z',
        periodEnd: '2028-12-31T00:00:00Z',
        pareaId: 1,
        tpLevelList: ['周', '年', '月', '季度', '按月周', '日'],
        tpLevelMap: {
          '1': '日',
          '2': '按月周',
          '3': '周',
          '4': '月',
          '5': '季度',
          '6': '年',
        },
        details: null,
      },
    ],
    total: 0,
    page: 0,
    pageSize: 0,
  },
  msg: '查询成功',
};
const getPlanningLevelListData: any = {
  code: 0,
  data: {
    list: [
      {
        planningLevelName: 'LocMat',
        planningLevelId: 55,
        tpLevel: 0,
        isCustom: false,
      },
      {
        planningLevelName: 'LocMatCust',
        planningLevelId: 56,
        tpLevel: 0,
        isCustom: false,
      },
      {
        planningLevelName: 'LocMatVendor',
        planningLevelId: 57,
        tpLevel: 0,
        isCustom: false,
      },
      {
        planningLevelName: 'LocMatSupply',
        planningLevelId: 58,
        tpLevel: 0,
        isCustom: false,
      },
      {
        planningLevelName: 'Res',
        planningLevelId: 59,
        tpLevel: 0,
        isCustom: false,
      },
      {
        planningLevelName: '订阅费计划等级',
        planningLevelId: 67,
        tpLevel: 0,
        isCustom: true,
      },
      {
        planningLevelName: '汇率计划等级',
        planningLevelId: 68,
        tpLevel: 0,
        isCustom: true,
      },
      {
        planningLevelName: '工序计划等级',
        planningLevelId: 71,
        tpLevel: 0,
        isCustom: true,
      },
    ],
    total: 8,
    page: 1,
    pageSize: 10,
  },
  msg: '查询成功',
};
const getKeyFigureListData: any = {
  code: 0,
  data: {
    list: [
      {
        kfId: 100,
        label: 'test',
        kfName: '测试',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 99,
        label: '设备利用率',
        kfName: 'EquipmentUtilizationRate',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 97,
        label: '非计划停线损失',
        kfName: 'UnplannedDowntimeLoss',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 96,
        label: '停线人数',
        kfName: 'NumberOfPersonnelOffTheLine',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 1,
      },
      {
        kfId: 95,
        label: '停线分钟',
        kfName: 'DowntimeMinutes',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 1,
      },
      {
        kfId: 94,
        label: '计划开工时间',
        kfName: 'ScheduledStartTime',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 81,
        label: '调整后计划',
        kfName: 'AdjustedPlan',
        planLevelID: 71,
        planLevelName: '工序计划等级',
        IsCustom: true,
        kfType: 1,
      },
      {
        kfId: 76,
        label: '客户欠费(累积)',
        kfName: 'CustomerArrears(Accumulate)',
        planLevelID: 67,
        planLevelName: '订阅费计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 72,
        label: '已收订阅费(累积)$',
        kfName: 'CollectedSubscriptionFees(Accumulate)$',
        planLevelID: 67,
        planLevelName: '订阅费计划等级',
        IsCustom: true,
        kfType: 3,
      },
      {
        kfId: 71,
        label: '汇率',
        kfName: 'ExchangeRate',
        planLevelID: 68,
        planLevelName: '汇率计划等级',
        IsCustom: true,
        kfType: 2,
      },
    ],
    total: 32,
    page: 1,
    pageSize: 10,
  },
  msg: '查询成功',
};
import { HttpResponse, http } from 'msw';
const Configuration = [
  http.get('/api/eigenvalue/getAttribute', () => {
    return HttpResponse.json(getAttributeData, { status: 200 });
  }),

  http.get('/api/eigenvalue/getMasterDataTypeList', () => {
    return HttpResponse.json(getMasterDataTypeListData, { status: 200 });
  }),

  http.get('/api/eigenvalue/getTimeProfileList', () => {
    return HttpResponse.json(getTimeProfileListData, { status: 200 });
  }),

  http.get('/api/eigenvalue/getPlanningLevelList', () => {
    return HttpResponse.json(getPlanningLevelListData, { status: 200 });
  }),

  http.get('/api/eigenvalue/getKeyFigureList', () => {
    return HttpResponse.json(getKeyFigureListData, { status: 200 });
  }),
];

export default Configuration;
