import { HttpResponse, http } from 'msw';
const getCronJobTaskData: any = {
  code: 0,
  data: {
    list: [
      {
        jobId: '4189fa64-dcba-42d6-802e-5912323e2bd3',
        jobName: '任务测试time永不',
        jobMode: 'time',
        taskStartTime: 1716994800,
        taskEndTime: 0,
        taskState: 1,
        taskTag: 'getPrice',
        taskUuid: '44fe7be4-64c9-400d-96cb-7a6dc1b69803',
      },
      {
        jobId: '4189fa64-dcba-42d6-802e-5912323e2bd3',
        jobName: '任务测试time永不',
        jobMode: 'time',
        taskStartTime: 1717012800,
        taskEndTime: 0,
        taskState: 3,
        taskTag: 'getPrice',
        taskUuid: '4db5fc48-a4da-4d04-9cec-4fd3613fe3d3',
      },
      {
        jobId: '4189fa64-dcba-42d6-802e-5912323e2bd3',
        jobName: '任务测试time永不',
        jobMode: 'time',
        taskStartTime: 1717030800,
        taskEndTime: 0,
        taskState: 3,
        taskTag: 'getPrice',
        taskUuid: 'e0f65988-2432-43bf-9c8d-7f36fa7c3494',
      },
      {
        jobId: '4189fa64-dcba-42d6-802e-5912323e2bd3',
        jobName: '任务测试time永不',
        jobMode: 'time',
        taskStartTime: 1717048800,
        taskEndTime: 0,
        taskState: 3,
        taskTag: 'getPrice',
        taskUuid: 'eb50285c-80bb-4307-8143-c6fbc5df5965',
      },
      {
        jobId: '4189fa64-dcba-42d6-802e-5912323e2bd3',
        jobName: '任务测试time永不',
        jobMode: 'time',
        taskStartTime: 1717066800,
        taskEndTime: 0,
        taskState: 3,
        taskTag: 'getPrice',
        taskUuid: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
      },
    ],
  },
  total: 244,
  msg: '查询成功',
};

const getLogsScpLogger: any = {
  code: 0,
  data: {
    list: [
      {
        api: '/plan/startPlanningRun',
        details: ' ',
        file: 'biz_scpLogger.go',
        func: 'business.(*LogService).GetLog:40',
        level: 'info',
        line: 40,
        msg: '运行供应链计划',
        paPvId: 1,
        taskId: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
        time: '2024-06-18T15:21:51+08:00',
        traceId: '6103094b9057226049ad5b2ccc869741',
      },
      {
        api: '/plan/startPlanningRun',
        details: ' ',
        file: 'biz_scpLogger.go',
        func: 'business.(*LogService).GetLog:41',
        level: 'info',
        line: 41,
        msg: '准备跑引擎',
        paPvId: 1,
        taskId: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
        time: '2024-06-18T15:21:51+08:00',
        traceId: '6103094b9057226049ad5b2ccc869741',
      },
      {
        api: '/plan/startPlanningRun',
        details: ' ',
        file: 'biz_scpLogger.go',
        func: 'business.(*LogService).GetLog:42',
        level: 'info',
        line: 42,
        msg: '引擎结束',
        paPvId: 1,
        taskId: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
        time: '2024-06-18T15:21:51+08:00',
        traceId: '6103094b9057226049ad5b2ccc869741',
      },
      {
        api: '/plan/startPlanningRun',
        details: ' ',
        file: 'biz_scpLogger.go',
        func: 'business.(*LogService).GetLog:43',
        level: 'info',
        line: 43,
        msg: '引擎返回信息',
        paPvId: 1,
        taskId: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
        time: '2024-06-18T15:21:51+08:00',
        traceId: '6103094b9057226049ad5b2ccc869741',
      },
      {
        api: '/plan/startPlanningRun',
        details: ' ',
        file: 'biz_scpLogger.go',
        func: 'business.(*LogService).GetLog:44',
        level: 'info',
        line: 44,
        msg: '成功',
        paPvId: 1,
        taskId: 'f95cff70-675b-4d12-8a24-bb424ea1b46c',
        time: '2024-06-18T15:21:51+08:00',
        traceId: '6103094b9057226049ad5b2ccc869741',
      },
    ],
    total: 5,
    page: 0,
    pageSize: 0,
  },
  msg: '查询成功',
};
const JobTemplate = [
  http.get('/api/cron/getCronJobTask', () => {
    return HttpResponse.json(getCronJobTaskData, { status: 200 });
  }),
  http.get('/api/scpLog/getLogsScpLogger', () => {
    return HttpResponse.json(getLogsScpLogger, { status: 200 });
  }),
];

export default JobTemplate;
