import { http, HttpResponse } from 'msw';

const SettingPageApi = [
  http.get('/api/mdDataView/getQualifiedAreaVersion', () => {
    return HttpResponse.json(
      ResponseListOk([
        {
          parea: 'MyECar',
          pversion: 'Base',
          paPvId: 1,
          mdId: 'TR1',
          isBaseVersion: true,
          authTypeName: '默认创建',
          authType: 'Default',
          userList: [49, 51, 52, 53, 54, 109, 2, 48],
          label: 'Base',
          value: 1,
        },
        {
          parea: 'MyECar',
          pversion: 'V001',
          paPvId: 2,
          mdId: 'TR2',
          isBaseVersion: false,
          authTypeName: '他人分享',
          authType: 'Shared',
          userList: [2, 48, 49, 51, 52, 53, 54, 109],
          label: 'V001',
          value: 2,
        },
        {
          parea: 'MyECar',
          pversion: 'V002',
          paPvId: 62,
          mdId: 'TR1',
          isBaseVersion: false,
          authTypeName: '他人分享',
          authType: 'Shared',
          userList: [2, 49, 51, 52, 53, 54, 109],
          label: 'V002',
          value: 62,
        },
        {
          parea: 'MyECar',
          pversion: 'qyTest',
          paPvId: 103,
          mdId: 'TR2',
          isBaseVersion: false,
          authTypeName: '他人分享',
          authType: 'Shared',
          userList: [49, 109, 53, 54, 51],
          label: 'qyTest',
          value: 103,
        },
      ]),
      { status: 200 },
    );
  }),
  http.get('/api/mdDataView/getMdIdList', () => {
    return HttpResponse.json(
      ResponseListOk([
        {
          mdId: 'TR2',
        },
        {
          mdId: 'CTDMD',
        },
        {
          mdId: 'ZWDMD',
        },
        {
          mdId: 'COL_WP',
        },
        {
          mdId: 'JWDYMD',
        },
        {
          mdId: 'SN_MD',
        },
        {
          mdId: 'TestMd',
        },
        {
          mdId: 'ECC1',
        },
        {
          mdId: 'JYDEMO',
        },
        {
          mdId: 'QYDEMO',
        },
        {
          mdId: 'ECC1M',
        },
        {
          mdId: 'TR1',
        },
        {
          mdId: 'De_MD',
        },
      ]),
      { status: 200 },
    );
  }),
  http.get('/api/user/getUserListByCoId', () => {
    return HttpResponse.json(
      ResponseListOk([
        {
          id: 1,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: '12092a84-9dd3-4846-9c7a-459556e4df91',
          userName: 'a303176530',
          nickName: '用户1',
          sideMode: 'dark',
          headerImg: 'https:///qmplusimg.henrongyi.top/1572075907logo.png',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 9528,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 109,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: '91387ab0-f13e-9382-2058-c7549318af89',
          userName: 'yangweiyi',
          nickName: '杨唯一',
          sideMode: 'light',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 53,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: 'a2af0bc9-ee4f-469d-9fb2-0a273a97963f',
          userName: 'wengxiangshun',
          nickName: '翁祥顺',
          sideMode: 'light',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 54,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: '4d70c4a7-b1a7-4d6a-87fc-392f68dff286',
          userName: 'huangwei',
          nickName: '黄伟',
          sideMode: 'light',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 51,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1732757708,
          updatedBy: 1,
          uuid: '21f4e3cd-5b42-4c79-8401-8aac08c884ab',
          userName: 'tongrui',
          nickName: '童锐',
          sideMode: 'light',
          headerImg:
            'https://scp-picture.oss-cn-hangzhou.aliyuncs.com/devpre/1/profilePhoto/avatar1732757708.png',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 2,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1732270892,
          updatedBy: 1,
          uuid: '935b43c3-d929-4b8c-b371-b42aac13ac31',
          userName: 'admin',
          nickName: 'Mr.奇淼',
          sideMode: 'light',
          headerImg:
            'https://scp-picture.oss-cn-hangzhou.aliyuncs.com/devpre/1/profilePhoto/avatar.png',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 49,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: '5563870b-9b24-47f0-8c5a-6e58770685bf',
          userName: 'qianyun',
          nickName: '钱云',
          sideMode: 'light',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 52,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1697008206,
          updatedBy: 1,
          uuid: '80607c48-656d-40c7-ae29-c12b2324df51',
          userName: 'sunsining',
          nickName: '孙思宁',
          sideMode: 'light',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
        {
          id: 48,
          createdAt: 1697008206,
          createdBy: 1,
          updatedAt: 1732268792,
          updatedBy: 1,
          uuid: '0276c73b-2d46-486c-8495-423003e98b1b',
          userName: 'admin2',
          nickName: 'Mr.奇淼2',
          sideMode: 'light',
          headerImg:
            'https://scp-picture.oss-cn-hangzhou.aliyuncs.com/devpre/48/profilePhoto/colorful-logo.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: 0,
            CreatedBy: 0,
            UpdatedAt: 0,
            UpdatedBy: 0,
            authorityId: 0,
            authorityName: '',
            parentId: null,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '17611111111',
          email: '<EMAIL>',
          enable: 1,
          coId: 1,
          coCode: '1001',
          preferPaPvId: null,
          pareaId: 0,
        },
      ]),
      { status: 200 },
    );
  }),
];

function ResponseListOk(data: any) {
  return {
    code: 0,
    msg: '查询成功',
    data: {
      list: data,
    },
  };
}

// function ResponseResultOk(data: any) {
//   return HttpResponse.json(
//     {
//       code: 0,
//       msg: '查询成功',
//       data: {
//         result: data,
//       },
//     },
//     { status: 200 },
//   );
// }

export default SettingPageApi;
