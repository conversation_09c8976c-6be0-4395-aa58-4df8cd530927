const authority = {
  code: 0,
  data: {
    list: [
      'home',
      'big',
      'applicationjob',
      'planningview',
      'supplyusage',
      'projectedstock',
      'viwemasterdata',
      'configuation',
      'applicationJobDetail',
    ],
  },
};
import { HttpResponse, http } from 'msw';
const Authority = [
  http.post('/api/authority/getMenus', () => {
    return HttpResponse.json(authority, { status: 200 });
  }),
];
export default Authority;
