import { http, HttpResponse } from 'msw';

const UserPageApi = [
  http.post('/api/user/changePassword', () => {
    return HttpResponse.json({ code: 0, msg: '密码修改成功' }, { status: 200 });
  }),

  http.post('/api/appBuilder/switchAi', () => {
    return HttpResponse.json(
      { code: 0, msg: 'AI助手状态切换成功' },
      { status: 200 },
    );
  }),

  http.get('/api/user/getUserInfo', () => {
    return HttpResponse.json({
      code: 0,
      data: {
        userInfo: {
          nickName: '测试用户',
          email: '',
          coCode: '测试公司',
          headerImg: '/path/to/avatar.png',
        },
      },
    });
  }),
];

export default UserPageApi;
