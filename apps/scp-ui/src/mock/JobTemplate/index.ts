import { http, HttpResponse } from 'msw';

const getJobTemplateByUserOnlyRead = {
  code: 0,
  data: {
    list: [
      {
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '复制计划版本',
        templateUuid: '159461d0-0aad-f782-35dd-59256149a9e7',
      },
      {
        isInt: true,
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '文件导入主数据（标准）',
        templateUuid: 'b117ca9d-6c7d-5fa7-cf5d-d1f3bc43bbaf',
      },
      {
        isInt: true,
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '文件导入业务数据（标准）',
        templateUuid: '21409eb7-8544-8782-c67f-ce23e8d95993',
      },
      {
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '重新生成计划对象',
        templateUuid: 'f3ace29b-4999-7e35-3754-0c4e83cd33fa',
      },
      {
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '复制关键指标',
        templateUuid: '550e8400-e29b-41d4-a716-************',
      },
      {
        isOrigin: true,
        isSystem: true,
        isSystemName: '是',
        sourceCode: 1,
        sourceName: '原始模板',
        stepsNum: 1,
        templateName: '销售与运营计划',
        templateUuid: 'd0c66c11-c04d-e163-868f-716ebbb9d931',
      },
    ],
    total: 6,
    page: 1,
    pageSize: 10,
  },
  msg: '操作成功',
};

const ApplicationJobDetails = [
  http.get('/api/plan/getJobTemplateByUserOnlyRead', () => {
    return HttpResponse.json(getJobTemplateByUserOnlyRead, { status: 200 });
  }),
];

export default ApplicationJobDetails;
