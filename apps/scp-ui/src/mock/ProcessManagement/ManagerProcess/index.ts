import { http, HttpResponse } from 'msw';

export default [
  http.get('/api/processTemplate/getTableData', () => {
    return HttpResponse.json({
      code: 0,
      data: {
        list: Array.from({ length: 10 }).map((_, index) => ({
          id: index,
          name: `模板${index}`,
          owner: `用户${index}`,
          status: index % 2 === 0 ? '完成' : '进行中',
          startTime: '2021-08-01',
          frequency: 1, // 修改为数字类型
          remainingFrequency: 1, // 修改为数字类型
        })),
      },
    });
  }),
];
