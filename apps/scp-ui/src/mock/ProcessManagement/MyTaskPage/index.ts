import { HttpResponse, http } from 'msw';
import { Task } from '@/pages/ProcessMagement/MyTaskPage/type';

export default [
  http.get('/api/tasks', () => {
    const data: Task[] = Array.from({ length: 10 }).map((_, index) => ({
      id: index,
      title: `任务${index}`,
      description: `这是任务${index}的描述`,
      status: index % 2 === 0 ? '完成' : '进行中',
      owner: `用户${index}`, // 模拟任务的所有者
    }));
    return HttpResponse.json({
      code: 0,
      data: {
        list: data,
      },
    });
  }),
];
