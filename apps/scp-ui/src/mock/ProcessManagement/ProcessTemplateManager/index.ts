import { HttpResponse, http } from 'msw';
import { type OwnerListType } from '@/pages/ProcessMagement/ProcessTemplateManager/types';
export default [
  http.get('/api/processTemplate/getOwnerList', () => {
    const data: OwnerListType[] = Array.from({ length: 10 }).map(
      (_, index) => ({
        id: index,
        label: `test${index}`,
        value: `test${index}`,
      }),
    );
    return HttpResponse.json({
      code: 0,
      data: {
        list: data,
      },
    });
  }),

  http.get('/api/processTemplate/getTableData', () => {
    return HttpResponse.json({
      code: 0,
      data: {
        list: Array.from({ length: 20 }).map((_, index) => ({
          key: index,
          id: index,
          name: `name${index}`,
          desc: `desc${index}`,
          owner: `owner${index}`,
          changeTime: `changeTime${index}`,
          steps: index,
        })),
      },
    });
  }),
  http.get('/api/editProcessTemplate/getTaskList', () => {
    return HttpResponse.json({
      code: 0,
      data: {
        list: Array.from({ length: 3 }).map((_, index) => ({
          id: index,
          taskName: `task${index}`,
          desc: `desc${index}`,
          worker: `worker${index}`,
          status: `changeTime${index}`,
          index: index + 1,
        })),
      },
    });
  }),
];
