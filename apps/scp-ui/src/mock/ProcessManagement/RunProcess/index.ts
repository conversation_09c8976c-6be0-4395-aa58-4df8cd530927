import { http, HttpResponse } from 'msw';
import type { StepDetail } from '@/pages/ProcessMagement/RunProcess/type';

export default [
  http.get('/api/runProcess/getRunProcessList', () => {
    return HttpResponse.json({
      code: 0,
      data: {
        list: Array.from({ length: 20 }).map(
          (_, index): StepDetail => ({
            key: index,
            id: index,
            stepName: `stepName${index}`,
            participant: `participant${index}`,
            reviewer: `reviewer${index}`,
            duration: `duration${index}`,
            status: `status${index}`,
            tasknums: index,
          }),
        ),
      },
    });
  }),
];
