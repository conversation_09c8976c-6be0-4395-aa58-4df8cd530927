const getAllFilterConditionsByMenuData = {
  code: 0,
  data: {
    list: [
      {
        id: 1,
        createdAt: 1712911609,
        createdBy: 0,
        updatedAt: 1712911609,
        updatedBy: 0,
        conditionContent: {
          type: 'string',
          field: 'matnr',
          label: '物料名称',
        },
      },
      {
        id: 2,
        createdAt: 1712911609,
        createdBy: 0,
        updatedAt: 1712911609,
        updatedBy: 0,
        conditionContent: {
          type: 'string',
          field: 'locno',
          label: '地点名称',
        },
      },
      {
        id: 3,
        createdAt: 1712911609,
        createdBy: 0,
        updatedAt: 1712911609,
        updatedBy: 0,
        conditionContent: {
          type: 'number',
          field: 'receipt_time_in_days',
          label: '收获期',
        },
      },
      {
        id: 4,
        createdAt: 1712911609,
        createdBy: 0,
        updatedAt: 1712911609,
        updatedBy: 0,
        conditionContent: {
          type: 'string',
          field: 'dispo',
          label: '物料计划员',
        },
      },
      {
        id: 5,
        createdAt: 1712911609,
        createdBy: 0,
        updatedAt: 1712911609,
        updatedBy: 0,
        conditionContent: {
          type: 'string',
          field: 'procu_type',
          label: '补货类型',
        },
      },
    ],
  },
  msg: '查询成功',
};
const getFilterSchemeInfoByMenuAndUserData = {
  code: 0,
  data: {
    list: [
      {
        schemeName: '初始方案',
        isDefault: true,
        isSystem: true,
        schemeId: 75,
        conditionList: [
          {
            id: 2,
            createdAt: 0,
            createdBy: 0,
            updatedAt: 0,
            updatedBy: 0,
            conditionContent: {
              type: 'string',
              field: 'locno',
              label: '地点名称',
            },
            preferValue: '',
            preferOperator: 'like',
          },
        ],
      },
      {
        schemeName: '推荐方案1',
        isDefault: false,
        isSystem: true,
        schemeId: 25,
        conditionList: [
          {
            id: 1,
            createdAt: 0,
            createdBy: 0,
            updatedAt: 0,
            updatedBy: 0,
            conditionContent: {
              type: 'string',
              field: 'matnr',
              label: '物料名称',
            },
            preferValue: '',
            preferOperator: 'like',
          },
        ],
      },
    ],
  },
  msg: '查询成功',
};
import { HttpResponse, http } from 'msw';
const HeaderFilter = [
  http.get('/api/filter/getAllFilterConditionsByMenu', () => {
    return HttpResponse.json(getAllFilterConditionsByMenuData, { status: 200 });
  }),
  http.get('/api/filter/getFilterSchemeInfoByMenuAndUser', () => {
    return HttpResponse.json(getFilterSchemeInfoByMenuAndUserData, {
      status: 200,
    });
  }),
];

export default HeaderFilter;
