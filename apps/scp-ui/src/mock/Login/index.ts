// import { HttpResponse, http } from 'msw';

// const loginPreData: any = {
//   code: 0,
//   data: {
//     serialNo: 'Tx26hyfi7jbdC81D0sYt',
//     userId: 50,
//     list: [
//       {
//         pareaId: 1,
//         parea: 'MyECar',
//         isPrefer: true,
//       },
//     ],
//   },
//   msg: '操作成功',
// };

// const loginData: any = {
//   code: 0,
//   data: {
//     user: {
//       id: 50,
//       createdAt: 1697008206,
//       createdBy: 1,
//       updatedAt: 1732759802,
//       updatedBy: 1,
//       uuid: '6a281ab8-71a0-4ce9-ae3a-20247978950e',
//       userName: 'liujinxu',
//       nickName: '刘金旭',
//       sideMode: 'light',
//       headerImg:
//         'https://scp-picture.oss-cn-hangzhou.aliyuncs.com/devpre/1/profilePhoto/d53ea718-ef47-489e-97bb-c623dabeb13a.png',
//       baseColor: '#fff',
//       activeColor: '#1890ff',
//       authorityId: 888,
//       authority: {
//         CreatedAt: 0,
//         CreatedBy: 0,
//         UpdatedAt: 0,
//         UpdatedBy: 0,
//         authorityId: 0,
//         authorityName: '',
//         parentId: null,
//         dataAuthorityId: null,
//         children: null,
//         menus: null,
//         defaultRouter: '404',
//       },
//       authorities: [],
//       phone: '17611111111',
//       email: '<EMAIL>',
//       enable: 1,
//       coId: 1,
//       coCode: '1001',
//       preferPaPvId: 1,
//       pareaId: 1,
//     },
//     token:
//       'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiNmEyODFhYjgtNzFhMC00Y2U5LWFlM2EtMjAyNDc5Nzg5NTBlIiwiSUQiOjUwLCJVc2VybmFtZSI6ImxpdWppbnh1IiwiTmlja05hbWUiOiLliJjph5Hml60iLCJBdXRob3JpdHlJZCI6ODg4LCJQYXJlYUlkIjoxLCJDb0lkIjoxLCJCdWZmZXJUaW1lIjo4NjQwMDAsImlzcyI6InFtUGx1cyIsImF1ZCI6WyJHVkEiXSwiZXhwIjoxNzM4ODk0NzI0LCJuYmYiOjE3MzYzMDI3MjR9.txCmgClQWtKwp-zpsVmRYJnGkB7fhpNOy_BcJw5VGxM',
//     expiresAt: 1738894724000,
//   },
//   msg: '操作成功',
// };

export default [
  //获取登陆验证码/base/captcha
  // getCaptcha: () => request.post('/api/base/captcha'),
  //登陆/base/login
  // login: (data: any) => request.post('/api/base/login', data),
  //预登陆接口
  // loginPre: (data: any) => request.post('/api/base/loginPre', data),
  //跳转int验证token
  // scpJumpToInt: () => request.post('/jump/scpJumpToInt'),
  // http.post('/api1/base/loginPre', () => {
  //   return HttpResponse.json(loginPreData, { status: 200 });
  // }),
  // http.post('/api1/base/login', () => {
  //   return HttpResponse.json(loginData, { status: 200 });
  // }),
];
