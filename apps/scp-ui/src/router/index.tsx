import type { RouteObject } from 'react-router-dom';
import { matchPath, Outlet } from 'react-router-dom';
import { AllowedRoute } from './type';
import React, { Suspense } from 'react';
import HomeIcon from '@/assets/Home.svg?react';
import DemandfulIcon from '@/assets/Demandful.svg?react';
import SupplyusageIcon from '@/assets/Supplyusage.svg?react';
import ProjectedStockIcon from '@/assets/ProjectedStock.svg?react';
import PlanningViewIcon from '@/assets/PlanningView.svg?react';
import ApplicationJobIcon from '@/assets/ApplicationJob.svg?react';
import ViewMasterDataIcon from '@/assets/ViewMasterData.svg?react';
import BIGIcon from '@/assets/BIG.svg?react';
import ConfiguationIcon from '@/assets/Configuation.svg?react';
import SettingIcon from '@/assets/Setting.svg?react';
import Vector from '@/assets/Vector.svg?react';

import AllPage from '@/pages';
import ErrorBoundary from '@/components/ErrorBoundary';

export type RouteInfoParams = {
  name?: string;
  icon?: React.ReactNode;
  path?: string;
  children?: RouteInfoParams[];
};

export const route_info: RouteInfoParams[] = [
  {
    icon: <HomeIcon />,
    path: '/home',
  },
  {
    icon: <DemandfulIcon />,
    path: '/demandful',
  },
  {
    path: '/supplyusage',
    icon: <SupplyusageIcon />,
  },
  {
    path: '/projectedstock',
    icon: <ProjectedStockIcon />,
  },
  {
    path: '/planningview',
    icon: <PlanningViewIcon />,
  },
  {
    path: '/big',
    icon: <BIGIcon />,
  },
  {
    path: '/applicationjob',
    icon: <ApplicationJobIcon />,
  },
  {
    path: '/viewMasterData',
    icon: <ViewMasterDataIcon />,
  },
  {
    path: '/configuation',
    icon: <ConfiguationIcon />,
  },
  {
    path: '/jobTemplate',
    icon: <img src='/JobTemplate.svg' alt='jobtemplate' />,
  },
  {
    path: '/setting',
    icon: <SettingIcon />,
  },
  {
    path: '/processManager',
    icon: <Vector />,
  },
];

const componentMap: {
  [key: string]: React.LazyExoticComponent<React.FC<any>>;
} = { ...AllPage };

function splitRoutePath(path: string) {
  if (!path.includes(':')) {
    return path.split('/').filter(Boolean);
  } else {
    const list = path.split('/').filter(Boolean);
    const lastPart = list.pop();
    list[list.length - 1] += '/' + lastPart;
    return list;
  }
}

function findBestMatch(path: string, allowedRoutes: Array<AllowedRoute>) {
  let bestMatch = null;
  let highestScore = 0;

  for (const route of allowedRoutes) {
    const match = matchPath(route.path, path);
    if (match) {
      const score = match.pathname.split('/').filter(Boolean).length;
      if (score > highestScore) {
        highestScore = score;
        bestMatch = route;
      }
    }
  }
  return bestMatch;
}
export function createNestedRoutes(routes: Array<AllowedRoute>): RouteObject[] {
  const routeMap: { [key: string]: RouteObject } = {};

  routes
    .sort((a, b) => {
      return a.path.length - b.path.length;
    })
    .forEach((route) => {
      const pathParts = splitRoutePath(route.path);
      console.log(pathParts, 'pathParts', pathParts.length);
      let currentLevel = routeMap;

      pathParts.forEach((part, index) => {
        const isLast = index === pathParts.length - 1;
        const currentPath = part;

        if (!currentLevel[currentPath]) {
          currentLevel[currentPath] = {
            path: isLast ? part : undefined,
            children: [],
          };
        }
        if (!currentLevel[currentPath].children) {
          currentLevel[currentPath].children = [];
        }
        if (isLast) {
          if (route.index) {
            currentLevel[currentPath].children.push({
              index: true,
              element: (
                <Suspense fallback={<div>Loading...</div>}>
                  <>
                    {route.component === 'Outlet' ? (
                      <Outlet />
                    ) : route.component === '' ||
                      route.component === undefined ||
                      componentMap[route.component] === undefined ? (
                      <AllPage.NotFound
                        findBestMatch={(path: any) => findBestMatch(path, [])}
                      />
                    ) : (
                      React.createElement(componentMap[route.component])
                    )}
                  </>
                </Suspense>
              ),
            });
          } else {
            currentLevel[currentPath].element = (
              <Suspense fallback={<div>Loading...</div>}>
                <>
                  {route.component === 'Outlet' ? (
                    <Outlet />
                  ) : route.component === '' ||
                    route.component === undefined ||
                    componentMap[route.component] === undefined ? (
                    <AllPage.NotFound
                      findBestMatch={(path: any) => findBestMatch(path, [])}
                    />
                  ) : (
                    React.createElement(componentMap[route.component])
                  )}
                </>
              </Suspense>
            );
          }
        }

        currentLevel = currentLevel[currentPath].children as unknown as {
          [key: string]: RouteObject;
        };
      });
    });

  // 递归处理 children
  const convertToArray = (obj: {
    [key: string]: RouteObject;
  }): RouteObject[] => {
    return Object.values(obj).map((route) => {
      route.errorElement = <ErrorBoundary />;
      if (route.children && Object.keys(route.children).length > 0) {
        return {
          ...route,
          children: convertToArray(
            route.children as unknown as { [key: string]: RouteObject },
          ),
        };
      }
      return route;
    });
  };

  return convertToArray(routeMap);
}

export default createNestedRoutes;

// export type AllowedRoute = { index?: boolean; path: string; component: string };
