import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import store, { persistor } from './store/index.ts';
import { NextUIProvider } from '@nextui-org/react';
import '@nutui/nutui-react/dist/style.css';
import '@/languages';

import { i18nScope } from './languages';
import { BrowserRouter } from 'react-router-dom';
import { AliveScope } from 'react-activation';

async function enableMocking() {
  if (
    import.meta.env.MODE !== 'development' ||
    import.meta.env.VITE_ENABLE_MSW !== 'true'
  )
    return;
  const { worker } = await import('./mock/worker.ts');
  console.log('worker', worker);
  return worker.start();
}

enableMocking().then(() =>
  i18nScope.ready(() => {
    ReactDOM.createRoot(document.getElementById('root')!).render(
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <BrowserRouter>
            <NextUIProvider>
              <AliveScope>
                <App />
              </AliveScope>
            </NextUIProvider>
          </BrowserRouter>
        </PersistGate>
      </Provider>,
    );
  }),
);
