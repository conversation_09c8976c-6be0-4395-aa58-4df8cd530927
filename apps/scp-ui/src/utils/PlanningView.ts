import dayjs, { Dayjs } from 'dayjs';
import updateLocale from 'dayjs/plugin/updateLocale';
dayjs.extend(updateLocale);
dayjs.updateLocale('zh-cn', {
  weekStart: 1,
});
type DateType = 'date' | 'week' | 'month' | 'quarter' | 'year';
const timeMap = new Map<number, DateType>([
  [1, 'date'],
  [2, 'date'],
  [3, 'week'],
  [4, 'month'],
  [5, 'quarter'],
  [6, 'year'],
]);
function formatDate(date: Dayjs, type: DateType): string {
  switch (type) {
    case 'date':
      return dayjs(date).locale('zh-cn').format('YYYY-MM-DD');
    case 'week':
      return dayjs(date).locale('zh-cn').startOf('week').format('YYYY-MM-DD');
    case 'month':
      return dayjs(date).locale('zh-cn').startOf('month').format('YYYY-MM-DD');
    case 'quarter':
      return dayjs(date)
        .locale('zh-cn')
        .startOf('quarter')
        .format('YYYY-MM-DD');
    case 'year':
      return dayjs(date).locale('zh-cn').startOf('year').format('YYYY-MM-DD');
    default:
      return dayjs(date).locale('zh-cn').format();
  }
}

export function getFormattedDate(date: Dayjs, formatType: number): string {
  const type = timeMap.get(formatType);
  if (!type) {
    throw new Error(`Unsupported format type: ${formatType}`);
  }
  return formatDate(date, type);
}
