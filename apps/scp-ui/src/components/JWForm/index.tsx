import { useEffect, useState, useRef, useCallback, forwardRef } from 'react';
import type { ColumnsState, ProColumns } from '@ant-design/pro-components';
import { useUpdateEffect } from 'ahooks';
import { Form, message, Flex } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import HeaderFilter from '../HeaderFilter';
import { formatDate } from '@/utils';
import { Pagination } from '../Pagination/Pagination';
import ajax from '@/api';
import _ from 'lodash';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

interface JWFormProps {
  pageName?: string;
  columns?: any[];
  extraFormItem?: any;
  callback?: any;
  watchTagName?: any;
  setFilterData?: any;
  processTableData?: any;
  processTableColumns?: any;
  selectRowIndex?: any;
  rowOnClick?: any;
  compressed?: boolean;
  pagationContainerBackground?: string;
  onChangePaPvId?: (paPvId: number) => void;
}

const JWForm = forwardRef(
  (
    {
      pageName = '',
      extraFormItem,
      callback,
      watchTagName,
      setFilterData,
      processTableData = (data: any) => data,
      processTableColumns = (columns: any) => columns,
      rowOnClick = (_: any, __: number) => {},
      selectRowIndex,
      compressed = false,
      onChangePaPvId,
      pagationContainerBackground = 'transparent',
    }: JWFormProps,
    ref: any,
  ) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [schemeId, setSchemeId] = useState<number>(-1);

    // const fullRef = useRef<any>(null);
    // const fullSize = useSize(fullRef);

    const [pageData, setPageData] = useState<any>({
      page: 1,
      pageSize: 10,
    });
    const [total, setTotal] = useState<number>(0);
    const [form] = Form.useForm();
    const abortControllerRef = useRef<AbortController | null>(null);

    const [proColumns, setProColumns] = useState<ProColumns<any>[]>([]);

    const [columnsStateMap, setColumnsStateMap] = useState<
      Record<string, ColumnsState>
    >({
      name: {
        show: false,
        order: 2,
      },
    });

    const [loading, setLoading] = useState<boolean>(false);
    const [dataSource, setDataSource] = useState<any[]>([]);

    const search = useCallback(async () => {
      // 如果存在之前的请求，取消它
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController();

      try {
        // 新请求开始时，确保设置 loading 为 true
        setLoading(true);

        const values = form.getFieldsValue();
        values.filter = values?.filter?.map((item: any) => {
          if (item.type === 'date')
            return {
              field: item.field || '',
              opt: item.opt || '',
              value:
                formatDate(
                  item.value,
                  item.opt === 'month'
                    ? 'month'
                    : item.opt === 'year'
                      ? 'year'
                      : 'date',
                ) || '',
              type: item.type || '',
            };
          return {
            field: item.field || '',
            opt: item.opt || '',
            value: item.value.toString() || '',
            type: item.type || '',
          };
        });

        const res = await ajax.getFilterResult(
          {
            ...values,
            page: pageData.page,
            pageSize: pageData.pageSize,
            menuTag: pageName,
          },
          { signal: abortControllerRef.current.signal },
        );

        if (res.code === 0) {
          const list = _.get(res, 'data.list', []) || [];
          const total = _.get(res, 'data.total', 0) || 0;

          const result = list.map((item: any) => {
            const reg = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z/;

            Object.keys(item).forEach((key) => {
              if (reg.test(item[key])) {
                item[key] = dayjs(item[key]).format('YYYY-MM-DD HH:mm:ss');
              }
            });
            return {
              ...item,
            };
          });

          setDataSource(await processTableData(result));
          setTotal(total);
          setLoading(false);
        } else if (res.code !== undefined) {
          setDataSource([]);
          setTotal(0);
          messageApi.error(res.msg);
          setLoading(false);
        } else {
          setDataSource([]);
          setTotal(0);
        }
      } catch (err: any) {
        if (err.name === 'AbortError') {
          // 如果请求被取消，直接返回，不设置 loading 为 false，等待新的请求完成时再取消 loading
          console.log('Request was aborted');
          return; // 取消请求后不继续执行 finally
        } else {
          setLoading(false);
          console.error(err);
          messageApi.error('An error occurred while fetching data');
        }
      } finally {
        // 新请求完成后才设置 loading 为 false
        callback && callback();
      }
    }, [form, pageData, messageApi]);

    //获取列表的数据
    const getTableData = async () => {
      try {
        const res = await ajax.getTableConfigColumn({
          schemeId,
          menuTag: pageName,
        });
        const result = _.get(res, 'data.result', []) || [];
        const _columns: Record<string, ColumnsState> = {};
        result.forEach((item: any) => {
          _columns[item.field] = {
            show: item.isVisible,
            order: item.order,
          };
        });
        setColumnsStateMap(_columns);

        const columns = result.map((item: any) => {
          return {
            title: item.name,
            dataIndex: item.field,
            key: item.field,
            width: item.width,
            onCell: (record: any, rowIndex: number) => {
              return {
                style: {
                  textAlign: 'center',
                  cursor: 'pointer',
                },
                onClick: () => rowOnClick(record, rowIndex),
              };
            },
            onHeaderCell: () => {
              return {
                style: {
                  textAlign: 'center',
                },
              };
            },
          };
        });
        setProColumns(processTableColumns(columns));
      } catch (error) {
        console.error(error);
      }
    };

    //保存列表的数据
    const saveTableData = async () => {
      try {
        const columns = proColumns.map((item) => {
          return {
            field: item.dataIndex,
            isVisible: columnsStateMap[item.dataIndex].show,
            name: item.title,
            order: columnsStateMap[item.dataIndex].order,
            width: item.width,
          };
        });
        const res = await ajax.saveTableConfigColumn({
          schemeId,
          menuTag: pageName,
          columns,
        });
        if (!res.code) {
          messageApi.success('保存成功');
        } else {
          messageApi.error(res.msg);
        }
      } catch (error) {
        console.error(error);
      }
    };

    useEffect(() => {
      return () => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      };
    }, []);

    useEffect(() => {
      getTableData();
    }, [schemeId]);

    useUpdateEffect(() => {
      search();
    }, [pageData]);

    return (
      <Flex
        // ref={fullRef}
        vertical={true}
        style={{
          height: '100%',
        }}>
        <Flex
          ref={ref}
          vertical={true}
          style={{
            // height: !compressed ? (fullSize?.height || 0) - 80 : '',
            height: !compressed ? '80%' : 'auto',
          }}>
          <header>
            <HeaderFilter
              onChangePaPvId={onChangePaPvId}
              extraFormItem={extraFormItem}
              pageName={pageName}
              form={form}
              watchTagName={watchTagName}
              onSearch={() => {
                setPageData({ ...pageData, page: 1 });
              }}
              setPFilterData={setFilterData}
              onChangeScheme={(schemeId: number) => {
                setSchemeId(schemeId);
              }}
              onSaveTableColumns={saveTableData}
            />
          </header>
          <main
            ref={ref}
            className={`bg-white rounded-2xl`}
            style={{
              marginTop: '10px',
              position: 'relative',
              overflowY: 'auto',
            }}>
            <ProTable
              rowKey={(_, index) => _.id || index}
              loading={loading}
              columns={proColumns}
              columnsState={{
                value: columnsStateMap,
                onChange: setColumnsStateMap,
              }}
              dataSource={
                !compressed ? dataSource : [dataSource[selectRowIndex]]
              }
              pagination={false}
              search={false}
              options={
                compressed
                  ? false
                  : {
                      reload: () => {
                        search();
                      },
                    }
              }
            />
          </main>
        </Flex>
        {contextHolder}
        <Flex
          justify='center'
          align='center'
          className={`${pagationContainerBackground} pt-4`}
          style={{
            height: !compressed ? '20%' : '0px',
          }}>
          {!compressed ? (
            <Pagination
              current={pageData.page}
              pageSize={pageData.pageSize}
              total={total}
              onPageChange={(page) => {
                setPageData({ ...pageData, page });
              }}></Pagination>
          ) : null}
        </Flex>
      </Flex>
    );
  },
);

export default JWForm;
