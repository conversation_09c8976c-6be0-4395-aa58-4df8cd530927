import { Select, SelectItem } from '@nextui-org/react';
import React, { useState } from 'react';

type PropsType = {
  item: any;
  chooseFlag: any;
  getSingleSelectIndex: any;
};

const SingleSelect: React.FC<PropsType> = (props) => {
  const { item, chooseFlag, getSingleSelectIndex } = props;
  const [selectDisabledKeys, setSelectDisabledKeys] = useState<any>('0');
  const extendChange = (values: any) => {
    getSingleSelectIndex(values, item.key);
    setSelectDisabledKeys(values);
  };
  return (
    <Select
      label={`选择某${item.value}`}
      className={
        `${chooseFlag === 1 || chooseFlag === 2 ? 'w-[49%]' : 'w-[32%]'}` +
        ' text-lg mr-[2%]'
      }
      size={'sm'}
      defaultSelectedKeys={[item.itemList[0].key.toString()]}
      disabledKeys={[selectDisabledKeys]}
      onChange={(values: any) => {
        extendChange(values.target.value);
      }}>
      {item.itemList.map((i: any) => (
        <SelectItem key={i.key.toString()} value={i.value}>
          {i.value.length > 11 ? i.value.substring(6) : i.value}
        </SelectItem>
      ))}
    </Select>
  );
};

export default SingleSelect;
