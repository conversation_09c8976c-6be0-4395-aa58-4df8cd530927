import { Button, Form, Select } from 'antd';
import { useEffect } from 'react';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSelector } from 'react-redux';
import { FormInstance } from 'antd';
type PropsType = {
  onSearch: (values: any) => void;
  form: FormInstance<any>;
};

const SearchByPlanVersion: React.FC<PropsType> = (props) => {
  const { onSearch, form } = props;
  const { t } = useVoerkaI18n();
  const user = useSelector((state: any) => state.login.user);
  const pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const onFinish = (values: any) => {
    onSearch(values);
  };
  useEffect(() => {
    const values = form.getFieldsValue();
    onSearch(values);
  }, []);
  return (
    <Form layout='inline' onFinish={onFinish} form={form}>
      <Form.Item
        name='paPvId'
        initialValue={user?.preferPaPvId === 0 ? null : user?.preferPaPvId}>
        <div className='flex text-center '>
          <div
            style={{
              color: 'var(--G1, #0D152D)',
              fontFamily: 'PingFang SC',
              height: '2.71429rem',
              fontSize: '1.57143rem',
              fontStyle: 'normal',
              fontWeight: '500',
              lineHeight: '2.71429rem',
              letterSpacing: ' 0.15714rem',
            }}>
            {t('计划版本')}：
          </div>
          <Select
            style={{
              width: '11.21429rem',
              height: '2.71429rem',
              borderRadius: ' 0.57143rem',
              border: '1px solid var(--Brand-2, #1890FF)',
            }}
            options={pVersionList}
            defaultValue='Base'
          />
        </div>
      </Form.Item>
      <Form.Item>
        <Button
          type='primary'
          htmlType='submit'
          onClick={onFinish}
          className='bg-brand-light'
          style={{
            display: 'flex',
            height: '2.71429rem',
            padding: '0.71429rem 1.64286rem',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '0.71429rem',
            flexShrink: ' 0',
            borderRadius: '1.35714rem',
            border: '1px solid var(--Brand, #2F6BFF)',
          }}>
          <span
            style={{
              color: '#FFF',
              fontFamily: 'PingFang SC',
              fontSize: '1.42857rem',
              fontStyle: 'normal',
              fontWeight: '600',
              lineHeight: 'normal',
              letterSpacing: '0.05714rem',
            }}>
            {t('查询')}
          </span>
        </Button>
      </Form.Item>
    </Form>
  );
};
export default SearchByPlanVersion;
