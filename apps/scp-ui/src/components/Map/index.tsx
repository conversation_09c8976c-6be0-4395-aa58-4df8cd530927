import mapboxgl, { StyleSpecification } from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { useEffect, useRef } from 'react';
import * as map from 'maplibre-gl';
import Marker from './Marker';
import { CustomerData } from '@/pages/Home';
const Map = ({
  setMap,
  data,
}: {
  setMap: (map: map.Map, marker: Marker) => void;
  data: CustomerData[];
}) => {
  const mapRef = useRef(null);
  const markerRef = useRef<Marker | null>(null);
  const MY_KEY = '607539e3e2cde7915e4209c26e34428e';
  const initOption: StyleSpecification = {
    version: 8,
    // id: '43f36e14-e3f5-43c1-84c0-50a9c80dc5c7',
    sources: {
      'tdt-vec': {
        type: 'raster',
        tiles: [
          `https://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=${MY_KEY}`,
        ],
        tileSize: 256,
      },
      'tdt-cva': {
        type: 'raster',
        tiles: [
          `https://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=${MY_KEY}`,
        ],
        tileSize: 256,
      },
    },
    layers: [
      {
        id: 'tdt-tiles-layer',
        type: 'raster',
        source: 'tdt-vec',
        paint: {
          //   'raster-brightness-max': 0.7,
          //   'raster-brightness-min': 0.3,
          //   'raster-hue-rotate': 20,
          //   'raster-saturation': 0.5,
        },
      },
      {
        id: 'tdt-cva-layer',
        type: 'raster',
        source: 'tdt-cva',
      },
    ],
  };

  useEffect(() => {
    const map = new mapboxgl.Map({
      center: [116.397411, 39.909186],
      zoom: 3.1,
      container: mapRef.current! as HTMLElement,
      style: initOption,
    });
    map.addControl(new mapboxgl.NavigationControl(), 'bottom-right');

    const el = document.createElement('img');
    el.className = 'marker';
    el.src = '/map/greenMapMarker.png';
    el.style.width = '30px';
    el.style.height = '30px';

    new mapboxgl.Marker({
      element: el,
      draggable: false,
    })
      .setLngLat([116.397411, 39.909186])
      .setPopup(new mapboxgl.Popup().setHTML('<h1>Hello World!</h1>'));
    // .addTo(map);

    map.on('load', () => {
      const Newmarker = new Marker(map);
      markerRef.current = Newmarker;
      setMap(map, Newmarker);
      // data.forEach((item) => {
      //   Newmarker.addSuccessMarker(item);
      // });
    });

    return () => {
      map.remove();
    };
  }, [data]);

  return (
    <div className='h-full'>
      <div className='h-full' ref={mapRef} />
    </div>
  );
};
export default Map;
