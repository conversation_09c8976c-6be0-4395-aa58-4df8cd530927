import { CustomerData } from '@/pages/Home';
import * as mapboxgl from 'maplibre-gl';
class Marker {
  private map: mapboxgl.Map;
  private MarkerList: mapboxgl.Marker[] = [];
  constructor(map: mapboxgl.Map) {
    this.map = map;
  }

  createIcon(src: string): HTMLImageElement {
    const el = document.createElement('img');
    el.src = src;
    el.style.width = '30px';
    el.style.height = '30px';
    return el;
  }

  createSuccessIcon(): HTMLImageElement {
    return this.createIcon('/map/greenMapMarker.png');
  }
  createWarnningIcon(): HTMLImageElement {
    return this.createIcon('/map/warnningMapMarker.png');
  }
  createDangerIcon(): HTMLImageElement {
    return this.createIcon('/map/dangerMapMarker.png');
  }

  addSuccessMarker(props: CustomerData) {
    this.addMarker(props, 'success');
  }
  addWarnningMarker(props: CustomerData) {
    this.addMarker(props, 'warnning');
  }
  addDangerMarker(props: CustomerData) {
    this.addMarker(props, 'danger');
  }

  addMarker(props: CustomerData, type: string) {
    try {
      let el = this.createSuccessIcon();
      switch (type) {
        case 'success':
          el = this.createSuccessIcon();
          break;
        case 'warnning':
          el = this.createWarnningIcon();
          break;
        case 'danger':
          el = this.createDangerIcon();
          break;
        default:
          break;
      }
      const marker = new mapboxgl.Marker({
        element: el,
        draggable: false,
      })
        .setLngLat([props.lng, props.lat])
        .setPopup(
          new mapboxgl.Popup().setHTML(`
    <div>
      <div><label>客户</label>:${props.customer}</div>
      <div></div><label>客户分组</label>:${props.customerGroup}</div>
      <div></div><label>地址</label>:${props.address}</div>
      <div></div><label>营业额</label>:${props.turnover}</div>
      <div></div><label>按时交付</label>:${props.qty_in_time}</div>
      <div></div><label>推迟交付</label>:${props.qty_delay}</div>
      <div></div><label>未交付</label>:${props.qty_nondel}</div>
    </div>`),
        )
        .addTo(this.map);
      this.MarkerList.push(marker);
    } catch (error) {
      console.log(error);
    }
  }
  showPopup(props: CustomerData) {
    this.MarkerList.forEach((item) => {
      if (
        item.getLngLat().lng === props.lng &&
        item.getLngLat().lat === props.lat
      ) {
        item.togglePopup();
      }
    });
  }
  closePopup() {
    this.MarkerList.forEach((item) => {
      if (item.getPopup().isOpen()) {
        item.togglePopup();
      }
    });
  }
  clearMarker() {
    this.MarkerList.forEach((item) => {
      item.remove();
    });
  }
}
export default Marker;
