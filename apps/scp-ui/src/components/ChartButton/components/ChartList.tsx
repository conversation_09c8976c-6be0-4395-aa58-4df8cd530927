import { memo, useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import useTypingEffect from './useTypingEffect';
import { App, Button, Form } from 'antd';
import { useSelector } from 'react-redux';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

interface ChartListProps {
  answer: string;
  question: string;
  isAlreadyTyping?: boolean;
  isLoading?: boolean;
  isCommand?: boolean;
  setIsAlready?: (isAlready: boolean) => void;
}

interface TypingMarkdownProps {
  markdownText: string;
  enableTypingEffect: boolean;
  speed?: number;
  isLoading?: boolean;
  setIsAlready?: (isAlready: boolean) => void;
}

const ChartList: React.FC<ChartListProps> = memo(
  ({
    question,
    answer,
    isAlreadyTyping = false,
    isLoading = false,
    isCommand,
    setIsAlready,
  }) => {
    const { message } = App.useApp();

    const form = Form.useFormInstance();
    const [isCommanded, setIsCommanded] = useState<boolean>(false);
    const [markDownText, setMarkDownText] = useState<string>('');

    const kfList = useSelector((state: any) => state.planningView.kfList);
    const attributeList = useSelector(
      (state: any) => state.planningView.attributeList,
    );

    const answerObj =
      Object.getPrototypeOf(answer) === Object.prototype
        ? answer
        : answer !== ''
          ? JSON.parse(answer)
          : {};

    const receiveData = () => {
      setIsCommanded(true);
      if (isCommand && answerObj.attributeId === null) {
        message.error('无主属性推荐');
      }
      form.setFieldsValue({
        kf: JSON.parse(answer).keyFigureId,
        md: JSON.parse(answer).attributeId,
        time: [
          {
            dateType: 1,
            rollingType: 0,
            dateRange: [
              dayjs(JSON.parse(answer).from),
              dayjs(JSON.parse(answer).to),
            ],
          },
        ],
      });
    };

    const convertMd = (md: number) => {
      return attributeList?.find((item: any) => item.value === md)?.label || '';
    };
    /**
     * @description 将特征值性从数字转换为中文
     * */
    const convertKf = (kf: number) => {
      return kfList?.find((item: any) => item.value === kf)?.label || '';
    };

    useEffect(() => {
      console.log(answerObj);

      const innerAnswer = isCommand
        ? answer === ''
          ? ''
          : `推荐特征值：${
              answerObj.keyFigureId !== null
                ? answerObj.keyFigureId
                    .map((item: number) => convertKf(item))
                    .join('、')
                : '无'
            }，推荐主属性：${
              answerObj.attributeId !== null
                ? answerObj.attributeId
                    .map((item: number) => convertMd(item))
                    .join('、')
                : '无'
            } 日期为 ${answerObj.from} - ${answerObj.to}`
        : answerObj.message;
      setMarkDownText(innerAnswer);
    }, [answer]);

    return (
      <>
        <div className='px-[30px] py-[20px] border-b w-full'>
          <div className='flex flex-row-reverse text-normal my-[10px] '>
            <div className='bg-[#EFF2F3] max-w-[800px] px-[10px] py-[5px] rounded-xl break-words'>
              {question}
            </div>
          </div>
          <TypingMarkdown
            isLoading={isLoading}
            markdownText={markDownText}
            enableTypingEffect={isAlreadyTyping}
            setIsAlready={setIsAlready}
          />
        </div>
        {isCommand && (
          <div className='flex flex-row-reverse mr-[50px] mt-[10px]'>
            <Button
              onClick={receiveData}
              className='text-white'
              style={{ backgroundColor: isCommanded ? '#26CC68' : '#CCE1FF' }}>
              {isCommanded ? '已采纳' : '推荐'}
            </Button>
          </div>
        )}
      </>
    );
  },
);

const TypingMarkdown: React.FC<TypingMarkdownProps> = ({
  markdownText,
  enableTypingEffect,
  speed = 50,
  isLoading = false,
  setIsAlready,
}) => {
  const [typedText, isTyping] = useTypingEffect(markdownText, speed);

  useEffect(() => {
    setIsAlready && setIsAlready(!enableTypingEffect || !isTyping);
  }, [isTyping, enableTypingEffect]);

  return (
    <div>
      {isLoading ? (
        <ReactMarkdown>loading...</ReactMarkdown>
      ) : enableTypingEffect && isTyping ? (
        <ReactMarkdown>{typedText}</ReactMarkdown>
      ) : (
        <ReactMarkdown>{markdownText}</ReactMarkdown>
      )}
    </div>
  );
};

export default ChartList;
