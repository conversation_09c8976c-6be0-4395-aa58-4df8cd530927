import { useEffect } from 'react';
import Collapsed from '@/assets/Collapsed.svg?react';
import AddNewChart from '@/assets/AddNewChart.svg?react';
import Icon from '@ant-design/icons';
import ajax from '@/api';
import _ from 'lodash';

interface SiderListProps {
  setChartCurrentId: (id: string | null) => void;
  currentId: string | null;
  recordList: Array<Omit<SiderItemProps, 'changeQA'>>;
  closeSider: () => void;
  changeQA: (newQA: Array<{ question: string; answer: string }>) => void;
}

const SiderList: React.FC<SiderListProps> = ({
  recordList,
  currentId,
  setChartCurrentId,
  closeSider,
  changeQA,
}) => {
  return (
    <div className='w-full h-full my-[10px] px-[10px]'>
      <div className='h-[40px] flex justify-between'>
        <Icon
          component={Collapsed}
          style={{ fontSize: 28, color: '#B9BCC6' }}
          onClick={closeSider}
        />
        <Icon
          component={AddNewChart}
          style={{ fontSize: 28, color: '#B9BCC6' }}
          onClick={() => {
            setChartCurrentId(null);
            changeQA([]);
          }}
        />
      </div>
      {/* <Input /> */}
      {recordList.map((item, index) => (
        <SiderItem
          key={index}
          {...item}
          changeQA={changeQA}
          setCurrentId={setChartCurrentId}
          currentId={currentId}
        />
      ))}
    </div>
  );
};
export interface SiderItemProps {
  title: string;
  record: Array<{ label: string; value: any }>;
  currentId: string | null;
  changeQA: (
    newQA: Array<{ question: string; answer: string; isTyping: boolean }>,
  ) => void;
  setCurrentId: (id: string) => void;
}
const SiderItem: React.FC<SiderItemProps> = ({
  title,
  record,
  currentId,
  changeQA,
  setCurrentId,
}) => {
  const getAiMessage = async (chartId: string) => {
    try {
      const res = await ajax.getAiMessagees({ convAppBuilderId: chartId });
      const list = _.get(res, 'data.list', []);

      const _returnQA: Array<{
        question: string;
        answer: string;
        isTyping: boolean;
      }> = [];

      const QAmap = new Map();
      let flagIndex = 0;

      list.forEach((item: any) => {
        if (!QAmap.has(item.answerUuid)) {
          QAmap.set(item.answerUuid, flagIndex);
          if (item.senderId !== 0) {
            _returnQA[flagIndex] = {
              question: item.messageText,
              answer: '',
              isTyping: false,
            };
          } else {
            _returnQA[flagIndex] = {
              question: '',
              answer: item.messageText,
              isTyping: false,
            };
          }
          flagIndex++;
        } else {
          const index = QAmap.get(item.answerUuid);
          if (item.senderId !== 0) {
            _returnQA[index].question = item.messageText;
          } else {
            _returnQA[index].answer = item.messageText;
          }
        }
      });
      changeQA(_returnQA);
    } catch (error) {
      console.error(error);
    }
  };
  const handleSelect = (chartId: string) => {
    setCurrentId(chartId);
  };
  useEffect(() => {
    if (currentId) getAiMessage(currentId);
  }, [currentId]);

  return (
    <div className='mt-[10px] pl-[10px]'>
      <div className='text-gray-500 text-[14px] font-bold'>
        {record.length !== 0 && title}
      </div>
      <div>
        {record.map((item) => {
          return (
            <div
              className={`mt-[10px] text-[14px] rounded-xl  line-clamp-1 hover:bg-gray-l7 active:bg-gray-200 px-[5px] py-[4px] cursor-pointer ${currentId === item.value ? 'bg-gray-l7' : ''}`}
              onClick={() => handleSelect(item.value)}
              key={item.value}>
              {item.label}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SiderList;
