import { useState, useEffect } from 'react';

const useTypingEffect = (text: any, speed = 50): [string, boolean] => {
  const [typedText, setTypedText] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(true);

  useEffect(() => {
    if (text === '') {
      setTypedText('');
      return;
    }

    const textArray = text?.split('') || '';
    let index = 0;
    const timer = setInterval(() => {
      setTypedText((prev) => prev + textArray[index]);
      index += 1;

      if (index >= textArray.length) {
        clearInterval(timer);
        setIsTyping(false);
      }
    }, speed);

    return () => clearInterval(timer);
  }, [text, speed]);

  return [typedText, isTyping];
};

export default useTypingEffect;
