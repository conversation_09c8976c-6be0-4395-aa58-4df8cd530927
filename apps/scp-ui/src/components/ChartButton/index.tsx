import { memo, useState, useEffect, useRef } from 'react';
import {
  Form,
  Button,
  App,
  Modal,
  Layout,
  Dropdown,
  Input,
  ConfigProvider,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import type { SiderItemProps } from './components/SiderList';
import { useSize } from 'ahooks';
import Collapsed from '@/assets/Collapsed.svg?react';
import { createStyles } from 'antd-style';
import { ChartList, SiderList } from './components';
import Icon from '@ant-design/icons';
import UpArrow from '@/assets/ArrowUpIcon.svg?react';
import ajax from '@/api';
import _ from 'lodash';
interface ChartButtonProps extends React.PropsWithChildren {
  open?: boolean;
  closeModal?: () => void;
  isCommand?: boolean;
}
const { Sider, Header, Content } = Layout;
const useStyle = createStyles({
  'my-modal-content': {
    padding: '0 !important',
    borderRadius: '12px !important',
    overflow: 'hidden !important',
  },
});

type NotChangeQA = Omit<SiderItemProps, 'changeQA'>;

const ChartButton: React.FC<ChartButtonProps> = memo(
  ({ children, open, closeModal, isCommand = false }) => {
    const [form] = Form.useForm();
    const inputRef = useRef(null);

    const size = useSize(inputRef);
    const { message } = App.useApp();

    const { styles } = useStyle();
    const [currentAIModel, setCurrentAIModel] = useState<string>('Qwen2.5-72B');
    const [siderCollapsed, setSiderCollapsed] = useState<boolean>(false);

    const [currentId, setCurrentId] = useState<string | null>(null);

    const [recordList, setRecordList] = useState<Array<NotChangeQA>>([]);

    const [QA, setQA] = useState<
      Array<{ question: string; answer: string; isTyping?: boolean }>
    >([]);
    const [loading, setLoading] = useState<boolean>(false);

    const [isTyping, setIsTyping] = useState<boolean>(true);

    const openSider = () => setSiderCollapsed(false);
    const closeSider = () => setSiderCollapsed(true);
    const changeQA = (
      newQA: Array<{ question: string; answer: string; isTyping?: boolean }>,
    ) => {
      setQA(newQA);
    };
    const classNames = {
      content: styles['my-modal-content'],
    };

    const sendQuestion = async (data: {
      query: string;
      conversationId: string | null;
      stream: boolean;
    }) => {
      try {
        setLoading(true);
        const requestApi = isCommand ? ajax.runModel : ajax.runQueryModel;
        const res = await requestApi(data);
        if (!res.code) {
          setQA([
            ...QA,
            {
              question: data.query,
              answer: res.data.result,
              isTyping: true,
            },
          ]);
          setCurrentId(res.data.result.conversationId);
        } else {
          message.error(res.msg);
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    const onFinish = ({ inputQuestion }: { inputQuestion: string }) => {
      form.resetFields();
      setQA([...QA, { question: inputQuestion, answer: '' }]);
      sendQuestion({
        query: inputQuestion,
        conversationId: currentId,
        stream: false,
      });
    };

    /**
     * @description 获取当前用户的会话列表
     * */

    const getConversationList = async () => {
      try {
        const res = await ajax.getUserDialogList({
          convType: isCommand ? 1 : 2,
        });
        const list = _.get(res, 'data.list', []) || [];

        const _recordList = list.map((item: any) => {
          return {
            title: item.label,
            record:
              item.convList?.map((convItem: any) => {
                return {
                  label: convItem.title,
                  value: convItem.convAppBuilderId,
                };
              }) || [],
          };
        });
        setRecordList(_recordList);
      } catch (error) {
        console.error(error);
      }
    };

    useEffect(() => {
      getConversationList();
    }, [currentId]);

    return (
      <App>
        <ConfigProvider
          theme={{
            components: {
              Button: {
                defaultHoverColor: '#2F6BFF',
              },
            },
          }}>
          {children}
          <Modal
            width={1200}
            classNames={classNames}
            className='relative'
            open={open}
            footer={null} // 移除默认的footer
            onCancel={closeModal}>
            <Layout className='h-[800px] w-full'>
              <Sider
                collapsed={siderCollapsed}
                collapsedWidth={0}
                style={{ backgroundColor: '#EFF2F3' }}>
                <SiderList
                  currentId={currentId}
                  setChartCurrentId={setCurrentId}
                  closeSider={closeSider}
                  changeQA={changeQA}
                  recordList={recordList}
                />
              </Sider>
              <Layout>
                <Header className='flex items-center px-[10px]'>
                  {siderCollapsed && (
                    <Icon
                      component={Collapsed}
                      style={{
                        fontSize: 28,
                        color: 'black',
                        marginBottom: '25px',
                        marginRight: '20px',
                      }}
                      onClick={openSider}
                    />
                  )}
                  <span className='font-[Smiley Sans] text-h1 ml-[10px] text-blue-500'>
                    AI助手
                  </span>
                  <Dropdown
                    menu={{
                      items: [
                        {
                          label: (
                            <div
                              onClick={() => setCurrentAIModel('Qwen2.5-72B')}>
                              Qwen2.5-72B
                            </div>
                          ),
                          key: 1,
                        },
                      ],
                    }}>
                    <span className='mt-[10px] ml-[10px] text-default-400 cursor-pointer hover:text-default-600'>
                      {currentAIModel}
                      <DownOutlined />
                    </span>
                  </Dropdown>
                </Header>
                <Content className='relative bg-white'>
                  <div
                    style={{ height: 800 - 120 - (size?.height || 0) }}
                    className='overflow-auto'>
                    {QA.map((item, index) => (
                      <ChartList
                        key={index}
                        {...item}
                        isAlreadyTyping={item.isTyping}
                        setIsAlready={(isTyping) => setIsTyping(isTyping)}
                        isCommand={isCommand}
                        isLoading={QA.length - 1 === index && loading}
                      />
                    ))}
                  </div>
                  <div className='w-full absolute bottom-2 left-0'>
                    <Form
                      form={form}
                      onFinish={onFinish}
                      className='flex justify-center'>
                      <div
                        ref={inputRef}
                        className=' flex float-left items-center absolute left-0 bottom-0 w-full  mb-[10px] p-[10px]'>
                        <Form.Item noStyle name='inputQuestion'>
                          <Input.TextArea
                            style={{ lineHeight: '40px' }}
                            className='bg-[#EFF2F3] border-none active:border-none hover:bg-[#EFF2F3] pr-16 w-full h-[48px]' // 设置固定高度
                            autoSize={{ minRows: 1, maxRows: 5 }} // 保留 autoSize 但设置最大行数
                          />
                        </Form.Item>
                        <Button
                          style={{ width: '36px', height: '36px' }}
                          icon={<UpArrow style={{ color: 'white' }} />}
                          htmlType='submit'
                          loading={loading || !isTyping}
                          type='primary'
                          className='absolute ml-[20px] rounded-full right-8'
                        />
                      </div>
                    </Form>
                  </div>
                </Content>
              </Layout>
            </Layout>
          </Modal>
        </ConfigProvider>
      </App>
    );
  },
);

export default ChartButton;
