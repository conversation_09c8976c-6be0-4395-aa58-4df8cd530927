// import React, { useState, useEffect, useRef } from 'react';
// import { useUpdateEffect } from 'ahooks';
// import { FloatButton, Button, Modal, Input, List, Avatar, message } from 'antd';
// // import { useSelector } from 'react-redux';
// import { ChartStarIcon } from '@/pages/PlanningView/PlanningView'; // 确保这个图标路径正确
// import ReactMarkdown from 'react-markdown';
// import styled from 'styled-components';
// import { RunModelResponse } from '@/api/PlanningView/model';
// import ajax from '@/api';
// import { createStyles } from 'antd-style';
// import LOGO from '/scmify_logo/SVG/logo-white.svg';
// import { FiSend } from 'react-icons/fi';
//
// const { TextArea } = Input;
//
// const useStyle = createStyles({
//   'my-modal-content': {
//     padding: '0 !important',
//     borderRadius: '12px !important',
//     overflow: 'hidden !important',
//     width: '1200px',
//     height: '800px',
//   },
// });
//
// const Container = styled.div`
//   display: flex;
//   align-items: center;
//   margin-top: 16px;
// `;
//
// const StyledTextArea = styled(TextArea)`
//   max-height: 150px; // 设置最大高度
//   resize: none; // 禁止手动调整大小
//   flex: 1; // 占据剩余空间
//   margin-right: 8px; // 右侧留出空间给按钮
// `;
//
// const ChatContainer = styled.div`
//   height: 600px; // 设置对话框最大高度
//   overflow-y: auto; // 超出内容时显示滚动条
//   margin-bottom: 16px;
//   border: 1px solid #f0f0f0;
//   padding: 8px;
// `;
//
// const MessageItem = styled.div<{ type: 'question' | 'answer' }>`
//   display: flex;
//   flex-direction: ${(props) =>
//     props.type === 'question' ? 'row-reverse' : 'row'};
//   align-items: flex-start;
//   margin-bottom: 16px;
// `;
//
// const MessageDetails = styled.div`
//   display: flex;
//   align-items: center;
//   margin-bottom: 4px;
// `;
//
// const MessageContent = styled.div<{ type: 'question' | 'answer' }>`
//   background-color: ${(props) =>
//     props.type === 'question' ? '#e6f7ff' : '#d5d7dd'};
//   border: 1px solid
//     ${(props) => (props.type === 'question' ? '#91d5ff' : '#d5d7dd')};
//   margin-left: ${(props) => (props.type === 'question' ? '0' : '8px')};
//   margin-right: ${(props) => (props.type === 'question' ? '8px' : '0')};
//   border-radius: 4px;
//   padding: 8px;
//   max-width: 80%;
//   word-wrap: break-word;
// `;
//
// interface Message {
//   type: 'question' | 'answer';
//   content: string;
// }
//
// interface TypingMessage extends Message {
//   displayContent: string;
// }
//
// const ChartButton: React.FC = () => {
//   // const isShowChartButton = useSelector(
//   //   (state: any) => state.layout.isShowChartButton,
//   // );
//   const { styles } = useStyle();
//   const [visible, setVisible] = useState(false);
//   const [messages, setMessages] = useState<TypingMessage[]>([]);
//   const [inputValue, setInputValue] = useState<string>('');
//   const [buttonLoading, setButtonLoading] = useState<boolean>(false);
//   const [typingMessage, setTypingMessage] = useState<string>('');
//   const [replyValue, setReplyValue] = useState<string>('');
//   const [currentConversationId, setCurrentConversationId] = useState<
//     string | null
//   >(null);
//
//   const [messageApi, contextHolder] = message.useMessage();
//
//   const textareaRef = useRef<HTMLTextAreaElement>(null);
//   const chatContainerRef = useRef<HTMLDivElement>(null);
//
//   const classNames = {
//     content: styles['my-modal-content'],
//   };
//
//   /**
//    * @description 获取AI状态
//    * */
//   const getAiStatus = async (): Promise<boolean> => {
//     try {
//       const res = await ajax.getAiSwitch();
//       return res.data.isAi;
//     } catch (error) {
//       console.error(error);
//     }
//     return false;
//   };
//
//   const showModal = async () => {
//     const _isShowChartButton = await getAiStatus();
//     if (_isShowChartButton) {
//       setVisible(true);
//     } else {
//       messageApi.error('请联系管理员开启AI助手');
//     }
//   };
//
//   const handleOk = () => {
//     setVisible(false);
//   };
//
//   const handleCancel = () => {
//     setVisible(false);
//   };
//
//   const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
//     setInputValue(e.target.value);
//   };
//
//   const handleSend = async () => {
//     const currentInputValue = inputValue.trim();
//     if (currentInputValue) {
//       const newMessage: TypingMessage = {
//         type: 'question',
//         content: currentInputValue,
//         displayContent: currentInputValue,
//       };
//       setMessages([...messages, newMessage]);
//       setInputValue(''); // 发送后清空输入框
//       // 获取推荐数据并更新状态
//       await getDataByRunModel({
//         query: currentInputValue,
//         conversationId: currentConversationId,
//         stream: false,
//         user: 'zhikaixu',
//       });
//     }
//   };
//
//   useUpdateEffect(() => {
//     if (typingMessage) {
//       const currentMessageIndex = messages.length - 1;
//       let currentCharIndex = 0;
//
//       const typingEffect = () => {
//         if (currentCharIndex < typingMessage.length) {
//           setMessages((prevMessages) => {
//             const updatedMessages = [...prevMessages];
//             updatedMessages[currentMessageIndex].displayContent +=
//               typingMessage[currentCharIndex];
//             currentCharIndex += 1;
//             return updatedMessages;
//           });
//           setTimeout(typingEffect, 50); // 调整打字速度
//         } else {
//           setTypingMessage(''); // 清空打字消息
//         }
//       };
//       typingEffect();
//     }
//   }, [typingMessage]);
//
//   const getDataByRunModel = async (data: {
//     query: string;
//     conversationId: string | null;
//     stream: boolean;
//     user: string;
//   }) => {
//     setButtonLoading(true);
//     try {
//       const res: RunModelResponse = (await ajax.runQueryModel(data)) as any;
//       if (!res.code) {
//         const data = res.data;
//         setCurrentConversationId(data.conversationId);
//         setReplyValue(data.message + '%' + messages.length);
//       }
//     } catch (error) {
//       console.error(error);
//     } finally {
//       setButtonLoading(false);
//     }
//   };
//
//   const handleSend2 = () => {
//     if (inputValue.trim() !== '') {
//       console.log('Send message:', inputValue);
//       setInputValue('');
//     }
//   };
//
//   const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
//     if (event.key === 'Enter' && !event.shiftKey) {
//       event.preventDefault();
//       handleSend();
//     }
//   };
//
//   // 监听 recommendData 变化并更新回复消息
//   useUpdateEffect(() => {
//     const reply = replyValue.split('%')[0];
//     const newReply: TypingMessage = {
//       type: 'answer',
//       content: reply,
//       displayContent: '',
//     };
//     setMessages((prevMessages) => [...prevMessages, newReply]);
//     setTypingMessage(reply);
//   }, [replyValue]);
//   // 每次消息更新时滚动到底部
//   useEffect(() => {
//     if (chatContainerRef.current) {
//       chatContainerRef.current.scrollTop =
//         chatContainerRef.current.scrollHeight;
//     }
//   }, [messages]);
//
//   useEffect(() => {
//     if (textareaRef.current) {
//       textareaRef.current.style.height = 'auto'; // Reset the height to auto to calculate the new height
//       const maxHeight =
//         9 *
//         parseFloat(getComputedStyle(textareaRef.current).lineHeight || '24px'); // Assuming line-height is 24px by default
//       textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, maxHeight)}px`;
//     }
//   }, [inputValue]);
//   return (
//     <>
//       {contextHolder}
//       <FloatButton
//         icon={<ChartStarIcon />}
//         onClick={showModal}
//         style={{ right: 24 }}>
//         助手
//       </FloatButton>
//       <Modal
//         width={1000}
//         classNames={classNames}
//         centered
//         title={
//           <div className='bg-primary-300 h-[100px] flex items-center pl-[30px]'>
//             <img src={LOGO} width={60} />
//             <span className='font-semibold text-h1 text-white'>AI助手</span>
//           </div>
//         }
//         open={visible}
//         onOk={handleOk}
//         onCancel={handleCancel}
//         footer={null} // 移除默认的footer
//       >
//         {/* <ChatContainer ref={chatContainerRef}> */}
//         {/*   <List */}
//         {/*     dataSource={messages} */}
//         {/*     renderItem={(item) => ( */}
//         {/*       <MessageItem type={item.type}> */}
//         {/*         <MessageDetails> */}
//         {/*           <Avatar>{item.type === 'question' ? 'U' : 'B'}</Avatar> */}
//         {/*         </MessageDetails> */}
//         {/*         <MessageContent type={item.type}> */}
//         {/*           <ReactMarkdown>{item.displayContent}</ReactMarkdown> */}
//         {/*         </MessageContent> */}
//         {/*       </MessageItem> */}
//         {/*     )} */}
//         {/*   /> */}
//         {/* </ChatContainer> */}
//         {/* <Container> */}
//         {/*   <StyledTextArea */}
//         {/*     value={inputValue} */}
//         {/*     onChange={handleInputChange} */}
//         {/*     placeholder='请输入问题...' */}
//         {/*     autoSize={{ minRows: 1, maxRows: 4 }} // 设置最小行数为1，最大行数为4 */}
//         {/*   /> */}
//         {/*   <Button loading={buttonLoading} type='primary' onClick={handleSend}> */}
//         {/*     发送 */}
//         {/*   </Button> */}
//         {/* </Container> */}
//         <div className='flex items-center p-4 border-t border-gray-300'>
//           <textarea
//             ref={textareaRef}
//             className='flex-grow resize-none p-2 bg-white rounded-lg border border-gray-300  focus:outline-none focus:ring-2 focus:ring-blue-500'
//             placeholder='Type a message...'
//             value={inputValue}
//             onChange={(e) => setInputValue(e.target.value)}
//             onKeyDown={handleKeyDown}
//             rows={1}
//           />
//           <button
//             className='ml-2 p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500'
//             onClick={handleSend2}>
//             <FiSend size={20} />
//           </button>
//         </div>
//       </Modal>
//     </>
//   );
// };
//
// export default ChartButton;
