import { Drawer } from 'antd';
import LeftArrow from '/leftArrow.svg';
import Chart from '../Chart/Chart';

function ChartDrawer({
  chartShow,
  setChartShow,
  chartOption,
}: {
  chartShow: boolean;
  setChartShow: (show: boolean) => void;
  chartOption: any;
}) {
  return (
    <Drawer
      forceRender={true}
      style={{
        position: 'absolute',
        height: '600px',
        top: '50%',
        transform: 'translateY(-50%)',
        borderTopLeftRadius: 16,
        borderBottomLeftRadius: 16,
      }}
      width={'1000px'}
      open={chartShow}
      onClose={() => {
        setChartShow(false);
      }}
      closeIcon={<img src={LeftArrow} style={{ rotate: '180deg' }}></img>}>
      <div
        style={{
          backgroundColor: '#F5F7FA',
          borderRadius: 10,
          width: '100%',
          height: '100%',
        }}>
        <Chart chartShow={chartShow} chartOption={chartOption}></Chart>
      </div>
    </Drawer>
  );
}

export default ChartDrawer;
