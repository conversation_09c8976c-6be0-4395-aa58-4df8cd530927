import { useState, useEffect, useRef } from 'react';
import * as echarts from 'echarts';

function Chart({
  chartShow = true,
  chartOption,
  chartKey,
  displayLine = false,
  width = '100%',
  height = '100%',
}: {
  chartShow?: boolean;
  chartOption: any;
  chartKey?: string;
  displayLine?: boolean;
  width?: string;
  height?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [chart, setChart] = useState<any>(null);

  useEffect(() => {
    if (!chartShow) {
      return;
    }
    const echartsDOM = document.querySelector(
      '.chart-container' + (chartKey ? '-' + chartKey : ''),
    ) as HTMLDivElement;
    setChart(echarts.init(echartsDOM));
  }, [chartShow]);

  useEffect(() => {
    if (chart) {
      chart.setOption(chartOption);
    }
  }, [chart, chartOption]);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    if (
      containerRef.current.clientHeight === 0 ||
      containerRef.current.clientWidth === 0
    ) {
      return;
    }
    if (chart) {
      chart.resize();
    }
  }, [containerRef.current?.clientHeight, containerRef.current?.clientWidth]);

  return (
    <div
      style={{
        position: 'relative',
        height: height,
        width: width,
      }}>
      <div
        ref={containerRef}
        className={'chart-container' + (chartKey ? '-' + chartKey : '')}
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          position: 'relative',
        }}></div>
      <div
        style={{
          width: '80%',
          height: '2px',
          backgroundColor:
            chartOption.series && chartOption.series.data[0] > 0
              ? '#53872a'
              : '#d04a22',
          position: 'absolute',
          left: '50%',
          top: '60%',
          transform: 'translate(-50%, -50%)',
          display: displayLine ? 'block' : 'none',
        }}></div>
    </div>
  );
}

export default Chart;
