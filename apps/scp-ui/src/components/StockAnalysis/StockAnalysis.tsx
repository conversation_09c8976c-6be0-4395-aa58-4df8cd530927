import { useState, useEffect, useRef } from 'react';
import { ConfigProvider, Flex, Table, Empty, App, Splitter } from 'antd';
import { motion } from 'framer-motion';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSize } from 'ahooks';
import ChartDrawer from './components/ChartDrawer/ChartDrawer';
import DownArrow from '/downArrow.svg';
import ChartIcon from '/chartIcon.svg';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import isLeapYear from 'dayjs/plugin/isLeapYear';
import dayjs from 'dayjs';
import Chart from './components/Chart/Chart';
import JWForm from '../JWForm';
import ajax from '@/api';
import './index.css';
import { useSelector } from 'react-redux';

dayjs.extend(dayOfYear);
dayjs.extend(isLeapYear);

interface Column {
  title: string;
  dataIndex: string;
  key: string;
  onCell?: any;
  onHeaderCell?: any;
  render?: any;
  shouldCellUpdate?: any;
}

function StockAnalysis() {
  const [stockData, setStockData] = useState<any>(null);
  const [stockSlectRow, setStockSlectRow] = useState<any>(null);
  const [allAnalysisData, setAllAnalysisData] = useState<any>(null);
  const [analysisTableData, setAnalysisTableData] = useState<any>(null);
  const [chartShow, setChartShow] = useState(false);
  const [chartOption, setChartOption] = useState<any>(null);
  const [paPvId, setPaPvId] = useState<number>(-1);
  const [compressed, setCompressed] = useState(false);

  const pVersionList = useSelector((state: any) => state.layout.pVersionList);

  const { message } = App.useApp();
  const { t } = useVoerkaI18n();

  useEffect(() => {
    if (!stockData) {
      return;
    }
    setAnalysisTableData(null);
    setStockSlectRow(null);
  }, [stockData]);

  useEffect(() => {
    if (!stockSlectRow) {
      return;
    }
    setAnalysisTableData(allAnalysisData[stockSlectRow.index]);
    setChartOption(getChartData(allAnalysisData[stockSlectRow.index]));
  }, [stockSlectRow]);

  const getChartData = (data: any, detail: boolean = true) => {
    let chartDataRow: any = {};
    data.forEach((item: any) => {
      chartDataRow[item.date] = item.stock;
    });
    chartDataRow = Object.keys(chartDataRow).map((key: any) => ({
      date: key,
      data: chartDataRow[key],
    }));

    return {
      ...(detail
        ? {
            xAxis: {
              type: 'category',
              data: chartDataRow.map((item: any) => item.date),
            },
            yAxis: {
              type: 'value',
              splitLine: {
                show: false,
              },
            },
          }
        : {
            xAxis: {
              type: 'category',
              data: chartDataRow.map((item: any) => item.date),
              show: false,
            },
            yAxis: {
              type: 'value',
              show: false,
            },
          }),
      series: {
        data: chartDataRow.map((item: any) => Number(item.data)),
        type: 'line',
        ...(detail
          ? {}
          : {
              symbol: 'none',
            }),
      },
      visualMap: {
        type: 'piecewise',
        dimension: 1,
        show: false,
        pieces:
          Math.max(...chartDataRow.map((item: any) => Number(item.data))) > 0
            ? [
                {
                  gt: 0,
                  lt:
                    Math.max(
                      ...chartDataRow.map((item: any) => Number(item.data)),
                    ) + 200,
                  color: 'green',
                },
                {
                  value: 0,
                  color: 'blue',
                },
              ]
            : [],
        outOfRange: {
          color: 'red',
        },
      },
    };
  };

  useEffect(() => {
    if (!stockSlectRow) {
      setAnalysisTableData(null);
      return;
    }
  }, [stockSlectRow]);

  const analysisTableColumns: Column[] = [
    { title: t('订单号'), dataIndex: 'OrderNo', key: 'OrderNo' },
    { title: t('地点'), dataIndex: 'location', key: 'location' },
    { title: t('物料'), dataIndex: 'material', key: 'material' },
    { title: t('数量'), dataIndex: 'quantity', key: 'quantity' },
    { title: t('预计库存'), dataIndex: 'stock', key: 'stock' },
    { title: t('条目类型'), dataIndex: 'info', key: 'info' },
    { title: t('计划相关时间'), dataIndex: 'date', key: 'date' },
    {
      title: t('相关地点'),
      dataIndex: 'relateLocation',
      key: 'relateLocation',
    },
    {
      title: t('相关物料'),
      dataIndex: 'relateMaterial',
      key: 'relateMaterial',
    },
  ];

  analysisTableColumns.forEach((column) => {
    column.onCell = () => {
      return {
        style: {
          textAlign: 'center',
        },
      };
    };
    column.onHeaderCell = () => {
      return {
        style: {
          textAlign: 'center',
        },
      };
    };
  });

  const arrowAnimation = {
    down: {
      rotate: 0,
    },
    up: {
      rotate: 180,
    },
  };

  const [resizeable, setResizeable] = useState(true);

  const [panelsPercent, setPanelsPercent] = useState([66, 34]);
  const [compressedTableHeight, setCompressedTableHeight] = useState(0);

  const fullRef = useRef<any>();
  const fullSize = useSize(fullRef);

  const tableRef = useRef<any>();
  const tableSize = useSize(tableRef);

  const JWFormRef = useRef<any>();
  const JWFormSize = useSize(JWFormRef);

  useEffect(() => {
    setCompressedTableHeight(
      (fullSize?.height || 0) - (JWFormSize?.height || 0) - 15,
    );
  }, [JWFormSize]);

  return (
    <ConfigProvider
      renderEmpty={() => <Empty description={t('数据为空')} />}
      theme={{
        components: {
          Table: {
            borderColor: '#fff',
            headerBg: '#fff',
            headerSplitColor: '#fff',
          },
        },
      }}>
      <Flex
        ref={fullRef}
        style={{
          height: '100%',
          width: '100%',
          backgroundColor: '#f7f7fb',
          position: 'relative',
        }}
        vertical={true}>
        <Splitter
          onResize={(sizes) => {
            const sum = sizes.reduce((acc, cur) => acc + cur, 0);
            setPanelsPercent(sizes.map((size) => (size / sum) * 100));
          }}
          layout={'vertical'}>
          <Splitter.Panel
            style={{
              overflow: 'hidden',
            }}
            size={compressed ? JWFormSize?.height : panelsPercent[0] + '%'}
            resizable={resizeable}>
            <JWForm
              ref={JWFormRef}
              pageName={'INV_ANAL'}
              processTableData={async (data: any) => {
                setStockData(data);
                let temp: any = [];
                await Promise.all(
                  data.map((item: any) => {
                    return ajax.getProjectView({
                      paPvId: paPvId,
                      ...item,
                    });
                  }),
                ).then((res: any) => {
                  const datas = res.map((item: any) => item.data.list);
                  temp = datas.map((item: any) => {
                    if (!item) {
                      return [];
                    }
                    return item.map((i: any, index: number) => {
                      return {
                        key: Date.now() + '' + index,
                        OrderNo: i.orderNo,
                        material: i.matnr,
                        location: i.locno,
                        quantity: i.quantity,
                        stock: i.projectedStock,
                        info: i.itemDesc,
                        date: i.date,
                        relateLocation: i.dependentLoc,
                        relateMaterial: i.dependentMat,
                      };
                    });
                  });
                  setAllAnalysisData(temp);
                });
                return data.map((item: any, index: number) => {
                  return {
                    ...item,
                    chart: getChartData(temp[index], false),
                  };
                });
              }}
              processTableColumns={(columns: any) => {
                return columns.map((item: any) => {
                  return {
                    ...item,
                    ...(item.dataIndex === 'chart'
                      ? {
                          render: (text: any, record: any) => {
                            return (
                              <Flex align='center' justify='center'>
                                <Chart
                                  chartOption={text}
                                  chartKey={record.id}
                                  width={item.width}
                                  height='70px'></Chart>
                              </Flex>
                            );
                          },
                        }
                      : {}),
                  };
                });
              }}
              rowOnClick={(record: any, index: number) => {
                setStockSlectRow({
                  record: record,
                  index: index,
                });
              }}
              onChangePaPvId={(paPvId: any) => {
                setPaPvId(paPvId);
              }}
              compressed={compressed}
              selectRowIndex={stockSlectRow ? stockSlectRow.index : null}
              pagationContainerBackground={'bg-white'}></JWForm>
          </Splitter.Panel>
          <Splitter.Panel
            style={{
              overflow: 'hidden',
            }}
            size={compressed ? compressedTableHeight : panelsPercent[1] + '%'}>
            <Flex
              vertical={true}
              style={{
                height: '100%',
                backgroundColor: '#fff',
                borderRadius: '0 0 10px 10px',
                position: 'relative',
              }}>
              <Flex
                vertical={false}
                align='center'
                style={{
                  marginTop: '10px',
                  padding: '0 16px',
                  position: 'relative',
                }}>
                <p
                  style={{
                    margin: '0',
                    marginTop: '10px',
                    fontWeight: 500,
                    letterSpacing: 0.72,
                    backgroundColor: '#F5F7FA',
                    padding: '6px 12px',
                  }}>
                  {stockSlectRow
                    ? t('物料') +
                      ':' +
                      stockSlectRow.record.matnr +
                      ' ' +
                      t('地点') +
                      ':' +
                      stockSlectRow.record.locno +
                      ' ' +
                      t('计划版本') +
                      ':' +
                      pVersionList.find((item: any) => item.value === paPvId)
                        .label
                    : t('暂未选择库存')}
                </p>
                <img
                  src={ChartIcon}
                  style={{
                    height: '24px',
                    width: '24px',
                    cursor: 'pointer',
                    position: 'absolute',
                    right: '70px',
                  }}
                  onClick={() => {
                    if (!chartOption) {
                      message.warning(t('暂无数据显示'));
                      return;
                    }
                    if (stockSlectRow) {
                      setChartShow(true);
                    }
                  }}></img>
                <motion.img
                  variants={arrowAnimation}
                  animate={compressed ? 'down' : 'up'}
                  src={DownArrow}
                  style={{
                    height: '24px',
                    width: '24px',
                    cursor: 'pointer',
                    position: 'absolute',
                    right: '16px',
                  }}
                  onClick={() => {
                    if (!compressed && !stockSlectRow) {
                      return;
                    }
                    setResizeable(compressed ? true : false);
                    setCompressed(!compressed);
                  }}></motion.img>
              </Flex>
              <Flex ref={tableRef} flex={1}>
                <Table
                  style={{
                    width: '100%',
                  }}
                  scroll={{
                    y: (tableSize?.height || 80) - 80,
                  }}
                  dataSource={analysisTableData}
                  columns={analysisTableColumns}
                  pagination={false}></Table>
              </Flex>
            </Flex>
          </Splitter.Panel>
        </Splitter>
      </Flex>
      <ChartDrawer
        chartShow={chartShow}
        setChartShow={setChartShow}
        chartOption={chartOption ? chartOption : {}}></ChartDrawer>
    </ConfigProvider>
  );
}

export default StockAnalysis;
