.flippingPagesWrap {
    margin-right: 1.5rem;
    padding: 1.64rem 1.64286rem;
    justify-content: space-between;
    display: flex;
    height: 4.21429rem;
    align-items: center;
    gap: 1.28571rem;
    flex-shrink: 0;
    border-radius: 1.78571rem;
    background-color: var(--white, #FFF);
}

.flippingPagesNum {
    display: flex;
    width: 2.85714rem;
    height: 2.85714rem;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background: var(--Brand-2, #1890FF);
    color: var(--white, #FFF);
    font-family: "PingFang SC";
    font-size: 1.14286rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.06857rem;
    cursor:pointer;
}

.preSvg {
    height: 2.85714rem;
    line-height: 2.85714rem;
    margin-right: 0.5rem;
}

.preWord {
    height: 2.85714rem;
    line-height: 2.85714rem;
    color: var(--Brand-2, #1890FF);
    font-family: "PingFang SC";
    font-size: 1.28571rem;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 0.06429rem;
}

.nextSvg {
    height: 2.85714rem;
    line-height: 2.85714rem;
    margin-left: 0.5rem;
}

.nextWord {
    height: 2.85714rem;
    line-height: 2.85714rem;
    color: var(--Brand-2, #1890FF);
    font-family: "PingFang SC";
    font-size: 1.28571rem;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 0.06429rem;
}

.jumpWrap {
    display: flex;
    /* width: 22.42857rem; */
    height: 4.21429rem;
    padding: 0rem 1.64286rem;
    align-items: center;
    gap: 1.28571rem;
    flex-shrink: 0;
    border-radius: 1.78571rem;
    background: var(--white, #FFF);
    color: var(--G4, #848A99);
    font-family: "PingFang SC";
    font-size: 1.28571rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.06429rem;
}

.numWrap {
    position: relative;
    text-align: center;
    height: 2.85714rem;
    line-height: 2.85714rem;
}

.num {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding: auto;
    margin: auto;
    color: #0C40DE;
}

.jumpBtn {
    cursor: pointer;
    width: 5.85714rem;
    height: 3.07143rem;
    line-height: 3.07143rem;
    flex-shrink: 0;
    border-radius: 2.57143rem;
    background: var(--Brand-2, #1890FF);
    color: var(--white, #FFF);
    font-family: "PingFang SC";
    font-size: 1.28571rem;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 0.06429rem;
    text-align: center;
}

.numberInput {
    height: 2.3rem;
    width: 2.3rem;
    border: none;
    text-align: center;
}