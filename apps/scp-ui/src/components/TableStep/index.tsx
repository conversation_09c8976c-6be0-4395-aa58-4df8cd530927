import { useState, useEffect } from 'react';
import { message } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';
type PropsType = {
  total: any;
  checkPageFn: any;
  reset: boolean;
};
const TableStep: React.FC<PropsType> = (props) => {
  const { t } = useVoerkaI18n();
  const { total, checkPageFn, reset } = props;
  const [pageArr, setPageArr] = useState<any>([]);
  const [currentNum, setCurrentNum] = useState<number>(1);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [jumpNumber, setjumpNumber] = useState<number>(1);
  const checkPage = (value: any) => {
    if (value === '...') return;
    const allPage = total === 0 ? 1 : Math.ceil(total / 10);
    if (value < 1) {
      message.error('已经是第一页了！');
      return;
    } else if (value > allPage) {
      message.error('已经是最后一页了！');
      return;
    }
    setPageNumber(value);
    if (allPage === 1 || allPage === 2 || allPage === 3) {
      setCurrentNum(value);
    } else if (allPage === 4) {
      setCurrentNum(value);
      if (value === 1) {
        setPageArr([1, 2, '...', 4]);
      } else if (value === 2 || value === 3) {
        setPageArr([1, 2, 3, 4]);
      } else if (value === 4) {
        setPageArr([1, '...', 3, 4]);
      }
    } else if (allPage === 5) {
      setCurrentNum(value);
      if (value === 1) {
        setPageArr([1, 2, '...', 5]);
      } else if (value === 2) {
        setPageArr([1, 2, 3, '...', 5]);
      } else if (value === 3) {
        setPageArr([1, 2, 3, 4, 5]);
      } else if (value === 4) {
        setPageArr([1, '...', 3, 4, 5]);
      } else if (value === 5) {
        setCurrentNum(4);
        setPageArr([1, '...', 4, 5]);
      }
    } else {
      if (value === 1) {
        setCurrentNum(1);
        setPageArr([1, 2, '...', allPage]);
      } else if (value === 2 && value + 3 < allPage) {
        setCurrentNum(2);
        setPageArr([1, 2, 3, '...', allPage]);
      } else if (value === 3 && value + 2 < allPage) {
        setCurrentNum(3);
        setPageArr([1, 2, 3, 4, '...', allPage]);
      } else if (value > 3 && value + 2 === allPage) {
        setCurrentNum(4);
        setPageArr([1, '...', value - 1, value, value + 1, allPage]);
      } else if (value > 3 && value + 2 < allPage) {
        setCurrentNum(4);
        setPageArr([1, '...', value - 1, value, value + 1, '...', allPage]);
      } else if (value > 3 && value + 1 === allPage) {
        setPageArr([1, '...', value - 1, value, allPage]);
      } else if (value === allPage) {
        setCurrentNum(4);
        setPageArr([1, '...', value - 1, allPage]);
      }
    }
  };

  const inputNumber = (value: any) => {
    const page = value.target.value;
    setjumpNumber(page);
  };
  const jump = () => {
    const allPage = total === 0 ? 1 : Math.ceil(total / 10);
    const z = /^(?:1|[2-9]\d*)$/;
    if (
      jumpNumber < 1 ||
      jumpNumber > allPage ||
      !z.test(jumpNumber.toString())
    ) {
      message.error('请输入正确的页码！');

      return;
    }
    setPageNumber(Number(jumpNumber));
    checkPage(Number(jumpNumber));
  };
  useEffect(() => {
    const allPage = total === 0 ? 1 : Math.ceil(total / 10);
    let _pageArr: any = [];
    if (allPage !== 0) {
      if (allPage === 1 || allPage === 2 || allPage === 3) {
        for (let i = 1; i <= allPage; i++) {
          _pageArr.push(i);
        }
      } else {
        _pageArr = [1, 2, '...', allPage];
      }
      setPageArr([..._pageArr]);
    }
  }, [total]);
  useEffect(() => {
    setCurrentNum(1);
  }, []);
  useEffect(() => {
    checkPageFn(pageNumber);
  }, [pageNumber]);
  useEffect(() => {
    setCurrentNum(1);
    checkPage(1);
  }, [reset]);

  return (
    <div className='flex'>
      {/* 翻页模块 */}
      <div className='flippingPagesWrap'>
        {/* 上一页 */}
        <div
          className='flex'
          style={{ cursor: 'pointer' }}
          onClick={() => {
            checkPage(pageNumber - 1);
          }}>
          <div className='preSvg'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='0.85714rem'
              height='2.85714rem'
              viewBox='0 0 9 14'
              fill='none'>
              <path d='M8 13L2 7L8 1' stroke='#1890FF' strokeWidth='2' />
            </svg>
          </div>
          <div className='preWord'>{t('上一页')}</div>
        </div>
        {/* 翻页数字 */}
        <div
          className='flex text-center'
          style={{
            color: 'var(--G5, #B9BCC6)',
            fontFamily: 'PingFang SC',
            fontSize: ' 1.14286rem',
            fontStyle: 'normal',
            fontWeight: '600',
            letterSpacing: '0.06857rem',
          }}>
          {pageArr.length === 1 &&
            pageArr.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  className='flippingPagesNum'
                  onClick={() => checkPage(item)}>
                  {item}
                </div>
              );
            })}
          {pageArr.length > 1 &&
            pageArr.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  className={pageArr.length == index + 1 ? '' : 'mr-[1.29rem]'}
                  style={
                    currentNum === index + 1
                      ? {
                          display: 'flex',
                          width: ' 2.85714rem',
                          height: ' 2.85714rem',
                          justifyContent: 'center',
                          alignItems: 'center',
                          flexShrink: '0',
                          borderRadius: '50%',
                          padding: '0.75rem 0rem',
                          background: 'var(--Brand-2, #1890FF)',
                          color: ' var(--white, #FFF)',
                          cursor: 'pointer',
                        }
                      : {
                          display: 'flex',
                          width: ' 2.85714rem',
                          height: ' 2.85714rem',
                          justifyContent: 'center',
                          alignItems: 'center',
                          flexShrink: '0',
                          borderRadius: '50%',
                          padding: '0.75rem 0rem',
                          background: 'var(--white, #FFF)',
                          color: ' var(--G5, #B9BCC6)',
                          cursor: 'pointer',
                        }
                  }
                  onClick={() => checkPage(item)}>
                  {item}
                </div>
              );
            })}
        </div>

        {/* 下一页 */}
        <div
          className='flex'
          style={{ cursor: 'pointer' }}
          onClick={() => {
            checkPage(pageNumber + 1);
          }}>
          <div className='nextWord'>{t('下一页')}</div>
          <div className='nextSvg'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='0.85714rem'
              height='2.85714rem'
              viewBox='0 0 9 14'
              fill='none'>
              <path d='M1 13L7 7L1 1' stroke='#1890FF' strokeWidth='2' />
            </svg>
          </div>
        </div>
      </div>
      {/* 跳转模块 */}
      <div className='jumpWrap'>
        {t('跳转至')}
        <div className='numWrap'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='2.85714rem'
            height='2.85714rem'
            viewBox='0 0 40 41'
            fill='none'>
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M30 1.5H10C5.02944 1.5 1 5.52944 1 10.5V30.5C1 35.4706 5.02944 39.5 10 39.5H30C34.9706 39.5 39 35.4706 39 30.5V10.5C39 5.52944 34.9706 1.5 30 1.5ZM10 0.5C4.47715 0.5 0 4.97715 0 10.5V30.5C0 36.0228 4.47715 40.5 10 40.5H30C35.5228 40.5 40 36.0228 40 30.5V10.5C40 4.97715 35.5228 0.5 30 0.5H10Z'
              fill='#0C40DE'
            />
          </svg>
          <div className='num relative'>
            <input
              type='text'
              className='numberInput opacity-0'
              defaultValue={pageNumber}
              onChange={(value: any) => {
                inputNumber(value);
              }}
            />
            <div className='absolute top-0 left-[1.08rem]'>{pageNumber}</div>
          </div>
        </div>
        {t('页')}
        <div className='jumpBtn' onClick={jump}>
          {t('跳转')}
        </div>
      </div>
    </div>
  );
};
export default TableStep;
