import React, { useState, useRef, useEffect } from 'react';
import { Upload, Button, Typography, App } from 'antd';
import type { RcFile, UploadProps } from 'antd/es/upload';

const MAX_IMAGE_SIZE = 600; // 最大图片尺寸
const PREVIEW_SIZE = 150; // 预览头像的大小

interface AvatarUploaderProps {
  onCrop: (croppedImageUrl: string) => void;
}

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
const AvatarUploader: React.FC<AvatarUploaderProps> = ({ onCrop }) => {
  const { message } = App.useApp();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  const cropRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0, width: 200, height: 200 });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [displaySize, setDisplaySize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (imageUrl) {
      const image = new Image();
      image.onload = () => {
        const { width, height } = calculateAspectRatioFit(
          image.width,
          image.height,
          MAX_IMAGE_SIZE,
          MAX_IMAGE_SIZE,
        );
        console.log('width:', width, 'height:', height);
        setImageSize({ width: image.width, height: image.height });
        setDisplaySize({ width, height });
        imageRef.current = image;
        // 重置裁剪框到新图片的中心
        const size = Math.min(width, height);
        setCrop({
          x: (width - size) / 2,
          y: (height - size) / 2,
          width: size,
          height: size,
        });
      };
      image.src = imageUrl;
    }
  }, [imageUrl]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isResizing && containerRef.current && cropRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        let newWidth = crop.width;
        let newHeight = crop.height;
        const newX = crop.x;
        const newY = crop.y;

        if (resizeDirection?.includes('e')) {
          newWidth = Math.min(
            Math.max(50, e.clientX - containerRect.left - crop.x),
            displaySize.width - crop.x,
          );
        }
        if (resizeDirection?.includes('s')) {
          newHeight = Math.min(
            Math.max(50, e.clientY - containerRect.top - crop.y),
            displaySize.height - crop.y,
          );
        }

        // 保持裁剪框为正方形
        const size = Math.min(newWidth, newHeight);
        setCrop({
          x: Math.max(0, Math.min(newX, displaySize.width - size)),
          y: Math.max(0, Math.min(newY, displaySize.height - size)),
          width: size,
          height: size,
        });
      } else if (
        isDragging &&
        !isResizing &&
        containerRef.current &&
        cropRef.current
      ) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const cropRect = cropRef.current.getBoundingClientRect();
        const newX = e.clientX - containerRect.left - cropRect.width / 2;
        const newY = e.clientY - containerRect.top - cropRect.height / 2;
        setCrop((prev) => ({
          ...prev,
          x: Math.max(0, Math.min(newX, displaySize.width - prev.width)),
          y: Math.max(0, Math.min(newY, displaySize.height - prev.height)),
        }));
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      setResizeDirection(null);
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, isResizing, resizeDirection, crop, displaySize]);

  useEffect(() => {
    if (imageRef.current) {
      updateCroppedImage();
    }
  }, [crop, imageSize, displaySize]);

  const calculateAspectRatioFit = (
    srcWidth: number,
    srcHeight: number,
    maxWidth: number,
    maxHeight: number,
  ) => {
    const ratio = Math.min(maxWidth / srcWidth, maxHeight / srcHeight);
    return { width: srcWidth * ratio, height: srcHeight * ratio };
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must smaller than 2MB!');
    }
    return isJpgOrPng && isLt2M;
  };

  const handleChange: UploadProps['onChange'] = (info) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      getBase64(info.file.originFileObj as RcFile).then((url) => {
        setImageUrl(url);
      });
    }
  };

  const updateCroppedImage = () => {
    if (canvasRef.current && imageRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        canvas.width = PREVIEW_SIZE;
        canvas.height = PREVIEW_SIZE;

        const scaleX = imageSize.width / displaySize.width;
        const scaleY = imageSize.height / displaySize.height;

        ctx.beginPath();
        ctx.arc(
          PREVIEW_SIZE / 2,
          PREVIEW_SIZE / 2,
          PREVIEW_SIZE / 2,
          0,
          Math.PI * 2,
        );
        ctx.closePath();
        ctx.clip();

        ctx.drawImage(
          imageRef.current,
          crop.x * scaleX,
          crop.y * scaleY,
          crop.width * scaleX,
          crop.height * scaleY,
          0,
          0,
          PREVIEW_SIZE,
          PREVIEW_SIZE,
        );

        const croppedImage = canvas.toDataURL('image/png');
        setCroppedImageUrl(croppedImage);
        onCrop(croppedImage);
      }
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!isResizing) {
      setIsDragging(true);
    }
  };

  const handleResizeStart = (direction: string) => (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsResizing(true);
    setIsDragging(false);
    setResizeDirection(direction);
  };

  return (
    <div className='flex flex-col space-y-6'>
      <Upload
        showUploadList={false}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        customRequest={({ onSuccess }) =>
          setTimeout(() => onSuccess?.('ok', undefined), 0)
        }>
        <Button>上传头像</Button>
      </Upload>
      {imageUrl && (
        <div className='flex justify-center items-start space-x-8'>
          <div
            ref={containerRef}
            className='relative border border-gray-200 rounded-lg'
            style={{
              width: `${displaySize.width}px`,
              height: `${displaySize.height}px`,
            }}>
            <img
              src={imageUrl}
              alt='Original'
              className='h-auto'
              style={{
                width: `${displaySize.width}px`,
                height: `${displaySize.height}px`,
              }}
            />
            <div
              ref={cropRef}
              className='absolute border-2 border-blue-500 cursor-move'
              style={{
                left: `${crop.x}px`,
                top: `${crop.y}px`,
                width: `${crop.width}px`,
                height: `${crop.height}px`,
              }}
              onMouseDown={handleMouseDown}>
              <div
                className='absolute top-0 right-0 w-4 h-4 bg-blue-500 cursor-ne-resize'
                onMouseDown={handleResizeStart('ne')}
              />
              <div
                className='absolute bottom-0 right-0 w-4 h-4 bg-blue-500 cursor-se-resize'
                onMouseDown={handleResizeStart('se')}
              />
            </div>
          </div>

          {croppedImageUrl && (
            <div className='flex flex-col items-center'>
              <Typography.Title level={5} className='mb-2'>
                预览
              </Typography.Title>
              <div
                className='rounded-full overflow-hidden border-4 border-gray-200'
                style={{
                  width: `${PREVIEW_SIZE}px`,
                  height: `${PREVIEW_SIZE}px`,
                }}>
                <img
                  src={croppedImageUrl}
                  alt='Cropped'
                  className='w-full h-full object-cover'
                />
              </div>
            </div>
          )}
        </div>
      )}

      <canvas ref={canvasRef} className='hidden' />
    </div>
  );
};

export default AvatarUploader;
