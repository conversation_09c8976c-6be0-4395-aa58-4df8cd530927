import { useEffect, useState } from 'react';
import { CalendarCard } from '@nutui/nutui-react';
import { Drawer, Button } from 'antd';
import { ConfigProvider as NutUIConfigProvider } from '@nutui/nutui-react';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

type PropsType = {
  getTime: any;
  width: string;
};

const MyDatePicker: React.FC<PropsType> = (props) => {
  const { getTime, width } = props;
  const [timeArr, setTimeArr] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const onChange = (values: any) => {
    const time: any[] = [];
    const _time: any[] = [];
    if (values.length === 2) {
      values.map((item: any) => {
        time.push(dayjs(item).format('YY-MM-DD'));
        _time.push(dayjs(item).format('YYYY-MM-DD'));
      });
      getTime(_time);
      setTimeArr([...time]);
    }
  };
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  const onOk = () => {
    setOpen(false);
  };
  useEffect(() => {
    // 获取当前时间过去六个月起止时间
    const now = dayjs();
    const sixMonthsAgo = now.subtract(6, 'month');
    const formattedNowDate = now.format('YY-MM-DD');
    const formattedSixMonthsAgoDate = sixMonthsAgo.format('YY-MM-DD');
    setTimeArr([formattedSixMonthsAgoDate, formattedNowDate]);
  }, []);
  return (
    <div
      className='rounded-lg overflow-hidden'
      style={{ width: `${width}`, height: '40px' }}>
      <div
        style={{
          backgroundColor: '#f4f4f5',
          color: '#71717a',
          height: '40px',
          lineHeight: '40px',
        }}
        className='px-3 flex justify-between'
        onClick={showDrawer}>
        {timeArr ? (
          <>
            <div>{timeArr[0]}</div>
            <div>{'->'}</div>
            <div>{timeArr[1]}</div>
          </>
        ) : (
          '日期选择'
        )}
      </div>
      <NutUIConfigProvider>
        <Drawer
          title='请选择日期范围'
          placement='bottom'
          height='auto'
          onClose={onClose}
          open={open}>
          <CalendarCard
            type='range'
            onChange={(values: any) => onChange(values)}
          />
          <div className='py-2 flex justify-center'>
            <Button
              onClick={onOk}
              className='w-60'
              style={{
                backgroundColor: '#0070f033',
                color: '#0070f0',
                border: 'none',
              }}>
              确定
            </Button>
          </div>
        </Drawer>
      </NutUIConfigProvider>
    </div>
  );
};
export default MyDatePicker;
