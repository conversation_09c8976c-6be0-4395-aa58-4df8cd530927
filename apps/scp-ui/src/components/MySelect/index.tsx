import React, { useEffect, useState } from 'react';
import { Listbox, ListboxItem } from '@nextui-org/react';

type PropsType = {
  itemKey: any;
  data: any;
  title: string;
  isDisable: boolean;
  width: string;
  getMultipleSelectIndex: any;
  viewType: any;
};

const MySelect: React.FC<PropsType> = (props) => {
  const {
    itemKey,
    data,
    title,
    isDisable,
    width,
    getMultipleSelectIndex,
    viewType,
  } = props;
  const [list, setList] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isChoose, setIsChoose] = useState<boolean>(false);
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = React.useState(new Set(['']));
  const [isLonger, setIsLonger] = useState<boolean>(false);
  const chooseItem = (value: any) => {
    console.log(value);

    const _chooseList = chooseList;
    let isChoosed = false;
    for (let i = 0; i < chooseList.length; i++) {
      if (chooseList[i].value === value.value) {
        _chooseList.splice(i, 1);
        isChoosed = true;
      }
    }
    if (!isChoosed) {
      _chooseList.push(value);
    }

    const setArr: any[] = [];
    _chooseList.map((item: any) => {
      setArr.push(item.key);
    });
    setSelectedKeys(new Set(setArr));
    setChooseList([..._chooseList]);
    chooseList.length > 0 ? setIsChoose(true) : setIsChoose(false);
    getMultipleSelectIndex(_chooseList, itemKey);
  };

  const clickButton = () => {
    if (isDisable) {
      setIsOpen(!isOpen);
    }
  };

  const vaguelySearch = (value: any) => {
    const _list: any[] = [];
    for (let i = 0; i < data.length; i++) {
      if (data[i].value.includes(value)) {
        _list.push(data[i]);
      }
    }
    setList([..._list]);
  };

  useEffect(() => {
    setList(data);
  }, []);

  useEffect(() => {
    if (isDisable) {
      const info: any = JSON.parse(
        localStorage.getItem(viewType.value + 'contentChoose') as any,
      );
      const _chooseList: any[] = [];
      if (info) {
        if (info.key === itemKey) {
          const arr = info.contentChoose.split(',');
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < data.length; j++) {
              if (arr[i] === data[j].key) {
                _chooseList.push(data[j]);
              }
            }
          }
        } else {
          for (let i = 0; i < 1; i++) {
            _chooseList.push(data[i]);
          }
        }
      } else {
        for (let i = 0; i < 1; i++) {
          _chooseList.push(data[i]);
        }
      }
      const setArr: any[] = [];
      _chooseList.map((item: any) => {
        setArr.push(item.key);
      });
      _chooseList.length > 0 ? setIsChoose(true) : setIsChoose(false);
      setSelectedKeys(new Set(setArr));
      setChooseList([..._chooseList]);
      getMultipleSelectIndex(_chooseList, itemKey);
    } else {
      setSelectedKeys(new Set([]));
      setChooseList([]);
    }
  }, [isDisable]);

  useEffect(() => {
    function getBodyHeight() {
      return (
        document.body.clientHeight || document.documentElement.clientHeight
      );
    }
    const myContent = document.getElementsByClassName('myContent') as any;
    const height = getBodyHeight();
    const REM = 16 * (height / 1080);
    const maxWidth = width === '49%' ? REM * 7.5 : REM * 3.5;
    const myContentWidth = myContent[0].offsetWidth;
    if (myContentWidth >= maxWidth) {
      setIsLonger(true);
    } else {
      setIsLonger(false);
    }
  }, [chooseList]);
  return (
    <div
      className='relative '
      style={{ display: `${isDisable ? 'block' : 'none'}`, width: `${width}` }}>
      <div
        className='opacity-0  fixed top-0 left-0'
        style={{
          width: '100vw',
          height: '100vh',
          display: `${isOpen ? 'block' : 'none'}`,
        }}
        onClick={clickButton}></div>
      <div className='text-lg h-12' onClick={clickButton}>
        <button
          className='relative w-full inline-flex shadow-sm tap-highlight-transparent rounded-small flex-col items-start justify-center  gap-0 outline-none h-12 min-h-unit-12 py-1.5 px-3'
          style={{ backgroundColor: `${isDisable ? '#f4f4f5' : '#f4f4f5'}` }}>
          <div
            className='absolute z-10  text-foreground-500 text-sm'
            style={{
              transition: 'all 0.25s',
              transformOrigin: 'left top',
              transform: `${
                isOpen || isChoose
                  ? 'translate(0,-8px) scale(0.85)'
                  : 'scale(1)'
              }`,
              color: `${
                isDisable ? (isOpen || isChoose ? '#52525b' : '') : '#b4b4b9'
              }`,
            }}>
            {title}选择
          </div>
          <div
            className='absolute z-10  text-foreground-500 text-xs bottom-1'
            style={{
              fontSize: '10px',
              display: `${isOpen || isChoose ? 'block' : 'none'}`,
            }}>
            <div
              className='flex overflow-hidden'
              style={{ maxWidth: `${width === '49%' ? '9rem' : '5rem'}` }}>
              <div
                className='overflow-hidden'
                style={{
                  whiteSpace: 'nowrap',
                  maxWidth: `${width === '49%' ? '7.5rem' : '3.5rem'}`,
                }}>
                <div
                  className='flex myContent'
                  style={{ whiteSpace: 'nowrap' }}>
                  {chooseList &&
                    chooseList.map((item: any, index: number) => {
                      return (
                        <div
                          key={index}
                          style={{
                            backgroundColor: '#0070f033',
                            color: '#0070f0',
                          }}
                          className='mr-0.5 px-0.5 py-0.5 rounded-md'>
                          {
                            item.value.split('/')[
                              item.value.split('/').length - 1
                            ]
                          }
                        </div>
                      );
                    })}
                </div>
              </div>
              <div
                style={{
                  color: '#0070f0',
                  display: `${isLonger ? 'block' : 'none'}`,
                }}
                className='ml-[0.5rem]'>
                ⋯
              </div>
            </div>
          </div>
          <svg
            aria-hidden='true'
            fill='none'
            focusable='false'
            height='1em'
            role='presentation'
            stroke='currentColor'
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth='1.5'
            viewBox='0 0 24 24'
            width='1em'
            data-slot='selectorIcon'
            className='absolute right-3 w-unit-4 h-unit-4'
            style={{
              transition: 'transform 0.2s',
              transform: `${isOpen ? 'rotate(180deg)' : 'rotate(0deg)'}`,
            }}>
            <path d='m6 9 6 6 6-6'></path>
          </svg>
        </button>
      </div>
      <div
        className='w-full text-sm rounded-large overflow-hidden overflow-y-auto absolute shadow-medium mt-1 bg-white'
        style={{
          maxHeight: '154px',
          transition: 'all 0.2s',
          border: '0.7px solid #e5e7eb',
          zIndex: `${isOpen ? '99' : '-1'}`,
          transformOrigin: 'top',
          transform: `${isOpen ? 'scale(1)' : 'scale(0)'}`,
        }}>
        {/* 搜索框 */}
        <div className='w-full p-2 bg-white relative'>
          <div
            style={{ border: '1px solid #d4d4d8', fontSize: '12px' }}
            className='rounded-sm pl-2 pr-5'>
            <input
              type='text'
              style={{ fontSize: '12px' }}
              className='w-full'
              onChange={(item) => vaguelySearch(item.target.value)}
            />
            <svg
              viewBox='64 64 896 896'
              focusable='false'
              data-icon='search'
              width='1em'
              height='1em'
              fill='#d4d4d8'
              aria-hidden='true'
              className='absolute right-3 top-3'>
              <path d='M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z'></path>
            </svg>
          </div>
        </div>
        <Listbox
          style={{ color: '#11181c' }}
          className=' rounded-b-lg left-0 p-2 bg-white border-t-1'
          aria-label='Multiple selection example'
          disallowEmptySelection
          selectionMode='multiple'
          selectedKeys={selectedKeys}
          onSelectionChange={setSelectedKeys as any}>
          {list.map((i: any) => (
            <ListboxItem
              key={i.key}
              value={i.value}
              className='px-2 my-0.25 h-8 leading-8 rounded-small'
              onClick={() => {
                chooseItem(i);
              }}>
              {i.value}
            </ListboxItem>
          ))}
        </Listbox>
      </div>
    </div>
  );
};

export default MySelect;
