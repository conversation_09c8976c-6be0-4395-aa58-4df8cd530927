import { ConfigProvider, Table } from 'antd';
import { useState, useEffect } from 'react';
import type { TableProps } from 'antd/lib/table';
import { Input } from 'antd';
import './JWTable.css';

type JWTableDataSource = {
  key: string;
  [key: string]: any;
  loading: boolean;
};
const JWTable: React.FC<TableProps<JWTableDataSource>> = (props) => {
  const [columns, setColumns] = useState<any[]>([]);
  const [newColumns, setNewColumns] = useState<any[]>([]);

  // 数据渲染
  useEffect(() => {
    const _testColumns = props.columns?.map((item: any) => {
      if (item.isFilter) {
        item.filters = props.dataSource
          ?.map((data: any) => {
            return {
              text: data[item.dataIndex],
              value: data[item.dataIndex],
            };
          })
          .filter(
            (filter, index, self) =>
              index ===
              self.findIndex(
                (t) => t.text === filter.text && t.value === filter.value,
              ),
          );
        item.onFilter = (value: any, record: any) => {
          return record[item.dataIndex] === value;
        };
        item.filterSearch = true;
      }
      return item;
    });
    setColumns(_testColumns as any[]);
    setNewColumns(_testColumns as any[]);
  }, [props]);

  // 筛选弹窗相关逻辑

  // 弹出框dom
  const filterTree = document.getElementById('filterTree');
  // 头部右键点击弹出筛选框
  const onHeaderRow = () => {
    return {
      onContextMenu: (event: any) => {
        event.preventDefault();
        // console.log(newColumns);
        filterTree!.style.display = 'block';
        filterTree!.style.top = event.pageY + 'px';
        if (event.pageX >= window.innerWidth - 200) {
          filterTree!.style.left = event.pageX - 200 + 'px';
        } else {
          filterTree!.style.left = event.pageX + 'px';
        }
        // 遍历判断哪些选中
        newColumns.map((item: any) => {
          const titleDom1 = document.getElementById(
            '_' + `${item.title}`,
          ) as HTMLInputElement;
          titleDom1!.checked = true;
        });
      },
    };
  };

  // 单击页面任何位置(除弹窗内)都会使筛选框消失
  // 判断是否单机区域为弹窗内
  let isOnfilterTree: boolean = false;
  const hold = () => {
    isOnfilterTree = true;
  };
  // 单击页面内任意位置判断弹窗是否显示
  document.onclick = function () {
    if (isOnfilterTree === true) {
      filterTree!.style.display = 'block';
      isOnfilterTree = false;
    } else {
      filterTree!.style.display = 'none';
    }
  };

  // 选中选项的函数

  const titleArray: string[] = [];
  newColumns.map((item: any) => {
    titleArray.push(item.title);
  });
  const chooseOptionHandler = (title: string, isClickInput: boolean) => {
    const titleDom = document.getElementById(
      '_' + `${title}`,
    ) as HTMLInputElement;
    if (!isClickInput) {
      if (titleDom!.checked) {
        titleArray.splice(titleArray.indexOf(title), 1);
      } else {
        titleArray.push(title);
      }
      titleDom!.checked = !titleDom!.checked;
    } else {
      if (titleDom!.checked) {
        titleArray.push(title);
      } else {
        titleArray.splice(titleArray.indexOf(title), 1);
      }
    }
    console.log(titleArray);
  };
  const cancelHandler = () => {
    setNewColumns(columns);
  };
  const okHandler = () => {
    const newColumnsArray: any[] = [];
    columns.map((item: any) => {
      for (let i: number = 0; i < titleArray.length; i++) {
        if (titleArray[i] === item.title) {
          newColumnsArray.push(item);
        }
      }
    });
    setNewColumns(newColumnsArray);
  };

  const inputChangeHandler = (value: string) => {
    console.log(value);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            borderColor: '#EFF2F3',
            headerBg: '#ffffff',
            headerColor: '#293147',
            headerSplitColor: '#ffffff',
            cellPaddingBlock: 10,
          },
        },
      }}>
      <Table
        {...props}
        columns={newColumns}
        bordered={false}
        onHeaderRow={onHeaderRow}
        pagination={false}
        loading={props.loading}
      />
      <div
        id={'filterTree'}
        onClick={() => {
          hold();
        }}
        className='bg-white'
        style={{
          display: 'none',
          position: 'absolute',
          zIndex: '9',
          width: '200px',
          borderRadius: '7px',
          boxShadow: '0 0 7px 2px rgba(0,0,0,0.1)',
          transition: 'all 0.2s',
        }}>
        {/* 搜索框 */}
        <div style={{ width: '100%', padding: '8px' }}>
          <Input
            placeholder='在筛选项中搜索'
            onChange={(e) => inputChangeHandler(e.target.value)}
            style={{
              padding: '4px 11px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              transition: 'all 0.2s',
            }}
          />
        </div>

        <div
          style={{
            width: '100%',
            height: '1px',
            backgroundColor: '#f0f0f0',
          }}></div>

        {/* 选择树 */}
        <div style={{ width: '100%', padding: '4px' }}>
          <ul style={{ width: '100%', fontSize: '14px' }}>
            {columns.map((item: any) => {
              return (
                <li
                  key={item.key}
                  style={{
                    width: '100%',
                    display: 'flex',
                    padding: '5px 12px',
                    cursor: 'pointer',
                    borderRadius: '4px',
                  }}
                  onClick={() => chooseOptionHandler(item.title, false)}>
                  <input
                    id={'_' + `${item.title}`}
                    type='checkbox'
                    style={{ width: '16px' }}
                    onChange={() => {}}
                    onClick={(e) => {
                      e.stopPropagation();
                      chooseOptionHandler(item.title, true);
                    }}
                  />
                  <div style={{ paddingLeft: '8px' }}>{item.title}</div>
                </li>
              );
            })}
          </ul>
        </div>

        <div
          style={{
            width: '100%',
            height: '1px',
            backgroundColor: '#f0f0f0',
          }}></div>

        {/* 按钮 */}
        <div
          className='ant-table-filter-dropdown-btns'
          style={{
            width: '100%',
            padding: '7px 8px',
            display: 'flex',
            justifyContent: 'space-between',
          }}>
          <button
            id={'resetting'}
            type='button'
            className='ant-btn css-dev-only-do-not-override-10yinqi ant-btn-link ant-btn-sm'
            onClick={() => {
              cancelHandler();
            }}
            disabled={true}>
            <span>重置</span>
          </button>
          <button
            type='button'
            className='ant-btn css-dev-only-do-not-override-10yinqi ant-btn-primary ant-btn-sm'
            onClick={() => {
              okHandler();
            }}>
            <span>确 定</span>
          </button>
        </div>
      </div>
    </ConfigProvider>
  );
};
export default JWTable;
