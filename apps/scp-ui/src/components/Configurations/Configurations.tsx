import { useState } from 'react';
import { ConfigProvider, Flex, Input, Button, Menu } from 'antd';
import { createStyles } from 'antd-style';
import AttributeConfiguration from './children/AttributeConfiguration/AttributeConfiguration';
import MdTypeConfiguration from './children/MdTypeConfiguration/MdTypeConfiguration';
import TimeProfileConfiguration from './children/TimeProfileConfiguration/TimeProfileConfiguration';
import PlanLevelConfiguration from './children/PlanLevelConfiguration/PlanLevelConfiguration';
import KeyFigureConfiguration from './children/KeyFigureConfiguration/KeyFigureConfiguration';
import ItemTypeConfiguration from './children/ItemTypeConfiguration/ItemTypeConfiguration';
import ForecastConfiguration from './children/ForecastConfiguration';
import { Pagination } from '../Pagination/Pagination';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

const useStyle = createStyles(({ css }: { css: any }) => {
  return {
    menu: css`
      background-color: transparent;
    `,
  };
});

function Configurations() {
  const { t } = useVoerkaI18n();

  const { styles } = useStyle();

  const [configTypeOption, setConfigTypeOption] = useState('Attribute');

  const [searchValue, setSearchValue] = useState('');

  const [searchedValue, setSearchedValue] = useState('');
  const showPaginationList = ['TimeProfile', 'Forecast'];

  const [total, setTotal] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const configTypeOptions = [
    {
      label: t('属性'),
      value: 'Attribute',
      key: 'Attribute',
    },
    {
      label: t('主数据类型'),
      value: 'MdType',
      key: 'MdType',
    },
    {
      label: t('时间配置'),
      value: 'TimeProfile',
      key: 'TimeProfile',
    },
    {
      label: t('计划层级'),
      value: 'PlanLevel',
      key: 'PlanLevel',
    },
    {
      label: t('关键指标'),
      value: 'KeyFigure',
      key: 'KeyFigure',
    },
    {
      label: t('条目类型'),
      value: 'ItemType',
      key: 'ItemType',
    },
    {
      label: t('预测冲销'),
      value: 'Forecast',
      key: 'Forecast',
    },
  ].map((item) => {
    const innerStyle = {
      display: 'block',
      padding: '0 18px',
      position: 'relative' as React.CSSProperties['position'],
      height: '100%',
    };
    return {
      ...item,
      label: (
        <span style={innerStyle}>
          {item.label}
          <div
            style={{
              width: '100%',
              height: '4px',
              position: 'absolute',
              bottom: 0,
              left: 0,
              borderRadius: '4px',
              backgroundColor:
                item.value === configTypeOption ? '#2F6BFF' : '#fff',
            }}></div>
        </span>
      ),
      style: {
        borderRadius: '8px',
        marginRight: '6px',
        height: '40px',
        lineHeight: '40px',
        padding: '0 12px',
        border: 'none',
        backgroundColor: 'white',

        fontFamily: 'Roboto',
        fontSize: '14px',
        fontStyle: 'normal',
        fontWeight: 500,
        letterSpacing: '0.72px',
      },
    };
  });

  return (
    <ConfigProvider
      theme={{
        components: {
          Radio: {
            colorBorder: 'none',
            buttonColor: '#B9BCC6',
            buttonSolidCheckedColor: '#0C40DE',
            buttonSolidCheckedBg: '#fff',
            buttonSolidCheckedHoverBg: '#fff',
            colorPrimary: 'none',
            colorPrimaryActive: 'none',
            colorPrimaryHover: 'none',
          },
        },
      }}>
      <Flex
        vertical={true}
        style={{
          height: '100%',
        }}>
        <Flex justify='space-between'>
          <ConfigProvider
            theme={{
              components: {
                Menu: {
                  horizontalItemSelectedColor: 'none',
                },
              },
            }}>
            <Menu
              className={styles.menu}
              items={configTypeOptions}
              mode={'horizontal'}
              onClick={(item) => {
                setConfigTypeOption(item.key);
                setSearchedValue('');
                setSearchValue('');
                setTotal(0);
                setCurrentPage(1);
                setPageSize(10);
              }}
              style={{
                flex: 'auto',
                minWidth: '0',
                borderBottom: 'none',
              }}></Menu>
          </ConfigProvider>
          <Flex align='center' gap={'20px'}>
            <Input
              style={{
                height: '32px',
              }}
              placeholder={t('搜索')}
              value={searchValue}
              onChange={(e) => {
                setSearchValue(e.target.value);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setSearchedValue(searchValue);
                }
              }}></Input>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    // defaultColor: '#0D152D',
                    // colorBorder: '#848A99',
                  },
                },
              }}>
              <Button
                type='primary'
                style={{
                  borderRadius: '36px',
                  padding: '10px 20px',
                }}
                onClick={() => {
                  setSearchedValue(searchValue);
                }}>
                {t('搜索')}
              </Button>
            </ConfigProvider>
          </Flex>
        </Flex>
        <div className='h-full my-[10px]'>
          {(() => {
            switch (configTypeOption) {
              case 'Attribute':
                return (
                  <AttributeConfiguration
                    searchedValue={searchedValue}
                    setTotal={setTotal}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                  />
                );
              case 'MdType':
                return (
                  <MdTypeConfiguration
                    searchedValue={searchedValue}
                    setTotal={setTotal}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                  />
                );
              case 'TimeProfile':
                return <TimeProfileConfiguration />;
              case 'PlanLevel':
                return (
                  <PlanLevelConfiguration
                    searchedValue={searchedValue}
                    setTotal={setTotal}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                  />
                );
              case 'KeyFigure':
                return (
                  <KeyFigureConfiguration
                    searchedValue={searchedValue}
                    setTotal={setTotal}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                  />
                );
              case 'ItemType':
                return (
                  <ItemTypeConfiguration
                    searchedValue={searchedValue}
                    setTotal={setTotal}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                  />
                );
              case 'Forecast':
                return <ForecastConfiguration />;
              default:
                return null;
            }
          })()}
        </div>
        <Flex justify='center'>
          {!showPaginationList.includes(configTypeOption) ? (
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={total}
              onPageChange={setCurrentPage}
            />
          ) : (
            <></>
          )}
        </Flex>
      </Flex>
    </ConfigProvider>
  );
}

export default Configurations;
