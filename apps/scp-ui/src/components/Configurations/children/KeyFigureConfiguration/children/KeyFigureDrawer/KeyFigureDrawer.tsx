import { useState, useEffect } from 'react';
import { Drawer, Flex, ConfigProvider, Input, Select, Table, Spin } from 'antd';
import LeftArrow from '/public/leftArrow.svg';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

const getExpression = (exp: any) => {
  const spanStyle = {
    display: 'block',
    whiteSpace: 'nowrap',
    margin: '5px 8px 5px 0px',
  };
  const darkStyle = {
    backgroundColor: 'rgb(217, 217, 217)',
    height: '32px',
    lineHeight: '32px',
    borderRadius: '6px',
    padding: '0 12px',
    display: 'block',
  };
  const selectStyle = {
    marginRight: '8px',
  };
  if (!exp) {
    return null;
  }
  if (!exp.calculate) {
    return (
      <Flex align='center' vertical={false}>
        {exp.aggOperationValue && exp.aggOperationValue.length > 0 ? (
          <span
            style={{
              ...spanStyle,
              ...darkStyle,
            }}>
            {exp.aggOperationValue}
          </span>
        ) : (
          <></>
        )}
        {'('}
        <span
          style={{
            ...spanStyle,
            ...darkStyle,
            marginLeft: '8px',
          }}>
          {exp.fieldFirst}
        </span>
        <Select value={'@' + exp.fieldSecond} style={selectStyle}></Select>
        {')'}
      </Flex>
    );
  }
  const result = (
    <div
      style={{
        display: 'ruby',
      }}>
      {exp.aggOperationValue && exp.aggOperationValue.length > 0 ? (
        <span
          style={{
            ...spanStyle,
            ...darkStyle,
          }}>
          {exp.aggOperationValue}
        </span>
      ) : (
        <></>
      )}
      {'('}
      <div
        style={{
          width: '8px',
          height: '100%',
          display: exp.calculate.operand1 ? 'block' : 'none',
        }}></div>
      {getExpression(exp.calculate.operand1)}
      {exp.calculate.operand2 ? (
        <Select
          value={exp.calculate.operationValue}
          style={{
            ...selectStyle,
            marginLeft: '8px',
          }}></Select>
      ) : (
        <></>
      )}
      {exp.calculate.operand2 ? getExpression(exp.calculate.operand2) : <></>}
      {')'}
    </div>
  );
  return result;
};

const getFormattedExpression = (exp: any, key?: any) => {
  const spanStyle = {
    whiteSpace: 'nowrap',
    margin: '5px 8px 5px 0px',
  };
  const darkStyle = {
    backgroundColor: 'rgb(217, 217, 217)',
    height: '32px',
    lineHeight: '32px',
    borderRadius: '6px',
    padding: '0 12px',
    display: 'block',
  };
  const selectStyle = {
    marginRight: '8px',
  };
  const result = (
    <div
      key={key}
      style={{
        display: 'ruby',
      }}>
      <span
        style={{
          ...spanStyle,
          ...darkStyle,
        }}>
        {exp.kfLabel}
      </span>
      <Select value={'@' + exp.plBaseName} style={selectStyle}></Select>
      {'='}
      {exp.expression.aggOperationValue ? (
        <span
          style={{
            ...spanStyle,
            ...darkStyle,
            marginLeft: '8px',
          }}>
          {exp.expression.aggOperationValue}
        </span>
      ) : (
        <></>
      )}
      <div
        style={{
          width: '8px',
          height: '100%',
          display:
            exp.expression.operand1 && !exp.expression.aggOperationValue
              ? 'block'
              : 'none',
        }}></div>
      {getExpression(exp.expression.operand1)}
      {exp.expression.operand2 ? (
        <Select
          value={exp.expression.operationValue}
          style={{
            ...selectStyle,
            marginLeft: '8px',
          }}></Select>
      ) : (
        <></>
      )}
      {exp.expression.operand2 ? getExpression(exp.expression.operand2) : <></>}
    </div>
  );
  return result;
};

// const getTextWidth = (text: string, font: string) => {
//   const canvas = document.createElement('canvas');
//   const context = canvas.getContext('2d');
//   if (!context) {
//     return 0;
//   }
//   context.font = font;
//   return context.measureText(text).width;
// };

function KeyFigureDrawer({
  drawerOpen,
  setDrawerOpen,
  drawerData,
}: {
  drawerOpen: boolean;
  setDrawerOpen: (open: boolean) => void;
  drawerData: any;
}) {
  const { t } = useVoerkaI18n();

  const [loading, setLoading] = useState(true);
  const [detailData, setDetailData] = useState<any>(null);
  const [tableScrollY, setTableScrollY] = useState(0);
  const [tableLoading, setTableLoading] = useState(true);
  const labels = [
    {
      label: t('名称'),
      key: 'label',
      double: false,
    },
    {
      label: t('是否为系统内置'),
      key: 'isSystemName',
      double: false,
    },
    {
      label: t('基础计划等级'),
      key: 'planLevelName',
      double: false,
    },
    {
      label: t('是否与时间相关'),
      key: 'periodWorkName',
      double: false,
    },
    {
      label: t('类别'),
      key: 'kfTypeName',
      double: false,
    },
    {
      label: t('聚合模式'),
      key: 'aggOperationMode',
      double: false,
    },
    {
      label: t('分解模式'),
      key: 'disaggregatedMode',
      double: false,
    },
    {
      label: t('分解依赖比例'),
      key: 'proportionality',
      double: false,
    },
    {
      label: t('参考特征值'),
      key: 'otherKfProportion',
      double: false,
    },
    {
      label: t('分解保留小数位'),
      key: 'round',
      double: false,
    },
    {
      label: t('过去预测天数'),
      key: 'backwardDays',
      double: false,
    },
    {
      label: t('未来预测天数'),
      key: 'forwardDays',
      double: false,
    },
    {
      label: t('预测冲销类型'),
      key: 'hedgeStrategyName',
      double: false,
    },
    {
      label: t('是否启用预测冲销'),
      key: 'isHedged',
      double: false,
    },
  ];

  const [data, setData] = useState<any>({
    label: '',
    isSystem: '',
    isSystemName: '',
    planLevelName: '',
    periodWork: '',
    periodWorkName: '',
    kfTypeName: '',
    aggOperationMode: '',
    disaggregatedMode: '',
    otherKfProportion: '',
    proportionality: '',
    disaggregatedModeCode: '',
    proportionalityCode: '',
    isDisaggregation: false,
    round: '',
    kfType: '',
    backwardDays: '',
    forwardDays: '',
    hedgeStrategyName: '',
    isHedged: '',
  });

  const getDetailData = async () => {
    const result = await ajax.getKeyFigureDetail({
      kfId: drawerData.kfId,
    });
    if (result.code === 0 && result.data) {
      setDetailData(result.data.exp);
    }
    data.isDisaggregation = result.data.isDisaggregation;
    data.aggOperationMode = result.data.aggOperationMode;
    data.disaggregatedMode = result.data.disaggregatedMode;
    data.otherKfProportion = result.data.otherKfProportion;
    data.proportionality = result.data.proportionality;
    data.disaggregatedModeCode = result.data.disaggregatedModeCode;
    data.proportionalityCode = result.data.proportionalityCode;
    data.isSystem = result.data.isCustom;
    data.isSystemName = result.data.isSystemName;
    data.periodWorkName = result.data.periodWorkName;
    data.round = result.data.round;

    data.kfType = result.data.kfType;
    if (data.kfType === 4) {
      const hedgeInfo = result.data.hedgeInfo;
      data.backwardDays = hedgeInfo.backwardDays;
      data.forwardDays = hedgeInfo.forwardDays;
      data.hedgeStrategyName = hedgeInfo.hedgeStrategyName;
      data.isHedged = hedgeInfo.isHedged ? '是' : '否';
    }

    setData(data);
    setTimeout(() => {
      setTableLoading(false);
    }, 500);
  };

  useEffect(() => {
    if (!drawerData || !drawerOpen) {
      return;
    }
    data.label = drawerData.label;
    data.planLevelName = drawerData.planLevelName;
    data.kfTypeName = drawerData.kfTypeName;

    setData(data);
    getDetailData();
    setLoading(false);
  }, [drawerData, drawerOpen]);

  return (
    <Drawer
      className='kf-drawer-body'
      afterOpenChange={() => {
        const tableContainer = document.querySelector(
          '#table-container',
        ) as HTMLDivElement;
        const drawerContainer = document.querySelector(
          '#drawer-container',
        ) as HTMLDivElement;
        if (tableContainer && drawerContainer) {
          setTableScrollY(
            drawerContainer.offsetHeight - tableContainer.offsetTop,
          );
        }
      }}
      loading={loading}
      style={{
        position: 'fixed',
        borderRadius: '10px',
        top: '10px',
        bottom: '10px',
        right: '15px',
        width: '800px',
        height: 'auto',
      }}
      open={drawerOpen}
      onClose={() => {
        setDrawerOpen(false);
        setLoading(true);
        setTableLoading(true);
      }}
      closeIcon={
        <Flex gap={'small'} vertical={false}>
          <img
            src={LeftArrow}
            style={{
              transform: 'rotate(180deg)',
            }}></img>
          <span>{t('关键指标')}</span>
        </Flex>
      }
      // extra={
      //   <ConfigProvider
      //     theme={{
      //       components: {
      //         Button: {
      //           defaultColor: '#0C40DE',
      //           colorBorder: '#0C40DE',
      //         },
      //       },
      //     }}>
      //     <Button
      //       style={{
      //         borderRadius: '36px',
      //       }}>
      //       {'保存'}
      //     </Button>
      //   </ConfigProvider>
      // }
    >
      <Flex
        vertical={true}
        gap={'16px'}
        id='drawer-container'
        style={{ height: '100%' }}>
        {labels.map((label: any) => {
          if (
            (label.key === 'aggOperationMode' ||
              label.key === 'disaggregatedMode') &&
            !data.isDisaggregation
          ) {
            return <></>;
          }
          if (
            label.key === 'proportionality' &&
            data.disaggregatedModeCode !== 'Equal'
          ) {
            return <></>;
          }
          if (
            label.key === 'otherKfProportion' &&
            data.proportionalityCode !== 'Other'
          ) {
            return <></>;
          }
          if (label.key === 'round' && !data.isDisaggregation) {
            return <></>;
          }
          if (
            data.kfType !== 4 &&
            (label.key === 'backwardDays' ||
              label.key === 'forwardDays' ||
              label.key === 'hedgeStrategyName' ||
              label.key === 'isHedged')
          ) {
            return <></>;
          }
          const labelStyle: React.CSSProperties = {
            width: '200px',
            textAlign: 'left',
            padding: '8px 20px',
            backgroundColor: '#EFF2F3',
            borderRadius: '12px 0px 0px 12px',
            whiteSpace: 'nowrap',

            /* 副标题/2 */
            fontFamily: 'PingFang SC',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: 600,
            lineHeight: 'normal',
            letterSpacing: '0.96px',
          };
          const inputStyle = {
            flex: 1,
            borderRadius: '0px 12px 12px 0px',
            border: '1px solid #EFF2F3',

            /* 副标题/2 */
            fontFamily: 'PingFang SC',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: 600,
            lineHeight: 'normal',
            letterSpacing: '0.96px',
          };
          return (
            <Flex key={label.key} vertical={false}>
              <span style={labelStyle}>{label.label}</span>
              <Input
                value={data[label.key]}
                style={{
                  ...inputStyle,
                  borderRadius:
                    label.double ||
                    (label.key === 'proportionality' &&
                      data.proportionality === '其他特征值')
                      ? '0'
                      : '0px 12px 12px 0px',
                }}></Input>
              {label.double ? (
                <>
                  <span
                    style={{
                      ...labelStyle,
                      borderRadius: '0',
                    }}>
                    {label.labelTwo}
                  </span>
                  <Input value={data[label.keyTwo]} style={inputStyle}></Input>
                </>
              ) : (
                <></>
              )}
            </Flex>
          );
        })}
        {/* <Flex
        vertical={true}
        gap={'middle'}>
          {detailData ? (
            detailData.map((exp: any, index: number) => {
              return getFormattedExpression(exp, index);
            })
          ) : (
            <></>
          )}
        </Flex> */}
        <div
          id='table-container'
          style={{
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
            paddingBottom: '8px',
            position: 'relative',
            display: detailData && data.isDisaggregation ? 'block' : 'none',
          }}>
          <ConfigProvider
            theme={{
              components: {
                Table: {
                  headerBg: 'none',
                  colorBgContainer: 'none',
                  rowHoverBg: '#fafafa',
                },
              },
            }}>
            <Table
              scroll={tableScrollY > 0 ? { y: tableScrollY } : {}}
              columns={[
                {
                  title: t('计算规则') + ':',
                  dataIndex: 'kf',
                  onHeaderCell: () => {
                    return {
                      style: {
                        padding: '8px 16px',

                        /* 副标题/2 */
                        fontFamily: 'PingFang SC',
                        fontSize: '16px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: 'normal',
                        letterSpacing: '0.96px',
                      },
                    };
                  },
                },
              ]}
              dataSource={
                detailData
                  ? detailData.map((exp: any, index: number) => {
                      return {
                        kf: getFormattedExpression(exp, index),
                      };
                    })
                  : []
              }
              pagination={false}></Table>
          </ConfigProvider>
          <Spin
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: '#fff',
              borderRadius: '12px',
              display: tableLoading ? 'flex' : 'none',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            spinning={tableLoading}></Spin>
        </div>
      </Flex>
    </Drawer>
  );
}

export default KeyFigureDrawer;
