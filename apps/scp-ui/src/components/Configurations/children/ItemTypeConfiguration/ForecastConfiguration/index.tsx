import React, { useEffect, useState } from 'react';
import { Switch, Select, InputNumber, Button, Form, App, Flex } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import { SelectProps } from 'antd/lib';
import _ from 'lodash';
import ajax from '@/api';

const ForecastConfiguration = () => {
  const { message } = App.useApp();
  const { t } = useVoerkaI18n();
  const [form] = Form.useForm();
  const [keyFeatureList, setKeyFeatureList] = useState<any[]>([]);
  const [forecastTypeList, setForecastTypeList] = useState<
    SelectProps['options']
  >([]);
  // 获取关键指标列表
  const getKeyFigureList = async () => {
    try {
      const res = await ajax.getKFigureList({ kfType: 4 });
      const list = _.get(res, 'data.list', []) || [];
      setKeyFeatureList(
        list.map((item) => ({
          value: item.kfId,
          label: item.label,
        })),
      );
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (keyFeatureList.length === 0) {
      return;
    }
    form.setFieldValue('kfId', keyFeatureList[0].value);
    getHedgeInfo();
  }, [keyFeatureList]);

  //获取预测冲销类型
  const getForecastTypeList = async () => {
    try {
      const res = await ajax.getHedgeStrategy();
      if (res.code === 0) {
        const list = _.get(res, 'data.list', []) || [];
        setForecastTypeList(
          list.map((item: any) => ({ label: item.name, value: item.code })),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getHedgeInfo = async () => {
    try {
      ajax
        .getHedgeInfo({
          kfId: form.getFieldValue('kfId'),
        })
        .then((res) => {
          const HedgeInfo = res.data.list;
          form.setFieldsValue({
            hedgeStrategy: HedgeInfo.hedgeStrategy,
            forwardDays: HedgeInfo.forwardDays,
            backwardDays: HedgeInfo.backwardDays,
          });
        })
        .catch((error) => {
          console.error(error);
        });
    } catch (error) {
      console.error(error);
    }
  };

  const submitForm = async (values?: any) => {
    if (!values) {
      values = form.getFieldsValue();
    }
    try {
      const res = await ajax.saveHedgeInfo(values);
      if (res.code === 0) {
        message.success(t('配置成功'));
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('错误:', error);
    }
  };

  //提交表单接口
  const onFinish = (values: any) => {
    submitForm(values);
  };

  useEffect(() => {
    getKeyFigureList();
    getForecastTypeList();
  }, []);

  return (
    <div className='w-full p-6 h-full bg-white rounded-2xl'>
      <div className='w-full'>
        <Form form={form} layout='vertical' onFinish={onFinish}>
          <div className='flex justify-between'>
            <div className='text-h2'>{t('预测冲销配置')}</div>
            <Form.Item
              label={<div className='text-s1'>{t('是否启用预测冲销')}</div>}
              name='isHedged'
              layout='horizontal'
              initialValue={true}>
              <Switch disabled />
            </Form.Item>
          </div>
          <Form.Item
            label={t('设置的关键指标')}
            name='kfId'
            rules={[{ required: true }]}>
            <Select options={keyFeatureList} />
          </Form.Item>
          <Form.Item
            label={t('预测冲销类型')}
            name='hedgeStrategy'
            rules={[{ required: true }]}>
            <Select options={forecastTypeList} />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prev, next) =>
              prev['hedgeStrategy'] !== next['hedgeStrategy']
            }>
            {({ getFieldValue }) => {
              const hedgeStrategy = getFieldValue('hedgeStrategy');
              return hedgeStrategy === 1 ? null : (
                <Form.Item
                  label={t('过去预测天数')}
                  name='forwardDays'
                  layout='horizontal'
                  rules={[{ required: true }]}>
                  <InputNumber className='w-[50px]' />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prev, next) =>
              prev['hedgeStrategy'] !== next['hedgeStrategy']
            }>
            {({ getFieldValue }) => {
              const hedgeStrategy = getFieldValue('hedgeStrategy');
              return hedgeStrategy === 2 ? null : (
                <Form.Item
                  label={t('未来预测天数')}
                  layout='horizontal'
                  name='backwardDays'
                  rules={[{ required: true }]}>
                  <InputNumber className='w-[50px]' />
                </Form.Item>
              );
            }}
          </Form.Item>
        </Form>
        <Flex>
          {/* <Button onClick={() => setFormDisabled(!formDisabled)}> */}
          {/* {t('更改')} */}
          {/* </Button> */}
          <Flex className='w-full flex gap-2 flex-row-reverse'>
            <Button onClick={() => form.resetFields()}>{t('重置')}</Button>
            <Button type='primary' onClick={() => submitForm()}>
              {t('保存')}
            </Button>
          </Flex>
        </Flex>
      </div>
    </div>
  );
};

export default ForecastConfiguration;
