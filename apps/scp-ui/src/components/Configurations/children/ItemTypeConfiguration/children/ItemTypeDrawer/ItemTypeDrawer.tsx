import { useState, useEffect } from 'react';
import { Drawer, Flex, ConfigProvider, Table, Empty, Spin } from 'antd';
import LeftArrow from '/public/leftArrow.svg';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

// const getTextWidth = (text: string, font: string) => {
//   const canvas = document.createElement('canvas');
//   const context = canvas.getContext('2d');
//   if (!context) {
//     return 0;
//   }
//   context.font = font;
//   return context.measureText(text).width;
// };

function MdTypeDrawer({
  drawerOpen,
  setDrawerOpen,
  drawerData,
}: {
  drawerOpen: boolean;
  setDrawerOpen: (open: boolean) => void;
  drawerData: any;
}) {
  const { t } = useVoerkaI18n();

  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState<any[]>([]);
  const [tableScrollY, setTableScrollY] = useState(0);
  const [tableLoading, setTableLoading] = useState(true);

  // const labels = [
  //   {
  //     label: 'ID',
  //     key: 'masterDataTypeId',
  //   },
  //   {
  //     label: '名称',
  //     key: 'MdTypeName',
  //   },
  //   {
  //     label: '是否为系统内置',
  //     key: 'isSystem',
  //   },
  //   {
  //     label: '状态',
  //     key: 'status',
  //   },
  // ];

  // const maxWidth = Math.max(
  //   ...labels.map((label: any) =>
  //     getTextWidth(label.label, '17px PingFang SC'),
  //   ),
  // );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'itemTypeId',
    },
    {
      title: t('名称'),
      dataIndex: 'itemTypeLabel',
    },
    {
      title: t('编码'),
      dataIndex: 'itemTypeCode',
    },
    {
      title: t('KFID'),
      dataIndex: 'kfId',
    },
    {
      title: t('KF名称'),
      dataIndex: 'kfLabel',
    },
    {
      title: t('KF编码'),
      dataIndex: 'kfCode',
    },
  ].map((column: any) => {
    return {
      ...column,
      onCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
      onHeaderCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
    };
  });

  const [data, setData] = useState<any>({
    itemTypeId: '',
    itemTypeLabel: '',
    itemTypeCode: '',
    kfId: '',
    kfLabel: '',
    kfCode: '',
  });

  const getDetailData = async () => {
    const result = await ajax.getItemTypeDetails({
      itemTypeId: drawerData.id,
    });
    if (result.code === 0 && result.data && result.data.length !== 0) {
      setDetailData(
        result.data.map((item: any, index: number) => {
          return {
            itemTypeId: item.itemTypeId,
            itemTypeLabel: item.itemTypeLabel,
            itemTypeCode: item.itemTypeCode,
            kfId: item.kfId,
            kfLabel: item.kfLabel,
            kfCode: item.kfCode,
            key: index,
          };
        }),
      );
      setTimeout(() => {
        setTableLoading(false);
      }, 500);
    }
  };

  useEffect(() => {
    if (!drawerData || !drawerOpen) {
      return;
    }
    data.itemTypeId = drawerData.id;
    getDetailData();
    setData(data);
    setLoading(false);
  }, [drawerData, drawerOpen]);

  return (
    <Drawer
      afterOpenChange={() => {
        if (tableScrollY === 0) {
          const tableHead = document.querySelector('.ant-table-thead');
          const drawerMainContainer = document.querySelector(
            '#drawer-main-container',
          );
          const tableContainer = document.querySelector(
            '.table-container',
          ) as HTMLDivElement;
          if (tableHead && drawerMainContainer && tableContainer) {
            setTableScrollY(
              drawerMainContainer.clientHeight -
                tableContainer.offsetTop -
                tableHead.clientHeight,
            );
          }
        }
      }}
      forceRender={true}
      loading={loading}
      style={{
        position: 'fixed',
        borderRadius: '10px',
        top: '10px',
        bottom: '10px',
        right: '15px',
        width: '800px',
        height: 'auto',
      }}
      open={drawerOpen}
      onClose={() => {
        setDrawerOpen(false);
        setLoading(true);
        setTableLoading(true);
      }}
      closeIcon={
        <Flex gap={'small'} vertical={false}>
          <img
            src={LeftArrow}
            style={{
              transform: 'rotate(180deg)',
            }}></img>
          <span>{t('条目类型')}</span>
        </Flex>
      }
      // extra={
      //   <ConfigProvider
      //     theme={{
      //       components: {
      //         Button: {
      //           defaultColor: '#0C40DE',
      //           colorBorder: '#0C40DE',
      //         },
      //       },
      //     }}>
      //     <Button
      //       style={{
      //         borderRadius: '36px',
      //       }}>
      //       {'保存'}
      //     </Button>
      //   </ConfigProvider>
      // }
    >
      <div
        style={{
          position: 'relative',
        }}>
        <ConfigProvider
          renderEmpty={() => {
            return <Empty description={t('暂无数据')}></Empty>;
          }}>
          <Table
            columns={columns}
            dataSource={detailData ? detailData : []}
            pagination={false}></Table>
          <Spin
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: '#fff',
              display: tableLoading ? 'flex' : 'none',
              paddingTop: '200px',
              justifyContent: 'center',
            }}
            spinning={tableLoading}></Spin>
        </ConfigProvider>
      </div>
    </Drawer>
  );
}

export default MdTypeDrawer;
