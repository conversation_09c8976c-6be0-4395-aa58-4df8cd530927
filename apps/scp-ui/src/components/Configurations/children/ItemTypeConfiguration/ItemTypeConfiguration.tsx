import { useState, useEffect } from 'react';
import { ConfigProvider, Empty, Table, Flex, Spin } from 'antd';
import ItemTypeDrawer from './children/ItemTypeDrawer/ItemTypeDrawer';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

function ItemTypeConfiguration({
  searchedValue,
  setTotal,
  pageSize,
  setPageSize,
  currentPage,
  setCurrentPage,
}: {
  searchedValue: string;
  setTotal: any;
  pageSize: number;
  setPageSize: any;
  currentPage: number;
  setCurrentPage: any;
}) {
  const { t } = useVoerkaI18n();

  // const [scorllY, setScrollY] = useState<any>(null);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [drawerData, setDrawerData] = useState<any>(null);
  const [rowData, setRowData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // useEffect(() => {
  //   const tableContainer = document.querySelector('.ant-table-wrapper');
  //   const tableHeader = document.querySelector('.ant-table-thead');
  //   if (tableContainer && tableHeader) {
  //     setScrollY(tableContainer.clientHeight - tableHeader.clientHeight - 64);
  //   }
  // }, []);

  const search = async () => {
    const result = await ajax.getItemTypeList({
      page: currentPage,
      pageSize: pageSize,
      ...(searchedValue.length > 0
        ? {
            label: searchedValue,
          }
        : {}),
    });
    if (result.data && result.code === 0) {
      if (!result.data.list || result.data.list.length === 0) {
        setDataSource([]);
      }
    }
    setRowData(result.data);
    setTimeout(() => {
      setLoading(false);
    }, 100);
  };

  useEffect(() => {
    search();
  }, [searchedValue]);

  useEffect(() => {
    search();
    setLoading(true);
  }, [currentPage]);

  const columns = [
    {
      title: t('名称'),
      dataIndex: 'label',
    },
    {
      title: t('是否自定义'),
      dataIndex: 'isCustom',
    },
    {
      title: t('唯一标识名称'),
      dataIndex: 'uniqueLabels',
      render: (text: any) => {
        return (
          <Flex gap={'4px'} justify='center'>
            {text.map((item: any, index: number) => {
              return (
                <span
                  style={{
                    border: '1px solid rgb(217, 217, 217)',
                    borderRadius: '4px',
                    padding: '0 7px',
                    backgroundColor: 'rgb(250, 250, 250)',
                  }}
                  key={index}>
                  {item}
                </span>
              );
            })}
          </Flex>
        );
      },
    },
    {
      title: t('唯一标识编码'),
      dataIndex: 'uniqueCodes',
      render: (text: any) => {
        return (
          <Flex gap={'4px'} justify='center'>
            {text.map((item: any, index: number) => {
              return (
                <span
                  style={{
                    border: '1px solid rgb(217, 217, 217)',
                    borderRadius: '4px',
                    padding: '0 7px',
                    backgroundColor: 'rgb(250, 250, 250)',
                  }}
                  key={index}>
                  {item}
                </span>
              );
            })}
          </Flex>
        );
      },
    },
  ].map((item: any) => {
    return {
      ...item,
      onCell: (record: any) => {
        return {
          style: {
            textAlign: 'center',
          },
          onClick: () => {
            if (item.dataIndex === 'action') {
              return;
            }
            setDrawerData(rowData.list[record.key]);
            setDrawerOpen(true);
          },
        };
      },
      onHeaderCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
    };
  });
  const [dataSource, setDataSource] = useState<any>(null);

  useEffect(() => {
    if (!rowData || !rowData.list || rowData.list.length === 0) {
      return;
    }
    setDataSource(
      rowData.list.map((item: any, index: number) => {
        return {
          id: item.id,
          key: index,
          label: item.label,
          isCustom: item.isCustom ? t('是') : t('否'),
          uniqueLabels: item.uniqueList.map((item: any) => item.label),
          uniqueCodes: item.uniqueList.map((item: any) => item.code),
          status: t('激活'),
        };
      }),
    );
    setCurrentPage(rowData.page);
    setTotal(rowData.total);
    setPageSize(rowData.pageSize);
  }, [rowData]);

  return (
    <div
      style={{
        height: '100%',
        backgroundColor: '#fff',
        borderRadius: '20px',
        position: 'relative',
      }}>
      <ConfigProvider
        renderEmpty={() => {
          return <Empty description={t('暂无数据')}></Empty>;
        }}
        theme={{
          components: {
            Table: {
              borderColor: 'none',
              headerSplitColor: 'none',
            },
          },
        }}>
        <Table
          loading={{
            spinning: false,
            wrapperClassName: 'my-table-spin',
          }}
          columns={columns}
          dataSource={dataSource}
          style={{
            height: '100%',
          }}
          // scroll={scorllY ? { y: scorllY } : {}}
          pagination={false}></Table>
        <ItemTypeDrawer
          drawerOpen={drawerOpen}
          setDrawerOpen={setDrawerOpen}
          drawerData={drawerData}></ItemTypeDrawer>
      </ConfigProvider>
      <Spin
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: '#fff',
          borderRadius: '20px',
          justifyContent: 'center',
          alignItems: 'center',
          display: loading ? 'flex' : 'none',
        }}
        spinning={loading}></Spin>
    </div>
  );
}

export default ItemTypeConfiguration;
