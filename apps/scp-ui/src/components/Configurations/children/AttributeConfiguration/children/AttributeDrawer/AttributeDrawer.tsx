import { useState, useEffect } from 'react';
import { Drawer, Flex, Input } from 'antd';
import LeftArrow from '/public/leftArrow.svg';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

const getTextWidth = (text: string, font: string) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) {
    return 0;
  }
  context.font = font;
  return context.measureText(text).width;
};

function AttributeDrawer({
  drawerOpen,
  setDrawerOpen,
  drawerData,
}: {
  drawerOpen: boolean;
  setDrawerOpen: (open: boolean) => void;
  drawerData: any;
}) {
  const { t } = useVoerkaI18n();

  const [loading, setLoading] = useState(true);
  const labels = [
    {
      label: 'ID',
      key: 'attributeId',
    },
    {
      label: t('名称'),
      key: 'attributeName',
    },
    {
      label: t('类型'),
      key: 'attributeType',
    },
    {
      label: t('是否为系统内置'),
      key: 'isSystemName',
    },
    {
      label: t('状态'),
      key: 'status',
    },
  ];
  const maxWidth = Math.max(
    ...labels.map((label: any) =>
      getTextWidth(t(label.label), '17px PingFang SC'),
    ),
  );

  const [data, setData] = useState<any>({
    attributeId: '',
    attributeName: '',
    attributeType: '',
    isSystem: '',
    status: '',
  });

  useEffect(() => {
    if (!drawerData || !drawerOpen) {
      return;
    }
    data.attributeId = drawerData.attributeId;
    data.attributeName = drawerData.label;
    data.attributeType = drawerData.valueType;
    data.isSystemName = drawerData.isSystemName;
    data.status = t('激活');
    setData(data);
    setLoading(false);
  }, [drawerData, drawerOpen]);

  return (
    <Drawer
      loading={loading}
      style={{
        position: 'fixed',
        borderRadius: '10px',
        top: '10px',
        bottom: '10px',
        right: '15px',
        width: '600px',
        height: 'auto',
      }}
      open={drawerOpen}
      onClose={() => {
        setDrawerOpen(false);
        setLoading(true);
      }}
      closeIcon={
        <Flex gap={'small'} vertical={false}>
          <img
            src={LeftArrow}
            style={{
              transform: 'rotate(180deg)',
            }}></img>
          <span>{t('属性')}</span>
        </Flex>
      }
      // extra={
      //   <ConfigProvider
      //     theme={{
      //       components: {
      //         Button: {
      //           defaultColor: '#0C40DE',
      //           colorBorder: '#0C40DE',
      //         },
      //       },
      //     }}>
      //     <Button
      //       style={{
      //         borderRadius: '36px',
      //       }}>
      //       {'保存'}
      //     </Button>
      //   </ConfigProvider>
      // }
    >
      <Flex vertical={true} gap={'16px'}>
        {labels.map((label: any) => {
          return (
            <Flex key={label.key} vertical={false}>
              <span
                style={{
                  width: `${maxWidth + 30}px`,
                  textAlign: 'left',
                  padding: '8px 20px',
                  backgroundColor: '#EFF2F3',
                  borderRadius: '12px 0px 0px 12px',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}>
                {label.label}
              </span>
              <Input
                value={data[label.key]}
                style={{
                  flex: 1,
                  borderRadius: '0px 12px 12px 0px',
                  border: '1px solid #EFF2F3',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}></Input>
            </Flex>
          );
        })}
      </Flex>
    </Drawer>
  );
}

export default AttributeDrawer;
