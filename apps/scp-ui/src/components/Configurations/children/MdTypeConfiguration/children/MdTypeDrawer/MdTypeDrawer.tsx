import { useState, useEffect } from 'react';
import {
  Drawer,
  Flex,
  ConfigProvider,
  Input,
  Table,
  Empty,
  Checkbox,
  Spin,
} from 'antd';
import LeftArrow from '/public/leftArrow.svg';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

const getTextWidth = (text: string, font: string) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) {
    return 0;
  }
  context.font = font;
  return context.measureText(text).width;
};

function MdTypeDrawer({
  drawerOpen,
  setDrawerOpen,
  drawerData,
}: {
  drawerOpen: boolean;
  setDrawerOpen: (open: boolean) => void;
  drawerData: any;
}) {
  const { t } = useVoerkaI18n();

  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState<any[]>([]);
  const [tableScrollY, setTableScrollY] = useState(0);
  const [tableLoading, setTableLoading] = useState(true);

  const labels = [
    {
      label: 'ID',
      key: 'masterDataTypeId',
    },
    {
      label: t('名称'),
      key: 'MdTypeName',
    },
    {
      label: t('是否为系统内置'),
      key: 'isSystemName',
    },
    {
      label: t('状态'),
      key: 'status',
    },
  ];
  const maxWidth = Math.max(
    ...labels.map((label: any) =>
      getTextWidth(label.label, '17px PingFang SC'),
    ),
  );

  const columns = [
    {
      title: (
        <Checkbox
          checked={detailData.every((item: any) => item.selected)}
          indeterminate={
            detailData.some((item: any) => item.selected) &&
            !detailData.every((item: any) => item.selected)
          }
          onChange={(event: any) => {
            detailData.forEach((item: any) => {
              item.selected = event.target.checked;
            });
            setDetailData([...detailData]);
          }}></Checkbox>
      ),
      dataIndex: 'select',
      render: (_: any, record: any) => {
        return (
          <Checkbox
            checked={detailData[record.key].selected}
            onChange={(event: any) => {
              detailData[record.key].selected = event.target.checked;
              setDetailData([...detailData]);
            }}></Checkbox>
        );
      },
      width: 40,
    },
    {
      title: t('名称'),
      dataIndex: 'label',
    },
    {
      title: 'isRoot',
      dataIndex: 'isRoot',
    },
    {
      title: 'key',
      dataIndex: 'isUnique',
    },
  ].map((column: any) => {
    return {
      ...column,
      onCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
      onHeaderCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
    };
  });

  const [data, setData] = useState<any>({
    masterDataTypeId: '',
    MdTypeName: '',
    isSystem: '',
    status: '',
  });

  const getDetailData = async () => {
    const result = await ajax.getMasterDataTypeDetails({
      masterDataTypeId: drawerData.masterDataTypeId,
    });
    if (
      result.code === 0 &&
      result.data.details &&
      result.data.details.length !== 0
    ) {
      setDetailData(
        result.data.details.map((item: any, index: number) => {
          return {
            key: index,
            label: item.attributeLabel,
            isRoot: item.isRoot ? 'true' : 'false',
            isUnique: item.isUnique ? 'true' : 'false',
            selected: false,
          };
        }),
      );
      setTimeout(() => {
        setTableLoading(false);
      }, 500);
    }
  };

  useEffect(() => {
    if (!drawerData || !drawerOpen) {
      return;
    }
    data.masterDataTypeId = drawerData.masterDataTypeId;
    data.MdTypeName = drawerData.label;
    data.isSystemName = drawerData.isSystemName;
    data.status = t('激活');
    getDetailData();
    setData(data);
    setLoading(false);
  }, [drawerData, drawerOpen]);

  return (
    <Drawer
      afterOpenChange={() => {
        if (tableScrollY === 0) {
          const tableHead = document.querySelector('.ant-table-thead');
          const drawerMainContainer = document.querySelector(
            '#drawer-main-container',
          );
          const tableContainer = document.querySelector(
            '.table-container',
          ) as HTMLDivElement;
          if (tableHead && drawerMainContainer && tableContainer) {
            setTableScrollY(
              drawerMainContainer.clientHeight -
                tableContainer.offsetTop -
                tableHead.clientHeight,
            );
          }
        }
      }}
      forceRender={true}
      loading={loading}
      style={{
        position: 'fixed',
        borderRadius: '10px',
        top: '10px',
        bottom: '10px',
        right: '15px',
        width: '600px',
        height: 'auto',
      }}
      open={drawerOpen}
      onClose={() => {
        setDrawerOpen(false);
        setLoading(true);
        setTableLoading(true);
      }}
      closeIcon={
        <Flex gap={'small'} vertical={false}>
          <img
            src={LeftArrow}
            style={{
              transform: 'rotate(180deg)',
            }}></img>
          <span>{t('主数据类型')}</span>
        </Flex>
      }
      // extra={
      //   <ConfigProvider
      //     theme={{
      //       components: {
      //         Button: {
      //           defaultColor: '#0C40DE',
      //           colorBorder: '#0C40DE',
      //         },
      //       },
      //     }}>
      //     <Button
      //       style={{
      //         borderRadius: '36px',
      //       }}>
      //       {'保存'}
      //     </Button>
      //   </ConfigProvider>
      // }
    >
      <Flex
        id='drawer-main-container'
        style={{
          height: '100%',
          position: 'relative',
          overflow: 'hidden',
        }}
        vertical={true}
        gap={'16px'}>
        {labels.map((label: any) => {
          return (
            <Flex key={label.key} vertical={false}>
              <span
                style={{
                  width: `${maxWidth + 30}px`,
                  textAlign: 'left',
                  padding: '8px 20px',
                  backgroundColor: '#EFF2F3',
                  borderRadius: '12px 0px 0px 12px',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}>
                {label.label}
              </span>
              <Input
                value={data[label.key]}
                style={{
                  flex: 1,
                  borderRadius: '0px 12px 12px 0px',
                  border: '1px solid #EFF2F3',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}></Input>
            </Flex>
          );
        })}
        <ConfigProvider
          renderEmpty={() => {
            return <Empty description={t('暂无数据')}></Empty>;
          }}>
          <div
            className='table-container'
            style={{
              position: 'relative',
            }}>
            <Table
              columns={columns}
              dataSource={detailData}
              pagination={false}
              scroll={tableScrollY > 0 ? { y: tableScrollY } : {}}></Table>
            <Spin
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: '#fff',
                display: tableLoading ? 'flex' : 'none',
                paddingTop: '200px',
                justifyContent: 'center',
              }}
              spinning={tableLoading}></Spin>
          </div>
        </ConfigProvider>
      </Flex>
    </Drawer>
  );
}

export default MdTypeDrawer;
