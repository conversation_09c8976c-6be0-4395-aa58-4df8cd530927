import { useState, useEffect } from 'react';
import { ConfigProvider, Empty, Table, Flex, Button, Spin } from 'antd';
import PlanLevelDrawer from './children/PlanLevelDrawer/PlanLevelDrawer';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

function PlanLevelConfiguration({
  searchedValue,
  setTotal,
  pageSize,
  setPageSize,
  currentPage,
  setCurrentPage,
}: {
  searchedValue: string;
  setTotal: any;
  pageSize: number;
  setPageSize: any;
  currentPage: number;
  setCurrentPage: any;
}) {
  const { t } = useVoerkaI18n();

  // const [scorllY, setScrollY] = useState<any>(null);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [drawerData, setDrawerData] = useState<any>(null);
  const [rowData, setRowData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // useEffect(() => {
  //   const tableContainer = document.querySelector('.ant-table-wrapper');
  //   const tableHeader = document.querySelector('.ant-table-thead');
  //   if (tableContainer && tableHeader) {
  //     setScrollY(tableContainer.clientHeight - tableHeader.clientHeight - 64);
  //   }
  // }, []);

  const search = async () => {
    const result = await ajax.getPlanningLevelList({
      page: currentPage,
      pageSize: pageSize,
      ...(searchedValue.length > 0
        ? {
            label: searchedValue,
          }
        : {}),
    });
    if (result.data && result.code === 0) {
      if (!result.data.list || result.data.list.length === 0) {
        setDataSource([]);
      }
    }
    setRowData(result.data);
    setTimeout(() => {
      setLoading(false);
    }, 100);
  };

  useEffect(() => {
    search();
  }, [searchedValue]);

  useEffect(() => {
    search();
    setLoading(true);
  }, [currentPage]);

  const columns = [
    {
      title: t('名称'),
      dataIndex: 'label',
    },
    {
      title: t('时间维度'),
      dataIndex: 'tpLevelName',
    },
    {
      title: t('是否为系统内置'),
      dataIndex: 'isSystemName',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      render: () => {
        const commonStyle = {
          cursor: 'pointer',
          border: 'none',
          backgroundColor: 'transparent',
          boxShadow: 'none',
        };
        const modifyStyle = {
          ...commonStyle,
        };
        const copyStyle = {
          ...commonStyle,
        };
        const deleteStyle = {
          ...commonStyle,
        };
        return (
          <Flex
            vertical={false}
            style={{
              justifyContent: 'center',
            }}>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    defaultColor: '#1677ff',
                  },
                },
              }}>
              <Button style={modifyStyle}>{t('修改')}</Button>
              <Button style={copyStyle}>{t('复制')}</Button>
            </ConfigProvider>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    defaultColor: 'rgb(242, 84, 69)',
                  },
                },
              }}>
              <Button style={deleteStyle}>{t('删除')}</Button>
            </ConfigProvider>
          </Flex>
        );
      },
    },
  ].map((item: any) => {
    return {
      ...item,
      onCell: (record: any) => {
        return {
          style: {
            textAlign: 'center',
          },
          onClick: () => {
            if (item.dataIndex === 'action') {
              return;
            }
            setDrawerData(rowData.list[record.key]);
            setDrawerOpen(true);
          },
        };
      },
      onHeaderCell: () => {
        return {
          style: {
            textAlign: 'center',
          },
        };
      },
    };
  });
  const [dataSource, setDataSource] = useState<any>(null);

  useEffect(() => {
    if (!rowData || !rowData.list || rowData.list.length === 0) {
      return;
    }
    setDataSource(
      rowData.list.map((item: any, index: number) => {
        return {
          key: index,
          label: item.planningLevelName,
          tpLevelName: item.tpLevelName,
          isSystemName: item.isSystemName,
          status: t('激活'),
        };
      }),
    );
    setCurrentPage(rowData.page);
    setTotal(rowData.total);
    setPageSize(rowData.pageSize);
  }, [rowData]);

  return (
    <div
      style={{
        height: '100%',
        backgroundColor: '#fff',
        borderRadius: '20px',
        position: 'relative',
      }}>
      <ConfigProvider
        renderEmpty={() => {
          return <Empty description={t('暂无数据')}></Empty>;
        }}
        theme={{
          components: {
            Table: {
              borderColor: 'none',
              headerSplitColor: 'none',
            },
          },
        }}>
        <Table
          loading={{
            spinning: false,
            wrapperClassName: 'my-table-spin',
          }}
          columns={columns}
          dataSource={dataSource}
          style={{
            height: '100%',
          }}
          // scroll={scorllY ? { y: scorllY } : {}}
          pagination={false}></Table>
        <PlanLevelDrawer
          drawerOpen={drawerOpen}
          setDrawerOpen={setDrawerOpen}
          drawerData={drawerData}></PlanLevelDrawer>
        <Spin
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: '#fff',
            borderRadius: '20px',
            justifyContent: 'center',
            alignItems: 'center',
            display: loading ? 'flex' : 'none',
          }}
          spinning={loading}></Spin>
      </ConfigProvider>
    </div>
  );
}

export default PlanLevelConfiguration;
