import { useState, useEffect } from 'react';
import {
  Drawer,
  Flex,
  Input,
  ConfigProvider,
  Empty,
  Table,
  Spin,
  Collapse,
  Checkbox,
} from 'antd';
import LeftArrow from '/public/leftArrow.svg';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import './index.css';

const getTextWidth = (text: string, font: string) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) {
    return 0;
  }
  context.font = font;
  return context.measureText(text).width;
};

function PlanLevelDrawer({
  drawerOpen,
  setDrawerOpen,
  drawerData,
}: {
  drawerOpen: boolean;
  setDrawerOpen: (open: boolean) => void;
  drawerData: any;
}) {
  const { t } = useVoerkaI18n();

  const [loading, setLoading] = useState(true);
  const [detailData, setDetailData] = useState<
    {
      details: any[];
    }[]
  >([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [items, setItems] = useState<any[] | null>(null);

  const labels = [
    {
      label: 'ID',
      key: 'planningLevelId',
    },
    {
      label: t('名称'),
      key: 'planningLevelName',
    },
    {
      label: t('时间维度'),
      key: 'tpLevelName',
    },
    {
      label: t('是否为系统内置'),
      key: 'isSystemName',
    },
    {
      label: t('状态'),
      key: 'status',
    },
  ];
  const maxWidth = Math.max(
    ...labels.map((label: any) =>
      getTextWidth(label.label, '17px PingFang SC'),
    ),
  );

  const [data, setData] = useState<any>({
    planningLevelId: '',
    planningLevelName: '',
    tpLevel: '',
    attributeType: '',
    isSystem: '',
    status: '',
    detail: '',
  });

  const getDetailData = async () => {
    const result = await ajax.getPlanningLevelDetail({
      planLevelId: drawerData.planningLevelId,
    });
    const details = result.data.details;
    setDetailData(
      details.map((detail: any) => {
        return {
          masterDataTypeId: detail.masterDataTypeId,
          label: detail.label,
          details: detail.details.map((item: any) => {
            return {
              attributeLabel: item.attributeLabel,
              isRoot: item.isRoot ? '是' : '否',
              isRequired: item.isRequired ? '是' : '否',
              status: t('激活'),
              selected: false,
            };
          }),
        };
      }),
    );
  };

  useEffect(() => {
    if (!detailData || detailData.length === 0) {
      return;
    }
    setItems(
      detailData.map((detail: any, outIndex: number) => {
        return {
          key: detail.masterDataTypeId,
          label: detail.label,
          children: (
            <Table
              pagination={false}
              columns={[
                {
                  title: (
                    <Checkbox
                      checked={detailData[outIndex].details.every(
                        (item: any) => item.selected,
                      )}
                      indeterminate={
                        detailData[outIndex].details.some(
                          (item: any) => item.selected,
                        ) &&
                        !detailData[outIndex].details.every(
                          (item: any) => item.selected,
                        )
                      }
                      onChange={(event: any) => {
                        detailData[outIndex].details.forEach((item: any) => {
                          item.selected = event.target.checked;
                        });
                        setDetailData([...detailData]);
                      }}></Checkbox>
                  ),
                  dataIndex: 'select',
                  render: (_: any, __: any, index: number) => {
                    return (
                      <Checkbox
                        checked={detailData[outIndex].details[index].selected}
                        onChange={(event: any) => {
                          detailData[outIndex].details[index].selected =
                            event.target.checked;
                          setDetailData([...detailData]);
                        }}></Checkbox>
                    );
                  },
                  width: 40,
                },
                {
                  title: '名称',
                  dataIndex: 'attributeLabel',
                },
                {
                  title: 'isRoot',
                  dataIndex: 'isRoot',
                },
                {
                  title: '是否必须',
                  dataIndex: 'isRequired',
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                },
              ]}
              dataSource={
                detail.details
                  ? detail.details.map((item: any) => {
                      return {
                        attributeLabel: item.attributeLabel,
                        isRoot: item.isRoot ? '是' : '否',
                        isRequired: item.isRequired ? '是' : '否',
                        status: t('激活'),
                      };
                    })
                  : []
              }></Table>
          ),
        };
      }),
    );
    setTimeout(() => {
      setTableLoading(false);
    }, 500);
  }, [detailData]);

  useEffect(() => {
    if (!drawerData || !drawerOpen) {
      return;
    }
    data.planningLevelId = drawerData.planningLevelId;
    data.planningLevelName = drawerData.planningLevelName;
    data.tpLevelName = drawerData.tpLevelName;
    data.isSystemName = drawerData.isSystemName;
    data.status = t('激活');
    setData(data);
    getDetailData();
    setTimeout(() => {
      setLoading(false);
    }, 100);
  }, [drawerData, drawerOpen]);

  return (
    <Drawer
      loading={loading}
      style={{
        position: 'fixed',
        borderRadius: '10px',
        top: '10px',
        bottom: '10px',
        right: '15px',
        width: '600px',
        height: 'auto',
      }}
      open={drawerOpen}
      onClose={() => {
        setDrawerOpen(false);
        setLoading(true);
        setTableLoading(true);
      }}
      closeIcon={
        <Flex gap={'small'} vertical={false}>
          <img
            src={LeftArrow}
            style={{
              transform: 'rotate(180deg)',
            }}></img>
          <span>{t('计划层级')}</span>
        </Flex>
      }
      // extra={
      //   <ConfigProvider
      //     theme={{
      //       components: {
      //         Button: {
      //           defaultColor: '#0C40DE',
      //           colorBorder: '#0C40DE',
      //         },
      //       },
      //     }}>
      //     <Button
      //       style={{
      //         borderRadius: '36px',
      //       }}>
      //       {'保存'}
      //     </Button>
      //   </ConfigProvider>
      // }
    >
      <Flex vertical={true} gap={'16px'}>
        {labels.map((label: any) => {
          return (
            <Flex key={label.key} vertical={false}>
              <span
                style={{
                  width: `${maxWidth + 30}px`,
                  textAlign: 'left',
                  padding: '8px 20px',
                  backgroundColor: '#EFF2F3',
                  borderRadius: '12px 0px 0px 12px',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}>
                {label.label}
              </span>
              <Input
                value={data[label.key]}
                style={{
                  flex: 1,
                  borderRadius: '0px 12px 12px 0px',
                  border: '1px solid #EFF2F3',

                  /* 副标题/2 */
                  fontFamily: 'PingFang SC',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                  letterSpacing: '0.96px',
                }}></Input>
            </Flex>
          );
        })}
        <ConfigProvider
          renderEmpty={() => {
            return <Empty description={t('暂无数据')}></Empty>;
          }}>
          <div
            className='table-container'
            style={{
              position: 'relative',
            }}>
            <Collapse
              style={{
                display: items ? 'block' : 'none',
              }}
              items={items ? items : undefined}></Collapse>
            <Spin
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: '#fff',
                display: tableLoading ? 'flex' : 'none',
                paddingTop: '200px',
                justifyContent: 'center',
              }}
              spinning={tableLoading}></Spin>
          </div>
        </ConfigProvider>
      </Flex>
    </Drawer>
  );
}

export default PlanLevelDrawer;
