import { useState, useEffect } from 'react';
import { Flex, Input, App, Spin } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import ajax from '@/api';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

function TimeProfileConfiguration() {
  const { t } = useVoerkaI18n();

  const [data, setData] = useState<any>(null);
  const [rowData, setRowData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const { message } = App.useApp();

  const search = async () => {
    const result = await ajax.getTimeProfileList();
    if (result.data && result.code === 0) {
      if (!result.data.list || result.data.list.length === 0) {
        message.info(t('暂无数据'));
      }
    }
    setRowData(result.data);
    setTimeout(() => {
      setLoading(false);
    }, 100);
  };

  useEffect(() => {
    search();
  }, []);

  useEffect(() => {
    if (!rowData || !rowData.list || rowData.list.length === 0) {
      return;
    }
    setData({
      name: rowData.list[0].name,
      profileDesc: rowData.list[0].profileDesc,
      tpLevel: rowData.list[0].tpLevelList.join(','),
      periodStart: dayjs(rowData.list[0].periodStart).format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      periodEnd: dayjs(rowData.list[0].periodEnd).format('YYYY-MM-DD HH:mm:ss'),
      status: t('激活'),
      pareaId: rowData.list[0].pareaId,
      details: rowData.list[0].details,
    });
  }, [rowData]);

  const headTitleStyle = {
    textAlign: 'left' as const,
    padding: '8px 20px',
    backgroundColor: '#EFF2F3',
    borderRadius: '12px 0px 0px 12px',

    /* 副标题/2 */
    fontFamily: 'PingFang SC',
    fontSize: '16px',
    fontStyle: 'normal',
    fontWeight: 600,
    lineHeight: 'normal',
    letterSpacing: '0.96px',
  };

  const inputStyle = {
    flex: 1,
    borderRadius: '0px 12px 12px 0px',
    border: '1px solid #EFF2F3',

    /* 副标题/2 */
    fontFamily: 'PingFang SC',
    fontSize: '14px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: 'normal',
    letterSpacing: '0.96px',
  };

  const pareaInfo = localStorage.getItem('pareaInfo');

  return (
    <div
      style={{
        backgroundColor: '#fff',
        borderRadius: '20px',
        position: 'relative',
      }}>
      <Flex
        vertical={true}
        style={{
          padding: '54px 96px',
        }}
        gap={'20px'}>
        <span
          style={{
            /* 副标题/1 */
            fontFamily: 'PingFang SC',
            fontSize: '14px',
            fontStyle: 'normal',
            fontWeight: 500,
            lineHeight: '24px',
            letterSpacing: '0.72px',
          }}>
          {t('当前时间配置')}
        </span>
        <Flex vertical={false}>
          <span
            style={{
              ...headTitleStyle,
              width: '120px',
            }}>
            {t('名称')}
          </span>
          <Input
            value={data?.name}
            style={{
              ...inputStyle,
              borderRadius: '0',
              width: '250px',
              flex: 'none',
            }}></Input>
          <span
            style={{
              ...headTitleStyle,
              borderRadius: '0',
              width: '120px',
            }}>
            {t('描述')}
          </span>
          <Input
            value={data?.profileDesc}
            style={{
              ...inputStyle,
              borderRadius: '0',
            }}></Input>
          <span
            style={{
              ...headTitleStyle,
              borderRadius: '0',
            }}>
            {t('时间维度')}
          </span>
          <Input value={data?.tpLevel} style={inputStyle}></Input>
        </Flex>
        <Flex vertical={false}>
          <span
            style={{
              ...headTitleStyle,
              width: '120px',
            }}>
            {t('开始时间')}
          </span>
          <Input
            value={data?.periodStart}
            style={{
              ...inputStyle,
              borderRadius: '0',
              width: '250px',
              flex: 'none',
            }}></Input>
          <span
            style={{
              ...headTitleStyle,
              borderRadius: '0',
              width: '120px',
            }}>
            {t('结束时间')}
          </span>
          <Input value={data?.periodEnd} style={inputStyle}></Input>
        </Flex>
        <Flex vertical={false}>
          <span
            style={{
              ...headTitleStyle,
              width: '120px',
            }}>
            {t('状态')}
          </span>
          <Input value={data?.status} style={inputStyle}></Input>
        </Flex>
        <Flex vertical={false}>
          <span
            style={{
              ...headTitleStyle,
              width: '120px',
            }}>
            {t('计划范围')}
          </span>
          <Input
            value={pareaInfo ? JSON.parse(pareaInfo).parea : ''}
            style={{
              ...inputStyle,
              borderRadius: '0',
              width: '250px',
              flex: 'none',
            }}></Input>
          <span
            style={{
              ...headTitleStyle,
              borderRadius: '0',
              width: '120px',
            }}>
            {t('其他')}
          </span>
          <Input value={data?.details} style={inputStyle}></Input>
        </Flex>
      </Flex>
      <Spin
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: '#fff',
          borderRadius: '20px',
          justifyContent: 'center',
          alignItems: 'center',
          display: loading ? 'flex' : 'none',
        }}
        spinning={loading}></Spin>
    </div>
  );
}

export default TimeProfileConfiguration;
