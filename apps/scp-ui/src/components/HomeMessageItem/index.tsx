import { Badge } from 'antd';
import { MessageProps } from '@/pages/Home';
const MessageItem: React.FC<{ item: MessageProps }> = ({ item }) => {
  return (
    <div className='my-2 p-2 w-full'>
      <Badge dot={item.isRead}>
        <h2 className=' text-sm font-bold'>{item.title}</h2>
      </Badge>
      <div className='flex'>
        <p className='mr-2'>发送人：{item.fromUser}</p>
        <p className='mr-2'>接收人：{item.toUser}</p>
        <p>时间：{item.time}</p>
      </div>
      <p className='w-full truncate'>{item.content}</p>
    </div>
  );
};
export default MessageItem;
