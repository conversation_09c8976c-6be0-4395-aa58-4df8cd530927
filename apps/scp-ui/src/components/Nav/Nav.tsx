import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { route_info } from '@/router';
import store from '@/store';
import { t } from 'i18next';
import { subNav } from '@/store/features/navSlice.ts';
import { useDispatch } from 'react-redux';

const Nav: React.FC = () => {
  const dispatch = useDispatch();
  const [navData, setNavData] = useState<any[]>([]);
  const [currentData, setCurrentData] = useState<string>(() => {
    const path = location.pathname;
    const index = route_info[0].children?.findIndex(
      (item) => item.path == path,
    );
    if (index === -1) return '/home';
    return path;
  });
  const navigate = useNavigate();
  // @ts-ignore
  const navs = JSON.parse(localStorage.getItem('navs'));
  const remove = (data: any) => {
    if (data.key === '/home') return;
    dispatch(subNav(data.key));
    const index = navs.findIndex((item: any) => item.key === data.key);
    if (index === -1) return;
    if (navs[index].key === currentData) {
      const key = navs[index - 1]?.key || navs[index + 1]?.key || '/home';
      setCurrentData(key);
      navigate(key);
    }
  };

  useEffect(() => {
    if (store.getState().nav.navs) {
      setNavData(store.getState().nav.navs);
    } else {
      // @ts-ignore
      setNavData(localStorage.getItem('navs') || []);
    }
    console.log(store.getState().nav.navs);
  }, []);
  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      setNavData(store.getState().nav.navs);
    });
    return () => unsubscribe();
  }, []);
  useEffect(() => {
    setCurrentData(window.location.pathname);
  }, [window.location.pathname]);

  return (
    <div
      className='flex overflow-x-scroll scroll-setting flex-nowrap overflow-y-hidden'
      style={{ width: '100%', height: '4.2rem' }}>
      {navData.map((item: any, index: number) => {
        return (
          <div
            key={index}
            className='bg-white flex flex-nowrap text-center justify-between relative'
            style={{
              height: '2.81rem',
              color: `${item.key === currentData ? '#0C40DE' : '#B9BCC6'}`,
              fontFamily: 'PingFang SC',
              fontSize: '1.375rem',
              fontStyle: 'normal',
              fontWeight: `${item.key === currentData ? '500' : '400'}`,
              lineHeight: '2.81rem',
              letterSpacing: '0.1375rem',
              marginRight: '0.62rem',
              borderRadius: '0.5rem',
              cursor: 'pointer',
            }}>
            <div
              className='hover-brand'
              style={{
                marginRight: '0.75rem',
                paddingLeft: '1.88rem',
                whiteSpace: 'nowrap',
              }}
              onClick={() => {
                navigate(item.key);
              }}>
              {t(item.label as string)}
            </div>
            <div
              className='hover-brand'
              style={{
                height: '1.5rem',
                marginTop: '0.655rem',
                paddingRight: '1.88rem',
              }}
              onClick={() => {
                remove(item);
              }}>
              <svg
                width='1.5625rem'
                height='1.5rem'
                viewBox='0 0 25 25'
                fill={`${item.key === currentData ? '#0C40DE' : '#B9BCC6'}`}
                xmlns='http://www.w3.org/2000/svg'>
                <g id='close'>
                  <path
                    id='Vector'
                    d='M18.3642 6.13672L12.0002 12.5007M12.0002 12.5007L5.63623 18.8646M12.0002 12.5007L18.3642 18.8646M12.0002 12.5007L5.63623 6.13672'
                    stroke={`${
                      item.key === currentData ? '#0C40DE' : '#B9BCC6'
                    }`}
                    strokeWidth='1.5'
                    strokeLinecap='round'
                  />
                </g>
              </svg>
            </div>
            {item.key === currentData && (
              <div
                className='w-[80%] h-[0.29rem] bg-brand absolute bottom-0 left-[10%]'
                style={{ borderRadius: '0.92857rem' }}></div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Nav;
