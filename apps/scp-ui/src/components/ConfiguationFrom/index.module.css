.wrap {
    height: calc(100vh - 14rem);
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-bottom-left-radius: 1.78571rem;
    border-bottom-right-radius: 1.78571rem;
}

.main {
    margin-top: 1.29rem;
    overflow-y: hidden;
    padding: 2.21rem;
    background-color: #fff;
    margin-bottom: 1.5rem;
    height: 100%;
    border-radius: 1.78571rem;
}

.label {
    display: flex;
    width: 13rem;
    height: 2.71429rem;
    padding: 0.57143rem 2.14286rem;
    justify-content: right;
    align-items: center;
    gap: 0.57143rem;
    border-radius: 0.85714rem 0rem 0rem 0.85714rem;
    background: var(--Grey-8, #EFF2F3);
    color: var(--Grey-2, #293147);
    font-family: "PingFang SC";
    font-size: 1.14286rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.06857rem;
}

.input {
    height: 2.71429rem;
    display: flex;
    padding: 0.57143rem 1.5rem;
    justify-content: center;
    align-items: center;
    gap: 0.57143rem;
    flex: 1 0 0;
    border-radius: 0rem 0.85714rem 0.85714rem 0rem;
    border: 1px solid var(--Grey-8, #EFF2F3);
    color: var(--Grey-2, #293147);
    font-family: "PingFang SC";
    font-size: 1.14286rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.06857rem;
}