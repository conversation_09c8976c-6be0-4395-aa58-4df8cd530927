import { Select } from 'antd';

type propsType = {
  item: any;
};
const Calculate: React.FC<propsType> = (props) => {
  const { item } = props;
  return (
    <div
      className='flex flex-wrap bg-neutral-5% p-[1rem]'
      style={{ borderRadius: '10px' }}>
      {/* {JSON.stringify(item)} */}
      <div
        className='mr-[1rem] px-[11px] mb-[1rem]'
        style={{
          backgroundColor: '#d9d9d9',
          borderRadius: '6px',
          fontSize: '14px',
          height: '32px',
          lineHeight: '30px',
        }}>
        {item.kfLabel}
      </div>
      <Select
        className='mr-[1rem]  mb-[1rem]'
        value={item.plBaseName}
        style={{ width: 150 }}
        options={[{ value: item.plBaseName, label: '@' + item.plBaseName }]}
      />
      <div className='mr-[1rem] h-[32px] leading-[32px]  mb-[1rem]'>=</div>

      {item.expression.aggOperationValue && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.expression.aggOperationValue}
        </div>
      )}
      {item.expression.aggOperationValue && (
        <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>{'('}</div>
      )}

      {item.expression.operand1.aggOperationValue && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.expression.operand1.aggOperationValue}
        </div>
      )}
      {item.expression.operand1.aggOperationValue && (
        <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>{'('}</div>
      )}

      {item.expression.operand1.calculate && (
        <Child item={item.expression.operand1.calculate} />
      )}
      {!item.expression.operand1.calculate && (
        <div
          className='mr-[1rem] px-[11px]  mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.expression.operand1.fieldFirst}
        </div>
      )}
      {!item.expression.operand1.calculate && (
        <Select
          className='mr-[1rem] mb-[1rem]'
          value={item.expression.operand1.fieldSecond}
          style={{ width: 150 }}
          options={[
            {
              value: item.expression.operand1.fieldSecond,
              label: '@' + item.expression.operand1.fieldSecond,
            },
          ]}
        />
      )}
      {item.expression.operand1.aggOperationValue && (
        <div className='h-[32px] leading-[32px] mb-[1rem] mr-[1rem]'>{')'}</div>
      )}

      {item.expression.operationValue && (
        <Select
          className='mr-[1rem] mb-[1rem]'
          value={item.expression.operationValue}
          style={{ width: 60 }}
          options={[
            {
              value: item.expression.operationValue,
              label: item.expression.operationValue,
            },
          ]}
        />
      )}

      {item.expression.operand2 &&
        item.expression.operand2.aggOperationValue && (
          <div
            className='mr-[1rem] px-[11px] mb-[1rem]'
            style={{
              backgroundColor: '#d9d9d9',
              borderRadius: '6px',
              fontSize: '14px',
              height: '32px',
              lineHeight: '30px',
            }}>
            {item.expression.operand2.aggOperationValue}
          </div>
        )}
      {item.expression.operand2 &&
        item.expression.operand2.aggOperationValue && (
          <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>
            {'('}
          </div>
        )}

      {item.expression.operand2 && item.expression.operand2.calculate && (
        <Child item={item.expression.operand2.calculate} />
      )}
      {item.expression.operand2 && !item.expression.operand2.calculate && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.expression.operand2.fieldFirst}
        </div>
      )}
      {item.expression.operand2 && !item.expression.operand2.calculate && (
        <Select
          className='mr-[1rem] mb-[1rem]'
          value={item.expression.operand2.fieldSecond}
          style={{ width: 150 }}
          options={[
            {
              value: item.expression.operand2.fieldSecond,
              label: '@' + item.expression.operand2.fieldSecond,
            },
          ]}
        />
      )}

      {item.expression.operand2 &&
        item.expression.operand2.aggOperationValue && (
          <div className='h-[32px] leading-[32px] mb-[1rem] mr-[1rem]'>
            {')'}
          </div>
        )}

      {item.expression.aggOperationValue && (
        <div className='h-[32px] leading-[32px] mb-[1rem]'>{')'}</div>
      )}
    </div>
  );
};
export default Calculate;
const Child: React.FC<propsType> = (props) => {
  const { item } = props;
  return (
    <div className='flex flex-wrap'>
      <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>{'('}</div>

      {item.operand1.aggOperationValue && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.operand1.aggOperationValue}
        </div>
      )}
      {item.operand1.aggOperationValue && (
        <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>{'('}</div>
      )}

      {item.operand1.calculate && <Child item={item.operand1.calculate} />}
      {!item.operand1.calculate && (
        <div
          className='mr-[1rem] px-[11px]  mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.operand1.fieldFirst}
        </div>
      )}
      {!item.operand1.calculate && (
        <Select
          className='mr-[1rem] mb-[1rem]'
          value={item.operand1.fieldSecond}
          style={{ width: 150 }}
          options={[
            {
              value: item.operand1.fieldSecond,
              label: '@' + item.operand1.fieldSecond,
            },
          ]}
        />
      )}
      {item.operand1.aggOperationValue && (
        <div className='h-[32px] leading-[32px] mb-[1rem] mr-[1rem]'>{')'}</div>
      )}
      <Select
        className='mr-[1rem] mb-[1rem]'
        value={item.operationValue}
        style={{ width: 60 }}
        options={[{ value: item.operationValue, label: item.operationValue }]}
      />

      {item.operand2.aggOperationValue && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.operand2.aggOperationValue}
        </div>
      )}
      {item.operand1.aggOperationValue && (
        <div className='mr-[1rem] h-[32px] leading-[32px] mb-[1rem]'>{'('}</div>
      )}
      {item.operand2.calculate && <Child item={item.operand2.calculate} />}
      {!item.operand2.calculate && (
        <div
          className='mr-[1rem] px-[11px] mb-[1rem]'
          style={{
            backgroundColor: '#d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
            height: '32px',
            lineHeight: '30px',
          }}>
          {item.operand2.fieldFirst}
        </div>
      )}
      {!item.operand2.calculate && (
        <Select
          className='mr-[1rem] mb-[1rem]'
          value={item.operand2.fieldSecond}
          style={{ width: 150 }}
          options={[
            {
              value: item.operand2.fieldSecond,
              label: '@' + item.operand2.fieldSecond,
            },
          ]}
        />
      )}
      {item.operand2.aggOperationValue && (
        <div className='h-[32px] leading-[32px] mb-[1rem] mr-[1rem]'>{')'}</div>
      )}
      <div className='h-[32px] leading-[32px] mr-[1rem] mb-[1rem]'>{')'}</div>
    </div>
  );
};
