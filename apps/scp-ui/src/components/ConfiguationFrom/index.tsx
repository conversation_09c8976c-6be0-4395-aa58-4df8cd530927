import {
  Config<PERSON>rovider,
  Table,
  Drawer,
  Input,
  Divider,
  Space,
  Collapse,
  // Button,
} from 'antd';
import TableStep from '@/components/TableStep';
import { useState, useEffect } from 'react';
import icon from '@/components/SVG';
import JWFormStyles from './index.module.css';
import type { CollapseProps } from 'antd';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import Calculate from './Calculate';

type propsType = {
  columns: any[];
  onSearch: any;
  data: any[];
  pageType: string;
  detailsList: any[];
  drawerColumns?: any[];
  total: number;
  getPageData: any;
  loading: boolean;
  isListNull: boolean;
};

const ConfiguationFrom: React.FC<propsType> = (props) => {
  const { t } = useVoerkaI18n();
  const co = {
    Attribute: {
      detailsList: [
        {
          label: 'ID',
          value: 'attributeId',
        },
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('类型'),
          value: 'valueType',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('类型'),
          value: 'valueType',
        },
      ],
    },
    MasterDataType: {
      detailsList: [
        {
          label: 'ID',
          value: 'masterDataTypeId',
        },
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      drawerColumns: [
        {
          title: t('名称'),
          dataIndex: 'attributeLabel',
          key: 'attributeLabel',
          isFilter: true,
        },
        {
          title: 'IsRoot',
          dataIndex: 'isRequired',
          key: 'isRequired',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
        {
          title: 'key',
          dataIndex: 'isUnique',
          key: 'isUnique',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'label',
        },
      ],
    },
    PlanLevel: {
      detailsList: [
        {
          label: 'ID',
          value: 'planningLevelId',
        },
        {
          label: t('名称'),
          value: 'planningLevelName',
        },
        {
          label: t('时间维度'),
          value: 'tpLevel',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      drawerColumns: [
        {
          title: t('名称'),
          dataIndex: 'attributeLabel',
          key: 'attributeLabel',
          isFilter: true,
        },
        {
          title: 'IsRoot',
          dataIndex: 'isRoot',
          key: 'isRoot',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
        {
          title: '是否必须',
          dataIndex: 'isRequired',
          key: 'isRequired',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => t('激活'),
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'planningLevelName',
        },
        {
          label: t('时间维度'),
          value: 'tpLevel',
        },
      ],
    },
    KeyFigure: {
      detailsList: [
        {
          label: 'ID',
          value: 'kfId',
        },
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('类型'),
          value: 'kfType',
        },
        {
          label: t('关联pl-ID'),
          value: 'planLevelID',
        },
        {
          label: t('关联pl-名称'),
          value: 'planLevelName',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'planningLevelName',
        },
        {
          label: t('类型'),
          value: 'kfType',
        },
        {
          label: t('关联pl-名称'),
          value: 'planLevelName',
        },
        {
          label: t('操作'),
          value: 'operation',
        },
      ],
    },
  };
  const {
    columns,
    drawerColumns,
    data,
    pageType,
    detailsList,
    total,
    getPageData,
    loading,
  } = props;
  const [newColumns, setNewColumns] = useState<any[]>([]);
  const [newdetailsList, setNewdetailsList] = useState<any[]>([]);
  const [tableData, setTableData] = useState<any>([]);
  const [currentDrawerData, setCurrentDrawerData] = useState<any>([]);
  const [items, setItems] = useState<CollapseProps['items']>([]);
  const [pageData, setPageData] = useState({
    page: 1,
    pageSize: 10,
  });
  const [open, setOpen] = useState(false);

  const plRowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows,
      );
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === 'Disabled User',
      name: record.name,
    }),
  };
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows,
      );
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === 'Disabled User',
      name: record.name,
    }),
  };
  const empty = {
    emptyText: !props.isListNull ? (
      <div></div>
    ) : (
      <icon.noSearchDataIcon isCollapse={false} />
    ),
  };

  const checkPage = (value: number) => {
    if (value === pageData.page) return;
    setPageData({ ...pageData, page: value });
    getPageData({ ...pageData, page: value });
  };
  const onClose = () => {
    setOpen(false);
    setNewdetailsList([]);
  };
  const dTitle: React.ReactNode = (
    <div className='flex justify-between'>
      <div>{pageType}</div>
    </div>
  );

  useEffect(() => {
    setTableData(data);
    setNewColumns([
      ...columns,
      {
        title: 'Action',
        key: 'action',
        render: () => (
          <Space size='middle'>
            <a>修改</a>
            <a>复制</a>
            <a className='text-error'>删除</a>
          </Space>
        ),
      },
    ]);
    setNewdetailsList(detailsList);
  }, [columns, data]);

  return (
    <div className={JWFormStyles.wrap}>
      <header className='flex'>
        <div></div>
      </header>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#EFF2F3',
              headerBg: '#ffffff',
              headerColor: '#293147',
              headerSplitColor: '#ffffff',
              cellPaddingBlock: 10,
            },
            Collapse: {
              headerBg: '#EFF2F3',
            },
          },
        }}>
        <main className={JWFormStyles.main}>
          <Table
            loading={loading}
            columns={newColumns}
            dataSource={tableData}
            onRow={(record) => {
              return {
                onClick: async () => {
                  function objToArray(obj: any) {
                    return Object.keys(obj).map((key) => ({
                      key,
                      value: obj[key],
                    }));
                  }
                  if (pageType === 'Attribute') {
                    const reArr = objToArray(record);
                    const _newdetailsList = co.Attribute.detailsList;
                    _newdetailsList.map((item: any) => {
                      reArr.map((i: any) => {
                        if (item.value === i.key) {
                          item.data = i.value;
                        }
                      });
                      if (item.value === 'isCustom') item.data = !item.data;
                    });
                    setNewdetailsList([..._newdetailsList]);
                  } else if (pageType === 'Master Data Type') {
                    const res = await ajax.getMasterDataTypeDetails({
                      masterDataTypeId: record.masterDataTypeId,
                    });
                    const reArr = objToArray(res.data);
                    const _newdetailsList = co.MasterDataType.detailsList;
                    _newdetailsList.map((item: any) => {
                      reArr.map((i: any) => {
                        if (item.value === i.key) {
                          item.data = i.value;
                        }
                      });
                      if (item.value === 'isCustom') item.data = !item.data;
                    });
                    setCurrentDrawerData([...res.data.details]);
                    setNewdetailsList([..._newdetailsList]);
                  } else if (pageType === 'Plan Level') {
                    const res = await ajax.getPlanningLevelDetail({
                      planLevelId: record.planningLevelId,
                    });
                    const reArr = objToArray(res.data);
                    const _newdetailsList = co.PlanLevel.detailsList;
                    _newdetailsList.map((item: any) => {
                      reArr.map((i: any) => {
                        if (item.value === i.key) {
                          item.data = i.value;
                        }
                      });
                      if (item.value === 'isCustom') item.data = !item.data;
                    });
                    const _items: CollapseProps['items'] = [];
                    res.data.details &&
                      res.data.details.map((item: any, index: number) => {
                        _items.push({
                          key: index,
                          label: item.label,
                          children: (
                            <Table
                              rowSelection={{
                                type: 'checkbox',
                                ...plRowSelection,
                              }}
                              columns={co.PlanLevel.drawerColumns}
                              dataSource={item.details}
                              rowKey={(_, index) => index as React.Key}
                              className='h-full'
                              pagination={false}
                              locale={empty}
                            />
                          ),
                        });
                      });
                    setItems([..._items]);
                    setCurrentDrawerData([...res.data.details]);
                    setNewdetailsList([..._newdetailsList]);
                  } else if (pageType === 'Key Figure') {
                    console.log(record.kfId);

                    const res = await ajax.getKeyFigureDetail({
                      kfId: record.kfId,
                    });
                    const reArr = objToArray(res.data);
                    const _newdetailsList = co.KeyFigure.detailsList;
                    _newdetailsList.map((item: any) => {
                      reArr.map((i: any) => {
                        if (item.value === i.key) {
                          if (i.key === 'kfType') {
                            item.data = i.value;
                            if (item.data === 1) {
                              item.data = '前端输入';
                            } else if (item.data === 2) {
                              item.data = '存储';
                            } else if (item.data === 3) {
                              item.data = '计算';
                            }
                          } else {
                            item.data = i.value;
                          }
                        }
                      });
                      if (item.value === 'isCustom') item.data = !item.data;
                    });
                    setCurrentDrawerData(res.data);
                    setNewdetailsList([..._newdetailsList]);
                  }
                  setOpen(true);
                },
              };
            }}
            rowKey={(_, index) => index as React.Key}
            className='h-full'
            scroll={{ y: 'calc(100vh - 32.5rem)' }}
            pagination={false}
            locale={empty}
          />
          <Drawer
            title={dTitle}
            onClose={onClose}
            open={open}
            width={pageType === 'Key Figure' ? 1300 : 600}>
            <div className='px-[2.6rem]'>
              <ConfigProvider
                theme={{
                  components: {
                    Input: {
                      activeBorderColor: '#ffffff',
                      activeShadow: '0 0 0 0 #ffffff',
                    },
                    Table: {
                      headerBg: '#EFF2F3',
                    },
                  },
                }}>
                {newdetailsList.map((item: any, index: number) => {
                  return (
                    <div className='flex mb-[1rem]' key={index}>
                      <div className={JWFormStyles.label}>{item.label}</div>
                      <div className={JWFormStyles.input}>
                        <Input
                          key={index}
                          defaultValue={item.data}
                          className='h-[2.5rem] border-none'
                        />
                      </div>
                    </div>
                  );
                })}

                <Divider plain></Divider>
                {pageType === 'Master Data Type' && (
                  <div>
                    {/* <div className='text-right'>
                      <Button type='primary' className='right-[1rem]'>
                        添加
                      </Button>
                      <Button danger>删除</Button>
                    </div> */}
                    <Table
                      rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                      }}
                      columns={drawerColumns}
                      dataSource={currentDrawerData}
                      rowKey={(_, index) => index as React.Key}
                      scroll={{ y: 'calc(100vh - 40rem)' }}
                      pagination={false}
                      locale={empty}
                    />
                  </div>
                )}
                {pageType === 'Plan Level' && <Collapse items={items} />}
                {pageType === 'Key Figure' && currentDrawerData.exp && (
                  <div>
                    计算公式：
                    <div className='border-1 h-[30vh] p-[2rem]'>
                      {currentDrawerData.exp.map((item: any, index: number) => {
                        return <Calculate key={index} item={item} />;
                      })}
                    </div>
                  </div>
                )}
              </ConfigProvider>
            </div>
          </Drawer>
        </main>
      </ConfigProvider>
      <footer className='flex justify-center'>
        <TableStep total={total} checkPageFn={checkPage} reset={false} />
      </footer>
    </div>
  );
};

export default ConfiguationFrom;
