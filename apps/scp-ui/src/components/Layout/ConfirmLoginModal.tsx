import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, App, Popconfirm } from 'antd';
import { setAgainLoginVisible } from '@/store/features/layoutSlice';
import { useSelector, useDispatch } from 'react-redux';
import ajax from '@/api/';
const ConfirmLoginModal: React.FC = () => {
  const { message } = App.useApp();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useDispatch();
  const user = useSelector((state: any) => state.login.user);
  const againLoginVisible = useSelector(
    (state: any) => state.layout.againLoginVisible,
  );
  const close = () => dispatch(setAgainLoginVisible(false));

  const againLogin = async (values: any) => {
    try {
      setLoading(true);
      const res = await ajax.loginAgain(values);
      if (res.code === 0) {
        message.success('登录成功,请继续操作');
        dispatch(setAgainLoginVisible(false));
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(user);
    if (user === null || user === undefined) {
      window.location.href = '/login';
    }
    setVisible(againLoginVisible || false);
  }, [dispatch, againLoginVisible]);
  return (
    <Modal
      title={'您的登录已过期，请重新登录'}
      open={visible}
      onCancel={close}
      footer={false}
      destroyOnClose={true}>
      <Form labelCol={{ span: 4 }} onFinish={againLogin}>
        <Form.Item label='用户名'>
          <div>{user?.userName}</div>
        </Form.Item>
        <Form.Item name='password' label='密码'>
          <Input type='password' />
        </Form.Item>
        <Form.Item label='公司编码'>
          <div>{user?.coCode}</div>
        </Form.Item>
        <Form.Item>
          <div className='grid grid-cols-4 justify-center gap-2'>
            <Popconfirm
              title='确定退出到登录页面吗'
              cancelText='取消'
              onConfirm={() => {
                dispatch(setAgainLoginVisible(false));
                window.location.href = '/login';
              }}
              okText='确定'>
              <Button className='col-span-1 col-start-2'>退出登录</Button>
            </Popconfirm>
            <Button
              loading={loading}
              className='col-span-1 col-start-3'
              type='primary'
              htmlType='submit'>
              再次登录
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConfirmLoginModal;
