import React, { useEffect, useState } from 'react';
import { Drawer, Layout, Menu, Tooltip, Modal } from 'antd';
import FloatChartButton from './FloatChartButton.tsx';
import Sider from 'antd/es/layout/Sider';
import ajax from '@/api';
import { Content, Header as LayoutHeader } from 'antd/es/layout/layout';
import { useAuth } from '@/hooks';
import { useNavigate, useLocation, useOutlet } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { CollapsedIcon } from './layout';
import Header from './Header.tsx';
import { useVoerkaI18n } from '@voerkai18n/react';
import MessageItem from '@/components/HomeMessageItem';
import {
  setPVersionList,
  setMasterList,
  setShowbeLoginCovered,
} from '@/store/features/layoutSlice.ts';
import { setShowLoginConfirm, setUser } from '@/store/features/loginSlice.ts';
import AntdConfigProvider from '@/components/Layout/AntdConfigProvider.tsx';
import ConfirmLoginModal from '@/components/Layout/ConfirmLoginModal.tsx';
import store from '@/store';
import _ from 'lodash';
// import KeepAlive from 'react-activation';
import { KeepAliveContext } from '../KeepAlive/KeepAlliveContext.ts';
import KeepAliveOutlet from '../KeepAlive/KeepAliveOutlet.tsx';

const JWLayout: React.FC = () => {
  const { t } = useVoerkaI18n();

  const { menuList, authMenus } = useAuth([]);

  const unKeepAliveRouteList = [
    '/home',
    '/checkApplication',
    '/runApplicationJob',
    '/setting',
    '/forecastModalAdd',
    '/editProcessTemplate',
  ];

  const navigate = useNavigate();

  const dispatch = useDispatch();
  const location = useLocation();
  const user = useSelector((state: any) => state.login.user);
  const coCode = user?.coCode || '';
  //当前选中的nav
  const [currentNav, setCurrentNav] = useState<string>(() => {
    const path = location.pathname;
    return path;
  });

  const [drawerVisible, setDrawerVisible] = useState(false);
  const [collapsed, setCollapsed] = useState<boolean>(false);

  const outlet = useOutlet();

  const getPaPv = async () => {
    const res = await ajax.getAreaAndVersion();
    if (res.code !== 0) return;
    const pVersionList = res.data.list.map((item: any) => {
      return {
        label: item.pversion,
        value: item.paPvId,
        key: item.paPvId,
        isBaseVersion: item.isBaseVersion,
      };
    });
    dispatch(setPVersionList(pVersionList));
  };

  const getUserInfo = async () => {
    try {
      const res = await ajax.getUserInfo();
      const userInfo = _.get(res, 'data.userInfo', {});
      dispatch(setUser(userInfo));
    } catch (error) {
      console.error(error);
    }
  };

  const getMasterList = async () => {
    try {
      const res = await ajax.getViewMasterDataList();
      if (res.code !== 0) return;
      dispatch(setMasterList(res.data.list));
    } catch (err) {
      console.error(err);
    }
  };

  // useEffect(() => {
  //   navs.map((item: NavProps) => {
  //     if (navs.find((nav: NavProps) => nav.key === item.key)) return;
  //     dispatch(addNav(item));
  //   });
  // }, [dispatch]);

  // useEffect(() => {
  //   if (window.location.pathname === '/') {
  //     navigate('/home');
  //   }
  //   getUserInfo();
  //   setCurrentNav('/' + window.location.pathname.split('/')[1]);
  // }, [window.location.pathname]);

  useEffect(() => {
    getUserInfo();
    getPaPv();
    getMasterList();
  }, [authMenus]);

  const handleLoginCovered = () => {
    localStorage.clear();
    navigate('/login');
  };

  return (
    <>
      <KeepAliveContext.Provider
        value={{
          outlet,
          key: location.pathname,
        }}>
        <div className='pchome'>
          <AntdConfigProvider>
            <Layout className='h-screen'>
              <Sider
                width={240}
                className='border border-solid border-r-neutral-5'
                collapsed={collapsed}
                collapsedWidth={108}>
                <div className='flex flex-col justify-between h-full'>
                  <div>
                    <div
                      className='h-20 flex justify-center items-center cursor-pointer text-brand font-bold text-[32px]'
                      onClick={() => {
                        navigate('/home');
                      }}>
                      <img
                        className='h-[50px] mt-[20px]'
                        src={
                          coCode.startsWith('509')
                            ? '/BlackLake.svg'
                            : !collapsed
                              ? '/scmify_logo/PNG/colorful-logo-font.png'
                              : '/scmify_logo/PNG/colorful-logo.png'
                        }
                      />
                    </div>
                    <Menu
                      mode='inline'
                      style={{
                        // display: 'flex',
                        // flexDirection: 'column',
                        height: 'calc(100vh - 140px)',
                        marginTop: '20px',
                        fontSize: '16px',
                        overflow: 'auto',
                      }}
                      defaultSelectedKeys={['/home']}
                      items={menuList.map((item: any, index: number) => {
                        const processChildren = (children: any) => {
                          if (children === undefined) return null;
                          return children.map((child: any) => {
                            return {
                              key: child.path,
                              label: child.name,
                              children: processChildren(child.children),
                            };
                          });
                        };
                        return {
                          key: item.path || index,
                          label: (
                            <Tooltip title={item.name}>
                              <div className='max-w-xs'>{item.name}</div>
                            </Tooltip>
                          ),
                          icon: item.icon,
                          children: processChildren(item.children),
                        };
                      })}
                      selectedKeys={[currentNav]}
                      onClick={({ key }) => {
                        navigate(key);
                        setCurrentNav(key);
                      }}
                    />
                  </div>
                  <div className='w-full flex justify-center pb-4'>
                    <CollapsedIcon
                      onClick={() => setCollapsed(!collapsed)}
                      style={{ transform: !collapsed ? 'rotate(180deg)' : '' }}
                    />
                  </div>
                </div>
              </Sider>
              <Layout>
                <LayoutHeader>
                  <Header openDrawer={() => setDrawerVisible(true)} />
                </LayoutHeader>
                <Content className='p-8 h-full overflow-auto'>
                  {unKeepAliveRouteList.includes(location.pathname) ? (
                    outlet
                  ) : (
                    <KeepAliveOutlet />
                  )}
                  {/* {outlet} */}
                </Content>
              </Layout>
            </Layout>
            <Drawer
              title='消息'
              open={drawerVisible}
              onClose={() => setDrawerVisible(false)}>
              {[].map((item: any, index: number) => {
                return <MessageItem key={index} item={item} />;
              })}
            </Drawer>
            <FloatChartButton />
          </AntdConfigProvider>
          <ConfirmLoginModal />
        </div>

        <Modal
          open={useSelector((state: any) => state.layout.showBeLoginCovered)}
          title={'下线提示'}
          cancelText={<></>}
          okText={t('确认')}
          onOk={() => {
            store.dispatch(setShowLoginConfirm(false));
            store.dispatch(setShowbeLoginCovered(false));
            handleLoginCovered();
          }}
          cancelButtonProps={{
            style: {
              display: 'none',
            },
          }}
          closable={false}>
          <span>{'该账号已被其他用户登录, 您已下线, 请重新登录。'}</span>
        </Modal>
      </KeepAliveContext.Provider>
    </>
  );
};
export default JWLayout;
