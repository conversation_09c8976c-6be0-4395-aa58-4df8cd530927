import { useState } from 'react';
import ChartButton from '../ChartButton';
import { FloatButton } from 'antd';
import ajax from '@/api';
import { ChartStarIcon } from '@/pages/PlanningViewNew/PlanningView'; // 确保这个图标路径正确

const FloatChartButton: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const closeModal = () => setModalVisible(false);
  // const openModal = () => setModalVisible(true);

  const getAiStatus = async (): Promise<boolean> => {
    try {
      const res = await ajax.getAiSwitch();
      return res.data.isAi;
    } catch (error) {
      console.error(error);
    }
    return false;
  };
  const showModal = async () => {
    const _isShowChartButton = await getAiStatus();
    if (_isShowChartButton) {
      setModalVisible(true);
    }
  };
  return (
    <div className='fixed right-4 bottom-4'>
      <ChartButton open={modalVisible} closeModal={closeModal}>
        <FloatButton onClick={showModal} icon={<ChartStarIcon />} />
      </ChartButton>
    </div>
  );
};

export default FloatChartButton;
