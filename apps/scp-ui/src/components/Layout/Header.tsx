import { memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setLanguage,
  setShowbeLoginCovered,
} from '@/store/features/layoutSlice';
import { Avatar, ConfigProvider, Dropdown, Switch } from 'antd';
import { useNavigate } from 'react-router-dom';
import type { MenuProps } from 'antd/lib';
import ajax from '@/api';
import { i18nScope } from '@/languages';
import { useVoerkaI18n } from '@voerkai18n/react';
import { setShowLoginConfirm } from '@/store/features/loginSlice';
import store from '@/store';

type HeaderProps = {
  openDrawer: () => void;
};

const Header: React.FC<HeaderProps> = ({ openDrawer }) => {
  const { t } = useVoerkaI18n();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const user = useSelector((state: any) => state.login.user);
  const token = useSelector((state: any) => state.login.token);
  const language = useSelector((state: any) => state.layout.language);
  const changeLanguage = async (language: string) => {
    dispatch(setLanguage(language));
    await i18nScope.change(language === 'en' ? 'en-US' : 'zh-CN');

    window.location.reload();
  };
  const items: MenuProps['items'] = [
    {
      label: t('用户设置'),
      key: '1',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='-3 -3 20 20'
          fill='none'>
          <path
            d='M7.50026 7.06251V0.499986C7.50026 0.223859 7.27402 0 6.99788 0C6.72175 0 6.49551 0.223859 6.49551 0.499986V7.06347C5.63752 7.28615 4.99842 8.06869 4.99842 8.99997C4.99842 9.93266 5.63627 10.7162 6.49991 10.9375C6.49714 10.9582 6.49567 10.9791 6.49551 11.0001V13.5C6.49551 13.7761 6.72175 14 6.99788 14C7.27402 14 7.50026 13.7761 7.50026 13.5V11.0001C7.49986 10.9795 7.49821 10.9589 7.49529 10.9385C8.36079 10.7184 9.00158 9.93379 9.00158 9.00008C9.00175 8.0674 8.3639 7.28395 7.50026 7.06251ZM7.70686 9.70705C7.54325 9.87065 7.32799 9.97247 7.09773 9.99515C6.86747 10.0178 6.63648 9.95997 6.4441 9.83144C6.25172 9.70291 6.10986 9.51164 6.04269 9.29025C5.97552 9.06885 5.9872 8.83101 6.07574 8.61726C6.16428 8.40351 6.3242 8.22707 6.52824 8.118C6.73229 8.00893 6.96785 7.97399 7.19477 8.01912C7.4217 8.06425 7.62596 8.18667 7.77274 8.36551C7.91952 8.54435 7.99975 8.76855 7.99976 8.99992C8.00013 9.13131 7.97443 9.26146 7.92416 9.38285C7.87388 9.50425 7.80002 9.61446 7.70686 9.70711V9.70705ZM2.4945 2.06254C2.49732 2.04181 2.49884 2.02092 2.49907 2V0.499986C2.49907 0.367501 2.44644 0.240443 2.35275 0.146762C2.25907 0.053081 2.13201 0.000451566 1.99952 0.000451566C1.86703 0.000451566 1.73996 0.053081 1.64628 0.146762C1.55259 0.240443 1.49996 0.367501 1.49996 0.499986V2C1.49957 2.02119 1.50052 2.04238 1.50278 2.06344C0.639148 2.28471 -1.56257e-08 3.06816 0 4.00096C1.56257e-08 4.93376 0.63926 5.71687 1.5029 5.93842C1.50069 5.95885 1.49973 5.9794 1.50002 5.99994V13.5C1.50002 13.5656 1.51294 13.6306 1.53804 13.6912C1.56315 13.7518 1.59995 13.8069 1.64633 13.8532C1.69272 13.8996 1.74779 13.9364 1.8084 13.9615C1.86901 13.9866 1.93397 13.9995 1.99957 13.9995C2.06518 13.9995 2.13014 13.9866 2.19074 13.9615C2.25135 13.9364 2.30642 13.8996 2.35281 13.8532C2.3992 13.8069 2.436 13.7518 2.4611 13.6912C2.48621 13.6306 2.49913 13.5656 2.49913 13.5V6C2.4989 5.9797 2.49745 5.95944 2.49478 5.93932C3.36022 5.71919 4.00033 4.93461 4.00033 4.00096C4.00033 3.06731 3.36005 2.28256 2.4945 2.06254ZM2.70663 4.7081C2.54302 4.8717 2.32776 4.97351 2.0975 4.99619C1.86724 5.01887 1.63625 4.96102 1.44387 4.83248C1.25149 4.70395 1.10963 4.51269 1.04246 4.29129C0.975292 4.06989 0.986972 3.83206 1.07551 3.6183C1.16405 3.40455 1.32397 3.22811 1.52801 3.11904C1.73206 3.00998 1.96762 2.97503 2.19454 3.02016C2.42147 3.0653 2.62573 3.18771 2.77251 3.36655C2.91929 3.5454 2.99952 3.7696 2.99953 4.00096C2.99989 4.13234 2.97419 4.26249 2.92392 4.38387C2.87364 4.50525 2.79978 4.61545 2.70663 4.7081ZM14 4.00096C14 3.06697 13.3586 2.28256 12.4929 2.06254C12.4951 2.04177 12.496 2.02088 12.4956 2V0.499986C12.4956 0.367501 12.4429 0.240443 12.3493 0.146762C12.2556 0.053081 12.1285 0.000451566 11.996 0.000451566C11.8635 0.000451566 11.7365 0.053081 11.6428 0.146762C11.5491 0.240443 11.4965 0.367501 11.4965 0.499986V2C11.4967 2.02123 11.4983 2.04242 11.5012 2.06344C10.6375 2.28471 9.99927 3.06816 9.99927 4.00096C9.99927 4.93376 10.6371 5.71716 11.5008 5.93848C11.498 5.95919 11.4965 5.98006 11.4964 6.00096V13.5C11.4964 13.6325 11.549 13.7596 11.6427 13.8532C11.7364 13.9469 11.8634 13.9995 11.9959 13.9995C12.1284 13.9995 12.2555 13.9469 12.3491 13.8532C12.4428 13.7596 12.4955 13.6325 12.4955 13.5V6.00096C12.4959 5.9804 12.495 5.95983 12.4929 5.93938C13.3587 5.7193 14 4.93489 14 4.00096ZM12.707 4.70804C12.5434 4.87161 12.3282 4.9734 12.0979 4.99606C11.8677 5.01871 11.6367 4.96084 11.4443 4.83229C11.252 4.70375 11.1101 4.51248 11.043 4.29109C10.9759 4.0697 10.9876 3.83188 11.0761 3.61815C11.1647 3.40442 11.3246 3.228 11.5286 3.11896C11.7327 3.00992 11.9682 2.975 12.1951 3.02015C12.422 3.0653 12.6263 3.18773 12.773 3.36657C12.9198 3.54542 13 3.76961 13 4.00096C13.0003 4.13233 12.9746 4.26247 12.9243 4.38384C12.8741 4.50522 12.8002 4.61541 12.707 4.70804Z'
            fill='black'
          />
        </svg>
      ),
      onClick: () => {
        navigate('/user');
      },
    },
    {
      label: t('进入集成'),
      key: '6',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'>
          <path
            d='M17.6603 11.6538L10.7659 14.71C10.1059 14.9934 9.84313 14.9934 9.23469 14.71L2.33969 11.6538C1.97375 11.4622 1.875 10.7528 1.875 10.3656C2.33531 10.625 2.9825 10.9138 2.95844 10.8991L10 14.1003L17.0416 10.8991C17.0538 10.8963 17.6816 10.5841 18.125 10.3656C18.125 10.7619 18 11.4975 17.6603 11.6538ZM17.6603 8.4525L10.7659 11.5087C10.1059 11.7919 9.84313 11.7919 9.23469 11.5087L2.33969 8.4525C1.79812 8.16906 1.815 7.27688 2.33969 6.94344L9.23406 3.35375C9.84344 3.05375 10.2247 3.03719 10.7653 3.35375L17.6603 6.94313C18.185 7.22656 18.1681 8.21875 17.6603 8.4525ZM10 3.96313L2.95844 7.69781L10 10.625L17.0416 7.69875L10 3.96313ZM10 3.96313C10.0375 3.975 10.0509 4.02188 10 3.96313V3.96313ZM10 17.3016L17.0416 14.1003C17.0538 14.0975 17.6816 13.7853 18.125 13.5669C18.125 13.9631 18 14.6987 17.6603 14.855L10.7659 17.9109C10.1059 18.1944 9.84313 18.1944 9.23469 17.9109L2.33969 14.855C1.97375 14.6634 1.875 13.9541 1.875 13.5669C2.33531 13.8263 2.9825 14.115 2.95844 14.1003L10 17.3016Z'
            fill='black'
          />
        </svg>
      ),
      onClick: async () => {
        const res = await ajax.scpJumpToInt();
        if (res.code === 0) {
          window.open(
            `${res.data.host}/home?scpToken=${token}&intToken=${res.data.intToken}&intUser=${res.data.intUserInfo}&pareaId=${localStorage.getItem('pareaId')}&scene=${res.data.scene}`,
          );
        }
      },
    },
    {
      label: t('注销'),
      key: '7',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='-1 -1 18 18'
          fill='none'>
          <path
            d='M13.5001 4.50004C14.3901 5.39006 14.9962 6.52402 15.2417 7.75851C15.4873 8.993 15.3613 10.2726 14.8796 11.4355C14.3979 12.5983 13.5822 13.5922 12.5357 14.2915C11.4891 14.9908 10.2587 15.364 9.00003 15.364C7.74136 15.364 6.51095 14.9908 5.46439 14.2915C4.41784 13.5922 3.60215 12.5983 3.12048 11.4355C2.6388 10.2726 2.51278 8.993 2.75833 7.75851C3.00389 6.52402 3.61 5.39006 4.50002 4.50004'
            stroke='#0D152D'
            strokeLinecap='round'
          />
          <path d='M9 8.00006V2.00006' stroke='#0D152D' strokeLinecap='round' />
        </svg>
      ),
      onClick: () => {
        ajax
          .logout()
          .then(() => {
            store.dispatch(setShowLoginConfirm(false));
            store.dispatch(setShowbeLoginCovered(false));
            localStorage.clear();
            navigate('/login');
          })
          .catch((err) => {
            console.error(err);
          });
      },
    },
  ];

  return (
    <div className='w-full h-full relative'>
      <div className='w-60 h-full flex justify-between items-center absolute right-0'>
        <ConfigProvider
          theme={{
            components: {},
          }}>
          {/* 中英文切换 */}
          {/* <div
            className='w-[80px] h-[40px] text-[22px] text-center'
            style={{
              borderRadius: '2.07143rem',
              background: '#EFEFEF',
              cursor: 'pointer',
            }}
            onClick={async () => {
              if (language === 'zh') {
                await i18nScope.change('en');
                changeLanguage('en');
              } else if (language === 'en') {
                await i18nScope.change('zh');
                changeLanguage('zh');
              }
            }}>
            <div
              className={
                'w-[40px] h-[40px] leading-[40px] ' +
                `${language === 'en' ? 'languageCheck' : ''}`
              }
              style={{
                borderRadius: '50%',
                background: '#BFD0F8',
                transition: 'transform 0.3s ease',
              }}>
              {language === 'en' ? 'EN' : '中'}
            </div>
          </div> */}
          <Switch
            checked={language === 'en'}
            checkedChildren='EN'
            unCheckedChildren='中'
            onChange={(checked) => {
              if (checked) {
                changeLanguage('en');
              } else {
                changeLanguage('zh');
              }
            }}
          />

          {/* 消息提示按钮 */}
          <div
            onClick={openDrawer}
            className='w-[38px] h-[38px] bg-neutral-5 rounded-full flex justify-center items-center'>
            {/* 无消息 */}
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='25'
              height='25'
              viewBox='0 0 25 25'
              fill='none'>
              <path
                d='M11.442 25C9.289 25 7.53577 23.2479 7.53577 21.0938C7.53577 20.6625 7.88577 20.3125 8.31702 20.3125C8.74827 20.3125 9.09827 20.6625 9.09827 21.0938C9.09827 22.3866 10.1504 23.4375 11.442 23.4375C12.7337 23.4375 13.7858 22.3866 13.7858 21.0938C13.7858 20.6625 14.1358 20.3125 14.567 20.3125C14.9983 20.3125 15.3483 20.6625 15.3483 21.0938C15.3483 23.2479 13.5952 25 11.442 25Z'
                fill='#FE5C73'
              />
              <path
                d='M20.0357 21.875H2.84824C1.84307 21.875 1.02539 21.0573 1.02539 20.0521C1.02539 19.5187 1.25771 19.0136 1.66283 18.6666C1.68896 18.6438 1.717 18.623 1.74618 18.6041C3.2753 17.2697 4.15039 15.35 4.15039 13.3228V10.4166C4.15039 6.39591 7.42226 3.125 11.442 3.125C11.6087 3.125 11.7889 3.12805 11.9556 3.15628C12.3816 3.22704 12.6692 3.63026 12.5982 4.05521C12.5275 4.48017 12.117 4.7678 11.6993 4.69685C11.6159 4.6833 11.5244 4.6875 11.442 4.6875C8.28381 4.6875 5.71289 7.25727 5.71289 10.4166V13.3228C5.71289 15.8396 4.60968 18.2209 2.68898 19.8551C2.67334 19.8677 2.6598 19.8792 2.64301 19.8906C2.61497 19.9261 2.58789 19.9802 2.58789 20.0521C2.58789 20.1937 2.70672 20.3125 2.84824 20.3125H20.0357C20.1775 20.3125 20.2963 20.1937 20.2963 20.0521C20.2963 19.9791 20.2692 19.9261 20.24 19.8906C20.2244 19.8792 20.2108 19.8677 20.1952 19.8551C18.2734 18.2198 17.1713 15.8396 17.1713 13.3228V12.1876C17.1713 11.7563 17.5213 11.4063 17.9525 11.4063C18.3838 11.4063 18.7338 11.7563 18.7338 12.1876V13.3228C18.7338 15.3511 19.6098 17.2718 21.1411 18.6073C21.1691 18.626 21.1962 18.6459 21.2212 18.6678C21.6265 19.0136 21.8588 19.5187 21.8588 20.0521C21.8588 21.0573 21.0411 21.875 20.0357 21.875Z'
                fill='#FE5C73'
              />
              <path
                d='M18.7338 10.4166C15.8619 10.4166 13.5254 8.08029 13.5254 5.2084C13.5254 2.3365 15.8619 0 18.7338 0C21.6057 0 23.942 2.3365 23.942 5.2084C23.942 8.08029 21.6057 10.4166 18.7338 10.4166ZM18.7338 1.5625C16.7233 1.5625 15.0879 3.19786 15.0879 5.2084C15.0879 7.21874 16.7233 8.8541 18.7338 8.8541C20.7441 8.8541 22.3795 7.21874 22.3795 5.2084C22.3795 3.19786 20.7441 1.5625 18.7338 1.5625Z'
                fill='#FE5C73'
              />
            </svg>
          </div>

          <Dropdown
            trigger={['click']}
            menu={{ items }}
            overlayStyle={{
              width: '200px',
            }}>
            <Avatar
              src={
                user?.headerImg ? <img src={user?.headerImg} /> : user?.nickName
              }
              className='w-[48px] h-[48px] cursor-pointer border-1'>
              {user?.nickName}
            </Avatar>
          </Dropdown>
        </ConfigProvider>
      </div>
    </div>
  );
};
const MemoHeader = memo(Header);
export default MemoHeader;
