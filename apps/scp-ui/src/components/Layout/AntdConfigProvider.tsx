import React, { useState, useEffect } from 'react';
import { ConfigProvider } from 'antd';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import { useSelector } from 'react-redux';

interface AntdConfigProviderProps extends React.PropsWithChildren {}
const AntdConfigProvider: React.FC<AntdConfigProviderProps> = ({
  children,
}) => {
  const language = useSelector((state: any) => state.layout.language);
  const [antdLanguagePackage, setAntdLanguagePackage] = useState(zhCN);

  useEffect(() => {
    if (language === 'en') {
      setAntdLanguagePackage(enUS);
    } else {
      setAntdLanguagePackage(zhCN);
    }
  }, [language]);

  return (
    <ConfigProvider
      locale={antdLanguagePackage}
      theme={{
        components: {
          Table: {
            headerBg: 'white',
            headerSplitColor: 'white',
            rowExpandedBg: 'white',
          },
          Layout: {
            bodyBg: '#f6f7fb',
            headerBg: '#ffffff',
            headerHeight: 68,
            headerPadding: '0 40px',
            siderBg: '#ffffff',
          },
          Menu: {
            fontSize: 18,
            iconSize: 25,
            itemColor: '#b9bcc6',
            itemSelectedBg: '#ffffff',
            itemSelectedColor: '#2f6bff',
            iconMarginInlineEnd: '40px',
            itemHoverBg: 'white',
            itemHoverColor: '#1890FF',
            // itemPaddingInline: 8,
            collapsedIconSize: 25,
            collapsedWidth: 108,
            // itemHeight: 50,
          },
          Collapse: {
            headerBg: 'white ',
          },
        },
      }}>
      {children}
    </ConfigProvider>
  );
};
export default AntdConfigProvider;
