import React, { useState, useEffect, useContext } from 'react';
import { HeaderFilterContext } from '../HeaderFilterContext';
import { Select, Form, Input, DatePicker, InputNumber } from 'antd';
import { SelectProps } from 'antd/lib';

type QuickFilterItemProps = {
  field: any;
};
const QuickFilterItem: React.FC<QuickFilterItemProps> = ({ field }) => {
  const form = Form.useFormInstance();
  const { filterData, filterTypeSelect } = useContext(HeaderFilterContext);

  const [fieldOptions, setFieldOptions] = useState<SelectProps['options']>([]);
  const [picker, setPicker] = useState<any>('date');
  const [currentOpt, setCurrentOpt] = useState<string>('');

  const getFormItem = (type: string) => {
    switch (type) {
      case 'string':
        return <Input className='w-[140px]' />;
      case 'date':
        return <DatePicker className='w-[140px]' picker={picker} />;
      case 'number':
        return <InputNumber className='w-[140px]' />;
      default:
        return <Input className='w-[140px]' />;
    }
  };

  useEffect(() => {
    const options: SelectProps['options'] = filterData.map((item) => {
      return {
        label: item.conditionContent.label,
        value: item.conditionContent.field,
      };
    });
    setFieldOptions(options);
  }, [filterData]);

  useEffect(() => {
    const _field = form.getFieldValue(['filter', field.name, 'field']);
    const type = filterData.find(
      (item) => item.conditionContent.field === _field,
    )?.conditionContent.type;

    let _picker = form.getFieldValue(['filter', field.name, 'opt']) || '';
    if (type === 'date' && _picker !== 'year' && _picker !== 'month') {
      _picker = 'date';
    }
    setPicker(_picker);
  }, [currentOpt]);
  return (
    <>
      <Form.Item
        noStyle
        {...field}
        name={[field.name, 'field']}
        rules={[{ required: true }]}>
        <Select
          className='w-[140px]'
          options={fieldOptions}
          onChange={() => {
            form.setFieldsValue({
              filter: {
                [field.name]: {
                  opt: undefined,
                  value: undefined,
                },
              },
            });
          }}
        />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prev, next) => {
          const fieldChange =
            prev?.filter[field.name]?.field !== next?.filter[field.name]?.field;
          return fieldChange;
        }}>
        {({ getFieldValue }) => {
          const _field = getFieldValue(['filter', field.name, 'field']);
          const type = filterData.find(
            (item) => item.conditionContent.field === _field,
          )?.conditionContent.type;
          return (
            <>
              <Form.Item
                noStyle
                {...field}
                name={[field.name, 'opt']}
                rules={[{ required: true, message: '不可为空' }]}>
                <Select
                  className='w-[140px] mx-2'
                  onChange={(value) => {
                    setCurrentOpt(value);
                    form.setFieldValue(['filter', field.name, 'type'], type);
                  }}
                  options={filterTypeSelect.get(type as string)}
                />
              </Form.Item>
              <Form.Item noStyle {...field} name={[field.name, 'value']}>
                {getFormItem(type || '')}
              </Form.Item>
            </>
          );
        }}
      </Form.Item>
    </>
  );
};

export default QuickFilterItem;
