import React, { useContext, memo, useEffect } from 'react';
import { HeaderFilterContext } from '../HeaderFilterContext';
import { useVoerkaI18n } from '@voerkai18n/react';
import { Form, Button } from 'antd';
import QuickFilterItem from './Item';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

type QuickFilterProps = {
  schemeId: number;
  saveScheme: () => void;
};
const QuickFilter: React.FC<QuickFilterProps> = memo(
  ({ schemeId, saveScheme }) => {
    const { t } = useVoerkaI18n();
    const { schemeData } = useContext(HeaderFilterContext);
    const form = Form.useFormInstance();
    useEffect(() => {
      const _initValues: {
        field: string;
        opt: string;
        value: any;
      }[] =
        schemeData
          .find((item) => item.schemeId === form.getFieldValue('schemeId'))
          ?.conditionList.map((item) => {
            const isDateType = item.conditionContent.type === 'date';
            return {
              field: item.conditionContent.field,
              opt: item.preferOperator,
              value: isDateType ? dayjs(item.preferValue) : item.preferValue,
              type: item.conditionContent.type,
            };
          }) || [];
      form.setFieldsValue({ filter: _initValues });
    }, [schemeId]);

    return (
      <Form.List name='filter'>
        {(fields, { add, remove }) => (
          <div className='flex flex-wrap'>
            {fields.map(({ key, name, ...field }) => {
              return (
                <div key={key} className='mb-2'>
                  <QuickFilterItem field={{ ...field, name }} />
                  <Button
                    onClick={() => remove(name)}
                    danger
                    type='dashed'
                    className='rounded-full ml-2 mr-4'>
                    {t('删除')}
                  </Button>
                </div>
              );
            })}
            <Button
              onClick={() => add()}
              type='dashed'
              className='rounded-full'>
              + {t('添加过滤项')}
            </Button>
            <Button
              onClick={() => saveScheme()}
              // type='primary'
              className='rounded-full ml-4'>
              {t('保存')}
            </Button>
          </div>
        )}
      </Form.List>
    );
  },
);

export default QuickFilter;
