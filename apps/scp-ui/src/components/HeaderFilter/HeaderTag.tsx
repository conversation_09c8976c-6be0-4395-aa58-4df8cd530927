import React from 'react';
import { FilterSchemeInfo } from '@/api/Filter/model';
import { Form, Button, Input, Tag, Popconfirm, App } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useState, useEffect } from 'react';
import ajax from '@/api';

interface HeaderTagProps {
  options: FilterSchemeInfo[];
  defaultSchemeId: number;
  menuTag: string;
  callback?: () => void;
  value?: number;
  onChange?: (value: number) => void;
}

const HeaderTag: React.FC<HeaderTagProps> = React.memo((props) => {
  const form = Form.useFormInstance();
  const {
    menuTag,
    options,
    value = -1,
    onChange,
    callback,
    defaultSchemeId,
  } = props;
  const { message } = App.useApp();
  const { t } = useVoerkaI18n();
  const [schemeId, setSchemeId] = useState<number>(value);
  const [isAddStatus, setIsAddStatus] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const disableDefaultAndSystem = options
    .filter((item) => item.isSystem || item.isDefault)
    .map((item) => item.schemeId);

  useEffect(() => {
    setSchemeId(value);
  }, [value]);

  const addNewScheme = async () => {
    setIsAddStatus(false);
    const params = {
      conditionList: [],
      isDefault: false,
      menuTag,
      schemeId: null,
      schemeName: inputValue,
    };
    try {
      const res = await ajax.saveScheme(params);
      if (res.code === 0) {
        console.log('添加成功');
        message.success('添加成功');
        callback && callback();
      } else {
        console.log('添加失败');
        message.error('添加失败');
      }
    } catch (error: any) {
      console.error(error);
      message.error(error);
    } finally {
      setInputValue('');
    }
  };

  const deleteScheme = async (schemeId: number) => {
    if (schemeId === defaultSchemeId) {
      console.log('默认方案不能删除');
      return;
    }
    try {
      const res = await ajax.deleteScheme({ schemeId });
      if (res.code === 0) {
        console.log('删除成功');
        callback && callback();
        form.setFieldsValue({ schemeId: defaultSchemeId });
      } else {
        console.log('删除失败');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const setDefaultScheme = async (id: number) => {
    try {
      const res = await ajax.setDefaultScheme({ schemeId: id });
      if (res.code === 0) {
        message.success('设置成功');
        callback && callback();
      } else {
        message.success('设置失败');

        console.log('设置失败');
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      {options.map((item: FilterSchemeInfo) => (
        <Tag.CheckableTag
          key={item.schemeId}
          onClick={() => {
            setSchemeId(item.schemeId);
            onChange && onChange(item.schemeId);
          }}
          checked={item.schemeId === schemeId}
          className='text-s2 h-[32px] mx-[5px] flex items-center'>
          {item.schemeName}
        </Tag.CheckableTag>
      ))}
      {isAddStatus ? (
        <div>
          <Input
            className='w-[150px] mx-[5px]'
            placeholder={t('按回车键确认')}
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
            }}
            onPressEnter={addNewScheme}
          />
          <Button
            danger
            type='link'
            onClick={() => {
              setIsAddStatus(false);
              setInputValue('');
            }}>
            {t('取消')}
          </Button>
        </div>
      ) : (
        <Button
          className='mx-[5px]'
          onClick={() => {
            setIsAddStatus(true);
          }}>
          {t('添加新方案')}
        </Button>
      )}
      <Button className='mr-[5px]' onClick={() => setDefaultScheme(schemeId)}>
        {t('设为默认方案')}
      </Button>
      <Popconfirm
        title='确定删除这个方案吗?'
        okText='确定'
        cancelText='取消'
        onConfirm={() => deleteScheme(schemeId)}>
        <Button
          disabled={disableDefaultAndSystem.includes(schemeId)}
          danger
          type='primary'>
          {t('删除')}
        </Button>
      </Popconfirm>
    </>
  );
});

export default HeaderTag;
