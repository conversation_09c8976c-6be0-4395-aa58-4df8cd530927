// HeaderFilterContext.ts
import { createContext } from 'react';
import {
  type GetAllFilterConditionsByMenuRes,
  type FilterSchemeInfo,
} from '@/api/Filter/model';

export const HeaderFilterContext = createContext({
  filterData: [] as GetAllFilterConditionsByMenuRes[],
  filterTypeSelect: new Map<string, { label: string; value: string }[]>(),
  schemeData: [] as FilterSchemeInfo[],
  menuTag: '',
});
