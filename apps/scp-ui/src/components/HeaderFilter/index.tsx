import React, { useState, useEffect, useContext } from 'react';
import { useUpdateEffect } from 'ahooks';
import { useSelector } from 'react-redux';
import { Dayjs } from 'dayjs';
import ajax from '@/api';
import _ from 'lodash';
import { useVoerkaI18n } from '@voerkai18n/react';
import { Button, message } from 'antd';
import { Select, Form } from 'antd';
import HeaderTag from './HeaderTag';
import QuickFilter from './QuickFilter';
import {
  FilterSchemeInfo,
  type GetAllFilterConditionsByMenuRes,
  type SaveSchemeReq,
} from '@/api/Filter/model';
import { formatDate } from '@/utils';
import { HeaderFilterContext } from './HeaderFilterContext';
import Arrow from '@/assets/Arrow.svg?react';

interface HeaderFilterProps {
  pageName?: string;
  extraFormItem?: React.ReactNode;
  onSearch: () => void;
  form: any;
  watchTagName?: string;
  setPFilterData?: (data: GetAllFilterConditionsByMenuRes[]) => void;
  onChangeScheme?: (schemeId: number) => void;
  onSaveTableColumns?: () => Promise<any>;
  onChangePaPvId?: (paPvId: number) => void;
}

const HeaderFilter = ({
  pageName = '1',
  extraFormItem,
  onSearch,
  form,
  watchTagName,
  onChangePaPvId,
  onChangeScheme,
  setPFilterData,
  onSaveTableColumns,
}: HeaderFilterProps): React.JSX.Element => {
  const againLoginVisible = useSelector(
    (state: any) => state.layout.againLoginVisible,
  );
  const { t } = useVoerkaI18n();

  const filter = Form.useWatch('filter', form);
  const watchTag = Form.useWatch(watchTagName || 'schemeId', form);
  const [messageApi, contextHolder] = message.useMessage();
  const [showFilter, setShowFilter] = useState<boolean>(true);

  const [filterData, setFilterData] = useState<
    GetAllFilterConditionsByMenuRes[]
  >([]);
  const [schemeData, setSchemeData] = useState<FilterSchemeInfo[]>([]);
  const [initSchemeId, setInitSchemeId] = useState<number>(-1);
  const [filterTypeSelect, setFilterTypeSelect] = useState<
    Map<string, { label: string; value: string }[]>
  >(new Map());

  const paPvId = Form.useWatch('paPvId', form);

  const tagId = Form.useWatch('schemeId', form);

  const pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const user = useSelector((state: any) => state.login.user);
  const getAllFilter = async () => {
    try {
      const res = await ajax.getAllFilterConditionsByMenu({
        menuTag: pageName,
      });
      if (res.code === 0) {
        const list = _.get(res, 'data.list', []) || [];
        setFilterData(list);
        if (setPFilterData) {
          setPFilterData(list);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getScheme = async () => {
    try {
      const res = await ajax.getFilterSchemeInfoByMenuAndUser({
        menuTag: pageName,
      });
      if (res.code === 0) {
        const list = _.get(res, 'data.list', []) || [];
        list.map((item: FilterSchemeInfo) => {
          if (item.isDefault) {
            if (initSchemeId === -1 || watchTagName) {
              form.setFieldsValue({ schemeId: item.schemeId });
              setInitSchemeId(item.schemeId);
            }
          }
        });
        setSchemeData(list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getFilterOperatorList = async () => {
    try {
      const filterTypeSelect = new Map();
      const res = await ajax.getFilterOperatorList();
      if (res.code !== 0) {
        messageApi.error(res.msg);
        return;
      }
      const list = _.get(res, 'data.list');
      for (const key in list) {
        list[key].forEach(
          (item: { opt: string; label: string; value: string }) => {
            item.value = item.opt;
          },
        );
        filterTypeSelect.set(key, list[key]);
        setFilterTypeSelect(filterTypeSelect);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const onFinish = async () => {
    onSearch();
  };
  /**
   * @description 保存方案
   * */
  const saveScheme = async () => {
    const schemeId = form.getFieldValue('schemeId');
    const schemeDataItem = schemeData.find(
      (item) => item.schemeId === schemeId,
    );

    const filter = form.getFieldValue('filter');
    //如果没有方案名称，则不保存，并且提示
    let flag = false;

    const saveProps: SaveSchemeReq = {
      isDefault: schemeDataItem?.isDefault || false,
      schemeName: schemeDataItem?.schemeName || '',
      menuTag: pageName,
      schemeId,
      conditionList: filter.map((item: any) => {
        const _data = filterData.find(
          (filterItem) => filterItem.conditionContent.field === item?.field,
        );
        if (!_data) {
          flag = true;
          return;
        }

        return {
          conditionId: _data?.id,
          preferOperator: item.opt || '',
          preferValue: item.value?.toString() || '',
        };
      }),
    };
    if (flag) {
      messageApi.error('过滤条件中含有空项');
      return;
    }
    try {
      const res = await ajax.saveScheme(saveProps);
      const tabelRes = await onSaveTableColumns?.();
      if (res.code === 0 && tabelRes?.code === 0) {
        messageApi.success('保存成功');
        getScheme();
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAllFilter();
    getScheme();
    getFilterOperatorList();
  }, [watchTag, againLoginVisible]);

  useUpdateEffect(() => {
    if (tagId !== -1) {
      onSearch();
      onChangeScheme?.(tagId);
    }
  }, [tagId]);

  useEffect(() => {
    if (paPvId && onChangePaPvId) {
      onChangePaPvId(paPvId);
    }
  }, [paPvId]);

  return (
    <HeaderFilterContext.Provider
      value={{
        filterData,
        schemeData,
        menuTag: pageName,
        filterTypeSelect,
      }}>
      {contextHolder}
      <Form form={form} onFinish={onFinish}>
        <div className='w-full px-[20px]  min-w-[600px] mb-[10px]'>
          {/*  我的方案部分  */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center'>
              <label className='text-s1'>{t('我的方案：')}</label>
              <Form.Item noStyle name='schemeId' initialValue={initSchemeId}>
                <HeaderTag
                  options={schemeData}
                  callback={getScheme}
                  defaultSchemeId={initSchemeId}
                  menuTag={pageName}
                />
              </Form.Item>
              <label className='text-s1 inline-block ml-4'>
                {t('计划版本：')}
              </label>
              <Form.Item
                noStyle
                name='paPvId'
                initialValue={user?.preferPaPvId || pVersionList[0]?.value}>
                <Select options={pVersionList} className='w-[128px]' />
              </Form.Item>
              {extraFormItem}
            </div>

            <div className='flex items-center'>
              <Button
                onClick={() => form.submit()}
                type='primary'
                className='rounded-full'>
                {t('查询')}
              </Button>
              <Button
                onClick={() => form.resetFields()}
                className='ml-[24px] rounded-full'>
                {t('重置')}
              </Button>
            </div>
          </div>

          {/* 计划版本部分  */}

          {/* 快速过滤部分  */}
          <div
            className='flex items-center'
            style={{
              position: 'relative',
              paddingRight: '24px',
            }}>
            <div
              className={`mt-[20px] flex relative ${showFilter ? 'block' : 'hidden'}`}>
              <label className='text-s1 min-w-[104px] inline-block'>
                {t('快速过滤：')}
              </label>
              <Form.Item
                noStyle
                shouldUpdate={(prev, next) => prev.schemeId != next.schemeId}>
                {({ getFieldValue }) => {
                  const schemeId = getFieldValue('schemeId');
                  return (
                    <QuickFilter schemeId={schemeId} saveScheme={saveScheme} />
                  );
                }}
              </Form.Item>
              {/* <Button className=' rounded-full' onClick={() => {}}>
              {t('保存')}
            </Button> */}
            </div>
            <div className='mt-[20px] relative'>
              {/* 快速过滤收起显示信息部分 */}
              <div
                className={`inline-block mb-4 ${showFilter ? 'hidden' : 'block'}`}>
                <label className='text-s1 min-w-[103px] inline-block'>
                  {t('快速过滤：')}
                </label>
                {filter?.map((item: any, index: number) => (
                  <FilterMessage key={index + item?.field} filterItem={item} />
                ))}
              </div>
            </div>
            <Arrow
              style={{
                width: '24px',
                height: '24px',
                position: 'absolute',
                right: '0',
                top: '50%',
                transform: 'translateY(-50%)',
              }}
              className={` rounded-full transition-transform duration-300 ${!showFilter ? 'rotate-180' : ''}`}
              onClick={() => {
                setShowFilter(!showFilter);
              }}
            />
          </div>
        </div>
      </Form>
    </HeaderFilterContext.Provider>
  );
};

type FilterMessageProps = {
  filterItem: {
    field: string;
    opt: string;
    value: string | Dayjs;
    type: string;
  };
};

const FilterMessage: React.FC<FilterMessageProps> = React.memo(
  ({ filterItem }) => {
    const { filterData, filterTypeSelect } = useContext(HeaderFilterContext);
    if (!filterItem) return null;
    const filterName =
      filterData.find((item) => {
        return item.conditionContent.field === filterItem.field;
      })?.conditionContent.label || '';
    const filterOption =
      filterTypeSelect
        .get(
          filterData.find((item) => {
            return item.conditionContent.field === filterItem.field;
          })?.conditionContent.type || '',
        )
        ?.find((item) => item.value === filterItem.opt)?.label || '';
    let value: any = filterItem.value;
    if (filterItem.type === 'date') {
      value = formatDate(
        filterItem.value as Dayjs,
        filterItem?.opt === 'year'
          ? 'year'
          : filterItem?.opt === 'month'
            ? 'month'
            : 'date',
      );
    }
    return (
      <div className='inline-block mx-[16px] text-s2'>
        <span>{filterName}/</span>
        <span>{filterOption}/</span>
        <span>{value}</span>
      </div>
    );
  },
);

export default HeaderFilter;
