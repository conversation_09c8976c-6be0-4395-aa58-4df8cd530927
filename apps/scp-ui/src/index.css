@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* font-size: calc(12 * (100vh / 1080)); */
  --nutui-calendar-active-background-color: #0070f0;
  --nutui-calendar-choose-background-color: #0070f033;
  --nutui-calendar-choose-color: #0070f0;
  --nutui-calendar-base-font-size: 12px;
}

@media (min-width: 1000px) {
  :root {
    font-size: calc(16 * (100vh / 1080));
  }
}

* {
  padding: 0;
  margin: 0;
  font-family: 'PingFang SC', sans-serif;
}

.box {
  border-radius: 8px;
  background: #ffffff;
  box-shadow:
    20px 20px 60px #bebebe,
    -20px -20px 60px #ffffff;
}

.ant-menu-item-selected svg path {
  fill: #2f6bff;
}

/* .ant-empty-normal {
  margin-top: 15rem;
} */
.ant-table-expanded-row-fixed {
  min-height: 0 !important;
}

.JobCard .ant-checkbox {
  display: none;
}

.JobCard :where(.css-dev-only-do-not-override-6fuvag).ant-checkbox + span {
  padding: 0;
  margin: 0;
}

.languageCheck {
  transform: translateX(40px);
}

.hover-brand {
  transition: all 0.5s;
}

.hover-brand:hover {
  color: #1890ff;
}

.scroll-setting {
  scrollbar-color: #e2e3e7 #f6f7fb;
}

input:focus {
  outline: none;
  /* 去掉默认的边框 */
  box-shadow: none;
  /* 去掉可能的阴影效果 */
  background-color: transparent;
  /* 保持背景色透明 */
  border-color: transparent;
  /* 保持边框颜色透明 */
}

/*antd*/
:where(.css-dev-only-do-not-override-v7o9gi).ant-tabs
  .ant-tabs-tab
  + .ant-tabs-tab {
  margin: 0;
}

:where(.css-dev-only-do-not-override-v7o9gi).ant-tabs
  .ant-tabs-tab
  + .ant-tabs-tab {
  display: flex;
  height: 3.21429rem;
  padding: 0rem 2.14286rem;
  margin-right: 0.71rem;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 0.42857rem 0.85714rem;
  flex-wrap: wrap;
  border-radius: 0.57143rem;
  background: var(--white, #fff);
  color: var(--Grey-5, #b9bcc6);
  font-family: Roboto;
  font-size: 1.28571rem;
  font-style: normal;
  font-weight: 500;
  line-height: 1.71429rem;
  letter-spacing: 0.05143rem;
}

:where(.css-dev-only-do-not-override-v7o9gi).ant-tabs .ant-tabs-tab {
  display: flex;
  height: 3.21429rem;
  padding: 0rem 2.14286rem;
  margin-right: 0.71rem;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 0.42857rem 0.85714rem;
  flex-wrap: wrap;
  border-radius: 0.57143rem;
  background: var(--white, #fff);
  color: var(--Grey-5, #b9bcc6);
  font-family: Roboto;
  font-size: 1.28571rem;
  font-style: normal;
  font-weight: 500;
  line-height: 1.71429rem;
  letter-spacing: 0.05143rem;
}

.ant-drawer-body {
  padding: 24px !important;
}

.timeDrawer .ant-drawer-body {
  padding: 8px !important;
}

/*nut-ui*/
.timeDrawer .nut-calendarcard-header-left {
  margin: 8px 16px !important;
}

.timeDrawer .nut-calendarcard-header-right {
  margin: 8px 16px !important;
}

.nut-calendarcard-day {
  height: 24px !important;
}

/* 自定义动画 */
@keyframes rotate-animation {
  0% {
    transform: rotate(-20deg);
  }

  50% {
    transform: rotate(20deg);
  }

  100% {
    transform: rotate(-20deg);
  }
}

.rotating-element {
  animation: rotate-animation 1s infinite !important;
}
/**
* @author: jinxu
* @description: 通用样式,禁用a标签的默认样式
*/

.disabled-link {
  @apply text-gray-400 cursor-not-allowed pointer-events-none;
}
