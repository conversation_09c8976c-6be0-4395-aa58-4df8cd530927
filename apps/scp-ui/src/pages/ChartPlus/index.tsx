import React, { useEffect, useState } from 'react';
import Header from './components/Header.tsx';
import Charts from './components/Charts.tsx';
import { Navbar, NavbarBrand } from '@nextui-org/react';
import { useNavigate } from 'react-router-dom';

const ChartPlus: React.FC = () => {
  const navigate = useNavigate();
  const [chartsInfo, setChartsInfo] = useState<any>({});
  const [viewType, setViewType] = useState<any>('');
  const [orientationType, setOrientationType] = useState('');
  const orientationChange = () => {
    if (window.orientation == 180 || window.orientation == 0) {
      setOrientationType('landscape'); //竖屏
    }
    if (window.orientation == 90 || window.orientation == -90) {
      setOrientationType('portrait'); //横屏
    }
  };
  const goToHome = () => {
    navigate('/mobile');
  };
  useEffect(() => {
    window.addEventListener('orientationchange', orientationChange);
    const _chartsInfo =
      new URLSearchParams(window.location.search).get('chartsInfo') || '{}';
    setChartsInfo({ ...JSON.parse(_chartsInfo) });
    const _viewType =
      new URLSearchParams(window.location.search).get('viewType') || '';
    setViewType(_viewType);
  }, []);
  useEffect(() => {
    orientationChange();
  }, [orientationType]);
  return (
    <>
      {orientationType === 'landscape' && (
        <div
          style={{
            height: '100vh',
            width: '100vw',
            backgroundColor: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: '0',
            zIndex: '99',
          }}>
          <div className='w-full absolute top-0'>
            <Navbar>
              <NavbarBrand>
                <p className='font-bold text-inherit' onClick={goToHome}>
                  {'< 返回'}
                </p>
              </NavbarBrand>
            </Navbar>
          </div>

          <div
            className='rotating-element'
            style={{ margin: '-60px 0 20px 0' }}>
            <svg
              d='1709274695795'
              className='icon'
              viewBox='0 0 1024 1024'
              version='1.1'
              xmlns='http://www.w3.org/2000/svg'
              id='2262'
              width='180'
              height='180'>
              <path
                d='M659.21024 132.73088H379.54048c-36.13184 0-65.68448 29.54752-65.68448 65.65888v656.6144c0 36.13696 29.54752 66.98496 65.68448 66.98496h279.66976c36.11136 0 65.65376-30.848 65.65376-66.98496V198.38976c0-36.11136-29.5424-65.65888-65.65376-65.65888M484.608 183.936h78.77632a13.12768 13.12768 0 0 1 0 26.24512H484.608a13.12256 13.12256 0 1 1 0-26.24512m39.38304 710.4768a39.38304 39.38304 0 0 1-39.40864-39.40864 39.38304 39.38304 0 0 1 39.40864-39.41376 39.37792 39.37792 0 0 1 39.41888 39.41376 39.3984 39.3984 0 0 1-39.41888 39.40864m169.37984-114.28864H345.40032V258.7904h347.94496v521.33376h0.0256zM983.88992 406.1952c-0.37376-1.23392-0.70144-2.48832-1.08032-3.72224a392.54528 392.54528 0 0 0-7.63904-20.79232c-0.98304-2.44736-1.90464-4.93056-2.95936-7.39328a307.79392 307.79392 0 0 0-8.11008-17.21856c-1.5616-3.11296-3.09248-6.30784-4.75136-9.41568-0.82944-1.52576-1.47456-3.10784-2.30912-4.58752-2.29376-4.06528-4.89472-7.84384-7.3216-11.76576-1.6896-2.7136-3.2768-5.43744-5.02784-8.1152a316.2624 316.2624 0 0 0-13.34272-18.49344c-0.83968-1.05984-1.54112-2.23744-2.4064-3.31264-0.14336-0.19456-0.33792-0.31232-0.49152-0.50688-21.65248-27.09504-47.13984-49.42336-75.42272-66.0992l-30.0032 61.66016c23.1936 13.16864 43.99104 31.29856 61.45536 53.55008a235.06944 235.06944 0 0 1 11.22304 15.55968c1.06496 1.664 2.07872 3.3536 3.10784 5.01248a231.90528 231.90528 0 0 1 7.98208 13.57824c1.09568 2.03776 2.06848 4.15232 3.10784 6.18496a245.4528 245.4528 0 0 1 6.67136 14.08c0.62464 1.52576 1.19808 3.09248 1.80736 4.62848a259.57376 259.57376 0 0 1 6.20544 16.80896c0.16896 0.5888 0.3328 1.20832 0.53248 1.77664a261.0688 261.0688 0 0 1 10.30656 106.21952c-0.03584 0.20992-0.09216 0.48128-0.11264 0.6656-5.67296 49.8432-25.87136 97.37216-60.40576 133.38112l-35.57888-44.61568-33.91488 165.96992 147.73248-23.26528-35.54816-44.57472c39.68512-41.35936 64.91648-94.27968 75.648-150.71744 0.24064-0.9216 0.57856-1.67936 0.73728-2.62656 1.28-6.92736 2.0736-13.9008 2.88768-20.84352 0.11264-0.84992 0.26624-1.66912 0.35328-2.49856a337.39776 337.39776 0 0 0-13.33248-138.51136zM50.81088 617.79968c0.37376 1.23904 0.70144 2.48832 1.08032 3.72736a392.54528 392.54528 0 0 0 7.63904 20.79232c0.98304 2.45248 1.90464 4.93056 2.95936 7.39328a307.79392 307.79392 0 0 0 8.11008 17.21856c1.5616 3.10784 3.09248 6.30784 4.75136 9.41568 0.82944 1.52576 1.47456 3.10784 2.30912 4.58752 2.29376 4.06528 4.89472 7.84384 7.3216 11.77088 1.6896 2.70848 3.2768 5.43744 5.02784 8.1152 4.24448 6.43584 8.73984 12.50304 13.34272 18.49344 0.83968 1.05984 1.54112 2.24256 2.4064 3.31264 0.14336 0.18944 0.33792 0.31232 0.49152 0.50688 21.65248 27.09504 47.13984 49.41824 75.42272 66.0992l30.0032-61.66016c-23.1936-13.16864-43.99104-31.29856-61.45536-53.55008a236.42624 236.42624 0 0 1-11.22304-15.55968c-1.06496-1.664-2.07872-3.3536-3.10784-5.01248a233.60512 233.60512 0 0 1-7.98208-13.57824c-1.09568-2.03776-2.06848-4.15232-3.10784-6.18496a243.98336 243.98336 0 0 1-6.67136-14.08512c-0.62464-1.52064-1.19808-3.09248-1.80736-4.62848a258.46272 258.46272 0 0 1-6.20544-16.80896c-0.16896-0.5888-0.3328-1.20832-0.53248-1.77152a261.07392 261.07392 0 0 1-10.30656-106.21952c0.03584-0.20992 0.09216-0.48128 0.11264-0.6656 5.67296-49.8432 25.87136-97.37216 60.40576-133.38112l35.57888 44.61568 33.91488-165.96992-147.73248 23.26528 35.54816 44.57472C77.42976 343.97184 52.1984 396.89216 41.46688 453.3248c-0.24064 0.9216-0.57856 1.67936-0.73728 2.62656-1.28 6.92736-2.0736 13.9008-2.88768 20.84352-0.11264 0.84992-0.26624 1.66912-0.35328 2.49856a337.18784 337.18784 0 0 0 13.32224 138.50624z'
                id='2263'
                fill='#707070'></path>
            </svg>
          </div>
          <div className='text-lg' style={{ color: '#707070' }}>
            为了更好的体验，请将手机/平板横置
          </div>
        </div>
      )}
      <div
        className='bg-zinc-200 relative overflow-hidden'
        style={{ height: '100vh', width: '100vw' }}>
        <div
          style={{ position: 'fixed', top: '0', zIndex: '9' }}
          className='w-full'>
          <Header viewType={viewType} />
        </div>
        <div className='bg-zinc-200 pt-1 mt-16' style={{ height: '300px' }}>
          <Charts chatrsInfo={chartsInfo} />
        </div>
      </div>
    </>
  );
};
export default ChartPlus;
