import ReactEcharts from 'echarts-for-react';

type PropsType = {
  chatrsInfo: any;
};

const Charts: React.FC<PropsType> = (props) => {
  const { chatrsInfo } = props;
  // const option = {
  //   xAxis: {
  //     data: [
  //       '24年第3周',
  //       '24年第4周',
  //       '24年第5周',
  //       '24年第6周',
  //       '24年第7周',
  //       '24年第8周',
  //     ],
  //   },
  //   yAxis: {},
  //   series: [
  //     {
  //       name: '杭州',
  //       type: 'bar',
  //       data: [26, 38, 18, 22, 23, 20],
  //     },
  //     {
  //       name: '嘉兴',
  //       type: 'bar',
  //       data: [14, 21, 8, 39, 34, 25],
  //     },
  //   ],
  //   tooltip: {
  //     triggerOn: 'click',
  //     showContent: true,
  //     formatter: function (params: any) {
  //       return (
  //         '名称：' +
  //         params.seriesName +
  //         '<br/>时间：' +
  //         params.name +
  //         '<br/>数量：' +
  //         params.value
  //       );
  //     },
  //     trigger: 'item',
  //   },
  //   legend: {
  //     type: 'plain',
  //     show: true,
  //   },
  // };
  return (
    <div className='bg-white pt-4'>
      <ReactEcharts option={chatrsInfo} />
    </div>
  );
};
export default Charts;
