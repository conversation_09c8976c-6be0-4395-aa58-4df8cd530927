import {
  Button,
  Checkbox,
  cn,
  Link,
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Select,
  SelectItem,
} from '@nextui-org/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ConfigProvider, Drawer, Form, Segmented } from 'antd';
import React, { useEffect, useState } from 'react';
import MySelect from '@/components/MySelect';
import {
  CalendarCard,
  ConfigProvider as NutUIConfigProvider,
} from '@nutui/nutui-react';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

type PropsType = {
  viewType: any;
};

const Header: React.FC<PropsType> = (props) => {
  const { viewType } = props;
  const navigate = useNavigate();
  const { state } = useLocation();
  const [initialForm] = Form.useForm();
  const [extendForm] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [data, setData] = useState<any>();
  const [childrenDrawer, setChildrenDrawer] = useState(false);
  const [timeArr, setTimeArr] = useState<any>(null);

  const goToHome = () => {
    navigate('/mobile');
  };

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const showChildrenDrawer = () => {
    setChildrenDrawer(true);
  };

  const onChildrenDrawerClose = () => {
    setChildrenDrawer(false);
  };

  const onOk = () => {
    setChildrenDrawer(false);
  };

  const getTime = (times: string[]) => {
    console.log(times);
  };

  const onChange = (values: any) => {
    const time: any[] = [];
    if (values.length === 2) {
      values.map((item: any) => {
        time.push(dayjs(item).format('YY-MM-DD'));
      });
      getTime(time);
      setTimeArr([...time]);
    }
  };

  useEffect(() => {
    setData(state);
  }, []);
  useEffect(() => {
    let _viewType = viewType;
    if (viewType === 'salesIssueOrder') {
      _viewType = '销售出库';
    } else if (viewType === 'productValue') {
      _viewType = '产值';
    }
    setTitle(_viewType);
  }, [viewType]);
  return (
    <div className='bg-white'>
      <Navbar>
        <NavbarBrand>
          <p className='font-bold text-inherit' onClick={goToHome}>
            {'< 返回'}
          </p>
        </NavbarBrand>
        <NavbarContent className='hidden sm:flex gap-4' justify='center'>
          <p className='font-bold text-inherit'>{title}</p>
        </NavbarContent>

        <NavbarContent justify='end'>
          <NavbarItem>
            <Button as={Link} color='primary' href='#' variant='flat'>
              <p className='font-bold text-inherit' onClick={showDrawer}>
                图表配置
              </p>
            </Button>
          </NavbarItem>
        </NavbarContent>
      </Navbar>
      <Drawer title='图表配置' onClose={onClose} open={open} width={500}>
        <Form
          form={initialForm}
          layout='inline'
          className='w-full flex justify-between'>
          <Form.Item noStyle name={'viewType'}>
            <Select
              label='视图类型'
              className='w-[49%] mb-2'
              size={'sm'}
              defaultSelectedKeys={['warehousingList']}>
              {data &&
                data.viewType.map((item: any) => (
                  <SelectItem key={item.key} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
            </Select>
          </Form.Item>
          <Form.Item noStyle name={'sort'}>
            <Select
              label='参数排序'
              className='w-[49%] mb-2'
              size={'sm'}
              defaultSelectedKeys={['material/factory']}>
              {data &&
                data.sort.map((item: any) => (
                  <SelectItem key={item.key} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
            </Select>
          </Form.Item>
          <Form.Item noStyle>
            <ConfigProvider
              theme={{
                components: {
                  Segmented: {
                    // @ts-ignore
                    trackBg: '#f4f4f5',
                    itemColor: '#000000',
                    itemSelectedColor: '#0070f0',
                  },
                },
              }}>
              {/* @ts-ignore */}
              <Segmented<string>
                options={['周', '月']}
                className='w-[49%]'
                block
                size='large'
              />
            </ConfigProvider>
          </Form.Item>
          <Form.Item noStyle>
            <div
              style={{ backgroundColor: '#f4f4f5', color: '#71717a' }}
              className='h-10 leading-10 px-3 flex justify-between w-[49%] rounded-lg'
              onClick={showChildrenDrawer}>
              {timeArr
                ? timeArr.map((item: any, index: any) => {
                    return <div key={index}>{item}</div>;
                  })
                : '日期选择'}
            </div>
          </Form.Item>
        </Form>
        <Form
          form={extendForm}
          layout='inline'
          className='w-full flex justify-between mt-2'>
          {data &&
            data.extends.options.map((item: any, index: number) => {
              return (
                <div
                  className='w-full flex justify-between mb-2'
                  key={item.label + index}>
                  <div
                    className='w-[49%]  pl-3 mr-0 rounded-lg'
                    style={{ backgroundColor: '#f4f4f5' }}>
                    <Form.Item noStyle>
                      <Checkbox
                        size='sm'
                        classNames={{ base: cn('w-full max-w-lg') }}>
                        <div
                          className='pl-1 h-12 leading-12 text-sm'
                          style={{
                            backgroundColor: '#f4f4f5',
                            color: '#71717a',
                          }}>
                          {`${item.label}`}展开
                        </div>
                      </Checkbox>
                    </Form.Item>
                  </div>
                  <Form.Item noStyle>
                    <MySelect
                      data={item.content}
                      title={item.label}
                      isDisable={true}
                      // @ts-ignore
                      defaultChooseList={item.defaultChooseList}
                      width={'49%'}
                    />
                  </Form.Item>
                </div>
              );
            })}
        </Form>
        <Drawer
          className='timeDrawer'
          title='日期选择'
          width={320}
          onClose={onChildrenDrawerClose}
          open={childrenDrawer}>
          <NutUIConfigProvider>
            <CalendarCard
              type='range'
              onChange={(values: any) => onChange(values)}
            />
            <div className='py-2 flex justify-center'>
              <Button
                onClick={onOk}
                className='w-60'
                style={{
                  backgroundColor: '#0070f033',
                  color: '#0070f0',
                  border: 'none',
                }}>
                确定
              </Button>
            </div>
          </NutUIConfigProvider>
        </Drawer>
      </Drawer>
    </div>
  );
};
export default Header;
