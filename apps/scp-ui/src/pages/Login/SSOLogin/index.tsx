import { useState, useEffect } from 'react';
import { Flex, Spin, App, Modal, Select } from 'antd';
import { setToken, setUser } from '@/store/features/loginSlice';
import { useNavigate, useLocation } from 'react-router-dom';
import { i18nScope } from '@/languages';
import { isMobile } from '@/utils';
import store from '@/store';
import ajax from '@/api';

const SSOLogin = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();

  const [loading, setLoading] = useState(true);
  const searchParams = new URLSearchParams(window.location.search);
  const authCode = searchParams.get('authCode');

  const location = useLocation();
  const type = location.pathname.split('/').pop();

  const getProvider = () => {
    switch (type) {
      case 'DingTalk':
        return 1;
      case 'EnterpriseWeChat':
        return 2;
      case 'Feishu':
        return 3;
      default:
        return 0;
    }
  };

  const [serialNo, setSerialNo] = useState(null);
  const [paList, setPaList] = useState([]);
  const [preferPa, setPreferPa] = useState(null);
  const [selectedPa, setSelectedPa] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [okLoading, setOkLoading] = useState(false);

  const [modalOkLoading, setModalOkLoading] = useState(false);

  const [loginConfirmShow, setLoginConfirmShow] = useState(false);

  const getAreaAndVersion = async () => {
    const res = await ajax.getAreaAndVersion();
    if (res.code !== 0) return;
    let paPvId: any = '';
    res.data.list.map((item: any) => {
      if (item.pversion === 'Base') {
        paPvId = item.paPvId;
      }
    });
    localStorage.setItem('defPaPvId', JSON.stringify(paPvId));
  };

  const ssoLoginPre = async () => {
    try {
      ajax
        .ssoLoginPre({
          authCode: authCode,
          provider: getProvider(),
        })
        .then(async (res: any) => {
          const { serialNo, list } = res.data;
          setSerialNo(serialNo);
          setPaList(list);
          const rawPreferPa = list.filter((item: any) => item.isPrefer);
          rawPreferPa.length === 1
            ? (setPreferPa(rawPreferPa[0].pareaId),
              setSelectedPa(rawPreferPa[0].pareaId))
            : null;
          setModalOpen(true);
          message.success('获取授权信息成功, 请选择计划域');
        })
        .catch((error) => {
          console.error(error);
          message.error('预登录失败，请稍后再试');
          // setTimeout(() => {
          //   navigate('/login');
          // }, 2000);
        });
    } catch (error) {
      console.error(error);
      message.error('预登录失败，请稍后再试');
      // setTimeout(() => {
      //   navigate('/login');
      // }, 2000);
    }
  };

  const handleCancelLoginConfirm = () => {
    setLoginConfirmShow(false);
    message.warning('已取消登录, 正在返回登录页...');
    setTimeout(() => {
      navigate('/login');
    }, 2000);
  };

  const ssoLogin = async () => {
    setOkLoading(true);
    try {
      ajax
        .ssoLogin({
          pareaId: selectedPa,
          serialNo: serialNo,
        })
        .then(async (res: any) => {
          if (res.code === 20620) {
            setOkLoading(false);
            setModalOpen(false);
            setLoginConfirmShow(true);
          } else {
            const { user, token } = res.data;
            store.dispatch(setToken(token));
            store.dispatch(setUser(user));
            localStorage.setItem('language', 'zh');
            await i18nScope.change('zh');

            setModalOpen(false);
            setLoading(false);
            setOkLoading(false);

            message.success('授权登录成功, 正在跳转...');

            setTimeout(() => {
              if (isMobile()) {
                navigate('/mobile');
                getAreaAndVersion();
              } else {
                navigate('/home');
              }
            }, 2000);
          }
        })
        .catch((error) => {
          console.error(error);
          setOkLoading(false);
          setModalOpen(false);
          message.error('登录失败，请稍后再试');
          setTimeout(() => {
            navigate('/login');
          }, 2000);
        });
    } catch (error) {
      console.error(error);
      setOkLoading(false);
      setModalOpen(false);
      message.error('登录失败，请稍后再试');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    }
  };

  useEffect(() => {
    if (!authCode) {
      message.error('非法访问');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
      return;
    }
    ssoLoginPre();
  }, []);

  return (
    <Flex className='flex justify-center items-center h-screen'>
      <Spin tip={'正在使用第三方授权登录...'} spinning={loading} size='large'>
        <div
          style={{
            padding: 100,
            background: '#fff',
          }}></div>
      </Spin>
      <Modal
        open={modalOpen}
        title={'请选择计划域'}
        onOk={() => {
          selectedPa ? ssoLogin() : message.warning('请选择计划域');
        }}
        okButtonProps={{ loading: okLoading }}
        okText={'确认登录'}
        onCancel={() => {
          setModalOpen(false);
          message.info('已取消授权登录, 正在返回登录页...');
          setTimeout(() => {
            navigate('/login');
          }, 2000);
        }}
        cancelText={'取消登录'}
        centered={true}>
        <Flex
          style={{
            padding: '10px 20px',
          }}>
          <Select
            style={{ width: 200 }}
            defaultValue={preferPa}
            value={selectedPa}
            onChange={(value) => setSelectedPa(value)}
            options={
              paList
                ? paList.map((item: any) => ({
                    label: item.parea,
                    value: item.pareaId,
                  }))
                : []
            }></Select>
        </Flex>
      </Modal>
      <Modal
        title={'登录覆盖'}
        open={loginConfirmShow}
        onCancel={handleCancelLoginConfirm}
        okText={'确定'}
        cancelText={'取消'}
        onOk={() => {
          setModalOkLoading(true);
          ajax
            .ssoLoginConfirm({
              pareaId: selectedPa,
              serialNo: serialNo,
            })
            .then(() => {
              ajax
                .ssoLogin({
                  pareaId: selectedPa,
                  serialNo: serialNo,
                })
                .then(async (res: any) => {
                  const { user, token } = res.data;
                  store.dispatch(setToken(token));
                  store.dispatch(setUser(user));
                  localStorage.setItem('language', 'zh');
                  await i18nScope.change('zh');

                  message.success('授权登录成功, 正在跳转...');

                  setTimeout(() => {
                    if (isMobile()) {
                      navigate('/mobile');
                      getAreaAndVersion();
                    } else {
                      navigate('/home');
                    }
                  }, 2000);
                })
                .catch((err) => {
                  console.error(err);
                })
                .finally(() => {
                  setModalOkLoading(false);
                });
            });
        }}
        okButtonProps={{
          loading: modalOkLoading,
        }}>
        {'当前账号已在其他地方登录, 是否覆盖登录?'}
      </Modal>
    </Flex>
  );
};

export default SSOLogin;
