import {
  Button,
  Form,
  Input,
  App,
  Select,
  Space,
  Modal,
  Collapse,
  Flex,
} from 'antd';
import React, { useEffect, useState } from 'react';
import useLogin from './hooks/uselogin';
import ajax from '@/api';
import { useDispatch } from 'react-redux';
import { setToken, setUser } from '@/store/features/loginSlice';
import { useNavigate } from 'react-router-dom';
import { i18nScope } from '@/languages';
import _ from 'lodash';
import CryptoJS from 'crypto-js';
import { persistor } from '@/store/index.ts';
import { setShowLoginConfirm } from '@/store/features/loginSlice';
import { setAgainLoginVisible } from '@/store/features/layoutSlice';
import { setShowbeLoginCovered } from '@/store/features/layoutSlice';
import { useSelector } from 'react-redux';
import { isMobile } from '@/utils';
import { useAliveController } from 'react-activation';
import store from '@/store';

interface CaptchaDataType {
  captchaId: string;
  picPath: string;
  msg: string;
}

type pareaType = {
  pareaId: number;
};

const random = Math.random().toString();
function Login() {
  const { clear } = useAliveController();
  const { message } = App.useApp();

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [parea, setParea] = useState<pareaType[]>([]);
  const [aesKey, setAesKey] = useState<string>('');
  const [serialNo, setSerialNo] = useState('');
  const { login, getCaptchaData, loginPre } = useLogin<CaptchaDataType>();
  const [captchaData, setCaptchaData] = useState<CaptchaDataType>({
    captchaId: '',
    picPath: '',
    msg: '',
  });
  const [disableParea, setDisableParea] = useState(true);
  const [userId, setUserId] = useState(-1);
  const [modalOkLoading, setModalOkLoading] = useState(false);

  useEffect(() => {
    store.dispatch(setShowLoginConfirm(false));
    store.dispatch(setShowbeLoginCovered(false));
    dispatch(setAgainLoginVisible(false));
  }, []);
  /**
   *@description 设置偏好计划域
   *@param pareaId
   *<AUTHOR>
   */
  const setPerferArea = () => {
    const currentParea = form.getFieldValue('pareaId');
    const obj = {
      userId: userId,
      preferType: 1,
      preferPareaId: currentParea,
    };
    if (obj.userId === -1) {
      message.error('请先登录');
      return;
    } else if (obj.preferPareaId === undefined) {
      message.error('请选择计划域');
      return;
    } else {
      ajax.setUserPerferParea(obj);
    }
  };

  /**
   * @description 获取aes加密的key
   * */
  const getAesKey = async () => {
    try {
      const res = await ajax.getAesKey(random);
      const data = _.get(res, 'data', '') as string;
      setAesKey(data);
    } catch (err) {
      console.error(err);
    }
  };

  /**
   * @description 密码加密函数
   * */

  const enCryptAES = ({ psw, sKey }: { psw: string; sKey: string }) => {
    const _sKey = CryptoJS.enc.Utf8.parse(sKey);
    const encrypt = CryptoJS.AES.encrypt(psw, _sKey, {
      iv: _sKey,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypt.toString();
  };

  const pareaChange = (value: string) => {
    localStorage.setItem('pareaId', value);
  };
  const getAreaAndVersion = async () => {
    const res = await ajax.getAreaAndVersion();
    if (res.code !== 0) return;
    let paPvId: any = '';
    res.data.list.map((item: any) => {
      if (item.pversion === 'Base') {
        paPvId = item.paPvId;
      }
    });
    localStorage.setItem('defPaPvId', JSON.stringify(paPvId));
  };

  const getLoginData = () => {
    const values = form.getFieldsValue();
    return {
      username: values.username,
      password: enCryptAES({ psw: values.password, sKey: aesKey }),
      pareaId: values.pareaId,
      coCode: values.coCode,
      serialNo: serialNo,
      randomString: random,
    };
  };

  const onFinish = (values: any) => {
    setLoading(true);
    const predata = {
      username: values.username,
      password: enCryptAES({ psw: values.password, sKey: aesKey }),
      captcha: values.captcha,
      coCode: values.coCode,
      captchaId: captchaData.captchaId,
      openCaptcha: true,
      randomString: random,
    };
    const loginData = getLoginData();

    if (disableParea) {
      loginPre(predata)
        .then((res: any) => {
          if (!res.code) {
            res.data.list.forEach((item: any) => {
              if (item.isPrefer) {
                form.setFieldValue('pareaId', item.pareaId);
                localStorage.setItem('pareaId', item.pareaId);
              }
            });
            setParea(res.data.list);
            setUserId(res.data.userId);
            setSerialNo(res.data.serialNo);
            setDisableParea(false);
          }
        })
        .catch((err) => {
          message.error(err);
          getCaptcha();
          getAesKey();
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      login(loginData)
        .then(async (res: any) => {
          const { user, token } = res.data;
          // localStorage.setItem('token', token);
          // localStorage.setItem('user', JSON.stringify(user));
          dispatch(setToken(token));
          dispatch(setUser(user));
          localStorage.setItem('language', 'zh');
          await i18nScope.change('zh');

          const pareaId = Number(localStorage.getItem('pareaId'));
          const pareaInfo = parea.find((item) => item.pareaId === pareaId);

          localStorage.setItem('pareaInfo', JSON.stringify(pareaInfo));

          if (isMobile()) {
            navigate('/mobile');
            getAreaAndVersion();
          } else {
            navigate('/home');
          }
        })
        .catch((err) => {
          message.error(err);
          getCaptcha();
          getAesKey();
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const getCaptcha = async () => {
    const res: CaptchaDataType = await getCaptchaData();
    setCaptchaData(res);
  };
  useEffect(() => {
    getCaptcha();
    getAesKey();
    persistor.purge();
    clear();
    // window.location.reload();
  }, []);

  const handleCancelLoginConfirm = () => {
    store.dispatch(setShowLoginConfirm(false));
    message.warning('已取消登录');
    setDisableParea(true);
    form.resetFields();
    getCaptcha();
  };

  const ssoAuthRedirect = (provider: number) => {
    ajax
      .ssoAuth({
        provider: provider,
      })
      .then((res) => {
        const { redirectUrl } = res.data;
        window.location.href = redirectUrl;
      })
      .catch((err) => {
        console.error(err);
      });
  };

  return (
    <div className='flex justify-center items-center h-screen'>
      <Form
        form={form}
        name='login'
        initialValues={{ remember: true }}
        onFinish={onFinish}
        className='w-[280px]'
        style={{
          position: 'relative',
        }}>
        <img
          src='/scmify_logo/PNG/colorful-logo-font.png'
          alt='logo'
          className=' w-40 mx-auto mb-10'
        />
        <Form.Item
          name='coCode'
          rules={[{ required: true, message: '请输入公司编码' }]}>
          <Input
            placeholder='公司编码'
            className='w-[280px]'
            disabled={!disableParea}
          />
        </Form.Item>
        <Form.Item
          name='username'
          rules={[{ required: true, message: '请输入用户名' }]}>
          <Input placeholder='用户名' id='username' className='w-[280px]' />
        </Form.Item>
        <Form.Item
          name='password'
          rules={[{ required: true, message: '请输入密码' }]}>
          <Input.Password placeholder='密码' className='w-[280px]' />
        </Form.Item>
        <Form.Item noStyle>
          <Space.Compact>
            <Form.Item name='pareaId'>
              <Select
                style={{ width: '130px' }}
                placeholder='计划域'
                disabled={disableParea}
                options={parea}
                fieldNames={{ label: 'parea', value: 'pareaId' }}
                onChange={pareaChange}
              />
            </Form.Item>
            <Button onClick={setPerferArea} className='w-[150px]'>
              设定为偏好计划域
            </Button>
          </Space.Compact>
        </Form.Item>
        <Form.Item name='captcha' dependencies={['username', 'password']}>
          <Space.Compact>
            <Input
              placeholder='验证码'
              style={{
                width: '180px',
                borderTopLeftRadius: '6px',
                borderBottomLeftRadius: '6px',
              }}
            />
            <img
              src={captchaData.picPath}
              alt='验证码'
              className='h-[32px] w-[100px] '
              style={{
                border: '1px solid #d9d9d9',
                borderTopRightRadius: '6px',
                borderBottomRightRadius: '6px',
              }}
              onClick={() => {
                getCaptcha();
                getAesKey();
              }}
            />
          </Space.Compact>
        </Form.Item>
        {!disableParea && (
          <Button
            // htmlType='reset'
            className='w-[135px] mb-4 mr-[10px]'
            onClick={() => {
              setDisableParea(true);
              form.resetFields();
            }}>
            重置
          </Button>
        )}
        <Button
          htmlType='submit'
          loading={loading}
          className={`mb-4 ${disableParea ? 'w-[280px]' : 'w-[135px]'}`}>
          {disableParea ? '获取计划域' : '登录'}
        </Button>
        <div
          style={{
            position: 'absolute',
            width: '100%',
            userSelect: 'none',
          }}>
          <Collapse>
            <Collapse.Panel header={'使用第三方授权登录'} key={0}>
              <Flex vertical={true} gap={10}>
                <Button onClick={() => ssoAuthRedirect(1)}>{'钉钉登录'}</Button>
                <Button onClick={() => ssoAuthRedirect(2)}>
                  {'企业微信登录'}
                </Button>
                <Button onClick={() => ssoAuthRedirect(3)}>{'飞书登录'}</Button>
              </Flex>
            </Collapse.Panel>
          </Collapse>
        </div>
      </Form>
      <Modal
        title={'登录覆盖'}
        open={useSelector((state: any) => state.login.showLoginConfirm)}
        onCancel={handleCancelLoginConfirm}
        okText={'确定'}
        cancelText={'取消'}
        onOk={() => {
          const loginData = getLoginData();
          setModalOkLoading(true);
          ajax
            .loginConfirm({
              ...loginData,
            })
            .then(() => {
              login(loginData)
                .then(async (res: any) => {
                  const { user, token } = res.data;
                  dispatch(setToken(token));
                  dispatch(setUser(user));
                  localStorage.setItem('language', 'zh');
                  await i18nScope.change('zh');

                  if (isMobile()) {
                    navigate('/mobile');
                    getAreaAndVersion();
                  } else {
                    navigate('/home');
                  }
                })
                .catch((err: any) => {
                  console.error(err);
                  getCaptcha();
                  getAesKey();
                })
                .finally(() => {
                  setModalOkLoading(false);
                });
            });
        }}
        okButtonProps={{
          loading: modalOkLoading,
        }}>
        {'当前账号已在其他地方登录, 是否覆盖登录?'}
      </Modal>
    </div>
  );
}

export default Login;
