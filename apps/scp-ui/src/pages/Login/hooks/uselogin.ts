import ajax from '@/api';

/**
 * @description Login hook
 * @returns {Array} [login, getCaptch]
 */
const useLogin = <T = any>() => {
  const login = (value: any) => {
    return ajax.login(value).then((res) => {
      const returnData = res;
      if (!returnData.code) {
        return Promise.resolve(returnData);
      } else {
        return Promise.reject(returnData.msg);
      }
    });
  };
  const loginPre = (value: any) => {
    return ajax.loginPre(value).then((res) => {
      const returnData = res;
      if (!returnData.code) {
        return Promise.resolve(returnData);
      } else {
        return Promise.reject(returnData.msg);
      }
    });
  };
  const getCaptchaData = async (): Promise<T> => {
    const res = await ajax.getCaptcha();
    if (!res.code) {
      return {
        msg: 'success',
        captchaId: res.data?.captchaId,
        picPath: res.data?.picPath,
      } as T;
    } else {
      return {
        captchaId: '',
        picPath: '',
        msg: res.msg,
      } as T;
    }
  };
  return { login, getCaptchaData, loginPre };
};

export default useLogin;
