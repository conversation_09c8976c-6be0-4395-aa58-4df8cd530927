import React, { useState, useEffect } from 'react';
import { Input, Table, Card, Button, Form, Select, Tag } from 'antd';
import { Task, SearchProps } from './type';
import api from '@/api/ProcessManagement';
import { useNavigate } from 'react-router-dom';
import { PageDataType } from '@/types';
import { removeUndefinedValues } from '@/utils';
import { t } from '@/languages';

// 修改 SearchBox：使用 Form.useForm 并为 Input 添加 name 属性，避免初始化时提交
const SearchBox: React.FC<SearchProps> = ({ onSearch }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} layout='inline' onFinish={(values) => onSearch(values)}>
      <Form.Item
        name='taskName'
        label={<div className='text-h4'>{t('模糊搜索')}</div>}>
        <Input placeholder={t('搜索任务') + '...'} style={{ width: 200 }} />
      </Form.Item>
      <Form.Item
        label={<div className='text-h4'>{t('状态筛选')}</div>}
        name='taskState'>
        <Select style={{ width: 120 }} allowClear>
          {/* <Select.Option value='all'>全部</Select.Option> */}
          <Select.Option value='1'>{t('待执行')}</Select.Option>
          <Select.Option value='2'>{t('执行中')}</Select.Option>
          <Select.Option value='3'>{t('已完成')}</Select.Option>
        </Select>
      </Form.Item>
      <Form.Item>
        <Button type='primary' htmlType='submit'>
          {t('搜索')}
        </Button>
        <Button style={{ marginLeft: 8 }} htmlType='reset'>
          {t('重置')}
        </Button>
      </Form.Item>
    </Form>
  );
};

// 修改 TaskTable：移除状态筛选按钮
const TaskTable: React.FC<{
  tasks: Task[];
  pageData: PageDataType;
  setPageData: (data: PageDataType) => void;
}> = ({ tasks, pageData, setPageData }) => {
  const navigate = useNavigate();
  const columns = [
    {
      title: t('名称'),
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('状态'),
      dataIndex: 'taskState',
      key: 'taskState',
      width: 100,
      render: (text: string) => <Tag>{text}</Tag>,
    },
    {
      title: t('流程'),
      dataIndex: 'instanceUuid',
      key: 'instanceUuid',
      render: (text: string, record: any) => {
        return (
          <Button
            type='link'
            onClick={() => {
              navigate(
                `/runProcess?templateUuid=${record.templateUuid}&instanceUuid=${text}&isCreate=false`,
              );
            }}>
            {t('点击跳转')}
          </Button>
        );
      },
    },
  ];

  return (
    <div>
      <Table
        // ...existing code...（移除原有的 title 状态按钮）
        dataSource={tasks}
        columns={columns}
        bordered
        rowKey='id'
        pagination={{
          current: pageData.current,
          pageSize: pageData.pageSize,
          position: ['bottomCenter'],
          total: pageData.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPageData({ ...pageData, current: page, pageSize });
          },
          onShowSizeChange: (_, size) => {
            setPageData({ ...pageData, pageSize: size });
          },
        }}
      />
    </div>
  );
};

export default function MyTaskPage() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(tasks);
  const [pageData, setPageData] = useState<PageDataType>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const getProcessInstanceTaskListByCurUser = async (data?: any) => {
    try {
      const res = await api.getProcessInstanceTaskListByCurUser(
        removeUndefinedValues({
          page: pageData.current,
          pageSize: pageData.pageSize,
          ...data,
        }),
      );
      if (!res.code) {
        const list = res.data.list || [];
        setTasks(list);
        setFilteredTasks(list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 修改 handleSearch 仅更新 searchQuery
  const handleSearch = (query: string) => {
    getProcessInstanceTaskListByCurUser(query);
  };

  useEffect(() => {
    getProcessInstanceTaskListByCurUser();
  }, []);

  return (
    <div className='bg-white rounded-2xl gap-4 flex flex-col p-4'>
      <div className='text-h3'>{t('我的任务')}</div>
      <Card>
        <SearchBox onSearch={handleSearch} />
      </Card>
      <main>
        <TaskTable
          tasks={filteredTasks}
          pageData={pageData}
          setPageData={setPageData}
        />
      </main>
    </div>
  );
}
