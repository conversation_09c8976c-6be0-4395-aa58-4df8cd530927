import React, { useEffect, useRef } from 'react';
import {
  Form,
  Button,
  Steps,
  Input,
  Select,
  Table,
  Card,
  Checkbox,
  Flex,
  DatePicker,
  Switch,
  App,
} from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import type { StepDetail } from './type';
import { useState } from 'react';
import ajax from '@/api';
import type { PageDataType } from '@/types';
import TaskDetailModal from './components/TaskDetailModal';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useSize } from 'ahooks';
import { t } from '@/languages';

export default function RunProcess() {
  const { message } = App.useApp();

  const [form] = Form.useForm();
  const [processForm] = Form.useForm();
  const [scheduleForm] = Form.useForm();
  const [timeForm] = Form.useForm();

  const [searchParams] = useSearchParams();

  const initTemplateUuid = searchParams.get('templateUuid');
  const initInstanceUuid = searchParams.get('instanceUuid');
  const isCreate = searchParams.get('isCreate');

  const [scheduleFormDisplay, setScheduleFormDisplay] = useState(false);

  const [tableData, setTableData] = useState<StepDetail[]>([]);
  const [pageData, setPageData] = useState<PageDataType>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedStepIndex, setSelectedStepIndex] = useState<number | null>(
    null,
  );

  const [ownerList, setOwnerList] = useState<any[]>([]);
  const [processTemplateList, setProcessTemplateList] = useState<any[] | null>(
    null,
  );
  const [chosenTemplateUuid, setChosenTemplateUuid] = useState<string | null>(
    initTemplateUuid,
  );
  const [templateDetail, setTemplateDetail] = useState<any>(null);

  const getProcessTemplateInfoByTemplateUuid = async (templateUuid: string) => {
    try {
      const result = await ajax.getProcessTemplateInfoByTemplateUuid({
        templateUuid: templateUuid,
      });
      if (result.code !== 0) {
        console.error(result.msg);
        message.error(result.msg);
        return;
      }
      const data = result.data.result;
      setChosenTemplateUuid(data.templateUuid);
      setTemplateDetail({
        templateName: data.templateName,
        description: data.description,
        timeZone: data.timeZone,
        workSchedule: data.workSchedule,
        ownerId: data.ownerId,
        stepList: data.stepList,
        taskList: data.taskList,
      });
    } catch (error) {}
  };

  useEffect(() => {
    if (!chosenTemplateUuid) return;
    getProcessTemplateInfoByTemplateUuid(chosenTemplateUuid);
  }, [chosenTemplateUuid]);

  useEffect(() => {
    if (!templateDetail) return;
    const owner = ownerList.find(
      (item) => item.value === templateDetail.ownerId,
    );
    const weekList: any = [];
    Object.keys(templateDetail.workSchedule).forEach((key: any) => {
      templateDetail.workSchedule[key] ? weekList.push(key) : null;
    });
    form.setFieldsValue({
      templateName: templateDetail.templateName,
      description: templateDetail.description,
      timeZone: templateDetail.timeZone,
      workSchedule: weekList,
      owner: owner ? owner.label : '',
    });
    const stepList = templateDetail.stepList;
    if (initInstanceUuid) {
      getProcessInstanceInfoByInstanceUuid(initInstanceUuid);
      return;
    }
    setTableData(
      stepList.map((item: any) => {
        let duration = '';
        item.duration['weekNum'] !== 0
          ? (duration = duration + `${item.duration['weekNum']}` + t('周'))
          : null;
        item.duration['dayNum'] !== 0
          ? (duration = duration + `${item.duration['dayNum']}` + t('天'))
          : null;
        item.duration['hourNum'] !== 0
          ? (duration = duration + `${item.duration['hourNum']}` + t('小时'))
          : null;
        item.duration['minuteNum'] !== 0
          ? (duration = duration + `${item.duration['minuteNum']}` + t('分钟'))
          : null;
        return {
          stepName: item.stepName,
          participant: item.workerIdList
            .map(
              (participant: any) =>
                ownerList.find((owner: any) => owner.value === participant)
                  ?.label,
            )
            .join(','),
          reviewer: ownerList.find(
            (owner: any) => owner.value === item.reviewerId,
          )?.label,
          duration: duration,
          tasknums: item.taskNum,
          taskList: item.taskList,
          durationObj: item.duration,
        };
      }),
    );
  }, [templateDetail]);

  useEffect(() => {
    if (!initInstanceUuid) return;
    setScheduleFormDisplay(false);
    processForm.resetFields();
    getProcessInstanceInfoByInstanceUuid(initInstanceUuid);
  }, [initInstanceUuid]);

  useEffect(() => {
    if (!initTemplateUuid) return;
    getProcessTemplateInfoByTemplateUuid(initTemplateUuid);
  }, [initTemplateUuid]);

  const navigate = useNavigate();

  const handleStepClick = (index: number) => {
    setSelectedStepIndex(index);
    setIsModalVisible(true);
  };

  const reviewInstanceStep = async (stepUuid: string, instanceUuid: any) => {
    try {
      ajax
        .reviewInstanceStep({
          instanceUuid: instanceUuid,
          stepUuid: stepUuid,
        })
        .then((res) => {
          if (res.code !== 0) {
            console.error(res.msg);
            message.error(res.msg);
            return;
          }
          message.success(t('审阅成功'));
          getProcessInstanceInfoByInstanceUuid(instanceUuid);
        });
    } catch (error) {
      console.error(error);
    }
  };

  const colums: any = [
    {
      title: t('步骤名称'),
      dataIndex: 'stepName',
      key: 'stepName',
    },
    {
      title: t('参与人'),
      dataIndex: 'participant',
      key: 'participant',
    },
    {
      title: t('审阅人'),
      dataIndex: 'reviewer',
      key: 'reviewer',
    },
    {
      title: t('状态'),
      dataIndex: 'stepStateName',
      key: 'stepStateName',
    },
    {
      title: t('持续时间'),
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: t('预计开始时间'),
      dataIndex: 'expectedStartTime',
      key: 'expectedStartTime',
    },
    {
      title: t('预计结束时间'),
      dataIndex: 'expectedEndTime',
      key: 'expectedEndTime',
    },
    {
      title: t('实际开始时间'),
      dataIndex: 'actualStartTime',
      key: 'actualStartTime',
    },
    {
      title: t('实际结束时间'),
      dataIndex: 'actualEndTime',
      key: 'actualEndTime',
    },
    {
      title: t('任务数目'),
      dataIndex: 'tasknums',
      key: 'tasknums',
      render: (text: string) => {
        return <div>{text}</div>;
      },
    },
    {
      title: t('操作'),
      dataIndex: 'options',
      width: 160,
      key: 'options',
      fixed: 'right',
      render: (_: any, record: any, index: number) => {
        return (
          <div>
            <Button type='link' onClick={() => handleStepClick(index)}>
              {initInstanceUuid ? t('编辑') : t('编辑(查看)')}
            </Button>
            <Button
              type='link'
              disabled={!initInstanceUuid || record.stepState === 5}
              onClick={() => {
                reviewInstanceStep(record.stepUuid, initInstanceUuid);
              }}>
              <Flex gap={10}>
                <span>{t('审阅')}</span>
                <Checkbox checked={record.stepState === 5}></Checkbox>
              </Flex>
            </Button>
          </div>
        );
      },
    },
  ].map((item) => {
    return {
      ...item,
      align: 'center',
    };
  });

  const getAllProcessTemplate = async () => {
    try {
      const result = await ajax.getAllProcessTemplate();
      if (result.code !== 0) {
        console.error(result.msg);
        message.error(result.msg);
        return;
      }
      const data = result.data.list;
      setProcessTemplateList(
        data.map((item: any) => ({
          templateName: item.templateName,
          templateUuid: item.templateUuid,
          description: item.description,
          ownerId: item.ownerId,
          timeZone: item.timeZone,
          weekSchedule: item.weekSchedule,
        })),
      );
    } catch (error) {}
  };

  const getOwnerList = async () => {
    try {
      const res = await ajax.getUserListByCoIdContainsSelf();
      if (res.code !== 0) {
        console.error(res.msg);
        message.error(res.msg);
        return;
      }
      const data = res.data.list;
      setOwnerList(
        data.map((item: any) => ({
          label: item.nickName,
          value: item.id,
        })),
      );
    } catch (error) {
      console.error(error);
    }
  };

  const createProcessInstance = async () => {
    try {
      ajax
        .createProcessInstance({
          templateUuid: chosenTemplateUuid,
          ...processForm.getFieldsValue([
            'instanceName',
            'description',
            'isExecuteNow',
          ]),
          expectStartTime: processForm
            .getFieldValue('expectStartTime')
            .format('YYYY-MM-DD HH:mm:ss'),
          scheduledTask: {
            isRepeated: processForm.getFieldValue('isRepeated'),
            timeUnit: scheduleForm.getFieldValue('timeUnit'),
            interval: Number(scheduleForm.getFieldValue('interval')),
            repeatCount: Number(scheduleForm.getFieldValue('repeatCount')),
          },
        })
        .then((res) => {
          if (res.code !== 0) {
            console.error(res.msg);
            message.error(res.msg);
            return;
          }
          message.success(t('创建流程实例成功'));
          resetFormData();
          navigate(-1);
        });
    } catch (error) {
      console.error(error);
    }
  };

  const getProcessInstanceInfoByInstanceUuid = async (instanceUuid: string) => {
    try {
      const res = await ajax.getProcessInstanceInfoByInstanceUuid({
        instanceUuid: instanceUuid,
      });
      if (res.code !== 0) {
        console.error(res.msg);
        message.error(res.msg);
        return;
      }
      const result: any = res.data.result;
      processForm.setFieldsValue({
        instanceName: result.instanceName,
        description: result.description,
      });
      timeForm.setFieldsValue({
        expectedStartTime: result.expectedStartTime,
        expectedEndTime: result.expectedEndTime,
        actualStartTime:
          result.actualStartTime === '' ? t('未开始') : result.actualStartTime,
        actualEndTime:
          result.actualEndTime === '' ? t('未结束') : result.actualEndTime,
      });
      setTableData(
        result.stepList.map((item: any) => {
          let duration = '';
          item.duration['weekNum'] !== 0
            ? (duration = duration + `${item.duration['weekNum']}` + t('周'))
            : null;
          item.duration['dayNum'] !== 0
            ? (duration = duration + `${item.duration['dayNum']}` + t('天'))
            : null;
          item.duration['hourNum'] !== 0
            ? (duration = duration + `${item.duration['hourNum']}` + t('小时'))
            : null;
          item.duration['minuteNum'] !== 0
            ? (duration =
                duration + `${item.duration['minuteNum']}` + t('分钟'))
            : null;
          return {
            stepName: item.stepName,
            participant: item.workerIdList
              .map(
                (participant: any) =>
                  ownerList.find((owner: any) => owner.value === participant)
                    ?.label,
              )
              .join(','),
            reviewer: ownerList.find(
              (owner: any) => owner.value === item.reviewerId,
            )?.label,
            duration: duration,
            tasknums: item.taskNum,
            taskList: item.taskList,
            durationObj: item.duration,
            isMustReview: item.isMustReview,
            stepState: item.stepState,
            stepStateName: item.stepStateName,
            stepUuid: item.stepUuid,
            expectedStartTime: item.expectedStartTime,
            expectedEndTime: item.expectedEndTime,
            actualStartTime: item.actualStartTime,
            actualEndTime: item.actualEndTime,
          };
        }),
      );
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getAllProcessTemplate();
    getOwnerList();
  }, []);

  const titleStyle = {
    // width: '90px',
    textAlign: 'left' as any,
  };

  const resetFormData = () => {
    form.resetFields();
    processForm.resetFields();
    scheduleForm.resetFields();
    timeForm.resetFields();
    setChosenTemplateUuid(null);
    setTableData([]);
    setScheduleFormDisplay(false);
  };

  useEffect(() => {
    if (isCreate === 'true') {
      resetFormData();
    }
  }, [isCreate]);

  const mainRef = useRef<HTMLDivElement>(null);
  const mainSize = useSize(mainRef);

  return (
    <div className='bg-white p-4 rounded-2xl gap-4 flex flex-col'>
      <div className='flex justify-between'>
        <div className='flex gap-4 items-center'>
          <ArrowLeftOutlined
            className='text-blue-500 cursor-pointer'
            onClick={() => {
              navigate(-1);
            }}
          />
          <h1 className='text-h3'>{t('运行流程')}</h1>
        </div>
        <Button
          type='primary'
          onClick={() => {
            if (!chosenTemplateUuid) {
              message.error(t('请选择流程模版'));
              return;
            }
            processForm.validateFields().then(() => {
              if (scheduleFormDisplay) {
                scheduleForm.validateFields().then(() => {
                  createProcessInstance();
                });
              } else {
                createProcessInstance();
              }
            });
          }}
          disabled={Boolean(initTemplateUuid)}>
          {t('运行')}
        </Button>
      </div>
      <Card>
        <label className='text-h3 mr-4'>{t('选择模版')}</label>
        <Select
          disabled={Boolean(initTemplateUuid)}
          placeholder={t('请选择流程模版')}
          style={{
            width: 200,
          }}
          options={
            processTemplateList
              ? processTemplateList.map((item: any) => ({
                  label: item.templateName,
                  value: item.templateUuid,
                }))
              : []
          }
          value={chosenTemplateUuid}
          onChange={(value) => setChosenTemplateUuid(value)}
        />
      </Card>
      <Card title={<div className='text-h3'>{t('基本信息')}</div>}>
        <Form
          form={form}
          disabled={true}
          className='mb-4 gap-4'
          colon={false}
          labelAlign='right'>
          <Form.Item noStyle>
            <Flex vertical={true}>
              <Flex vertical={false} gap={10}>
                <Form.Item
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('模版名称')}
                    </div>
                  }
                  name={'templateName'}>
                  <Input className='w-[200px]' />
                </Form.Item>
                <Form.Item
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('模版描述')}
                    </div>
                  }
                  name={'description'}>
                  <Input style={{ width: 200 }} />
                </Form.Item>
              </Flex>
              <Flex vertical={false} gap={10}>
                <Form.Item
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('时区')}
                    </div>
                  }
                  name={'timeZone'}>
                  <Input className='w-[200px]' />
                </Form.Item>
                <Form.Item
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('负责人')}
                    </div>
                  }
                  name={'owner'}>
                  <Input className='w-[200px]' />
                </Form.Item>
              </Flex>
              <Form.Item
                label={
                  <div className='text-h4' style={titleStyle}>
                    {t('工作日')}
                  </div>
                }
                name={'workSchedule'}>
                <Checkbox.Group>
                  <Checkbox value='sunday'>{t('星期日')}</Checkbox>
                  <Checkbox value='monday'>{t('星期一')}</Checkbox>
                  <Checkbox value='tuesday'>{t('星期二')}</Checkbox>
                  <Checkbox value='thursday'>{t('星期三')}</Checkbox>
                  <Checkbox value='wednesday'>{t('星期四')}</Checkbox>
                  <Checkbox value='friday'>{t('星期五')}</Checkbox>
                  <Checkbox value='saturday'>{t('星期六')}</Checkbox>
                </Checkbox.Group>
              </Form.Item>
            </Flex>
          </Form.Item>
        </Form>
      </Card>
      <Card title={<div className='text-h3'>{t('流程信息')}</div>}>
        <Form
          form={processForm}
          disabled={Boolean(initTemplateUuid)}
          className='mb-4 gap-4'
          colon={false}
          labelAlign='right'>
          <Form.Item noStyle>
            <Flex vertical={true}>
              <Flex vertical={false} gap={10}>
                <Form.Item
                  required
                  rules={[
                    {
                      required: true,
                      message: t('请输入流程实例名称'),
                    },
                  ]}
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('实例名称')}
                    </div>
                  }
                  name={'instanceName'}>
                  <Input
                    className='w-[200px]'
                    placeholder={t('请输入流程实例名称')}
                  />
                </Form.Item>
                <Form.Item
                  required
                  rules={[
                    {
                      required: true,
                      message: t('请输入流程实例描述'),
                    },
                  ]}
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('实例描述')}
                    </div>
                  }
                  name={'description'}>
                  <Input
                    style={{ width: 200 }}
                    placeholder={t('请输入流程实例描述')}
                  />
                </Form.Item>
              </Flex>
              {isCreate === 'true' ? (
                <Flex vertical={false} gap={21}>
                  <Form.Item
                    required
                    rules={[
                      {
                        required: true,
                        message: t('请选择开始时间'),
                      },
                    ]}
                    label={
                      <div className='text-h4' style={titleStyle}>
                        {t('开始时间')}
                      </div>
                    }
                    name={'expectStartTime'}>
                    <DatePicker
                      showTime
                      className='w-[200px]'
                      format={{
                        format: 'YYYY-MM-DD HH:mm:ss',
                      }}
                    />
                  </Form.Item>
                  <Form.Item noStyle>
                    <Flex vertical={false} gap={10}>
                      <Form.Item
                        label={
                          <div className='text-h4' style={titleStyle}>
                            {t('立刻执行')}
                          </div>
                        }
                        name={'isExecuteNow'}>
                        <Switch />
                      </Form.Item>
                      <Form.Item
                        initialValue={false}
                        label={
                          <div className='text-h4' style={titleStyle}>
                            {t('重复执行')}
                          </div>
                        }
                        name={'isRepeated'}>
                        <Switch
                          onChange={(checked) => {
                            setScheduleFormDisplay(checked);
                          }}
                        />
                      </Form.Item>
                    </Flex>
                  </Form.Item>
                </Flex>
              ) : null}
            </Flex>
          </Form.Item>
        </Form>
      </Card>
      <Card
        title={<div className='text-h3'>{t('时间信息')}</div>}
        style={{
          display: isCreate === 'true' ? 'none' : 'block',
        }}>
        <Form
          form={timeForm}
          disabled={true}
          className='mb-4 gap-4'
          colon={false}
          labelAlign='right'>
          <Form.Item noStyle>
            <Flex vertical={true}>
              <Flex vertical={false} gap={20}>
                <Form.Item
                  label={
                    <div className='text-h4' style={{ textAlign: 'left' }}>
                      {t('预计开始时间')}
                    </div>
                  }
                  name={'expectedStartTime'}>
                  <Input className='w-[200px]' />
                </Form.Item>
                <Form.Item
                  label={
                    <div className='text-h4' style={{ textAlign: 'left' }}>
                      {t('预计结束时间')}
                    </div>
                  }
                  name={'expectedEndTime'}>
                  <Input style={{ width: 200 }} />
                </Form.Item>
              </Flex>
              <Flex vertical={false} gap={20}>
                <Form.Item
                  label={
                    <div className='text-h4' style={{ textAlign: 'left' }}>
                      {t('实际开始时间')}
                    </div>
                  }
                  name={'actualStartTime'}>
                  <Input className='w-[200px]' />
                </Form.Item>
                <Form.Item
                  label={
                    <div className='text-h4' style={{ textAlign: 'left' }}>
                      {t('实际结束时间')}
                    </div>
                  }
                  name={'actualEndTime'}>
                  <Input style={{ width: 200 }} />
                </Form.Item>
              </Flex>
            </Flex>
          </Form.Item>
        </Form>
      </Card>
      <Card
        title={<div className='text-h3'>{t('执行详情')}</div>}
        style={{
          display: scheduleFormDisplay ? 'block' : 'none',
        }}>
        <Form
          form={scheduleForm}
          className='mb-4 gap-4'
          colon={false}
          labelAlign='right'>
          <Form.Item noStyle>
            <Flex vertical={true}>
              <Flex vertical={false} gap={10}>
                <Form.Item
                  required
                  rules={[
                    {
                      required: true,
                      message: t('请选择时间单位'),
                    },
                  ]}
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('时间单位')}
                    </div>
                  }
                  name={'timeUnit'}>
                  <Select
                    options={[
                      {
                        label: t('月'),
                        value: 'month',
                      },
                      {
                        label: t('周'),
                        value: 'week',
                      },
                    ]}
                    placeholder={t('请选择时间单位')}
                    style={{
                      width: 200,
                    }}
                  />
                </Form.Item>
                <Form.Item
                  required
                  rules={[
                    {
                      required: true,
                      message: t('请输入时间间隔'),
                    },
                  ]}
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('时间间隔')}
                    </div>
                  }
                  name={'interval'}>
                  <Input
                    style={{ width: 200 }}
                    placeholder={t('请输入时间间隔')}
                  />
                </Form.Item>
              </Flex>
              <Flex vertical={false} gap={21}>
                <Form.Item
                  required
                  rules={[
                    {
                      required: true,
                      message: t('请输入重复次数'),
                    },
                  ]}
                  label={
                    <div className='text-h4' style={titleStyle}>
                      {t('重复次数')}
                    </div>
                  }
                  name={'repeatCount'}>
                  <Input
                    className='w-[200px]'
                    placeholder={t('请输入重复次数')}
                  />
                </Form.Item>
              </Flex>
            </Flex>
          </Form.Item>
        </Form>
      </Card>
      <main ref={mainRef}>
        <Table
          title={() => {
            return (
              <div className='flex justify-between'>
                <div className='text-h3'>{t('步骤列表')}</div>
                <Steps
                  className='w-4/5'
                  items={tableData.map((item) => {
                    return {
                      title: item.stepName,
                    };
                  })}
                />
              </div>
            );
          }}
          bordered
          tableLayout='auto'
          columns={(isCreate === 'true'
            ? colums.filter(
                (item: any) =>
                  item.dataIndex !== 'stepStateName' &&
                  item.dataIndex !== 'expectedStartTime' &&
                  item.dataIndex !== 'expectedEndTime' &&
                  item.dataIndex !== 'actualStartTime' &&
                  item.dataIndex !== 'actualEndTime',
              )
            : colums
          ).map((item: any) => ({
            ...item,
            minWidth: 150,
          }))}
          scroll={{ x: (mainSize?.width || 2) - 2 }}
          dataSource={tableData}
          pagination={{
            current: pageData.current,
            pageSize: pageData.pageSize,
            position: ['bottomCenter'],
            total: pageData.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPageData({
                ...pageData,
                current: page,
                pageSize,
              });
            },
            onShowSizeChange: (_, size) => {
              setPageData({
                ...pageData,
                pageSize: size,
              });
            },
          }}
        />
      </main>
      <TaskDetailModal
        visible={isModalVisible}
        operationDisabled={!initInstanceUuid}
        step={selectedStepIndex !== null ? tableData[selectedStepIndex] : null}
        onClose={() => setIsModalVisible(false)}
        onChange={() => {
          if (!initInstanceUuid) return;
          getProcessInstanceInfoByInstanceUuid(initInstanceUuid);
        }}
      />
    </div>
  );
}
