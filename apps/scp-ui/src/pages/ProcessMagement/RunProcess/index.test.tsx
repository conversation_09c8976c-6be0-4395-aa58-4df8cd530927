import { render, screen } from '@testing-library/react';
import ProcessTemplateManager from '.';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';

import store from '@/store/index.ts';

describe('ProcessTemplateManager', () => {
  it('should render page', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(screen.getByText('运行流程')).toBeInTheDocument();
  });

  it('should render with filter', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(
      screen.getByText(/模版名称/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/模版描述/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/时区/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/负责人/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/工作日/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
  });

  it('should render tabel', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(screen.getAllByRole('table')).toHaveLength(1);
  });
});
