import React, { useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Table,
  Row,
  Col,
  Select,
  Flex,
  Button,
  App,
} from 'antd';
import ajax from '@/api';
import { t } from '@/languages';

interface TaskDetailModalProps {
  visible: boolean;
  operationDisabled: boolean;
  step: any | null;
  onClose: () => void;
  onChange?: any;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  visible,
  operationDisabled = true,
  step,
  onClose,
  onChange = () => {},
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (step) {
      form.setFieldsValue({
        stepName: step.stepName,
        reviewer: step.reviewer,
        participant: step.participant.split(','),
        duration: step.durationObj,
      });
      setDataSource(
        step.taskList.map((item: any) => {
          return {
            taskName: item.taskName,
            taskState: item.taskState,
            taskStateName: item.taskStateName,
            taskUuid: item.taskUuid,
            instanceTaskId: item.instanceTaskId,
          };
        }),
      );
    } else {
      form.resetFields();
      setDataSource([]);
    }
  }, [step, form]);

  const startInstanceTask = async (instanceTaskId: string) => {
    try {
      ajax
        .startInstanceTask({
          instanceTaskId: instanceTaskId,
        })
        .then((result) => {
          onChange();
          if (result.code !== 0) {
            console.error(result.msg);
            message.error(result.msg);
            return;
          }
          message.success(t('启动任务成功'));
        });
    } catch (error) {
      console.log(error);
    }
  };

  const completeInstanceTask = async (instanceTaskId: string) => {
    try {
      ajax
        .completeInstanceTask({
          instanceTaskId: instanceTaskId,
        })
        .then((result) => {
          onChange();
          if (result.code !== 0) {
            console.error(result.msg);
            message.error(result.msg);
            return;
          }
          message.success(t('完成任务成功'));
        });
    } catch (error) {
      console.log(error);
    }
  };

  const taskColumns = [
    {
      title: t('任务名称'),
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: t('状态'),
      dataIndex: 'taskStateName',
      key: 'taskStateName',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      key: 'operation',
      render: (_: any, record: any) => {
        return (
          <Flex justify='center' align='center'>
            <Button
              disabled={operationDisabled || record.taskState === 3}
              onClick={() => {
                record.taskState === 1
                  ? startInstanceTask(record.instanceTaskId)
                  : completeInstanceTask(record.instanceTaskId);
              }}>
              {record.taskState === 1 ? t('开始') : t('完成')}
            </Button>
          </Flex>
        );
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title={t('步骤详情(只读)')}
      open={visible}
      onCancel={onClose}
      onOk={() => {
        form.validateFields().then(() => {
          onClose();
        });
      }}
      destroyOnClose
      footer={null}>
      <Form form={form} layout='vertical' disabled>
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name='stepName' label={t('步骤名称')}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name='reviewer' label={t('审阅人')}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name='participant' label={t('参与人')}>
                <Select mode='multiple' />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name='duration' label={t('持续时间')}>
                <SimpleDurationShow />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <div>
          <Table
            bordered
            title={() => {
              return <div className='text-h4'>{t('任务列表')}</div>;
            }}
            columns={
              operationDisabled
                ? taskColumns.filter(
                    (item: any) => item.dataIndex !== 'taskStateName',
                  )
                : taskColumns
            }
            dataSource={dataSource}
          />
        </div>
      </Form>
    </Modal>
  );
};

const SimpleDurationShow = ({
  value = {
    weekNum: 0,
    dayNum: 0,
    hourNum: 0,
    minuteNum: 0,
  },
  onChange = () => {},
}: {
  value?: any;
  onChange?: any;
}) => {
  return (
    <Flex gap={10}>
      <Input
        addonBefore={t('持续')}
        addonAfter={t('周')}
        value={value.weekNum}
        onChange={onChange}
      />
      <Input
        addonBefore={t('持续')}
        addonAfter={t('天')}
        value={value.dayNum}
        onChange={onChange}
      />
      <Input
        addonBefore={t('持续')}
        addonAfter={t('小时')}
        value={value.hourNum}
        onChange={onChange}
      />
      <Input
        addonBefore={t('持续')}
        addonAfter={t('分钟')}
        value={value.minuteNum}
        onChange={onChange}
      />
    </Flex>
  );
};

export default TaskDetailModal;
