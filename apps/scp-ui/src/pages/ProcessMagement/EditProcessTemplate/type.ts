export type ProcessDetail = {};

export type StepTableColumns = {
  key: React.Key;
  stepUuid: React.Key;
  owner: number;
  index: number;
  duringTime: string;
  tasknums: number;
  children?: StepTableColumns[];
  options?: React.ReactNode;
};

export type TaskTableColumns = {
  taskUuid: string;
  description?: string;
  taskName?: string;
  taskSeq?: number;
};

export type StepListItem = {
  duration: {
    weekNum: number;
    dayNum: number;
    hourNum: number;
    minuteNum: number;
  };
  reviewerName: string;
  isMustReview: boolean;
  reviewerId: number;
  workerIdList: number[];
  taskList: TaskTableColumns[];
  stepName: string;
  stepUuid: React.Key;
};
