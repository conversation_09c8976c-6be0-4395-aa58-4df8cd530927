import { render, screen } from '@testing-library/react';
import EditProcessTemplate from '.';
import userEvent from '@testing-library/user-event';
describe('EditProcessTemplate', () => {
  it('should render page', () => {
    render(<EditProcessTemplate />);
    expect(screen.getByText('编辑模版')).toBeInTheDocument();
  });
  it('should render header', () => {
    render(<EditProcessTemplate />);
    expect(screen.getByText('模版名称')).toBeInTheDocument();
    expect(screen.getByText('工作日')).toBeInTheDocument();
    expect(screen.getByText('时区')).toBeInTheDocument();
  });
  it('should render table', () => {
    render(<EditProcessTemplate />);
    expect(screen.getAllByRole('table')).toHaveLength(1);
  });
  it('should render modal', async () => {
    render(<EditProcessTemplate />);

    await userEvent.click(screen.getByText('新 建'));
    expect(screen.getByText('参与人')).toBeInTheDocument();
  });
});
