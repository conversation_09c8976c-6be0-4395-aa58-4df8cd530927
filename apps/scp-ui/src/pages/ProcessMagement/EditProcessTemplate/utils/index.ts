export function mapDaysToWeekdays(days: number[]): Record<string, boolean> {
  const dayMap: Record<number, string> = {
    1: 'monday',
    2: 'tuesday',
    3: 'wednesday',
    4: 'thursday',
    5: 'friday',
    6: 'saturday',
    7: 'sunday',
  };

  const result: Record<string, boolean> = {
    monday: false,
    tuesday: false,
    wednesday: false,
    thursday: false,
    friday: false,
    saturday: false,
    sunday: false,
  };

  days.forEach((day) => {
    if (dayMap[day]) {
      result[dayMap[day]] = true;
    }
  });

  return result;
}

export function mapWeekdaysToDays(weekdays: Record<string, boolean>): number[] {
  const dayMap: Record<string, number> = {
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6,
    sunday: 7,
  };

  return Object.entries(weekdays)
    .filter(([, isSelected]) => isSelected)
    .map(([weekday]) => dayMap[weekday])
    .sort((a, b) => a - b);
}
