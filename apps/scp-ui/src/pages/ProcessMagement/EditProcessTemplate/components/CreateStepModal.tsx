import {
  Modal,
  Form,
  Input,
  InputNumber,
  Table,
  Row,
  Col,
  Button,
  TableProps,
  App,
  Select,
  ModalProps,
  Switch,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import type { TaskTableColumns, StepListItem } from '../type';
import { useEffect, useReducer, useState } from 'react';
import ajax from '@/api';
import { t } from '@/languages';
import _ from 'lodash';

interface CreateStepModalProps {
  visible: boolean;
  onCancel: () => void;
  step: StepListItem | null;
  addTableItem: (value: StepListItem) => void;
  changeTableItem: (value: StepListItem) => void;
  isEdit: boolean;
}

const taskColumns: TableProps<TaskTableColumns>['columns'] = [
  {
    title: t('顺序'),
    dataIndex: 'index',
    key: 'index',
    render: (_, __, index) => index + 1,
  },
  {
    title: t('任务名称'),
    dataIndex: 'taskName',
    key: 'taskName',
  },
  // {
  //   title: '执行人',
  //   dataIndex: 'worker',
  //   key: 'worker',
  // },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   key: 'status',
  // },
];

export default function CreateStepModal({
  visible,
  onCancel,
  addTableItem,
  changeTableItem,
  step,
  isEdit,
}: CreateStepModalProps) {
  const [form] = Form.useForm();
  const [innerForm] = Form.useForm();
  const { message } = App.useApp();
  const [userList, setUserList] = useState<{ label: string; value: any }[]>([]);

  // const isMustReview = Form.useWatch('isMustReview', form);

  const innerUuid = uuidv4();

  const stepUuid = isEdit ? step?.stepUuid : innerUuid;

  const [dataSource, setDataSource] = useState<TaskTableColumns[]>([]);
  const [addNewTaskModalVisible, setAddNewTaskModalVisible] = useState(false);
  const [taskTemplateList, setTaskTemplateList] = useState([]);
  const innerModal: ModalProps = {
    open: addNewTaskModalVisible,
    title: t('新建任务'),
    width: 900,
    destroyOnClose: true,
    onCancel: () => {
      setAddNewTaskModalVisible(false);
    },
    onOk: () => {
      const values = innerForm.getFieldsValue();
      createTask(values);
      setAddNewTaskModalVisible(false);
    },
  };

  const [taskTemplateModal, dispatchTaskTemplateModal] = useReducer(
    (state: ModalProps, action: any) => {
      switch (action.type) {
        case 'open':
          return {
            ...state,
            open: true,
          };
        case 'close':
          return {
            ...state,
            open: false,
          };
        default:
          return state;
      }
    },
    {
      open: false,
      width: 600,
      destroyOnClose: true,
      footer: null,
      onCancel: () => {
        dispatchTaskTemplateModal({ type: 'close' });
      },
    },
  );

  /**
   * @description 从后端获取任务列表
   * @api /api/task/getTaskList
   */
  // const getTaskList = async () => {
  //   try {
  //     const res = await ajax.getTaskList();
  //     const list = _.get(res, 'data.list', []) || [];
  //     setDataSource(list);
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  /**
   * @ description 创建任务
   */
  const createTask = async (values: any) => {
    try {
      const res = await ajax.createProcess(values);
      if (!res.code) {
        message.success(t('创建成功'));
        const taskUuid = _.get(res, 'data.result', {});
        getTaskDetail(taskUuid);
        getTaskTemplateList();
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * @description 从后端获取任务模版列表
   */
  const getTaskTemplateList = async () => {
    try {
      const res = await ajax.getTaskTemplateList();
      const list = _.get(res, 'data.list', []) || [];
      setTaskTemplateList(
        list.map((item: any) => {
          return {
            label: item.taskName,
            value: item.taskUuid,
          };
        }),
      );
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * @description 获取任务详情根据taskUuid
   */
  const getTaskDetail = async (taskUuid: string) => {
    try {
      const res = await ajax.getTaskDetail(taskUuid);
      if (!res.code) {
        const result = _.get(res, 'data.result', {});
        setDataSource((prev) => {
          return [...prev, { ...result, key: result.id || Date.now() }];
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * @description 获取用户列表
   */

  const getUserList = async () => {
    try {
      const res = await ajax.getUserListByCoIdContainsSelf();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setUserList(
          list.map((item: any) => ({
            key: item.id,
            label: item.nickName,
            value: item.id,
          })),
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (step) {
      form.setFieldsValue(step);
      setDataSource(step?.taskList);
    } else {
      form.resetFields();
    }
  }, [step, form]);

  useEffect(() => {
    getTaskTemplateList();
    getUserList();
  }, []);

  return (
    <Modal
      open={visible}
      width={1200}
      destroyOnClose
      title={step ? t('编辑步骤') : t('新建步骤')}
      onCancel={() => {
        setDataSource([]);
        onCancel();
      }}
      onOk={() => {
        form.validateFields().then((values) => {
          isEdit
            ? changeTableItem({
                ...values,
                stepUuid,
                taskList: dataSource.map((item) => ({
                  taskUuid: item.taskUuid,
                })),
                reviewerName: userList.find((item: any) => {
                  return item?.value === values.reviewerId;
                })?.label,
              })
            : addTableItem({
                ...values,
                stepUuid,
                taskList: dataSource.map((item) => ({
                  taskUuid: item.taskUuid,
                })),
                reviewerName: userList.find((item: any) => {
                  return item?.value === values.reviewerId;
                })?.label,
              });

          setDataSource([]);
          onCancel();
        });
      }}>
      <Form form={form} layout='vertical'>
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='stepName'
                label={t('步骤名称')}
                rules={[{ required: true, message: t('请输入步骤名称') }]}>
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name='isMustReview'
                label={t('是否必须审阅')}
                valuePropName='checked'>
                <Switch />
              </Form.Item>
            </Col>
            <Form.Item shouldUpdate noStyle>
              {({ getFieldValue }) => {
                if (!getFieldValue('isMustReview')) {
                  return null;
                }
                return (
                  <Col span={12}>
                    <Form.Item
                      name='reviewerId'
                      label={t('审阅人')}
                      rules={[{ required: true, message: t('请输入审阅人') }]}>
                      <Select options={userList} />
                    </Form.Item>
                  </Col>
                );
              }}
            </Form.Item>
            <Col span={12}>
              <Form.Item
                name='workerIdList'
                label={t('参与人')}
                rules={[{ required: true, message: t('参与人') }]}>
                <Select mode='multiple' options={userList} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={t('时间')} name={['duration', 'weekNum']}>
                <InputNumber addonBefore={t('持续')} addonAfter={t('周')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=' ' name={['duration', 'dayNum']}>
                <InputNumber addonBefore={t('持续')} addonAfter={t('天')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=' ' name={['duration', 'hourNum']}>
                <InputNumber addonBefore={t('持续')} addonAfter={t('小时')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=' ' name={['duration', 'minuteNum']}>
                <InputNumber addonBefore={t('持续')} addonAfter={t('分钟')} />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <div>
          <Table<TaskTableColumns>
            title={() => {
              return (
                <div className='flex justify-between'>
                  <div className='text-h4'>{t('任务列表')}</div>
                  <div className='flex gap-4'>
                    <Button
                      type='primary'
                      onClick={() =>
                        dispatchTaskTemplateModal({ type: 'open' })
                      }>
                      {t('选择并添加')}
                    </Button>

                    <Button
                      type='primary'
                      onClick={() => setAddNewTaskModalVisible(true)}>
                      {t('新建')}
                    </Button>
                  </div>
                </div>
              );
            }}
            bordered
            columns={taskColumns}
            dataSource={dataSource}
          />
        </div>
      </Form>
      <Modal {...innerModal}>
        <Form layout='inline' form={innerForm}>
          {/* <Form.Item label='顺序' name='index'>
            <Input className='w-[200px]' />
          </Form.Item> */}
          <Form.Item label={t('任务名称')} name='taskName'>
            <Input className='w-[200px]' />
          </Form.Item>
          <Form.Item label={t('描述')} name='description'>
            <Input className='w-[200px]' />
          </Form.Item>
        </Form>
      </Modal>
      <Modal {...taskTemplateModal}>
        <Form
          onFinish={async (values) => {
            await getTaskDetail(values.taskUuid);
            dispatchTaskTemplateModal({ type: 'close' });
          }}>
          <Form.Item label={t('任务模版')} name='taskUuid'>
            <Select options={taskTemplateList} style={{ width: 200 }} />
          </Form.Item>
          <Button type='primary' htmlType='submit'>
            {t('确定')}
          </Button>
        </Form>
      </Modal>
    </Modal>
  );
}
