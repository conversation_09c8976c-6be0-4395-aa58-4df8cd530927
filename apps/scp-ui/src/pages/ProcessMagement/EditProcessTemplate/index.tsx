import {
  Card,
  Button,
  Steps,
  Input,
  Table,
  Form,
  Checkbox,
  App,
  Select,
} from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd';
import { DeleteButton } from '@repo/ui';
import type { StepTableColumns, StepListItem } from './type';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import ajax from '@/api';
import CreateStepModal from './components/CreateStepModal';
import _ from 'lodash';
import { mapWeekdaysToDays, mapDaysToWeekdays } from './utils';
import { removeUndefinedValues } from '@/utils';
import { isNil } from '@/utils/isTypes';
import { t } from '@/languages';

//TODO: 保存接口需要进行重新对接， 目前部分修改存在问题。
export default function EditProcessTemplate() {
  const location = useLocation();
  const navigate = useNavigate();
  const processUuid =
    (location.state as { processUuid?: string })?.processUuid ?? undefined;
  const isEdit = !!processUuid;
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingStep, setEditingStep] = useState<StepListItem | null>(null);
  const [timeZoneList, setTimeZoneList] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);
  const [stepList, setStepList] = useState<StepListItem[]>([]);

  const handleCreate = () => {
    setEditingStep(null);
    setIsModalVisible(true);
  };

  const handleEdit = (stepUuid: React.Key) => {
    const _editStep = stepList.find((item) => {
      return item.stepUuid === stepUuid;
    });
    setEditingStep(_editStep || null);
    setIsModalVisible(true);
  };

  const columns: TableProps<StepTableColumns>['columns'] = [
    // {
    //   title: '单独标识',
    //   dataIndex: 'stepUuid',
    //   key: 'stepUuid',
    // },
    {
      title: t('步骤名称'),
      dataIndex: 'stepName',
      key: 'stepName',
    },
    {
      title: t('审阅人'),
      dataIndex: 'reviewerName',
      key: 'reviewerName',
    },
    {
      title: t('持续时间'),
      dataIndex: 'duringTime',
      key: 'duringTime',
    },
    {
      title: t('任务数目'),
      dataIndex: 'tasknums',
      key: 'tasknums',
    },
    {
      title: t('操作'),
      dataIndex: 'options',
      key: 'options',
      render: (_, record: StepTableColumns) => {
        return (
          <div>
            <Button type='link' onClick={() => handleEdit(record.stepUuid)}>
              {t('编辑')}
            </Button>
            <DeleteButton
              deleteTitle={t('确定删除吗') + '?'}
              type='link'
              onClick={() => {
                deleteCurrentStep(record.stepUuid);
              }}>
              {t('删除')}
            </DeleteButton>
          </div>
        );
      },
    },
  ];

  const getDefaultNumber = (
    value: number | null | undefined,
    defaultValue = 0,
  ): number => {
    return isNil(value) ? defaultValue : value;
  };
  const onSubmit = () => {
    const _values = form.getFieldsValue(true);
    console.log(_values, '_values');
    const values = {
      ..._values,
      workSchedule:
        _values.workSchedule && mapDaysToWeekdays(_values.workSchedule),
      stepList,
    };
    submitProcessTemplate(values);
  };

  /**
   * @description: 创建模版
   */

  const submitProcessTemplate = async (values: any) => {
    try {
      const res = await (isEdit
        ? ajax.updateProcessTemplate({ ...values, templateUuid: processUuid })
        : ajax.createProcessTemplate(values));
      if (!res.code) {
        message.info(t('保存成功'));
        navigate('/processManager/processTemplateManager');
      }
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * @description: 获取模版详情
   */
  const getProcessTemplateInfoByTemplateUuid = async () => {
    try {
      const res = await ajax.getProcessTemplateInfoByTemplateUuid({
        templateUuid: processUuid as string,
      });
      if (!res.code) {
        const data = _.get(res, 'data.result', {});
        form.setFieldsValue({
          templateName: data.templateName,
          timeZone: data.timeZone,
          description: data.description,
          workSchedule: mapWeekdaysToDays(data.workSchedule),
        });
        setStepList(data.stepList);
      }
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * @description 删除当前步骤
   */
  const deleteCurrentStep = (stepUuid: React.Key) => {
    const _stepList = stepList.filter((step) => {
      return step.stepUuid !== stepUuid;
    });
    setStepList(_stepList);
  };

  /**
   * @description: 获取时区列表
   */
  const getTimeZoneList = async () => {
    try {
      const res = await ajax.getSystemTimeZoneList();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setTimeZoneList(
          list.map((item: string) => {
            return {
              label: item,
              value: item,
            };
          }),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (processUuid) {
      getProcessTemplateInfoByTemplateUuid();
    }
    getTimeZoneList();
  }, [processUuid]);

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className='bg-white p-4 rounded-2xl flex flex-col gap-4'>
      <div className='flex justify-between'>
        <div className='text-h3 flex items-center gap-2'>
          <LeftOutlined
            className='text-blue-500 cursor-pointer'
            onClick={handleBack}
          />
          <span>{t('编辑模版')}</span>
        </div>
        <Button type='primary' onClick={onSubmit}>
          {t('保存')}
        </Button>
      </div>
      <Card title={<h1 className='text-h3'>{t('基本信息')}</h1>}>
        <Form
          form={form}
          layout='inline'
          className='mb-4 gap-4'
          colon={false}
          labelAlign='right'>
          <Form.Item
            label={<div className='text-h4'>{t('模版名称')}</div>}
            name='templateName'>
            <Input className='w-[200px]' />
          </Form.Item>
          <Form.Item
            label={<div className='text-h4'>{t('时区')}</div>}
            name='timeZone'>
            <Select style={{ width: 200 }} options={timeZoneList} />
          </Form.Item>
          <Form.Item
            label={<div className='text-h4'>{t('描述')}</div>}
            name='description'>
            <Input style={{ width: 200 }} />
          </Form.Item>
          {/* <Form.Item
            label={<div className='text-h4'>负责人</div>}
            name='owners'>
            <Select style={{ width: 200 }} />
          </Form.Item> */}
          <Form.Item
            label={<div className='text-h4'>{t('工作日')}</div>}
            name='workSchedule'>
            <Checkbox.Group>
              <Checkbox value={7}>{t('星期日')}</Checkbox>
              <Checkbox value={1}>{t('星期一')}</Checkbox>
              <Checkbox value={2}>{t('星期二')}</Checkbox>
              <Checkbox value={3}>{t('星期三')}</Checkbox>
              <Checkbox value={4}>{t('星期四')}</Checkbox>
              <Checkbox value={5}>{t('星期五')}</Checkbox>
              <Checkbox value={6}>{t('星期六')}</Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Card>
      <Table
        title={() => (
          <div className='text-h4 flex justify-between'>
            <div className='w-[100px]'>{t('步骤列表')}</div>
            <Steps
              className='w-4/5'
              current={0}
              items={stepList.map((item, index) => ({
                title: <div key={index}>{item.stepName}</div>,
              }))}
            />
            <Button type='primary' onClick={handleCreate}>
              {t('新建')}
            </Button>
          </div>
        )}
        bordered
        columns={columns}
        dataSource={stepList.map((item, index) => {
          return removeUndefinedValues({
            key: index,
            index: index,
            stepName: item.stepName,
            stepUuid: item.stepUuid,
            owner: item.reviewerId,
            reviewerName: item.reviewerName,
            duringTime:
              getDefaultNumber(item.duration.weekNum) +
              t('周') +
              getDefaultNumber(item.duration.dayNum) +
              t('天') +
              getDefaultNumber(item.duration.hourNum) +
              t('小时') +
              getDefaultNumber(item.duration.minuteNum) +
              t('分钟'),
            tasknums: item.taskList.length,
          }) as StepTableColumns;
        })}
      />
      <CreateStepModal
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        addTableItem={(item: StepListItem) => {
          setStepList([...stepList, item]);
        }}
        // TODO: 更改的逻辑需要重新设计，目前存在重复修改的问题，需要进行优化
        // FIXED: 已经修复
        changeTableItem={(item: StepListItem) => {
          const _stepList = [...stepList];
          const index = _stepList.findIndex((step) => {
            return step.stepUuid === item.stepUuid; // 使用不同的变量名 step
          });
          if (index !== -1) {
            // 确保找到目标项
            _stepList[index] = item;
            setStepList(_stepList);
          } else {
            console.error(t('未找到对应的 stepUuid'));
          }
        }}
        step={editingStep}
        isEdit={!!editingStep}
      />
    </div>
  );
}
