import ajax from '@/api';
import { useNavigate } from 'react-router-dom';
import { Select, Form, Table, Button, Input, Card, Space } from 'antd';
import type { TableProps } from 'antd';
import { type OwnerListType, TableDataType } from './types';
import { useEffect, useState } from 'react';
import { DeleteButton } from '@repo/ui';
import { useActivate } from 'react-activation';
import { t } from '@/languages';
import _ from 'lodash';

export default function ProcessTempalteManager() {
  const [form] = Form.useForm();

  const [ownerList, setOwnerList] = useState<OwnerListType[]>([]);
  const [tableData, setTableData] = useState<TableDataType[]>([]);
  const [pageData, setPageData] = useState<{
    current: number;
    pageSize: number;
    total: number;
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const navigate = useNavigate();
  //TODO: 对接相应的字段，目前修改时间没有对接
  const columns: TableProps<TableDataType>['columns'] = [
    {
      title: t('模板名称'),
      dataIndex: 'templateName',
      key: 'templateName',
    },
    {
      title: t('模版描述'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('模版负责人'),
      dataIndex: 'ownerName',
      key: 'ownerName',
    },
    // {
    //   title: '修改时间',
    //   dataIndex: 'changeTime',
    //   key: 'changeTime',
    // },
    {
      title: t('步骤数'),
      dataIndex: 'stepNums',
      key: 'stepNums',
    },
    {
      title: t('操作'),
      dataIndex: 'options',
      key: 'options',
      render: (_: any, record: any) => {
        return (
          <div>
            <Button
              type='link'
              onClick={() => {
                navigate('/editProcessTemplate', {
                  state: { processUuid: record.templateUuid },
                });
              }}>
              {t('编辑(查看)')}
            </Button>
            <Button type='link'>{t('复制')}</Button>
            <DeleteButton
              deleteTitle={t('确定删除吗')}
              type='link'
              onClick={() => {
                ajax.deleteProcessTemplate(record.templateUuid).then((res) => {
                  if (!res.code) getProcessTalbeData();
                });
              }}>
              {t('删除')}
            </DeleteButton>
          </div>
        );
      },
    },
  ].map((item) => {
    return {
      ...item,
      align: 'center',
    };
  });

  /**
   * @description get owner list
   * @return {viod}
   */
  const getUserList = async () => {
    try {
      const res = await ajax.getUserListByCoIdContainsSelf();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setOwnerList(
          list.map((item: any) => ({
            label: item.nickName,
            value: item.id,
          })),
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * @description get Table data
   * @return {viod}
   */
  const getProcessTalbeData = async () => {
    try {
      const rawValues = {
        ...form.getFieldsValue(),
        page: pageData.current,
        pageSize: pageData.pageSize,
      };
      const values = Object.fromEntries(
        Object.entries(rawValues).filter(([, v]) => v !== undefined),
      );
      const res = await ajax.getProcessTalbeData(values);
      if (res.code !== 0) {
        console.error(res.msg);
      }
      // 为每条数据添加唯一的 key
      setTableData(
        res.data.list.map((item: any) => ({
          ...item,
          key: item.templateUuid, // 使用 templateUuid 作为唯一的 key
        })),
      );
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getUserList();
    getProcessTalbeData();
  }, []);

  useActivate(() => {
    getProcessTalbeData();
  });

  return (
    <div className='bg-white rounded-2xl p-4 flex flex-col gap-4'>
      <div className='text-h3'>{t('管理流程模板')}</div>
      <Card>
        <Form layout='inline' form={form}>
          <Form.Item
            name='templateName'
            label={<div className='text-h4'>{t('模糊搜索')}</div>}>
            <Input placeholder={t('请输入模板名称')} />
          </Form.Item>
          <Form.Item
            name='ownerId'
            label={<div className='text-h4'>{t('所有人')}</div>}>
            <Select
              options={ownerList}
              style={{ width: '200px' }}
              placeholder={t('请选择所有人')}
            />
          </Form.Item>
          {/* 添加搜索和重置按钮 */}
          <Form.Item>
            <Space>
              <Button type='primary' onClick={getProcessTalbeData}>
                {t('搜索')}
              </Button>
              <Button htmlType='reset'>{t('重置')}</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <main>
        <Table<TableDataType>
          bordered
          title={() => (
            <div className='flex justify-between'>
              <div className=' text-h4'>{t('流程模板列表')}</div>
              <div className='option'>
                <Button
                  type='primary'
                  onClick={() => {
                    navigate('/editProcessTemplate');
                  }}>
                  {t('新建')}
                </Button>
              </div>
            </div>
          )}
          columns={columns}
          dataSource={tableData}
          pagination={{
            current: pageData.current,
            pageSize: pageData.pageSize,
            position: ['bottomCenter'],
            total: pageData.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPageData({
                ...pageData,
                current: page,
                pageSize,
              });
            },
            onShowSizeChange: (_, size) => {
              setPageData({
                ...pageData,
                pageSize: size,
              });
            },
          }}
        />
      </main>
    </div>
  );
}
