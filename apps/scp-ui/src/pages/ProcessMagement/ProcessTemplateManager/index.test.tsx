import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import ProcessTemplateManager from '.';
import userEvent from '@testing-library/user-event';
import { HttpResponse, http } from 'msw';
import { server } from '@/mock/server';
import store from '@/store/index.ts';

describe('ProcessTemplateManager', () => {
  it('should render page', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(screen.getByText('管理流程模板')).toBeInTheDocument();
  });

  describe('filter render', () => {
    it('should render with filter', () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      expect(
        screen.getByText(/所有人/i, {
          selector: 'div',
        }),
      ).toBeInTheDocument();
    });

    it('should render with select options', async () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );

      userEvent.click(screen.getByRole('combobox'));

      await waitFor(() => {
        expect(screen.getAllByText(/test/i).length).toBeGreaterThan(10);
      });
    });

    it('if get null data for owner select', async () => {
      server.use(
        http.get('api/processTemplate/getOwnerList', () => {
          return HttpResponse.json({
            code: 0,
            data: {
              list: [],
            },
          });
        }),
      );
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      userEvent.click(screen.getByRole('combobox'));
      await waitFor(() => {
        expect(screen.queryAllByText(/test/i).length).toEqual(0);
      });
    });
  });

  describe('table render', () => {
    it('should render with table', async () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });
    });

    it('should render with table data', async () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      await waitFor(() => {
        expect(screen.getAllByRole('row').length).toBeGreaterThan(10);
      });
    });

    it('if get null data for table', async () => {
      server.use(
        http.get('/api/processTemplate/getTableData', () => {
          return HttpResponse.json({
            code: 0,
            data: {
              list: [],
            },
          });
        }),
      );
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      await waitFor(() => {
        expect(
          screen.getByText('No data', {
            selector: 'div',
          }),
        ).toBeInTheDocument();
      });
    });

    it('render options button', async () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      await waitFor(() => {
        expect(screen.queryAllByText(/删除/i).length).toBeGreaterThan(9);
      });
    });

    it('click page button', async () => {
      render(
        <Provider store={store}>
          <Router>
            <ProcessTemplateManager />
          </Router>
        </Provider>,
      );
      await waitFor(() => {
        userEvent.click(
          screen.getByText('2', {
            selector: 'a',
          }),
        );
        expect(screen.getByText('19')).toBeInTheDocument();
      });
    });
  });
});
