import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import ProcessTemplateManager from '.';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';

import store from '@/store/index.ts';

describe('ProcessTemplateManager', () => {
  it('should render page', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(screen.getByText('管理流程')).toBeInTheDocument();
  });
  it('should render with filter', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(
      screen.getByText(/所有人/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
  });
  it('should render tabel', () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    expect(screen.getAllByRole('table')).toHaveLength(1);
  });

  it('jump to edit page', async () => {
    render(
      <Provider store={store}>
        <Router>
          <ProcessTemplateManager />
        </Router>
      </Provider>,
    );
    await fireEvent.click(screen.getByText('新 建'));

    await waitFor(() => {
      expect(window.location.pathname).toBe('/runProcess'); // 根据实际的路径进行修改
    });
  });
});
