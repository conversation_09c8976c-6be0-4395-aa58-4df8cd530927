import ajax from '@/api';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Select, Form, Table, Button, Input, Card, App, Flex } from 'antd';
import { type OwnerListType, ProcessListDataType } from './types';
import { useEffect, useState, useRef } from 'react';
import { useSize } from 'ahooks';
import { useActivate } from 'react-activation';
import { t } from '@/languages';
import _ from 'lodash';

export default function ManagerProcess() {
  const { message } = App.useApp();

  const [ownerList, setOwnerList] = useState<OwnerListType[]>([]);
  const [chosenOwnerId, setChosenOwnerId] = useState<number>();
  const [tableData, setTableData] = useState<ProcessListDataType[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [pageData, setPageData] = useState<{
    current: any;
    pageSize: any;
    total: any;
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const navigate = useNavigate();

  const startProcessInstance = async (instanceUuid: any) => {
    try {
      ajax
        .startProcessInstance({
          instanceUuid: instanceUuid,
        })
        .then((res) => {
          if (res.code !== 0) {
            console.error(res.msg);
            message.error(res.msg);
            return;
          }
          message.success(t('流程实例启动成功'));
          getProcessInstanceList();
        });
    } catch (error) {
      console.error(error);
    }
  };

  const columns: any = [
    {
      title: t('流程名称'),
      dataIndex: 'instanceName',
      key: 'instanceName',
    },
    {
      title: t('流程描述'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('流程所有人'),
      dataIndex: 'ownerId',
      key: 'ownerId',
      render: (text: any) => {
        const owner = ownerList.find((item) => item.value === text);
        return <span>{owner ? owner.label : ''}</span>;
      },
    },
    {
      title: t('状态'),
      dataIndex: 'instanceStateName',
      key: 'instanceStateName',
    },
    {
      title: t('预计开始时间'),
      dataIndex: 'expectedStartTime',
      key: 'expectedStartTime',
    },
    {
      title: t('预计结束时间'),
      dataIndex: 'expectedEndTime',
      key: 'expectedEndTime',
    },
    {
      title: t('实际开始时间'),
      dataIndex: 'actualStartTime',
      key: 'actualStartTime',
    },
    {
      title: t('实际结束时间'),
      dataIndex: 'actualEndTime',
      key: 'actualEndTime',
    },
    {
      title: t('操作'),
      dataIndex: 'options',
      key: 'options',
      fixed: 'right',
      render: (_: any, record: any) => {
        return (
          <div>
            <Button
              type='link'
              style={{
                display: record.instanceState === 1 ? 'inline-block' : 'none',
              }}
              onClick={() => {
                startProcessInstance(record.instanceUuid);
              }}>
              {t('启动')}
            </Button>
            <Button
              type='link'
              onClick={() => {
                navigate(
                  '/runProcess?templateUuid=' +
                    record.templateUuid +
                    '&instanceUuid=' +
                    record.instanceUuid +
                    '&isCreate=false',
                );
              }}>
              {t('编辑')}
            </Button>
            <Button
              type='link'
              onClick={() => {
                deleteProcessInstance(record.instanceUuid);
              }}>
              {t('删除')}
            </Button>
          </div>
        );
      },
    },
  ].map((item) => {
    return {
      ...item,
      align: 'center',
    };
  });

  /**
   * @description get owner list
   * @return {viod}
   */
  const getOwnerList = async () => {
    try {
      const res = await ajax.getUserListByCoIdContainsSelf();
      if (res.code !== 0) {
        console.error(res.msg);
        message.error(res.msg);
        return;
      }
      const data = res.data.list;
      setOwnerList(
        data.map((item: any) => ({
          label: item.nickName,
          value: item.id,
        })),
      );
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * @description get Table data
   * @return {viod}
   */
  const getProcessInstanceList = async () => {
    try {
      const res = await ajax.getProcessInstanceList({
        page: pageData.current,
        pageSize: pageData.pageSize,
        ...(chosenOwnerId ? { ownerId: chosenOwnerId } : null),
        ...(searchValue !== ''
          ? {
              instanceName: searchValue,
            }
          : null),
      });
      if (res.code !== 0) {
        console.error(res.msg);
        message.error(res.msg);
        return;
      }
      setTableData(res.data.list);
      setPageData({
        current: res.data.page,
        pageSize: res.data.pageSize,
        total: res.data.total,
      });
    } catch (error) {
      console.error(error);
    }
  };

  const deleteProcessInstance = async (instanceUuid: any) => {
    try {
      ajax
        .deleteProcessInstance({
          instanceUuid: instanceUuid,
        })
        .then((res) => {
          if (res.code !== 0) {
            console.error(res.msg);
            message.error(res.msg);
            return;
          }
          message.success(t('删除流程实例成功'));
          getProcessInstanceList();
        });
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getProcessInstanceList();
  }, [pageData.current]);

  useEffect(() => {
    getOwnerList();
    getProcessInstanceList();
  }, []);

  useActivate(() => {
    getOwnerList();
    getProcessInstanceList();
  });

  const mainRef = useRef<HTMLDivElement>(null);
  const mainSize = useSize(mainRef);

  return (
    <div className='bg-white rounded-2xl gap-4 flex flex-col p-4'>
      <div className='text-h3'>{t('管理流程')}</div>
      <Card>
        <Form layout='inline'>
          <Form.Item label={<div className='text-h4'>{t('模糊搜索')}</div>}>
            <Input
              value={searchValue}
              placeholder={t('请输入流程实例名称')}
              onChange={(e) => setSearchValue(e.target.value)}
              onKeyDown={(e) => {
                e.key === 'Enter' ? getProcessInstanceList() : null;
              }}
            />
          </Form.Item>
          <Form.Item label={<div className='text-h4'>{t('所有人')}</div>}>
            <Select
              options={ownerList}
              placeholder={t('请选择所有人')}
              style={{
                width: '200px',
              }}
              value={chosenOwnerId}
              onChange={(value) => setChosenOwnerId(value)}
            />
          </Form.Item>
          <Form.Item>
            <Flex gap={10}>
              <Button type='primary' onClick={() => getProcessInstanceList()}>
                {t('搜索')}
              </Button>
              <Button
                onClick={() => {
                  setSearchValue('');
                  setChosenOwnerId(undefined);
                }}>
                {t('重置')}
              </Button>
            </Flex>
          </Form.Item>
        </Form>
      </Card>
      <main ref={mainRef}>
        <Table<ProcessListDataType>
          bordered
          title={() => (
            <div className='flex justify-between'>
              <div className=' text-h4'>{t('流程列表')}</div>
              <div className='option'>
                <Button
                  type='primary'
                  onClick={() => {
                    navigate('/runProcess?isCreate=true');
                  }}>
                  {t('新建')}
                </Button>
              </div>
            </div>
          )}
          columns={columns.map((item: any) => ({
            ...item,
            minWidth: 150,
          }))}
          tableLayout='auto'
          dataSource={tableData}
          scroll={{ x: (mainSize?.width || 0) - 2 }}
          pagination={{
            current: pageData.current,
            pageSize: pageData.pageSize,
            position: ['bottomCenter'],
            total: pageData.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPageData({
                ...pageData,
                current: page,
                pageSize,
              });
            },
            onShowSizeChange: (_, size) => {
              setPageData({
                ...pageData,
                pageSize: size,
              });
            },
          }}
        />
      </main>
    </div>
  );
}
