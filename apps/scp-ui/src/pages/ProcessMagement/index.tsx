import React from 'react';
export default {
  ProcessTempalteManager: React.lazy(() => import('./ProcessTemplateManager')),
  ManagerProcess: React.lazy(() => import('./ManagerProcess')),
  MyTaskPage: React.lazy(() => import('./MyTaskPage')),
  ProcessTemplateManager: React.lazy(() => import('./ProcessTemplateManager')),
  EditProcessTemplate: React.lazy(() => import('./EditProcessTemplate')),
  RunProcess: React.lazy(() => import('./RunProcess')),
};
