import JWForm from '@/components/JWForm';
import { useState, useEffect } from 'react';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

const ViewMasterData: React.FC = () => {
  const location = useLocation();
  const { t } = useVoerkaI18n();

  const co = {
    factorySupplies: {
      columns: [
        {
          title: t('物料'),
          dataIndex: 'matnr',
          key: 'matnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.matnr.localeCompare(b.matnr),
        },
        {
          title: t('地点'),
          dataIndex: 'locno',
          key: 'locno',
          isFilter: true,
          sorter: (a: any, b: any) => a.locno.localeCompare(b.locno),
        },
        {
          title: t('收获期(计划颗粒度)'),
          dataIndex: 'receiptTimeInDays',
          key: 'receiptTimeInDays',
          sorter: (a: any, b: any) => a.receiptTimeInDays - b.receiptTimeInDays,
        },
        {
          title: t('物料计划员'),
          dataIndex: 'dispo',
          key: 'dispo',
          isFilter: true,
          sorter: (a: any, b: any) => a.dispo.localeCompare(b.dispo),
        },
        {
          title: t('补货类型'),
          dataIndex: 'procuType',
          key: 'procuType',
          isFilter: true,
          sorter: (a: any, b: any) => a.procuType.localeCompare(b.procuType),
        },
        // {
        //   title: t('库存持有成本'),
        //   dataIndex: 'custAttr01',
        //   key: 'custAttr01',
        //   isFilter: true,
        //   sorter: (a: any, b: any) => a.custAttr01 - b.custAttr01,
        // },
      ],
    },
    transportAndProcurementTypes: {
      columns: [
        {
          title: t('物料'),
          dataIndex: 'matnr',
          key: 'matnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.matnr.localeCompare(b.matnr),
        },
        {
          title: t('工厂-从'),
          dataIndex: 'locnoFrom',
          key: 'locnoFrom',
          isFilter: true,
          sorter: (a: any, b: any) => a.locnoFrom.localeCompare(b.locnoFrom),
        },
        {
          title: t('工厂-从-类型'),
          dataIndex: 'loctypeFrom',
          key: 'loctypeFrom',
          isFilter: true,
          sorter: (a: any, b: any) =>
            a.loctypeFrom.localeCompare(b.loctypeFrom),
        },
        {
          title: t('工厂-至'),
          dataIndex: 'locnoTo',
          key: 'locnoTo',
          isFilter: true,
          sorter: (a: any, b: any) => a.locnoTo.localeCompare(b.locnoTo),
        },
        {
          title: t('工厂-至-类型'),
          dataIndex: 'loctypeTo',
          key: 'loctypeTo',
          isFilter: true,
          sorter: (a: any, b: any) => a.loctypeTo.localeCompare(b.loctypeTo),
        },
        {
          title: t('有效-从'),
          dataIndex: 'validFrom',
          key: 'validFrom',
        },
        {
          title: t('有效-至'),
          dataIndex: 'validTo',
          key: 'validTo',
        },
        {
          title: t('计划交货时间'),
          dataIndex: 'plannedDelTime',
          key: 'plannedDelTime',
        },
        {
          title: 'Min Lot',
          dataIndex: 'minLot',
          key: 'minLot',
          sorter: (a: any, b: any) => a.minLot - b.minLot,
        },
        {
          title: 'Max Lot',
          dataIndex: 'maxLot',
          key: 'maxLot',
          sorter: (a: any, b: any) => a.maxLot - b.maxLot,
        },
        {
          title: 'Rounding Lot',
          dataIndex: 'roundingLot',
          key: 'roundingLot',
          sorter: (a: any, b: any) => a.roundingLot - b.roundingLot,
        },
        {
          title: t('禁用'),
          dataIndex: 'isBlocked',
          key: 'isBlocked',
          isFilter: true,
          sorter: (a: any, b: any) => a.isBlocked.localeCompare(b.isBlocked),
        },
        {
          title: t('运输模式'),
          dataIndex: 'motName',
          key: 'motName',
          isFilter: true,
          sorter: (a: any, b: any) => a.motName.localeCompare(b.motName),
        },
        {
          title: t('运输转运成本'),
          dataIndex: 'custAttr01',
          key: 'custAttr01',
          isFilter: true,
          sorter: (a: any, b: any) => a.custAttr01 - b.custAttr01,
        },
      ],
    },
    productionSourceParent: {
      columns: [
        {
          title: t('生产源'),
          dataIndex: 'ppSosId',
          key: 'ppSosId',
          isFilter: true,
          sorter: (a: any, b: any) => a.ppSosId.localeCompare(b.ppSosId),
        },
        {
          title: 'sourceId',
          dataIndex: 'sourceId',
          key: 'sourceId',
          isFilter: true,
          sorter: (a: any, b: any) => a.sourceId - b.sourceId,
        },
        {
          title: t('有效-从'),
          dataIndex: 'validFrom',
          key: 'validFrom',
        },
        {
          title: t('有效-至'),
          dataIndex: 'validTo',
          key: 'validTo',
        },
        {
          title: t('物料'),
          dataIndex: 'matnr',
          key: 'matnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.matnr.localeCompare(b.matnr),
        },
        {
          title: t('地点'),
          dataIndex: 'locno',
          key: 'locno',
          isFilter: true,
          sorter: (a: any, b: any) => a.locno.localeCompare(b.locno),
        },
        {
          title: t('产出量'),
          dataIndex: 'outQuantity',
          key: 'outQuantity',
          sorter: (a: any, b: any) => a.outQuantity - b.outQuantity,
        },
        {
          title: 'Min Lot',
          dataIndex: 'minLot',
          key: 'minLot',
          sorter: (a: any, b: any) => a.minLot - b.minLot,
        },
        {
          title: 'Max Lot',
          dataIndex: 'maxLot',
          key: 'maxLot',
          sorter: (a: any, b: any) => a.maxLot - b.maxLot,
        },
        {
          title: 'Rounding Lot',
          dataIndex: 'roundingLot',
          key: 'roundingLot',
          sorter: (a: any, b: any) => a.roundingLot - b.roundingLot,
        },
        {
          title: t('损耗率'),
          dataIndex: 'scrap',
          key: 'scrap',
          sorter: (a: any, b: any) => a.scrap - b.scrap,
        },
        {
          title: t('生产成本'),
          dataIndex: 'custAttr01',
          key: 'custAttr01',
          isFilter: true,
          sorter: (a: any, b: any) => a.custAttr01 - b.custAttr01,
        },
      ],
    },
    productionSourceChild: {
      columns: [
        {
          title: t('生产源'),
          dataIndex: 'ppSosId',
          key: 'ppSosId',
          isFilter: true,
          sorter: (a: any, b: any) => a.ppSosId.localeCompare(b.ppSosId),
        },
        {
          title: t('条目信息'),
          dataIndex: 'itemId',
          key: 'itemId',
          isFilter: true,
          sorter: (a: any, b: any) => a.itemId.localeCompare(b.itemId),
        },
        {
          title: t('有效-从'),
          dataIndex: 'validFrom',
          key: 'validFrom',
        },
        {
          title: t('有效-至'),
          dataIndex: 'validTo',
          key: 'validTo',
        },
        {
          title: t('物料'),
          dataIndex: 'matnr',
          key: 'matnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.matnr.localeCompare(b.matnr),
        },
        {
          title: t('消耗量'),
          dataIndex: 'inQuantity',
          key: 'inQuantity',
          sorter: (a: any, b: any) => a.inQuantity - b.inQuantity,
        },
        {
          title: 'Activity',
          dataIndex: 'activityId',
          key: 'activityId',
          isFilter: true,
          sorter: (a: any, b: any) => a.activityId.localeCompare(b.activityId),
        },
      ],
    },
    productionSourceActivity: {
      columns: [
        {
          title: t('生产源'),
          dataIndex: 'ppSosId',
          key: 'ppSosId',
          isFilter: true,
          sorter: (a: any, b: any) => a.ppSosId.localeCompare(b.ppSosId),
        },
        {
          title: t('Activity'),
          dataIndex: 'activityId',
          key: 'activityId',
          isFilter: true,
          sorter: (a: any, b: any) => a.activityId.localeCompare(b.activityId),
        },
        {
          title: t('Activity 持续时间'),
          dataIndex: 'actDurFix',
          key: 'actDurFix',
          sorter: (a: any, b: any) => a.actDurFix - b.actDurFix,
        },
        {
          title: t('Activity 描述'),
          dataIndex: 'actText',
          key: 'actText',
          isFilter: true,
          sorter: (a: any, b: any) => a.actText.localeCompare(b.actText),
        },
        {
          title: t('Activity 序号'),
          dataIndex: 'seqnr',
          key: 'seqnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.seqnr - b.seqnr,
        },
      ],
    },
    capacityDirection: {
      columns: [
        {
          title: t('生产源'),
          dataIndex: 'ppSosId',
          key: 'ppSosId',
          isFilter: true,
          sorter: (a: any, b: any) => a.ppSosId.localeCompare(b.ppSosId),
        },
        {
          title: t('产线名'),
          dataIndex: 'resName',
          key: 'resName',
          isFilter: true,
          sorter: (a: any, b: any) => a.resName.localeCompare(b.resName),
        },
        {
          title: t('有效-从'),
          dataIndex: 'validFrom',
          key: 'validFrom',
        },
        {
          title: t('有效-至'),
          dataIndex: 'validTo',
          key: 'validTo',
        },
        {
          title: t('消耗量'),
          dataIndex: 'consQty',
          key: 'consQty',
          sorter: (a: any, b: any) => a.consQty - b.consQty,
        },

        {
          title: t('产出量'),
          dataIndex: 'outQty',
          key: 'outQty',
          sorter: (a: any, b: any) => a.outQty - b.outQty,
        },
      ],
    },
    ColieProcess: {
      columns: [
        {
          title: t('地点'),
          dataIndex: 'locno',
          key: 'locno',
          isFilter: true,
          width: 200,
          sorter: (a: any, b: any) => a.itemDesc.localeCompare(b.itemDesc),
        },
        {
          title: t('物料'),
          dataIndex: 'matnr',
          key: 'matnr',
          isFilter: true,
          sorter: (a: any, b: any) => a.itemDesc.localeCompare(b.itemDesc),
        },
        {
          title: t('工序'),
          dataIndex: 'workingProcedure',
          key: 'workingProcedure',
          isFilter: true,
          sorter: (a: any, b: any) => a.itemDesc.localeCompare(b.itemDesc),
        },
        {
          title: t('辅助列'),
          dataIndex: 'auxiliaryColumn',
          key: 'auxiliaryColumn',
          isFilter: true,
        },
        {
          title: t('标准工时'),
          dataIndex: 'standardWorkingHours',
          key: 'standardWorkingHours',
          isFilter: true,
        },
        {
          title: t('节拍时间'),
          dataIndex: 'tactTime',
          key: 'tactTime',
          isFilter: true,
        },
      ],
    },
    Sass: {
      columns: [
        {
          title: t('顾客'),
          dataIndex: 'customer',
          key: 'customer',
          isFilter: true,
          sorter: (a: any, b: any) => a.customer.localeCompare(b.customer),
        },
        {
          title: t('客户类别'),
          dataIndex: 'customerCategory',
          key: 'customerCategory',
          isFilter: true,
          sorter: (a: any, b: any) =>
            a.customerCategory.localeCompare(b.customerCategory),
        },
        {
          title: t('订阅计划'),
          dataIndex: 'subscriptionPlan',
          key: 'subscriptionPlan',
          isFilter: true,
          sorter: (a: any, b: any) =>
            a.subscriptionPlan.localeCompare(b.subscriptionPlan),
        },
        {
          title: t('产品类别'),
          dataIndex: 'productCategory',
          key: 'productCategory',
          isFilter: true,
          sorter: (a: any, b: any) =>
            a.productCategory.localeCompare(b.productCategory),
        },
      ],
    },
  };
  const menuTag = location.pathname.split('/').pop();

  const [columns, setColumns] = useState<any[]>([]);
  const language = useSelector((state: any) => state.layout.language);

  const getColumn = (mdId: string) => {
    switch (mdId) {
      case 'LOC_MAT_MD':
        setColumns(co.factorySupplies.columns);
        break;
      case 'TRANS_PUR_MD':
        setColumns(co.transportAndProcurementTypes.columns);
        break;
      case 'PROD_H_MD':
        setColumns(co.productionSourceParent.columns);
        break;
      case 'PROD_C_MD':
        setColumns(co.productionSourceChild.columns);
        break;
      case 'PROD_A_MD':
        setColumns(co.productionSourceActivity.columns);
        break;
      case 'CAP_DIR_MD':
        setColumns(co.capacityDirection.columns);
        break;
      case 'PROC_MD':
        setColumns(co.ColieProcess.columns);
        break;
      case 'SVC_CHG_MD':
        setColumns(co.Sass.columns);
        break;
    }
  };

  useEffect(() => {
    getColumn(menuTag || '');
  }, [language]);

  return (
    <JWForm
      pageName={menuTag}
      columns={columns}
      callback={() => {
        getColumn(menuTag || '');
      }}
      watchTagName={'mdType'}
    />
  );
};

export default ViewMasterData;
