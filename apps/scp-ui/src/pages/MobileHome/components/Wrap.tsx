import Options from './Options';
import Charts from './Charts';
import React, { useState } from 'react';

type PropsType = {
  viewType: any;
};

const Wrap: React.FC<PropsType> = (props) => {
  const { viewType } = props;
  const [chartsData, setChartsData] = useState<any>({});
  const [chartsDitle, setChartsDitle] = useState<string>('');
  const [allItemArr, setAllItemArr] = useState<any>([]);
  const getTitle = (chartsTitle: any) => {
    setChartsDitle(chartsTitle);
  };
  const getChartsData = (values: any) => {
    if (values.list.value) {
      setChartsData(values);
    } else {
      setChartsData({});
    }
  };
  const getParamsData = (values: any) => {
    const _allItemArr: any[] = [];
    values.map((item: any) => {
      _allItemArr.push(...item.itemList);
    });
    setAllItemArr([..._allItemArr]);
  };
  return (
    <div className='mb-4'>
      <Options
        viewType={viewType}
        getChartsData={getChartsData}
        getParamsData={getParamsData}
        getTitle={getTitle}
      />
      <div className='w-full flex h-1'>
        <div
          className='w-full'
          style={{
            backgroundImage: 'linear-gradient(90deg,rgb(244 244 245),#fff',
          }}></div>
      </div>
      <div className='w-full flex h-1'>
        <div
          className='w-full'
          style={{
            backgroundImage:
              'linear-gradient(90deg,#fff,rgb(59 130 246 / 0.5))',
          }}></div>
      </div>
      <Charts
        chartsData={chartsData}
        viewType={viewType}
        allItemArr={allItemArr}
        chartsDitle={chartsDitle}
      />
    </div>
  );
};
export default Wrap;
