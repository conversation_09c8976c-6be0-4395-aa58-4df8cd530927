import ReactEcharts from 'echarts-for-react';
import { Button } from '@nextui-org/react';
import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';

type PropsType = {
  chartsData: any;
  viewType: any;
  allItemArr: any;
  chartsDitle: string;
};

const Charts: React.FC<PropsType> = (props) => {
  const { chartsData, viewType, allItemArr, chartsDitle } = props;
  const [data, setData] = useState<any>({});
  const [title, setTitle] = useState<any>('');
  const navigate = useNavigate();
  const goToChartPlus = () => {
    navigate(
      '/chart-plus?chartsInfo=' +
        `${JSON.stringify(data)}` +
        '&viewType=' +
        `${viewType.value}`,
    );
  };

  useEffect(() => {
    if (chartsData && chartsData.list && chartsData.list.value) {
      let name: string = '';
      const series: any[] = [];
      chartsData.list.value.map((item: any) => {
        if (item.key === 'salesIssueOrder') {
          name = '销售出库单数量';
        } else if (item.key === 'productValue') {
          name = '产值单数量';
        } else {
          allItemArr.map((i: any) => {
            if (i.key === item.key) {
              name = i.value;
            }
          });
        }
        series.push({
          name: name,
          type: 'bar',
          data: item.value,
        });
      });
      const options = {
        xAxis: {
          data: chartsData.list.date || [],
        },
        yAxis: {},
        series: series,
        tooltip: {
          confine: true,
          triggerOn: 'click',
          showContent: true,
          formatter: function (params: any) {
            return (
              '名称：' +
              params.seriesName +
              '<br/>时间：' +
              params.name +
              '<br/>数量：' +
              params.value
            );
          },
          trigger: 'item',
        },
        legend: {
          type: 'plain',
          show: true,
        },
      };
      setData(options);
    }
  }, [chartsData]);
  useEffect(() => {
    setTitle(chartsDitle || viewType.label);
  }, [chartsDitle]);
  return (
    <div className='bg-white p-4 pb-0' id={'wrap'}>
      <div className='flex justify-between h-8'>
        <div>{title}</div>
        <Button size={'sm'} onClick={goToChartPlus} style={{ zIndex: '9' }}>
          查看详情
        </Button>
      </div>
      <ReactEcharts
        notMerge={true}
        option={data}
        style={{ height: '13rem', width: '100%' }}
      />
    </div>
  );
};
export default Charts;
