import { Form, Segmented, ConfigProvider } from 'antd';
import React, { useState, useEffect } from 'react';
import { Select, SelectItem, Checkbox, cn } from '@nextui-org/react';
import MySelect from '@/components/MySelect';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import MyDatePicker from '@/components/DatePicker';
import ajax from '@/api';
import SingleSelect from '@/components/SingleSelect';
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');
dayjs.extend(customParseFormat);

type PropsType = {
  viewType: any;
  getChartsData: any;
  getParamsData: any;
  getTitle: any;
};

const Options: React.FC<PropsType> = (props) => {
  const { viewType, getChartsData, getParamsData, getTitle } = props;
  const [initialForm] = Form.useForm();
  const [timeForm] = Form.useForm();
  const [extendForm] = Form.useForm();
  const [currentViewType, setCurrentViewType] = useState<any>({});

  // 参数数据
  const [paramsList, setParamsList] = useState<any[]>([]);
  // 视图下拉框数据
  const [viewList, setViewList] = useState<any[]>([]);

  const [viewTypeDisabledKeys, setViewTypeDisabledKeys] = useState<any>('0');
  const [sortDisabledKeys, setSortDisabledKeys] = useState<any>('0');

  const [sortList, setSortList] = useState<any>([]);
  const [extendList, setExtendList] = useState<any>([]);
  const [chooseFlag, setChooseFlag] = useState<number>(0);

  const [getChartsDataParams, setGetChartsDataParams] = useState<any>({
    paPvId: 3,
    viewType: '',
    date: [],
    dateType: '',
    value: [
      {
        key: '',
        type: 'IN',
        content: '',
      },
    ],
  });

  /**
   * @description:获取参数数据
   */
  const getViewList = async () => {
    const defPaPvId = Number(localStorage.getItem('defPaPvId'));

    // 检查 defPaPvId 是否为 null
    if (!defPaPvId) {
      console.error('defPaPvId is null.'); // 可以添加错误处理逻辑
      return; // 提前返回，不继续后续逻辑
    }

    const res = (await ajax.getViewList({ paPvId: defPaPvId })) as any;
    if (res.code === 0) {
      setViewList(res.data);
      res.data.map((item: any) => {
        if (viewType.key === item.key) {
          setViewTypeDisabledKeys(viewType.key);
        }
      });
    }
  };
  const getParams = async (values: any) => {
    setExtendList([]);
    const params = {
      viewType: values.key,
      paPvId: Number(localStorage.getItem('defPaPvId')) || undefined,
    };
    const res = (await ajax.getParams(params)) as any;
    if (res.code === 0) {
      const _paramsList: any[] = [];
      res.data.map((item: any) => {
        if (values.attributeList.includes(item.key)) {
          _paramsList.push({
            ...item,
            isChoose: false,
            lineThrough: false,
          });
        }
      });
      setParamsList([..._paramsList]);
      getParamsData(_paramsList);
    }
  };

  /**
   * @description:修改部分参数重新获取参数数据并更新数据
   */
  // 修改视图类型
  const viewTypeChange = (values: any) => {
    setSortList([]);
    for (let i = 0; i < viewList.length; i++) {
      if (viewList[i].key == values) {
        const _currentViewType = { ...viewList[i] };
        setCurrentViewType({ ..._currentViewType });
        getTitle(_currentViewType.label);
        localStorage.removeItem(viewType.key + 'contentChoose');
        setViewTypeDisabledKeys(values);
        // getParams(viewList[i].key).then();
        const params = {
          ...getChartsDataParams,
          value: [],
          viewType: viewList[i].key,
        };
        setGetChartsDataParams({ ...params });
        updateCharts(params).then();
      }
    }
  };

  // 修改参数排序
  const sortChange = (values: any) => {
    setSortDisabledKeys(values);
    const _paramsList: any[] = [];
    for (let i = 0; i < sortList[values].length; i++) {
      for (let j = 0; j < paramsList.length; j++) {
        if (paramsList[j].value === sortList[values][i]) {
          _paramsList.push(paramsList[j]);
        }
      }
    }
    _paramsList.map((item: any) => {
      item.isChoose = false;
      item.lineThrough = false;
    });
    setExtendList([..._paramsList]);
  };

  /**
   * @description:修改部分参数更新数据
   */

  // 修改时间颗粒度
  // @ts-ignore
  const segmentedOnChange = (values: any) => {
    if (values === '月') {
      values = 'month';
    } else if (values === '周') {
      values = 'week';
    }
    const params = {
      ...getChartsDataParams,
      dateType: values,
    };
    setGetChartsDataParams({ ...params });
    updateCharts(params).then();
  };
  // 修改时间
  const getTime = async (times: string[]) => {
    const params = {
      ...getChartsDataParams,
      date: times,
    };
    setGetChartsDataParams({ ...params });
    updateCharts(params).then();
  };
  // 点击参数展开
  const changeCheckBox = (key: string) => {
    // getOptionData(key)
    extendList.map((item: any) => {
      if (key === item.key) {
        item.isChoose = !item.isChoose;
      }
    });
    let flag = 0;
    for (let i = 0; i < extendList.length; i++) {
      if (extendList[i].isChoose === true) {
        flag++;
      }
      if (flag > 1) {
        if (extendList[0].isChoose === true) {
          for (let j = 0; j < flag - 1; j++) {
            extendList[j].lineThrough = extendList[j].isChoose === true;
          }
        } else {
          for (let j = 1; j < flag; j++) {
            extendList[j].lineThrough = extendList[j].isChoose === true;
          }
        }
      } else {
        for (let j = 0; j < extendList.length; j++) {
          extendList[j].lineThrough = false;
        }
      }
    }
    setChooseFlag(flag);
    // setExtendList([...extendList]);
    const params_value: any[] = [];
    let contentChoose: string = '';
    // @ts-ignore
    const info: any = JSON.parse(
      localStorage.getItem(viewType.key + 'contentChoose') as string,
    );
    extendList.map((item: any) => {
      if (item.isChoose) {
        if (item.lineThrough) {
          params_value.push({
            key: item.key,
            type: 'IN',
            content: item.itemList[0].key,
          });
        } else {
          item.itemList.map((i: any) => {
            if (i.isDefault === true) {
              contentChoose = contentChoose
                ? contentChoose + ',' + i.key
                : i.key + ',';
            }
            if (
              info &&
              item.key === info.key &&
              info.viewType === viewType.key
            ) {
              const a = info.contentChoose.split(',');
              a.map((item: any) => {
                if (i.key === item) {
                  i.isDefault = true;
                }
              });
              contentChoose = info.contentChoose;
            }
          });
          if (contentChoose[contentChoose.length - 1] === ',') {
            contentChoose = contentChoose.slice(0, -1);
          }
          params_value.push({
            key: item.key,
            type: 'IN',
            content: contentChoose,
          });
        }
      }
    });
    setExtendList([...extendList]);
    const params = {
      ...getChartsDataParams,
      value: params_value,
    };
    setGetChartsDataParams({ ...params });
    updateCharts(params).then();
  };

  const getSingleSelectIndex = async (index: any, key: string) => {
    let contentChoose: any;
    extendList.map((item: any) => {
      if (item.key === key) {
        contentChoose = index;
      }
    });
    getChartsDataParams.value.map((item: any) => {
      if (item.key === key) {
        item.content = contentChoose;
      }
    });
    setGetChartsDataParams({ ...getChartsDataParams });
    updateCharts(getChartsDataParams).then();
  };

  const getMultipleSelectIndex = async (values: any, key: string) => {
    let contentChoose: any = '';
    values.map((item: any) => {
      contentChoose = contentChoose
        ? contentChoose + item.key + ','
        : item.key + ',';
    });
    if (contentChoose[contentChoose.length - 1] === ',') {
      contentChoose = contentChoose.slice(0, -1);
    }

    getChartsDataParams.value.map((item: any) => {
      if (item.key === key) {
        item.content = contentChoose;
      }
    });
    const info: any = JSON.stringify({
      key: key,
      contentChoose: contentChoose,
      viewType: viewType.key,
    });
    localStorage.setItem(viewType.key + 'contentChoose', info);
    setGetChartsDataParams({ ...getChartsDataParams });
    updateCharts(getChartsDataParams).then();
  };

  const updateCharts = async (params: any) => {
    const res = (await ajax.updateCharts(params)) as any;
    if (res.code === 0) {
      getChartsData(res.data);
    }
  };

  useEffect(() => {
    setExtendList([...paramsList]);
    const paramsArr: string[] = [];
    paramsList.map((item: any) => {
      paramsArr.push(item.value);
    });
    let totalArr: any[] = [];
    // 递归输出数组
    const recursion = (val: any[], arr: any[]) => {
      val.forEach((item: any) => {
        if (!arr.includes(item)) {
          const arr1 = [...arr, item];
          if (arr1.length == val.length) {
            totalArr = [...totalArr, arr1];
          }
          if (arr1.length < val.length) {
            recursion(val, arr1);
          }
        }
      });
    };
    recursion(paramsArr, []);
    console.log(totalArr, 'totalArr');

    setSortList([...totalArr]);
  }, [paramsList]);
  useEffect(() => {
    localStorage.removeItem(viewType.key + 'contentChoose');
    setCurrentViewType(viewType);
    getViewList().then();
    // 获取当前时间过去六个月起止时间
    const now = dayjs();
    const sixMonthsAgo = now.subtract(6, 'month');
    const formattedNowDate = now.format('YYYY-MM-DD');
    const formattedSixMonthsAgoDate = sixMonthsAgo.format('YYYY-MM-DD');
    setGetChartsDataParams({
      ...getChartsDataParams,
      viewType: viewType.key,
      date: [formattedSixMonthsAgoDate, formattedNowDate],
      dateType: 'month',
      value: [],
    });
    updateCharts({
      ...getChartsDataParams,
      viewType: viewType.key,
      date: [formattedSixMonthsAgoDate, formattedNowDate],
      dateType: 'month',
      value: [],
    }).then();
  }, []);
  useEffect(() => {
    getParams(currentViewType).then();
  }, [currentViewType]);
  return (
    <div className='bg-white p-4 text-sm'>
      <Form form={initialForm} layout='inline' className='flex justify-between'>
        <Form.Item noStyle name={'viewType'}>
          <Select
            label='视图类型'
            className='w-[49%]'
            size={'sm'}
            // defaultSelectedKeys={[viewType.key.toString()] || []}
            disabledKeys={[viewTypeDisabledKeys.toString()]}
            disableAnimation={true}
            onChange={(values: any) => {
              viewTypeChange(values.target.value);
            }}>
            {viewList &&
              viewList.map((item: any) => (
                <SelectItem
                  key={item.key.toString()}
                  value={item.key.toString()}>
                  {item.label}
                </SelectItem>
              ))}
          </Select>
        </Form.Item>
        <Form.Item noStyle name={'sort'}>
          <Select
            label='参数排序'
            className='w-[49%]'
            size={'sm'}
            disableAnimation={true}
            defaultSelectedKeys={['0']}
            disabledKeys={[sortDisabledKeys]}
            onChange={(values: any) => {
              sortChange(values.target.value);
            }}>
            {sortList &&
              sortList.map((item: any, index: number) => (
                <SelectItem key={index} value={item}>
                  {currentViewType.paramsNumber === 2
                    ? item[0] + '/' + item[1]
                    : item[0] + '/' + item[1] + '/' + item[2]}
                </SelectItem>
              ))}
          </Select>
        </Form.Item>
      </Form>
      <Form
        form={timeForm}
        layout={'inline'}
        className='flex justify-between mt-2'>
        <Form.Item noStyle name={'particle'}>
          <ConfigProvider
            theme={{
              components: {
                Segmented: {
                  // @ts-ignore
                  trackBg: '#f4f4f5',
                  itemColor: '#71717a',
                  itemSelectedColor: '#0070f0',
                },
              },
            }}>
            {/*  @ts-ignore */}
            <Segmented<string>
              options={['月', '周']}
              className='w-[49%]'
              block
              size='large'
              onChange={(value: any) => segmentedOnChange(value)}
            />
          </ConfigProvider>
        </Form.Item>
        <Form.Item noStyle name={'time'}>
          <MyDatePicker getTime={getTime} width={'49%'} />
        </Form.Item>
      </Form>
      <Form
        form={extendForm}
        layout='inline'
        className='flex justify-between mt-2'>
        {extendList &&
          extendList.map((item: any, index: number) => {
            return (
              <div
                key={index}
                className={
                  `${
                    currentViewType.paramsNumber === 2 ? 'w-[49%]' : 'w-[32%]'
                  }` + ' pl-3 mr-0 rounded-lg'
                }
                style={{ backgroundColor: '#f4f4f5' }}>
                <Form.Item
                  noStyle
                  key={item.key}
                  name={item.key}
                  valuePropName={'checked'}
                  initialValue={item.isChoose}>
                  <Checkbox
                    size='sm'
                    lineThrough={item.lineThrough}
                    classNames={{ base: cn('w-full max-w-lg') }}
                    onChange={() => changeCheckBox(item.key)}>
                    <div
                      className='pl-1 h-10 leading-10 text-sm'
                      style={{ backgroundColor: '#f4f4f5', color: '#71717a' }}>
                      {item.value}展开
                    </div>
                  </Checkbox>
                </Form.Item>
              </div>
            );
          })}
      </Form>
      <Form layout='inline' className='w-full flex mt-2 bg-white'>
        {extendList &&
          extendList.map((item: any, index: any) => {
            return (
              <Form.Item noStyle key={index} name={item.key}>
                {item.lineThrough === true ? (
                  <SingleSelect
                    item={item}
                    chooseFlag={chooseFlag}
                    getSingleSelectIndex={getSingleSelectIndex}
                  />
                ) : (
                  <MySelect
                    itemKey={item.key}
                    viewType={viewType}
                    data={item.itemList}
                    title={item.value}
                    isDisable={item.isChoose}
                    width={`${
                      chooseFlag === 1 || chooseFlag === 2 ? '49%' : '32%'
                    }`}
                    getMultipleSelectIndex={getMultipleSelectIndex}></MySelect>
                )}
              </Form.Item>
            );
          })}
      </Form>
    </div>
  );
};
export default Options;
