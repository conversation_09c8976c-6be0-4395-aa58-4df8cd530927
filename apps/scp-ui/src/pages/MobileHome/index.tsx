import Wrap from './components/Wrap';
import Header from './components/Header';
import React, { useState, useEffect } from 'react';
import ajax from '@/api';
import ConfirmLoginModal from '@/components/Layout/ConfirmLoginModal.tsx';

const Mobile: React.FC = () => {
  if (import.meta.env.VITE_SHOW_VCONSOLE === 'true') {
    import('vconsole').then((item: any) => {
      const vConsole = new item.default();
      console.log(vConsole);
    });
  }

  const [initViewType, setInitViewType] = useState<any>([]);
  /**
   * @description:获取初始表单类型
   */
  const getDefaultViewList = async () => {
    const res = (await ajax.getInitViewType()) as any;
    if (res.code === 0) {
      setInitViewType([...res.data]);
    }
  };
  useEffect(() => {
    getDefaultViewList();
  }, []);
  return (
    <div
      className='h-full w-full'
      style={{ backgroundColor: 'rgb(228 228 231)' }}>
      <div
        style={{ position: 'fixed', top: '0', zIndex: '9999' }}
        className='w-full'>
        <Header></Header>
      </div>
      <div className='bg-zinc-200 pt-[4px]  mt-[64px]'>
        {initViewType.map((item: any, index: number) => (
          <Wrap viewType={item} key={index}></Wrap>
        ))}
      </div>
      <ConfirmLoginModal />
    </div>
  );
};
export default Mobile;
