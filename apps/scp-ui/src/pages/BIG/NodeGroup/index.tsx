import cytoscape, { Core } from 'cytoscape';
import NodeGroupType from './type';
import { transformNodeType, transformStatus } from '@/utils/Big';
import {
  rawMaterialHtml,
  transportHtml,
  orderHtml,
  purchaseHtml,
  productionHtml,
  stockHtml,
  semiProductHtml,
  productHtml,
  polymerizationHtml,
  itemHtml,
} from './nodeHtml';
class NodeGroup implements NodeGroupType {
  cy: Core;
  zoomType: 'small' | 'big' = 'small';
  nodes: cytoscape.NodeDefinition[] = [];
  edges: cytoscape.EdgeDefinition[] = [];
  zoom: number = 1.5;
  layout: any;
  currentNode: cytoscape.NodeSingular | null = null;
  constructor(
    cy: Core,
    nodes: cytoscape.NodeDefinition[] = [],
    edges: cytoscape.EdgeDefinition[] = [],
  ) {
    this.cy = cy;
    this.cy.minZoom(0.5);
    this.cy.maxZoom(2);
    this.nodes = nodes;
    this.edges = edges;
    this.render();
    this.cy.on('zoom', () => this.checkLabelByZoom(this.cy.zoom()));
  }
  createNodes = (
    nodes: cytoscape.NodeDefinition | cytoscape.NodeDefinition[],
  ) => {
    this.cy.add(nodes);
  };
  createEdges = (
    edges: cytoscape.EdgeDefinition | cytoscape.EdgeDefinition[],
  ) => {
    this.cy.add(edges);
  };
  createEdgesByType = (edge: cytoscape.EdgeDefinition) => {
    this.createEdges(edge);
    // edge.style['line-color'] = status === 'normal' ? '#ccc' : 'red';
  };
  createNodeByType = (
    type: string,
    node: cytoscape.NodeDefinition,
    status: string[] = [],
  ) => {
    node.data['html'] = this.createNodeHtml(
      type,
      node.data,
      status,
      this.zoomType,
    );
    this.createNodes(node);
  };
  createNodeHtml = (
    type: string,
    data: any,
    status: string[] = [],
    zoomType: 'small' | 'big',
  ): string => {
    let html = '';
    switch (type) {
      case 'order':
        html = orderHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
          data['childrenLength'],
        );
        break;
      case 'raw':
        html = rawMaterialHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        if (data['isItemInput']) {
          html = itemHtml(
            data['materialName'],
            data['locationName'],
            transformStatus(status),
            data['lightNums'],
            zoomType,
            data['is_active'],
          );
        }
        break;
      case 'transport':
        html = transportHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'purchase':
        html = purchaseHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'production':
        html = productionHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'stock':
        html = stockHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'semiProduction':
        html = semiProductHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'product':
        html = productHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      case 'polymerization':
        html = polymerizationHtml(
          data['materialName'],
          data['locationName'],
          transformStatus(status),
          data['lightNums'],
          zoomType,
          data['is_active'],
        );
        break;
      default:
        break;
    }
    return html;
  };
  /**
   *@description 点击边激活整条边
   *@param edge
   *@return void
   *@auther liujinxu
   */
  clickEdge = (edge: cytoscape.EdgeSingular) => {
    const source = edge.data('source');
    const target = edge.data('target');
    console.log(source, target);
  };
  setZoomLevel = (zoom: number) => {
    this.zoom = zoom;
  };
  checkLabelByZoom = (zoom: number) => {
    const beforeZoom = this.zoomType;
    if (zoom < this.zoom) {
      this.zoomType = 'small';
    } else {
      this.zoomType = 'big';
    }
    if (beforeZoom !== this.zoomType) {
      this.updateNodeStatus();
    }
  };
  updateNodeStatus = () => {
    this.cy.nodes().forEach((node: cytoscape.NodeSingular) => {
      const nodeData: any = node.data(); // Explicitly define the type of nodeData as any
      nodeData.html = this.createNodeHtml(
        transformNodeType(nodeData['orderType']),
        nodeData,
        nodeData.status,
        this.zoomType,
      );
    });
    this.cy.resize();
  };
  clickNode = (node: cytoscape.NodeSingular): cytoscape.NodeSingular | null => {
    node.data('is_active', !node.data('is_active'));
    console.log(node.data());
    //消除其他的is_active
    this.cy.nodes().forEach((n: cytoscape.NodeSingular) => {
      if (n.data('id') !== node.data('id')) {
        n.data('is_active', false);
      }
    });
    this.currentNode = node;
    this.updateNodeStatus();
    if (node.data('is_active')) {
      return node;
    } else {
      return null;
    }
  };
  removeAllNodeAcitve = () => {
    this.cy.nodes().forEach((node: cytoscape.NodeSingular) => {
      node.data('is_active', false);
    });
    this.updateNodeStatus();
  };
  getCurrentNode = () => this.currentNode;
  render = () => {
    this.cy.remove(this.cy.elements());
    this.nodes.forEach((node: any) => {
      this.createNodeByType(
        transformNodeType(node['orderType']),
        // { data: { ...node, lightNums: checkoutNodeDeeper(node['orderType']) } },
        { data: { ...node, lightNums: node['lightLevel'] } },
        node.status,
      );
    });
    this.edges.forEach((edge: any) => {
      this.createEdgesByType({ data: { ...edge } });
    });
    this.layout = this.cy.layout({
      name: 'dagre',
      //@ts-ignore
      rankDir: 'LR',
      directed: true,
      // fit: true, // Whether to fit
      // padding: 50,
      // klay: {
      //   spacing: 150,
      //   nodeLayering: 'INTERACTIVE',
      //   // borderSpacing: 150,
      // },
      nodeSep: 50,
      edgeSep: 200,
      rankSep: 250,
    });
    this.cy.style([
      {
        selector: 'node',
        style: {
          width: 100,
          height: 100,
          shape: 'rectangle',
        },
      },
      {
        selector: 'edge',
        style: {
          'line-color': (edge: cytoscape.EdgeSingular): string => {
            if (edge.data('delayInDays')) {
              return '#fcc441';
            }
            return '#E2E3E7';
          },
          'curve-style': 'bezier',
          'taxi-direction': 'leftward',
          label: (edge: cytoscape.EdgeSingular) => {
            let time: string = '';
            const quantity: number = edge.data('quantity') || 0;
            if (edge.data('inputTime')) {
              time = edge.data('inputTime');
            }
            if (edge.data('outputTime')) {
              time = edge.data('outputTime');
            }
            return `${quantity} \n ${time}`;
          },
          'text-background-color': 'white', // 将文本背景颜色设为透明
          'text-background-opacity': 1, // 设置文本背景透明度为0，使其不可见
          'font-size': '12px',
          'text-background-padding': '3px', // 设置文本背景内边距
          'text-border-color': '#ccc', // 设置文本边框颜色
          'text-border-style': 'solid', // 设置文本边框样式
          'text-wrap': 'wrap',
          // 'text-border-width': 2, // 设置文本边框宽度
          'text-border-opacity': 1, // 设置文本边框透明度
        },
      },
    ]);

    this.layout.run();
  };
  useDrageLayout = () => {
    const options = {
      name: 'dagre',
      //@ts-ignore
      rankDir: 'LR',
      directed: true,
      // fit: true, // Whether to fit
      // padding: 50,
      // klay: {
      //   spacing: 150,
      //   nodeLayering: 'INTERACTIVE',
      //   // borderSpacing: 150,
      // },
      nodeSep: 50,
      edgeSep: 200,
      rankSep: 250,
    };
    this.cy.layout(options).run();
  };
  useKlayLayout = () => {
    const options = {
      name: 'klay',
      //@ts-ignore
      fit: true, // Whether to fit
      padding: 50,
      klay: {
        spacing: 150,
        nodeLayering: 'INTERACTIVE',
        // borderSpacing: 150,
      },
    };
    this.cy.layout(options).run();
  };
  useTaxiLine = () => {
    this.cy
      .style([
        {
          selector: 'node',
          style: {
            width: 100,
            height: 100,
            shape: 'rectangle',
          },
        },
        {
          selector: 'edge',
          style: {
            'line-color': '#E2E3E7',
            'curve-style': 'taxi',
            'taxi-direction': 'leftward',
            label: (edge: cytoscape.EdgeSingular) => {
              return `${edge.data('quantity')}\n${edge.data('time')}`;
            },
            'text-background-color': 'white', // 将文本背景颜色设为透明
            'text-background-opacity': 1, // 设置文本背景透明度为0，使其不可见
            'font-size': '12px',
            'text-background-padding': '3px', // 设置文本背景内边距
            'text-border-color': '#ccc', // 设置文本边框颜色
            'text-border-style': 'solid', // 设置文本边框样式
            'text-wrap': 'wrap',
            // 'text-border-width': 2, // 设置文本边框宽度
            'text-border-opacity': 1, // 设置文本边框透明度
          },
        },
      ])
      .update();
    this.cy.resize();
  };
  usebezierLine = () => {
    this.cy
      .style([
        {
          selector: 'node',
          style: {
            width: 100,
            height: 100,
            shape: 'rectangle',
          },
        },
        {
          selector: 'edge',
          style: {
            'line-color': '#E2E3E7',
            'curve-style': 'bezier',
            'taxi-direction': 'leftward',
            label: (edge: cytoscape.EdgeSingular) => {
              return `${edge.data('quantity')}\n${edge.data('time')}`;
            },
            'text-background-color': 'white', // 将文本背景颜色设为透明
            'text-background-opacity': 1, // 设置文本背景透明度为0，使其不可见
            'font-size': '12px',
            'text-background-padding': '3px', // 设置文本背景内边距
            'text-border-color': '#ccc', // 设置文本边框颜色
            'text-border-style': 'solid', // 设置文本边框样式
            'text-wrap': 'wrap',
            // 'text-border-width': 2, // 设置文本边框宽度
            'text-border-opacity': 1, // 设置文本边框透明度
          },
        },
      ])
      .update();
    this.cy.resize();
  };
}

export default NodeGroup;
