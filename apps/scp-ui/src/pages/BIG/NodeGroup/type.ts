import cytoscape from 'cytoscape';
interface NodeGroupType {
  cy: cytoscape.Core;
  createNodes: (
    nodes: cytoscape.NodeDefinition | cytoscape.NodeDefinition[],
  ) => void;
  createEdges: (
    edges: cytoscape.EdgeDefinition | cytoscape.EdgeDefinition[],
  ) => void;
  createNodeByType: (type: string, node: cytoscape.NodeDefinition) => void;
  checkLabelByZoom: (zoom: number) => void;
  setZoomLevel: (zoom: number) => void;
  clickNode: (node: cytoscape.NodeSingular) => cytoscape.NodeSingular | null;
}

export default NodeGroupType;
