import { useEffect, useRef, useState } from 'react';
import { App } from 'antd';
import ajax from '@/api';
import cytoscape from 'cytoscape';
import dagre from 'cytoscape-dagre';
import klay from 'cytoscape-klay';
import cytoscapeHTML from 'cytoscape-html';
import SiderMessageCard from './SiderMessageCard';
import type { MessageDataProps } from './SiderMessageCard';
import SearchDrawer from './SearchDrawer';
import {} from 'antd-style';
//@ts-ignore
import NodeGroup from './NodeGroup';
import { transformStatus } from '@/utils/Big';
cytoscape.use(cytoscapeHTML);
cytoscape.use(dagre as any);
cytoscape.use(klay as any);
type SiderData = {
  data: MessageDataProps | null;
  status: 'warning' | 'normal' | 'error';
  icon: string;
};
const Graph = () => {
  const { message } = App.useApp();
  const ref = useRef<HTMLDivElement>(null);
  const fullScreenRef = useRef<HTMLDivElement>(null);
  const popperRef = useRef<HTMLDivElement>(null);
  const [isHidden, setIsHidden] = useState<boolean>(false);
  const [edgeGroup, setEdgeGroup] = useState([]);
  const [nodeGroup, setNodeGroup] = useState([]);
  const [currentNode, setCurrentNode] = useState<cytoscape.NodeSingular | null>(
    null,
  );
  const [group, setGroup] = useState<NodeGroup | null>(null);
  const [siderData, setSiderData] = useState<SiderData>({
    data: null,
    status: 'normal',
    icon: '',
  });
  const [loading, setLoading] = useState<boolean>(false);
  // const user = useSelector((state: any) => state.login.user);

  // const paPvId = user?.preferPaPvId || -1;
  //获取数据
  function launchFullscreen(element: any) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }
  }

  const onSearch = (data: any) => {
    getBigData(data);
    setCurrentNode(null);
    group?.removeAllNodeAcitve();
  };

  //big接口
  const getBigData = async (data: any) => {
    try {
      setLoading(true);
      const { code, data: res, msg } = await ajax.getBIGData(data);
      if (!code) {
        let { edgeGroup, nodeGroup } = res;
        const edgeMap = new Map<string, boolean>();
        edgeGroup =
          edgeGroup
            ?.map((item: any) => {
              const { content, ...rest } = item;
              return { ...content, ...rest };
            })
            .filter((item: any) => {
              if (!edgeMap.get(item.id)) {
                edgeMap.set(item.id, true);
                return true;
              } else {
                return false;
              }
            }) || [];
        nodeGroup =
          nodeGroup?.map((item: any) => {
            const { content, ...rest } = item;
            return { ...content, ...rest };
          }) || [];

        setEdgeGroup(edgeGroup);
        setNodeGroup(nodeGroup);
      } else {
        message.warning(msg);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const cy = cytoscape({
      container: ref.current,
    });
    const group = new NodeGroup(cy, nodeGroup, edgeGroup);
    setGroup(group);
    group.cy.on('click', (node) => {
      //输出点击的节点
      if (node.target !== group.cy) {
        //如果点击的是节点
        if (node.target.isNode()) {
          setCurrentNode(group.clickNode(node.target));
        }
      } else {
        //如果点击的是画布
        setCurrentNode(null);
        group.removeAllNodeAcitve();
      }
    });
    group.setZoomLevel(1.3);
    //@ts-ignore
    cy.nodes().renderHTMLNodes({ hideOriginal: true });
  }, [nodeGroup, edgeGroup]);

  useEffect(() => {
    const data = currentNode?.data() || null;
    if (!data) {
      setIsHidden(false);
      return;
    }
    setIsHidden(true);
    setSiderData({
      data: data,
      status: transformStatus(data.status),
      icon: data.orderType,
    });
  }, [currentNode]);

  return (
    <div
      className='h-[calc(100vh-9rem)] w-full overflow-hidden bg-white relative'
      style={{ borderRadius: '1.78571rem' }}
      ref={fullScreenRef}>
      <header>
        <SearchDrawer loading={loading} data={null} onSearch={onSearch} />
      </header>
      <div
        className=' cursor-pointer absolute left-2 bottom-2 w-8 h-8 flex justify-center items-center bg-white rounded-md shadow-md z-10'
        onClick={() => launchFullscreen(fullScreenRef.current)}>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'>
          <path
            d='M16.5 3C16.1022 3 15.7206 2.84196 15.4393 2.56066C15.158 2.27936 15 1.89782 15 1.5C15 1.10218 15.158 0.720644 15.4393 0.43934C15.7206 0.158035 16.1022 0 16.5 0L19.5 0C20.6935 0 21.8381 0.474106 22.682 1.31802C23.5259 2.16193 24 3.30653 24 4.5V7.5C24 7.89782 23.842 8.27936 23.5607 8.56066C23.2794 8.84196 22.8978 9 22.5 9C22.1022 9 21.7206 8.84196 21.4393 8.56066C21.158 8.27936 21 7.89782 21 7.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3H16.5ZM16.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V16.5C21 16.1022 21.158 15.7206 21.4393 15.4393C21.7206 15.158 22.1022 15 22.5 15C22.8978 15 23.2794 15.158 23.5607 15.4393C23.842 15.7206 24 16.1022 24 16.5V19.5C24 20.6935 23.5259 21.8381 22.682 22.682C21.8381 23.5259 20.6935 24 19.5 24H16.5C16.1022 24 15.7206 23.842 15.4393 23.5607C15.158 23.2794 15 22.8978 15 22.5C15 22.1022 15.158 21.7206 15.4393 21.4393C15.7206 21.158 16.1022 21 16.5 21ZM7.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V7.5C3 7.89782 2.84196 8.27936 2.56066 8.56066C2.27936 8.84196 1.89782 9 1.5 9C1.10218 9 0.720644 8.84196 0.43934 8.56066C0.158035 8.27936 0 7.89782 0 7.5V4.5C0 3.30653 0.474106 2.16193 1.31802 1.31802C2.16193 0.474106 3.30653 0 4.5 0L7.5 0C7.89782 0 8.27936 0.158035 8.56066 0.43934C8.84196 0.720644 9 1.10218 9 1.5C9 1.89782 8.84196 2.27936 8.56066 2.56066C8.27936 2.84196 7.89782 3 7.5 3ZM7.5 21C7.89782 21 8.27936 21.158 8.56066 21.4393C8.84196 21.7206 9 22.1022 9 22.5C9 22.8978 8.84196 23.2794 8.56066 23.5607C8.27936 23.842 7.89782 24 7.5 24H4.5C3.30653 24 2.16193 23.5259 1.31802 22.682C0.474106 21.8381 0 20.6935 0 19.5L0 16.5C0 16.1022 0.158035 15.7206 0.43934 15.4393C0.720644 15.158 1.10218 15 1.5 15C1.89782 15 2.27936 15.158 2.56066 15.4393C2.84196 15.7206 3 16.1022 3 16.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H7.5Z'
            fill='#D5D7DD'
          />
        </svg>
      </div>
      <div className='w-full h-full' ref={ref}></div>
      <div ref={popperRef}></div>

      <SiderMessageCard
        data={siderData.data}
        status={siderData.status}
        icon={siderData.icon}
        isHidden={isHidden}
      />
    </div>
  );
};

export default Graph;
