// eslint-disable prettier/prettier
import BigSelect from './components/BigSelect';
import { Button } from 'antd';
import BigButton from './components/BigButton';
import { Form, message } from 'antd';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import store from '@/store';
import { ReloadOutlined } from '@ant-design/icons';
import { useVoerkaI18n } from '@voerkai18n/react';
import Selector from './components/Selector/Selector';
type PropsType = {
  data: any;
  onSearch: any;
  loading: boolean;
};
const SearchDrawer: React.FC<PropsType> = (props) => {
  const { t } = useVoerkaI18n();
  const { onSearch } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const info = () => {
    messageApi.error('您还没有选择任何选项!');
  };
  const pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const typeList = [
    {
      key: 'question',
      value: '按问题分类',
      label: t('按问题分类'),
    },
    {
      key: 'bi',
      value: '按BI图分类',
      label: t('按BI图分类'),
    },
  ];
  const [detailsList, setDetailsList] = useState<any>([]);
  const [detailsTitle, setDetailsTitle] = useState<string>('请选择前置选项');
  const [isDetailsDisable, setIsDetailsDisable] = useState<boolean>(true);

  const [searchData, setSearchData] = useState<any>({});
  const [pvId, setPvId] = useState<any>('');
  const [classify, setClassify] = useState<any>('');
  const [isChooseAllCondition, setIsChooseAllCondition] =
    useState<boolean>(false);
  const [sliderListData, setSliderListData] = useState<any>([]);
  const getPvId = (values: any) => {
    setPvId(values);
    searchData.paPvId = values;
    setSearchData({ ...searchData });
  };
  const getType = (values: any) => {
    setIsChooseAllCondition(false);
    if (values) {
      setIsDetailsDisable(false);
      if (values.key === 'bi') {
        setDetailsTitle(t('订单/凭证'));
        setDetailsList([
          {
            value: 'voucher',
            label: t('凭证'),
          },
          {
            value: 'order',
            label: t('订单'),
          },
        ]);
      } else if (values.key === 'question') {
        setDetailsTitle(t('风险类别'));
        setDetailsList([
          {
            value: 'riskVoucher',
            label: t('风险凭证'),
          },
          {
            value: 'redundantSupply',
            label: t('冗余供应'),
          },
          {
            value: 'deliveryRisk',
            label: t('交付风险'),
          },
        ]);
      }
    }
  };
  const getDetails = (values: any) => {
    if (values) {
      setClassify(values.key);
      setIsChooseAllCondition(true);
      switch (values.key) {
        case 'voucher':
          setSliderListData([
            {
              value: 'transport',
              label: '运输',
            },
            {
              value: 'production',
              label: '生产',
            },
            {
              value: 'purchase',
              label: '采购',
            },
            {
              value: 'inventory',
              label: '库存',
            },
          ]);
          break;
        case 'order':
          setSliderListData([
            {
              value: 'forecastOrders',
              label: '预测订单',
            },
            {
              value: 'SO',
              label: 'SO',
            },
          ]);
          break;
        case 'riskVoucher':
          setSliderListData([
            {
              value: 'transport',
              label: '运输',
            },
            {
              value: 'production',
              label: '生产',
            },
            {
              value: 'purchase',
              label: '采购',
            },
          ]);
          break;
        case 'redundantSupply':
          setSliderListData([
            {
              value: 'transport',
              label: '运输',
            },
            {
              value: 'production',
              label: '生产',
            },
            {
              value: 'purchase',
              label: '采购',
            },
            {
              value: 'inventory',
              label: '库存',
            },
          ]);
          break;
        case 'deliveryRisk':
          setSliderListData([
            {
              value: 'forecastOrders',
              label: '预测订单',
            },
            {
              value: 'SO',
              label: 'SO',
            },
          ]);
          break;
        default:
          break;
      }
    }
  };
  const getChooseList = () => {
    const _chooseList: string[] = [];
    const list = JSON.parse(localStorage.getItem('bigChooseList') as string);
    if (list) {
      list.map((item: any) => {
        _chooseList.push(...item.orderList);
      });
    }
    searchData.orderIds = _chooseList;
    setSearchData({ ...searchData });
  };

  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      getChooseList();
    });
    localStorage.removeItem('bigChooseList');
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    getChooseList();
  }, [localStorage.getItem('bigChooseList')]);

  return (
    <div className='px-[2.64rem] pt-[1.71rem] justify-between'>
      {contextHolder}
      <Form layout='inline' style={{ display: 'none', textAlign: 'center' }}>
        <BigSelect
          title={t('计划版本')}
          data={pVersionList}
          getPvId={getPvId}
        />
        <BigSelect title={t('按...分类')} data={typeList} getType={getType} />
        <BigSelect
          title={detailsTitle}
          data={detailsList}
          isDetailsDisable={isDetailsDisable}
          getDetails={getDetails}
        />
        <BigButton
          type={'chooseId'}
          isChooseAllCondition={isChooseAllCondition}
          sliderListData={sliderListData}
          pvId={pvId}
          classify={classify}
        />
        <BigButton
          type={'search'}
          onClick={() => {
            if (store.getState().big.CHOOSE_LIST.length === 0) {
              info();
              return;
            }
            onSearch(searchData);
          }}
        />
      </Form>
      <div className='flex' style={{ display: 'none' }}>
        <Button
          className='mr-[10px] h-[40px] w-[40px]'
          type='primary'
          icon={<ReloadOutlined />}
          loading={props.loading}
          onClick={() => {
            onSearch({ paPvId: pvId, orderIds: [] });
          }}
        />
        <BigButton type={'isChoose'} />
      </div>
      <Selector onSearch={onSearch} paPvId={pvId} loading={props.loading} />
    </div>
  );
};

export default SearchDrawer;
