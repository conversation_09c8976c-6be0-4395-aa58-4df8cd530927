import { useMemo } from 'react';
import {
  transformNodeType,
  transformNodeName,
  transformTypeToName,
} from '@/utils/Big';
export type MessageDataProps = {
  date: string;
  id: string;
  materialName: string;
  materialType: string;
  price: string;
  location: string;
};
export type SiderMessageCardProps = {
  icon: string;
  status: 'warning' | 'normal' | 'error'; //状态
  data: any;
  isHidden?: boolean;
};
const SiderMessageCard: React.FC<SiderMessageCardProps> = ({
  icon,
  status,
  data,
  isHidden,
}) => {
  const TableParams = useMemo(
    () => getSliderMessageCard(data?.orderType || '', data),
    [data],
  );

  return (
    // style={{display:"none"}}要删除
    <div
      className={`flex flex-col items-start justify-start w-[25rem] h-[46rem] pr-12 absolute right-[2.86rem] top-[9rem] ${
        isHidden ? 'translate-x-0' : 'translate-x-[30rem]'
      }  rounded-[1.78571rem] border border-[#E2E3E7] bg-white shadow-custom-shadow transition-transform-opacity`}>
      <div className='mt-[1.86rem] ml-[3.21rem]'>
        <p className='text-[#0D152D] font-[PingFang SC] text-[1.28571rem] font-medium  leading-[1.71429rem] tracking-[0.05143rem]'>
          节点类型
        </p>
        <div className='h-[2.78571rem] mt-[0.64rem] flex items-center'>
          <div id='icon' className='w-[3.21rem] h-full bg-white'>
            <SiderMessageIcon
              icon={transformNodeType(icon)}
              nodeStatus={status}
              isItemInput={data && (data['isItemInput'] || false)}
            />
          </div>
          <p className='ml-[1.74rem] text-[1rem]'>
            {transformNodeName(icon, data && (data['isItemInput'] || false))}
          </p>
        </div>
        <p className='mt-[1rem] font-medium text-[1.28571rem] font-[PingFang SC]'>
          状态
        </p>
        <div className='flex gap-1'>
          {data?.status ? (
            data?.status.map((item: string, index: number) => {
              return <StatusTag status={item} key={index} />;
            })
          ) : (
            <StatusTag status='normal' />
          )}
        </div>
        <div className='mt-[1.43rem]'>
          <p className='text-[#0D152D] font-[PingFang SC] text-[1.28571rem] font-medium  leading-[1.71429rem] tracking-[0.05143rem]'>
            节点信息
          </p>
        </div>
      </div>
      <div id='message-table' className='ml-[4rem] mt-[2.43rem]'>
        <div className='w-full'>
          <table className='table-auto border-collapse h-[22.714rem]'>
            <tbody>
              {TableParams.map((item) => {
                return (
                  <tr key={item.label}>
                    <th className='text-right pr-[1.86rem] text-[1.14286rem] font-medium whitespace-nowrap'>
                      {item.label}
                    </th>
                    <td className='border-l border-[#E2E3E7] pl-[1.86rem] text-[1rem] font-normal'>
                      {item.value}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

const StatusTag: React.FC<{ status: string }> = ({ status }) => {
  let statusBackgroundColor = 'bg-success';
  let text = '正常';
  switch (status) {
    case 'deliveryRisk':
      text = '交付风险';
      statusBackgroundColor = 'bg-error';
      break;
    case 'redundantRisk':
      text = '冗余供应';
      statusBackgroundColor = 'bg-error';
      break;
    case 'delayDelivery':
      text = '交付风险';
      statusBackgroundColor = 'bg-warning';
      break;
    case 'riskCertificate':
      text = '风险凭证';
      statusBackgroundColor = 'bg-error';
      break;
  }
  return (
    <div
      style={{ lineHeight: '2.857rem' }}
      className={`w-[9.642rem] h-[2.857rem] rounded-xl ${statusBackgroundColor} text-center font-semibold text-[1.42857rem] text-white mt-[0.79rem]`}>
      {text}
    </div>
  );
};

const SiderMessageIcon: React.FC<{
  icon: string;
  nodeStatus: 'warning' | 'normal' | 'error';
  isItemInput?: boolean;
}> = ({ icon, nodeStatus, isItemInput = false }) => {
  let color;
  switch (nodeStatus) {
    case 'warning':
      color = '#FFCF5A';
      break;
    case 'error':
      color = '#F25445';
      break;
    default:
      color = '#E2E3E7';
  }
  switch (icon) {
    case 'raw':
      if (isItemInput) {
        return (
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='4rem'
            height='4rem'
            viewBox='0 0 47 47'
            fill='none'>
            <g filter="${isActive ? 'url(#drop-shadow)' : ''}">
              <path
                d='M6.70633 22.8539C6.70633 18.2045 6.70276 13.5554 6.70767 8.9062C6.71056 6.3574 7.71171 5.33545 10.1966 5.33477C18.5512 5.33275 26.9058 5.33275 35.2603 5.33477C37.6686 5.33545 38.7333 6.3763 38.7384 8.80877C38.7527 15.2951 38.7647 21.782 38.7096 28.2679C38.7043 28.8983 38.3792 29.6755 37.9425 30.1293C34.8111 33.3819 31.6283 36.5848 28.415 39.7553C28.0134 40.1515 27.3398 40.4712 26.789 40.4762C21.2942 40.527 15.7987 40.505 10.3032 40.5018C7.74491 40.5005 6.70945 39.4713 6.70722 36.914C6.70321 32.2272 6.70633 27.5407 6.70633 22.8539ZM27 36.914C28.601 35.2924 34.4229 29.7275 36 28.1293H27V36.914Z'
                fill={color}
              />
              <path
                d='M22.8418 17.091C25.5861 17.0912 28.3306 17.0867 31.0749 17.0932C32.2095 17.0959 32.8555 17.5945 32.8851 18.4585C32.9161 19.3581 32.2222 19.9494 31.0515 19.9514C25.4887 19.9609 19.9257 19.9615 14.3629 19.9505C13.2089 19.9483 12.5072 19.3302 12.5529 18.4351C12.5966 17.5792 13.243 17.0955 14.3861 17.0932C17.2043 17.0869 20.0231 17.091 22.8418 17.091ZM22.7322 14.0146C19.9513 14.0146 17.1702 14.0211 14.3892 14.0114C13.1677 14.0071 12.5275 13.4883 12.5529 12.5687C12.5767 11.7049 13.2111 11.191 14.3584 11.1892C19.9206 11.1787 25.4827 11.1789 31.0448 11.1883C32.2392 11.1904 32.9212 11.7553 32.8938 12.6502C32.8675 13.5065 32.2222 14.0071 31.0758 14.0112C28.2943 14.0211 25.5132 14.0146 22.7322 14.0146Z'
                fill='white'
              />
            </g>
          </svg>
        );
      }
      // 原材料
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='44'
          height='39'
          viewBox='0 0 44 39'
          fill='none'>
          <path
            d='M34.2387 16.2425C33.8886 16.0774 33.6653 15.7251 33.6653 15.3381V5.67688C33.6653 5.2902 33.4423 4.93817 33.0928 4.77286L23.39 0.184645C23.1408 0.0668093 22.854 0.0570573 22.5974 0.157693L11.636 4.45634C11.253 4.60653 11.0011 4.97593 11.0011 5.38731V15.6075C11.0011 16.0271 10.7392 16.402 10.3453 16.5464L0.655791 20.0987C0.261884 20.2431 0 20.6181 0 21.0376V33.2884C0 33.6928 0.243514 34.0574 0.617043 34.2122L11.7565 38.8302C12.0173 38.9383 12.3117 38.9311 12.5669 38.8104L22.4009 34.1602C22.6735 34.0313 22.9897 34.0322 23.2615 34.1628L32.9006 38.7937C33.1722 38.9242 33.4882 38.9253 33.7607 38.7966L43.427 34.2313C43.7769 34.0661 44 33.714 44 33.3271V21.4805C44 21.0935 43.7766 20.7412 43.4266 20.5761L34.2387 16.2425ZM32.1649 15.6583C32.1649 16.0524 31.9335 16.4097 31.5738 16.5709L24.5717 19.7079C23.9102 20.0044 23.1629 19.5203 23.1629 18.7954V15.5116C23.1629 13.5411 24.3203 11.7543 26.1185 10.9487L30.7561 8.87095C31.4176 8.57454 32.1649 9.05859 32.1649 9.78354V15.6583ZM22.6031 2.51488C22.8562 2.40992 23.1413 2.41342 23.3918 2.52457L29.2761 5.1359C30.0685 5.48756 30.0686 6.61209 29.2763 6.9639L26.007 8.41547C24.0829 9.2698 21.8995 9.33123 19.9304 8.58644L16.0307 7.11144C15.1835 6.791 15.1647 5.59934 16.0014 5.25238L22.6031 2.51488ZM5.05866 22.7889C4.2091 22.4581 4.2091 21.2559 5.05866 20.9251L11.7781 18.3089C12.0264 18.2122 12.3031 18.2186 12.5467 18.3268L18.4391 20.943C19.2313 21.2948 19.2313 22.4192 18.4391 22.771L15.2108 24.2043C13.2621 25.0696 11.0486 25.1211 9.06176 24.3475L5.05866 22.7889ZM21.1681 31.4767C21.1681 31.8651 20.9432 32.2184 20.5913 32.3827L13.5936 35.6509C12.9305 35.9606 12.1704 35.4766 12.1704 34.7449V31.1448C12.1704 29.1746 13.3274 27.3881 15.1253 26.5822L19.7591 24.5051C20.4207 24.2086 21.1681 24.6926 21.1681 25.4177V31.4767ZM26.7027 22.7794C25.8844 22.4367 25.8844 21.2773 26.7027 20.9346L32.9364 18.3244C33.1903 18.2181 33.4768 18.2211 33.7284 18.3328L39.6072 20.943C40.3995 21.2948 40.3995 22.4192 39.6072 22.771L36.4878 24.156C34.4751 25.0497 32.1829 25.0741 30.1515 24.2235L26.7027 22.7794ZM42.332 31.4765C42.332 31.865 42.107 32.2183 41.755 32.3827L34.7529 35.6513C34.0899 35.9608 33.3299 35.4768 33.3299 34.7452V31.1455C33.3299 29.1749 34.4873 27.3882 36.2856 26.5825L40.9231 24.5048C41.5847 24.2084 42.332 24.6924 42.332 25.4174V31.4765Z'
            fill={color}
          />
        </svg>
      );
    case 'order':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='47'
          height='47'
          viewBox='0 0 47 47'
          fill='none'>
          <path
            d='M27.713 3.15088H18.8157C17.7244 3.15088 16.8519 4.12185 16.8519 5.32203C16.8519 6.52498 17.726 7.49882 18.8157 7.49882H27.7681C28.8045 7.49882 29.6783 6.52498 29.6783 5.32203C29.6785 4.1218 28.8057 3.15088 27.713 3.15088ZM37.3741 4.59999H32.9434C32.9885 4.83788 33.0145 5.0785 33.0145 5.32203C33.0159 7.14961 31.6901 8.9483 30.1131 8.9483H16.5013C14.8415 8.9483 13.514 7.14961 13.514 5.32203C13.5156 5.0785 13.5416 4.83788 13.5867 4.59999H9.10246C5.49789 4.59999 4.6123 6.64352 4.6123 9.59148V38.7925C4.6123 41.9593 5.88044 43.7317 9.37464 43.7317H37.1016C40.5959 43.7317 42.2945 42.342 42.2945 38.7941V9.59143C42.2948 6.64376 40.7045 4.60022 37.3741 4.60022L37.3741 4.59999ZM32.822 33.4315H13.5547C12.8445 33.4315 12.1356 32.5603 12.1356 31.849C12.1356 31.1401 12.7356 30.5329 13.4458 30.5329H32.6581C33.3681 30.5329 33.9681 31.0849 34.0219 31.849C34.0219 32.5606 33.532 33.4315 32.822 33.4315ZM32.9003 25.6997H13.6327C12.9225 25.6997 12.4703 24.9361 12.4703 24.2243C12.4703 23.5127 13.0706 22.8011 13.7805 22.8011H32.9928C33.7031 22.8011 34.303 23.4604 34.3566 24.2243C34.3567 24.9358 33.6089 25.6997 32.9003 25.6997ZM32.9003 17.7588H13.6327C12.9225 17.7588 12.4703 16.9949 12.4703 16.2834C12.4703 15.5717 13.0706 14.8602 13.7805 14.8602H32.9928C33.7031 14.8602 34.303 15.5198 34.3566 16.2834C34.3567 16.9949 33.6089 17.7588 32.9003 17.7588Z'
            fill={color}
          />
        </svg>
      );
    case 'transport':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='48'
          height='48'
          viewBox='0 0 48 48'
          fill='none'>
          <g transform='scale(-1, 1) translate(-48, 0)'>
            <path
              d='M3.52679 35.7831C3.86774 34.1208 4.77211 32.6271 6.08709 31.5544C7.40206 30.4818 9.04701 29.8959 10.744 29.8959C12.441 29.8959 14.0859 30.4818 15.4009 31.5544C16.7159 32.6271 17.6202 34.1208 17.9612 35.7831H28.566C28.907 34.1208 29.8114 32.6271 31.1263 31.5544C32.4413 30.4818 34.0863 29.8959 35.7832 29.8959C37.4802 29.8959 39.1252 30.4818 40.4401 31.5544C41.7551 32.6271 42.6595 34.1208 43.0004 35.7831H44.6206C45.4019 35.7831 46.1512 35.4728 46.7036 34.9203C47.256 34.3679 47.5664 33.6186 47.5664 32.8373V25.4729H15.1627V15.4498C15.1629 14.9504 15.0362 14.4591 14.7945 14.0221C14.5527 13.5851 14.2038 13.2167 13.7805 12.9516C13.3572 12.6866 12.8735 12.5334 12.3748 12.5066C11.8761 12.4799 11.3788 12.5803 10.9296 12.7986L2.09219 17.0921C1.59475 17.3337 1.17539 17.7106 0.882095 18.1794C0.588799 18.6483 0.433411 19.1902 0.433708 19.7433V32.8373C0.433708 33.6186 0.744064 34.3679 1.29651 34.9203C1.84895 35.4728 2.59823 35.7831 3.3795 35.7831H3.52679ZM44.6206 6.3252H21.0543C20.273 6.3252 19.5237 6.63555 18.9713 7.188C18.4188 7.74044 18.1085 8.48972 18.1085 9.27099V22.5271H47.5664V9.27099C47.5664 8.48972 47.256 7.74044 46.7036 7.188C46.1512 6.63555 45.4019 6.3252 44.6206 6.3252ZM35.7832 41.6747C35.203 41.6747 34.6284 41.5604 34.0923 41.3384C33.5562 41.1163 33.0691 40.7908 32.6587 40.3805C32.2484 39.9702 31.923 39.4831 31.7009 38.947C31.4788 38.4109 31.3645 37.8363 31.3645 37.256C31.3645 36.6758 31.4788 36.1012 31.7009 35.5651C31.923 35.029 32.2484 34.5419 32.6587 34.1315C33.0691 33.7212 33.5562 33.3958 34.0923 33.1737C34.6284 32.9516 35.203 32.8373 35.7832 32.8373C36.9551 32.8373 38.0791 33.3029 38.9077 34.1315C39.7364 34.9602 40.2019 36.0841 40.2019 37.256C40.2019 38.4279 39.7364 39.5519 38.9077 40.3805C38.0791 41.2092 36.9551 41.6747 35.7832 41.6747ZM10.744 41.6747C10.1637 41.6747 9.58913 41.5604 9.05302 41.3384C8.51693 41.1163 8.02981 40.7908 7.6195 40.3805C7.20919 39.9702 6.88371 39.4831 6.66165 38.947C6.43959 38.4109 6.32529 37.8363 6.32529 37.256C6.32529 36.6758 6.43959 36.1012 6.66165 35.5651C6.88371 35.029 7.20919 34.5419 7.6195 34.1315C8.02981 33.7212 8.51693 33.3958 9.05302 33.1737C9.58913 32.9516 10.1637 32.8373 10.744 32.8373C11.9159 32.8373 13.0398 33.3029 13.8685 34.1315C14.6971 34.9602 15.1627 36.0841 15.1627 37.256C15.1627 38.4279 14.6971 39.5519 13.8685 40.3805C13.0398 41.2092 11.9159 41.6747 10.744 41.6747Z'
              fill={color}
            />
          </g>
        </svg>
      );
    case 'purchase':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='48'
          height='40'
          viewBox='0 0 48 40'
          fill='none'>
          <path
            d='M48 16.7765V17.0197C48 18.2356 47.0154 19.2083 45.7846 19.2083H43.8154L39.0154 38.2978C38.7692 39.2705 37.9077 40 36.8 40H11.3231C10.2154 40 9.35385 39.2705 9.10769 38.2978L4.18462 19.3299H2.21538C0.984616 19.3299 0 18.3572 0 17.1413C0 15.9254 0.984616 14.9527 2.21538 14.9527H45.9077C47.0154 14.9527 47.7538 15.6822 48 16.7765ZM17.2308 21.5185C17.2308 20.3026 16.2462 19.3299 15.0154 19.3299C13.7846 19.3299 12.8 20.3026 12.8 21.5185V33.4342C12.8 34.6501 13.7846 35.6228 15.0154 35.6228C16.2462 35.6228 17.2308 34.6501 17.2308 33.4342V21.5185ZM26.2154 21.5185C26.2154 20.3026 25.2308 19.3299 24 19.3299C22.7692 19.3299 21.7846 20.3026 21.7846 21.5185V33.4342C21.7846 34.6501 22.7692 35.6228 24 35.6228C25.2308 35.6228 26.2154 34.6501 26.2154 33.4342V21.5185ZM35.2 21.5185C35.2 20.3026 34.2154 19.3299 32.9846 19.3299C31.7538 19.3299 30.7692 20.3026 30.7692 21.5185V33.4342C30.7692 34.6501 31.7538 35.6228 32.9846 35.6228C34.2154 35.6228 35.2 34.6501 35.2 33.4342V21.5185ZM26.5846 3.2802C26.4615 2.91543 26.3385 2.55066 26.3385 2.1859C26.3371 1.69352 26.5046 1.2152 26.8135 0.828815C27.1224 0.442427 27.5546 0.170728 28.0398 0.0579548C28.5249 -0.0548189 29.0345 -0.00202562 29.4854 0.207739C29.9363 0.417504 30.302 0.771886 30.5231 1.21319L36.5538 12.6425H31.5077L26.5846 3.2802ZM21.4154 3.2802L16.4923 12.6425H11.4462L17.4769 1.21319C17.698 0.771886 18.0637 0.417504 18.5146 0.207739C18.9656 -0.00202562 19.4751 -0.0548189 19.9602 0.0579548C20.4454 0.170728 20.8776 0.442427 21.1865 0.828815C21.4954 1.2152 21.6629 1.69352 21.6615 2.1859C21.7846 2.67225 21.6615 3.03702 21.4154 3.2802Z'
            fill={color}
          />
        </svg>
      );
    case 'production':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='43'
          height='42'
          viewBox='0 0 43 42'
          fill='none'>
          <path
            d='M0.40625 30.3438H36.9688C38.4606 30.3438 39.8913 30.9364 40.9462 31.9913C42.0011 33.0462 42.5938 34.4769 42.5938 35.9688C42.5938 37.4606 42.0011 38.8913 40.9462 39.9462C39.8913 41.0011 38.4606 41.5938 36.9688 41.5938H0.40625V30.3438ZM36.9688 38.7812C37.7147 38.7812 38.43 38.4849 38.9575 37.9575C39.4849 37.43 39.7812 36.7147 39.7812 35.9688C39.7812 35.2228 39.4849 34.5075 38.9575 33.98C38.43 33.4526 37.7147 33.1562 36.9688 33.1562C36.2228 33.1562 35.5075 33.4526 34.98 33.98C34.4526 34.5075 34.1562 35.2228 34.1562 35.9688C34.1562 36.7147 34.4526 37.43 34.98 37.9575C35.5075 38.4849 36.2228 38.7812 36.9688 38.7812ZM27.125 38.7812C27.8709 38.7812 28.5863 38.4849 29.1137 37.9575C29.6412 37.43 29.9375 36.7147 29.9375 35.9688C29.9375 35.2228 29.6412 34.5075 29.1137 33.98C28.5863 33.4526 27.8709 33.1562 27.125 33.1562C26.3791 33.1562 25.6637 33.4526 25.1363 33.98C24.6088 34.5075 24.3125 35.2228 24.3125 35.9688C24.3125 36.7147 24.6088 37.43 25.1363 37.9575C25.6637 38.4849 26.3791 38.7812 27.125 38.7812ZM17.2812 38.7812C18.0272 38.7812 18.7425 38.4849 19.27 37.9575C19.7974 37.43 20.0938 36.7147 20.0938 35.9688C20.0938 35.2228 19.7974 34.5075 19.27 33.98C18.7425 33.4526 18.0272 33.1562 17.2812 33.1562C16.5353 33.1562 15.82 33.4526 15.2925 33.98C14.7651 34.5075 14.4688 35.2228 14.4688 35.9688C14.4688 36.7147 14.7651 37.43 15.2925 37.9575C15.82 38.4849 16.5353 38.7812 17.2812 38.7812ZM7.4375 38.7812C8.18342 38.7812 8.89879 38.4849 9.42624 37.9575C9.95368 37.43 10.25 36.7147 10.25 35.9688C10.25 35.2228 9.95368 34.5075 9.42624 33.98C8.89879 33.4526 8.18342 33.1562 7.4375 33.1562C6.69158 33.1562 5.97621 33.4526 5.44876 33.98C4.92132 34.5075 4.625 35.2228 4.625 35.9688C4.625 36.7147 4.92132 37.43 5.44876 37.9575C5.97621 38.4849 6.69158 38.7812 7.4375 38.7812ZM28.5312 20.5H36.9688V28.9375H28.5312V20.5ZM3.21875 0.8125H11.6562V9.25H3.21875V0.8125ZM15.875 0.8125H24.3125V9.25H15.875V0.8125ZM28.5312 10.6562H36.9688V19.0938H28.5312V10.6562ZM15.875 20.5H24.3125V28.9375H15.875V20.5ZM15.875 10.6562H24.3125V19.0938H15.875V10.6562ZM3.21875 20.5H11.6562V28.9375H3.21875V20.5ZM3.21875 10.6562H11.6562V19.0938H3.21875V10.6562Z'
            fill={color}
          />
        </svg>
      );
    case 'stock':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='38'
          height='41'
          viewBox='0 0 38 41'
          fill='none'>
          <path
            d='M1.90366e-05 17.2696C1.90366e-05 14.883 1.51157 11.7994 3.38624 10.3903C3.38624 10.3903 16.8889 0 19 0C21.1111 0 34.6349 10.4054 34.6349 10.4054C36.4927 11.808 38 14.8765 38 17.2675V36.6777C38.0011 37.2442 37.893 37.8054 37.682 38.3291C37.4709 38.8529 37.161 39.3291 36.7699 39.7305C36.3788 40.1318 35.9142 40.4505 35.4027 40.6683C34.8911 40.8862 34.3425 40.9989 33.7883 41H4.21168C3.65733 40.9994 3.10853 40.8871 2.5967 40.6695C2.08487 40.4519 1.62004 40.1332 1.22885 39.7317C0.837645 39.3303 0.527747 38.8539 0.316889 38.3298C0.106031 37.8058 -0.00164679 37.2444 1.90366e-05 36.6777V17.2696ZM8.44446 15.1247V19.4016C8.44446 20.623 9.39868 21.5789 10.5767 21.5789H14.7567C15.9537 21.5789 16.8889 20.6036 16.8889 19.3995V15.1268C16.8889 13.9033 15.9347 12.9474 14.7567 12.9474H10.5767C9.37968 12.9474 8.44446 13.9227 8.44446 15.1268V15.1247ZM23.826 12.6992L20.8704 15.7224C20.026 16.5856 20.0366 17.9537 20.8704 18.8039L23.826 21.8249C24.6704 22.6881 26.0089 22.6773 26.8407 21.8249L29.7962 18.8039C30.6407 17.9407 30.6301 16.5726 29.7962 15.7224L26.8407 12.7014C25.9962 11.8382 24.6578 11.849 23.826 12.7014V12.6992ZM8.44446 28.0742V32.3512C8.44446 33.5725 9.39868 34.5285 10.5767 34.5285H14.7567C15.9537 34.5285 16.8889 33.5531 16.8889 32.349V28.0764C16.8889 26.8528 15.9347 25.8969 14.7567 25.8969H10.5767C9.37968 25.8969 8.44446 26.8723 8.44446 28.0764V28.0742ZM21.1111 28.0742V32.3512C21.1111 33.5725 22.0653 34.5285 23.2433 34.5285H27.4233C28.6203 34.5285 29.5556 33.5531 29.5556 32.349V28.0764C29.5556 26.8528 28.6013 25.8969 27.4233 25.8969H23.2433C22.0463 25.8969 21.1111 26.8723 21.1111 28.0764V28.0742Z'
            fill={color}
          />
        </svg>
      );
    case 'semiProduction':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='48'
          height='48'
          viewBox='0 0 48 48'
          fill='none'>
          <path
            d='M39.2677 25.9501C39.35 25.3101 39.4118 24.66 39.4118 24C39.4118 23.34 39.35 22.6901 39.2677 22.0499L43.6155 18.74C44.006 18.44 44.1189 17.9 43.862 17.46L39.7509 10.5301C39.6279 10.3232 39.4341 10.1662 39.2042 10.0874C38.9744 10.0085 38.7237 10.013 38.4969 10.1L33.3786 12.1101C32.3201 11.32 31.1586 10.6501 29.9046 10.14L29.1338 4.84C29.0412 4.36986 28.6201 4 28.1061 4H19.8838C19.6412 4.0001 19.4064 4.08429 19.2204 4.23785C19.0344 4.39141 18.9091 4.60456 18.8664 4.84L18.0955 10.14C16.8547 10.6433 15.6866 11.3057 14.6215 12.1101L9.50323 10.1C9.04066 9.93014 8.50609 10.1 8.24923 10.5301L4.13809 17.46C3.88095 17.8901 3.99409 18.4299 4.38466 18.74L8.7218 22.0499C8.6398 22.6899 8.57809 23.34 8.57809 24C8.57809 24.66 8.6398 25.3099 8.72209 25.9501L4.38495 29.26C3.99409 29.56 3.88124 30.1 4.13809 30.54L8.24923 37.4699C8.50637 37.9 9.04066 38.08 9.50323 37.9L14.6215 35.8899C15.6801 36.68 16.8415 37.3499 18.0955 37.86L18.8664 43.16C18.9091 43.3954 19.0344 43.6086 19.2204 43.7621C19.4064 43.9157 19.6412 43.9999 19.8838 44H28.1061C28.3486 43.9999 28.5835 43.9157 28.7695 43.7621C28.9555 43.6086 29.0808 43.3954 29.1235 43.16L29.8943 37.86C31.1353 37.3568 32.3034 36.6943 33.3686 35.8899L38.4866 37.9C38.9495 38.0699 39.4837 37.9 39.7409 37.4699L43.8517 30.54C44.1089 30.1099 43.9957 29.5701 43.6052 29.26L39.2677 25.9501Z'
            fill={color}
          />
          <path
            d='M34.3925 18C32.8013 15.2438 30.1802 13.2327 27.1061 12.409C24.032 11.5852 20.7565 12.0165 18.0003 13.6078C15.2441 15.199 13.233 17.8201 12.4093 20.8942C11.5855 23.9683 12.0168 27.2438 13.6081 30L34.3925 18Z'
            fill='white'
          />
          <path
            d='M13.6081 30C15.1993 32.7562 17.8203 34.7673 20.8945 35.591C23.9686 36.4148 27.2441 35.9835 30.0003 34.3922C32.7564 32.801 34.7676 30.18 35.5913 27.1058C36.415 24.0317 35.9838 20.7562 34.3925 18L13.6081 30Z'
            fill={color}
          />
        </svg>
      );
    case 'product':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='44'
          height='44'
          viewBox='0 0 44 44'
          fill='none'>
          <path
            d='M22.66 2.77202L30.712 7.23802C31.152 7.67802 31.614 8.14002 31.614 8.58002V17.512C31.614 17.952 31.174 18.414 30.712 18.854L22.66 23.342C22.22 23.782 21.758 23.782 20.878 23.342L12.826 18.876C12.386 18.436 11.924 17.974 11.924 17.534V8.58002C11.924 8.14002 12.364 7.67802 12.826 7.23802L20.878 2.77202C21.34 2.33202 22.22 2.33202 22.66 2.77202ZM32.054 20.658C32.494 20.218 32.956 20.218 33.836 20.658L41.888 25.124C42.328 25.564 42.79 26.026 42.79 26.466V35.42C42.79 35.86 42.35 36.322 41.888 36.762L33.836 41.228C33.396 41.668 32.934 41.668 32.054 41.228L24.002 36.762C23.562 36.322 23.1 35.86 23.1 35.42V26.488C23.1 26.048 23.54 25.586 24.002 25.146L32.054 20.658ZM10.142 20.658C10.582 20.218 11.044 20.218 11.924 20.658L19.976 25.124C20.416 25.564 20.878 26.026 20.878 26.466V35.42C20.878 35.86 20.438 36.322 19.976 36.762L11.924 41.228C11.484 41.668 11.022 41.668 10.142 41.228L2.11196 36.762C1.67196 36.322 1.20996 35.86 1.20996 35.42V26.488C1.20996 26.048 1.64996 25.586 2.11196 25.146L10.142 20.658Z'
            fill={color}
          />
        </svg>
      );
    case 'polymerization':
      return (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='36'
          height='36'
          viewBox='0 0 36 36'
          fill='none'>
          <path
            d='M1.50075 22.5004H4.5C5.2245 22.5004 5.8275 23.0157 5.96925 23.6996L6.00075 24.0011V34.4993C6.00075 34.8461 5.88059 35.1823 5.66073 35.4505C5.44086 35.7188 5.13485 35.9026 4.79475 35.9708L4.5 36H1.50075C1.15554 35.9999 0.820919 35.8808 0.553285 35.6628C0.285652 35.4447 0.10137 35.1411 0.0315 34.803L0 34.4993V24.0011C0 23.2766 0.51525 22.6714 1.197 22.5319L1.50075 22.5004ZM15.3675 23.0629L15.6195 23.2429L17.6917 24.5141C17.9364 24.7591 18.0887 25.0812 18.1228 25.4257C18.1568 25.7703 18.0705 26.116 17.8785 26.4041L17.6917 26.6335L15.6217 27.0003H28.4985C28.7649 26.9997 29.0267 27.0701 29.2569 27.2043C29.4871 27.3386 29.6773 27.5317 29.8079 27.7639C29.9386 27.9961 30.005 28.2589 30.0002 28.5253C29.9955 28.7917 29.9198 29.052 29.781 29.2795L29.5583 29.5607L25.0583 34.0606C24.8495 34.2698 24.5832 34.4124 24.2933 34.4701L24.0008 34.4993H7.497V24.7841L12.9398 22.6084C13.4168 22.4172 14.7308 22.7097 15.3653 23.0629H15.3675ZM36 6.13668V17.0106C36 17.2356 35.8808 17.5731 35.5275 17.6833L25.4587 22.5004V11.181L36 6.13668ZM13.5 6.13668L24.039 11.0685V22.5004L13.9748 17.6811C13.7362 17.5686 13.5 17.3436 13.5 17.0083V6.13668ZM16.6973 9.72307C16.461 10.0606 16.578 10.5083 16.9335 10.6208L20.7248 12.414C21.078 12.5265 21.5528 12.414 21.6698 12.0765C21.789 11.739 21.6698 11.2935 21.3165 11.181L17.6445 9.38783C17.289 9.27534 16.8165 9.38783 16.6995 9.72532L16.6973 9.72307ZM25.1055 0.0843724L35.289 4.90372L24.75 9.94806L14.211 4.90372L24.3945 0.0843724C24.6308 -0.0281241 24.867 -0.0281241 25.1055 0.0843724Z'
            fill={color}
          />
        </svg>
      );
  }
};

const getSliderMessageCard = (orderType: string, data: any) => {
  const nodeType = transformNodeType(orderType);
  switch (nodeType) {
    case 'order':
      if (data.childrenLength >= 2 && orderType === 'VC') {
        return orderCardMessageForManyItemsOrder(data);
      }
      return orderCardMessage(data);
    case 'polymerization':
    case 'raw':
      return outputOrInputCardMessage(data);
    case 'purchase':
      return purchaseCardMessage(data);
    case 'production':
      return productionCardMessage(data);
    case 'transport':
      return transportCardMessage(data);
    case 'stock':
      return stockCardMessage(data);
    default:
      return [];
  }
};
const orderCardMessage = (data: any) => {
  return [
    {
      label: '需求日期',
      value: data.inputTime,
    },
    {
      label: '凭证号',
      value: data.orderNo,
    },
    {
      label: '需求类型',
      value: transformTypeToName(data.orderType),
    },
    {
      label: '物料名称',
      value: data.materialName,
    },
    {
      label: '客户名称',
      value: data.customerName,
    },
    {
      label: '需求数量',
      value: data.quantity,
    },
    {
      label: '未交付数量',
      value: data.un_supply_qty,
    },
    {
      label: '地点',
      value: data.locationName,
    },
  ];
};
//SO多条item的父组件的显示卡片
const orderCardMessageForManyItemsOrder = (data: any) => {
  return [
    {
      label: '需求日期范围',
      value: data?.timeRange,
    },
    {
      label: '凭证号',
      value: data.orderNo,
    },
    {
      label: '需求类型',
      value: transformTypeToName(data.orderType),
    },
    {
      label: '条目数量',
      value: data.childrenLength,
    },
    {
      label: '客户',
      value: data.customerName,
    },
  ];
};

const outputOrInputCardMessage = (data: any) => {
  if (data?.isItemInput) {
    return [
      {
        label: '需求日期',
        value: data?.inputTime || data?.outputTime,
      },
      {
        label: '凭证号',
        value: data?.orderNo,
      },
      {
        label: '条目号',
        value: data?.itemId,
      },
      {
        label: '需求类型',
        value: '销售订单',
      },
      {
        label: '客户',
        value: data?.customerName,
      },
      {
        label: '需求数量',
        value: data?.quantity,
      },
      {
        label: '未交付数量',
        value: data.un_supply_qty,
      },
      {
        label: '地点',
        value: data?.locationName,
      },
    ];
  }
  return [
    {
      label: '需求日期',
      value: data?.inputTime || data?.outputTime,
    },
    {
      label: '物料名称',
      value: data?.materialName,
    },
    {
      label: '数量',
      value: data?.quantity,
    },
    {
      label: '地点',
      value: data?.locationName,
    },
  ];
};
const purchaseCardMessage = (data: any) => {
  return [
    {
      label: '供应日期',
      value: data?.outputTime,
    },
    {
      label: '凭证号',
      value: data?.orderNo,
    },
    {
      label: '采购状态',
      value: transformTypeToName(data?.orderType),
    },
    {
      label: '物料名称',
      value: data?.materialName,
    },
    {
      label: '物料类别',
      value: data?.materialCategory,
    },
    {
      label: '数量',
      value: data?.quantity,
    },
    {
      label: '地点',
      value: data?.locationName,
    },
    {
      label: '供应商',
      value: data?.vendorName,
    },
  ];
};
const productionCardMessage = (data: any) => {
  return [
    {
      label: '凭证号',
      value: data?.orderNo,
    },
    {
      label: '凭证状态',
      value: transformTypeToName(data?.orderType),
    },
    {
      label: '物料名称',
      value: data?.materialName,
    },
    {
      label: '生产源',
      value: data?.ppSosId,
    },
    {
      label: '数量',
      value: data?.quantity,
    },
    {
      label: '地点',
      value: data?.locationName,
    },
  ];
};
const transportCardMessage = (data: any) => {
  return [
    {
      label: '凭证号',
      value: data?.orderNo,
    },
    {
      label: '凭证类型',
      value: transformTypeToName(data?.orderType),
    },
    {
      label: '物料名称',
      value: data?.materialName,
    },
    {
      label: '离港地点',
      value: data?.locationName,
    },
    {
      label: '入港地点',
      value: data?.locationTo,
    },
    {
      label: '数量',
      value: data?.quantity,
    },
    {
      label: '运输方式',
      value: data?.motName == '' ? '未知' : data.motName,
    },
  ];
};
const stockCardMessage = (data: any) => {
  return [
    {
      label: '凭证号',
      value: data?.orderNo,
    },
    {
      label: '物料名称',
      value: data?.materialName,
    },
    {
      label: '地点',
      value: data?.locationName,
    },
  ];
};
export default SiderMessageCard;
