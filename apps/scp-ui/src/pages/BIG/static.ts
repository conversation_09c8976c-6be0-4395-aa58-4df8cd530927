const data: any[] = [
  {
    id: '-690000002-290000002',
    source: '-690000002',
    target: '-290000002',
    quantity: 100,
    time: '2024-02-02',
    status: 'normal',
  },
  {
    id: '-190000014-690000002',
    source: '-190000014',
    target: '-690000002',
    quantity: 0,
    time: '2024-02-01',
    status: 'normal',
  },
  {
    id: '-190000013-690000002',
    source: '-190000013',
    target: '-690000002',
    quantity: 0,
    time: '2024-02-01',
    status: 'normal',
  },
  {
    id: '-290000011-190000013',
    source: '-290000011',
    target: '-190000013',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000003-290000003',
    source: '-690000003',
    target: '-290000003',
    quantity: 100,
    time: '2024-02-06',
    status: 'normal',
  },
  {
    id: '-190000016-690000003',
    source: '-190000016',
    target: '-690000003',
    quantity: 0,
    time: '2024-02-05',
    status: 'normal',
  },
  {
    id: '-190000015-690000003',
    source: '-190000015',
    target: '-690000003',
    quantity: 0,
    time: '2024-02-05',
    status: 'normal',
  },
  {
    id: '-290000012-190000015',
    source: '-290000012',
    target: '-190000015',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000004-290000004',
    source: '-690000004',
    target: '-290000004',
    quantity: 100,
    time: '2024-02-09',
    status: 'normal',
  },
  {
    id: '-190000018-690000004',
    source: '-190000018',
    target: '-690000004',
    quantity: 0,
    time: '2024-02-08',
    status: 'normal',
  },
  {
    id: '-190000017-690000004',
    source: '-190000017',
    target: '-690000004',
    quantity: 0,
    time: '2024-02-08',
    status: 'normal',
  },
  {
    id: '-290000013-190000017',
    source: '-290000013',
    target: '-190000017',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000005-290000005',
    source: '-690000005',
    target: '-290000005',
    quantity: 100,
    time: '2024-02-12',
    status: 'normal',
  },
  {
    id: '-190000020-690000005',
    source: '-190000020',
    target: '-690000005',
    quantity: 0,
    time: '2024-02-11',
    status: 'normal',
  },
  {
    id: '-190000019-690000005',
    source: '-190000019',
    target: '-690000005',
    quantity: 0,
    time: '2024-02-11',
    status: 'normal',
  },
  {
    id: '-290000014-190000019',
    source: '-290000014',
    target: '-190000019',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000006-290000006',
    source: '-690000006',
    target: '-290000006',
    quantity: 100,
    time: '2024-03-02',
    status: 'normal',
  },
  {
    id: '-190000022-690000006',
    source: '-190000022',
    target: '-690000006',
    quantity: 0,
    time: '2024-03-01',
    status: 'normal',
  },
  {
    id: '-190000021-690000006',
    source: '-190000021',
    target: '-690000006',
    quantity: 0,
    time: '2024-03-01',
    status: 'normal',
  },
  {
    id: '-290000015-190000021',
    source: '-290000015',
    target: '-190000021',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '900000025-190000021',
    source: '900000025',
    target: '-190000021',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000007-290000007',
    source: '-690000007',
    target: '-290000007',
    quantity: 100,
    time: '2024-03-03',
    status: 'normal',
  },
  {
    id: '-190000024-690000007',
    source: '-190000024',
    target: '-690000007',
    quantity: 0,
    time: '2024-03-02',
    status: 'normal',
  },
  {
    id: '-190000023-690000007',
    source: '-190000023',
    target: '-690000007',
    quantity: 0,
    time: '2024-03-02',
    status: 'normal',
  },
  {
    id: '-290000016-190000023',
    source: '-290000016',
    target: '-190000023',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000008-290000008',
    source: '-690000008',
    target: '-290000008',
    quantity: 100,
    time: '2024-03-04',
    status: 'normal',
  },
  {
    id: '-190000026-690000008',
    source: '-190000026',
    target: '-690000008',
    quantity: 0,
    time: '2024-03-03',
    status: 'normal',
  },
  {
    id: '-190000025-690000008',
    source: '-190000025',
    target: '-690000008',
    quantity: 0,
    time: '2024-03-03',
    status: 'normal',
  },
  {
    id: '-290000017-190000025',
    source: '-290000017',
    target: '-190000025',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000009-290000009',
    source: '-690000009',
    target: '-290000009',
    quantity: 100,
    time: '2024-03-05',
    status: 'normal',
  },
  {
    id: '-190000028-690000009',
    source: '-190000028',
    target: '-690000009',
    quantity: 0,
    time: '2024-03-04',
    status: 'normal',
  },
  {
    id: '-190000027-690000009',
    source: '-190000027',
    target: '-690000009',
    quantity: 0,
    time: '2024-03-04',
    status: 'normal',
  },
  {
    id: '-290000018-190000027',
    source: '-290000018',
    target: '-190000027',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-690000025-290000021',
    source: '-690000025',
    target: '-290000021',
    quantity: 100,
    time: '2024-02-01',
    status: 'normal',
  },
  {
    id: '-690000026-290000022',
    source: '-690000026',
    target: '-290000022',
    quantity: 100,
    time: '2024-02-05',
    status: 'normal',
  },
  {
    id: '-690000027-290000023',
    source: '-690000027',
    target: '-290000023',
    quantity: 100,
    time: '2024-02-08',
    status: 'normal',
  },
  {
    id: '-690000028-290000024',
    source: '-690000028',
    target: '-290000024',
    quantity: 100,
    time: '2024-02-11',
    status: 'normal',
  },
  {
    id: '-690000029-290000025',
    source: '-690000029',
    target: '-290000025',
    quantity: 100,
    time: '2024-02-20',
    status: 'normal',
  },
  {
    id: '-690000030-290000026',
    source: '-690000030',
    target: '-290000026',
    quantity: 100,
    time: '2024-03-01',
    status: 'normal',
  },
  {
    id: '-690000031-290000027',
    source: '-690000031',
    target: '-290000027',
    quantity: 100,
    time: '2024-03-02',
    status: 'normal',
  },
  {
    id: '-690000032-290000028',
    source: '-690000032',
    target: '-290000028',
    quantity: 100,
    time: '2024-03-03',
    status: 'normal',
  },
  {
    id: '-690000033-290000029',
    source: '-690000033',
    target: '-290000029',
    quantity: 100,
    time: '2024-03-04',
    status: 'normal',
  },
  {
    id: '53200005900000024',
    source: '53200005',
    target: '900000024',
    quantity: 100,
    time: '2024-02-20',
    status: 'normal',
  },
  {
    id: '80000000753200005',
    source: '800000007',
    target: '53200005',
    quantity: 0,
    time: '2024-02-20',
    status: 'normal',
  },
  {
    id: '-290000025800000007',
    source: '-290000025',
    target: '800000007',
    quantity: 0,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000002900000034',
    source: '-290000002',
    target: '900000034',
    quantity: 100,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000003900000034',
    source: '-290000003',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000004900000034',
    source: '-290000004',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000005900000034',
    source: '-290000005',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000006900000034',
    source: '-290000006',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000007900000034',
    source: '-290000007',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000008900000034',
    source: '-290000008',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '-290000009900000034',
    source: '-290000009',
    target: '900000034',
    quantity: 200,
    time: '',
    status: 'normal',
  },
  {
    id: '900000024900000034',
    source: '900000024',
    target: '900000034',
    quantity: 500,
    time: '',
    status: 'normal',
  },
  {
    id: '900000034',
    node_type: 'FCT',
    order_id: '900000034',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 2000,
    output_quantity: 0,
    requested_quantity: 18000,
  },
  {
    id: '-290000002',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000003',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000004',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000005',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000006',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000007',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000008',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000009',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 200,
    requested_quantity: 0,
  },
  {
    id: '-290000011',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000012',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000013',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000014',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000015',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000016',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000017',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000018',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000021',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000022',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000023',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000024',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000025',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000026',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000027',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000028',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-290000029',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '900000025',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '900000024',
    node_type: 'output',
    order_id: '',
    material_name: 'TR1_成品1',
    location_name: 'TR1_长三角',
    material_category: '类别1',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 500,
    requested_quantity: 0,
  },
  {
    id: '-690000002',
    node_type: 'JPA',
    order_id: '-690000002',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000014',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000013',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000003',
    node_type: 'JPA',
    order_id: '-690000003',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000016',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000015',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000004',
    node_type: 'JPA',
    order_id: '-690000004',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000018',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000017',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000005',
    node_type: 'JPA',
    order_id: '-690000005',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000020',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000019',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000006',
    node_type: 'JPA',
    order_id: '-690000006',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000022',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000021',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000007',
    node_type: 'JPA',
    order_id: '-690000007',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000024',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000023',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000008',
    node_type: 'JPA',
    order_id: '-690000008',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000026',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000025',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000009',
    node_type: 'JPA',
    order_id: '-690000009',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000028',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-190000027',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品2',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 100,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000025',
    node_type: 'JBP',
    order_id: '-690000025',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000026',
    node_type: 'JBP',
    order_id: '-690000026',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000027',
    node_type: 'JBP',
    order_id: '-690000027',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000028',
    node_type: 'JBP',
    order_id: '-690000028',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000029',
    node_type: 'JBP',
    order_id: '-690000029',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000030',
    node_type: 'JBP',
    order_id: '-690000030',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000031',
    node_type: 'JBP',
    order_id: '-690000031',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000032',
    node_type: 'JBP',
    order_id: '-690000032',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '-690000033',
    node_type: 'JBP',
    order_id: '-690000033',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '53200005',
    node_type: 'FE',
    order_id: '53200005',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
  {
    id: '800000007',
    node_type: '',
    order_id: '',
    material_name: 'TR1_半成品1',
    location_name: 'TR1_长三角',
    material_category: '类别3',
    price: 50,
    status: 'normal',
    input_quantity: 0,
    output_quantity: 0,
    requested_quantity: 0,
  },
];
export default data;
