import { useState, useEffect } from 'react';
import { Popover, Radio, Flex, Button, ConfigProvider } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import IDTable from './child/IDTable';
import IDSelected from './child/IDSelected';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
import { t } from '@/languages';

type MappingType = {
  [key: string]: { label: string; value: string[] };
};
const mapping: MappingType = {
  production: { label: t('生产'), value: ['JPA', 'PA', 'FE'] },
  transportation: { label: t('运输'), value: ['BES', 'BAS', 'JBS'] },
  purchase: { label: t('采购'), value: ['BAP', 'BEP', 'JBP'] },
  inventory: { label: t('库存'), value: ['STK'] },
  order: { label: t('预测订单'), value: ['FCT'] },
  so: { label: t('SO'), value: ['VC', 'VJ'] },
  ld: { label: t('时序需求'), value: ['LD'] },
};

function RightSelector({
  paPvId,
  rowData,
  setRowData,
  firstSelectValue,
  setSecondSelectValue,
  thirdSelectValue,
  setThirdSelectValue,
  IDSelectButtonShow,
  onSearch,
  setThirdSelectDisable,
  loading,
}: {
  paPvId: any;
  rowData: any[];
  setRowData: any;
  firstSelectValue: any;
  setSecondSelectValue: any;
  thirdSelectValue: string;
  setThirdSelectValue: any;
  IDSelectButtonShow: boolean;
  onSearch: any;
  setThirdSelectDisable: any;
  loading: boolean;
}) {
  const { t } = useVoerkaI18n();

  useEffect(() => {
    getIDFormData();
    if (thirdSelectValue !== 'order') {
      setOptions(
        [
          'production',
          'transportation',
          'purchase',
          'inventory',
          ...(thirdSelectValue === 'delivery' ? ['order', 'so'] : []),
        ].map((item) => {
          return {
            label: t(mapping[item as keyof MappingType].label),
            value: item,
            style: {
              height: '27px',
              fontSize: '14px',
              lineHeight: '27px',
              borderRadius: '4px',
            },
          };
        }),
      );
    } else {
      setOptions(
        ['order', 'so', 'ld'].map((item) => {
          return {
            label: t(mapping[item as keyof MappingType].label),
            value: item,
            style: {
              height: '27px',
              fontSize: '14px',
              lineHeight: '27px',
              borderRadius: '4px',
            },
          };
        }),
      );
    }
    if (leftPopOpen) setLeftPopOpen(false);
  }, [thirdSelectValue]);

  const [rowTypes, setRowTypes] = useState<any>();
  const [rowTitles, setRowTitles] = useState<any>();
  const [type, setType] = useState<any>();
  const [titles, setTitles] = useState<any>();
  const [data, setData] = useState<any>();
  const [dataNeedUpdate, setDataNeedUpdate] = useState(false);

  useEffect(() => {
    if (!rowData || rowData.length === 0) {
      setData(null);
      return;
    }
    if (dataNeedUpdate) {
      setData(
        rowData.map((item) => {
          return {
            selected: false,
            itemID: item.itemId,
            itemNo: item.itemNo,
            key: item.key + '_' + item.itemId,
            OrderID: item.value,
            type: item.type,
            ...item.content,
          };
        }),
      );
      setDataNeedUpdate(false);
    }
  }, [rowData]);

  useEffect(() => {
    if (!rowData || rowData.length === 0) {
      setRowTypes(null);
      // message.warning(t('查询到的数据为空'));
      return;
    }
    setRowTypes([...new Set(rowData.map((item) => item.type))]);
  }, [rowData]);

  useEffect(() => {
    if (!rowTypes) {
      setRowTitles(null);
      return;
    }
    setRowTitles(
      rowTypes.map((type: any) => {
        const titleList = ['OrderID']
          .concat(
            Object.keys(
              rowData.find((item) => item.type === type)?.content || {},
            ),
          )
          .filter((item) => item != 'order_id');
        return {
          type: type,
          titles: [
            ...titleList.map((item) => {
              return {
                key: item,
                title: item,
              };
            }),
          ],
        };
      }),
    );
  }, [rowTypes]);

  useEffect(() => {
    if (!rowTitles) return;
    const types = mapping[type].value;
    setTitles(rowTitles.find((item: any) => types.includes(item.type))?.titles);
  }, [rowTitles]);

  const [options, setOptions] = useState<any>();

  const [leftPopOpen, setLeftPopOpen] = useState(false);

  useEffect(() => {
    if (!options) return;
    setType(options[0].value);
  }, [options]);

  const leftPopOpenStyle = {
    backgroundColor: '#0C40DE',
    color: 'white',
  };

  const leftPopCloseStyle = {
    backgroudColor: 'white',
    color: '#1890FF',
    border: '1px solid #1890FF',
  };

  function handleClickReset() {
    setLeftPopOpen(false);
    setSecondSelectValue('');
    setThirdSelectValue('');
    setThirdSelectDisable(true);
    if (!data) return;
    setData(
      data.map((item: any) => {
        return {
          ...item,
          selected: false,
        };
      }),
    );
  }

  const [BIGSearchParams, setBIGSearchParams] = useState<{
    orderIds: string[];
    paPvId: number;
  }>();

  useEffect(() => {
    if (!data) {
      return;
    }
    setBIGSearchParams({
      orderIds: data
        .filter((item: any) => item.selected)
        .map((item: any) => item.key as string),
      paPvId: 1,
    });
  }, [data]);
  useEffect(() => {}, [BIGSearchParams]);

  const getIDFormData = async () => {
    if (firstSelectValue) paPvId = firstSelectValue.value;
    let rowData: any;
    let requested = false;
    switch (thirdSelectValue) {
      case 'risk': {
        rowData = await ajax.getDelayRiskOrder({ paPvId: paPvId });
        requested = true;
        break;
      }
      case 'redun': {
        rowData = await ajax.getRedundancyRiskOrder({ paPvId: paPvId });
        requested = true;
        break;
      }
      case 'delivery': {
        rowData = await ajax.getDeliveryRiskOrder({ paPvId: paPvId });
        requested = true;
        break;
      }
      case 'certification': {
        rowData = await ajax.getAllCertificate({ paPvId: paPvId });
        requested = true;
        break;
      }
      case 'order': {
        rowData = await ajax.getAllOrder({ paPvId: paPvId });
        requested = true;
        break;
      }
    }
    if (requested && rowData && rowData.data) setRowData(rowData.data);
    setDataNeedUpdate(true);
  };

  return (
    <Flex justify='space-between' gap={'large'} style={{ flex: '1' }}>
      <Flex gap={'middle'}>
        <ConfigProvider
          theme={{
            components: {
              Popover: {
                borderRadiusLG: 20,
              },
              Radio: {
                buttonSolidCheckedBg: '#1890FF',
                buttonSolidCheckedHoverBg: '#1890FF',
                colorBorder: '#fff',
                colorPrimary: 'none',
                colorPrimaryActive: 'none',
                colorPrimaryHover: 'none',
              },
            },
          }}>
          <Popover
            content={
              <Flex
                vertical={true}
                gap={'10px'}
                style={{
                  height: '400px',
                  width: '600px',
                  padding: '0 0 45px 0',
                  position: 'relative',
                }}
                align='center'>
                <Radio.Group
                  options={options}
                  value={type}
                  optionType='button'
                  onChange={(e) => {
                    setType(e.target.value);
                    if (!rowTitles) return;
                    const nextTitles = rowTitles.find((item: any) =>
                      mapping[
                        e.target.value as keyof MappingType
                      ].value.includes(item.type),
                    );
                    setTitles(nextTitles ? nextTitles.titles : []);
                  }}
                  buttonStyle='solid'
                  style={{
                    border: '1px solid #E2E3E7',
                    borderRadius: '4px',
                    userSelect: 'none',
                  }}></Radio.Group>
                <div
                  style={{
                    width: '100%',
                    height: '250px',
                    position: 'relative',
                  }}>
                  <IDTable
                    types={type ? mapping[type].value : []}
                    titles={titles}
                    data={data}
                    setData={setData}
                  />
                </div>
                <Flex
                  vertical={false}
                  gap={'18px'}
                  justify='center'
                  align='center'
                  style={{
                    height: '48px',
                    position: 'absolute',
                    bottom: '10px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                  }}>
                  <Button
                    onClick={() => setLeftPopOpen(false)}
                    style={{
                      width: '84px',
                      height: '30px',
                      borderRadius: '15px',
                      fontSize: '14px',
                      lineHeight: '30px',
                      backgroundColor: '#1890FF',
                      color: 'white',
                      border: '1px solid #1890FF',
                    }}>
                    {t('确认')}
                  </Button>
                  <Button
                    onClick={handleClickReset}
                    style={{
                      width: '84px',
                      height: '30px',
                      borderRadius: '15px',
                      fontSize: '14px',
                      lineHeight: '30px',
                      border: '1px solid #1890FF',
                      color: '#1890FF',
                    }}>
                    {t('重置')}
                  </Button>
                </Flex>
              </Flex>
            }
            trigger={'click'}
            arrow={false}
            placement='bottom'
            open={leftPopOpen}
            onOpenChange={(open) => {
              if (!data) {
                return;
              } else {
                setLeftPopOpen(open);
              }
            }}
            rootClassName='my-popover-class'>
            <Button
              onClick={() => {
                setLeftPopOpen(!leftPopOpen);
              }}
              style={{
                display: IDSelectButtonShow ? 'block' : 'none',
                ...(leftPopOpen ? leftPopOpenStyle : leftPopCloseStyle),
              }}>
              {t('选择订单')}
            </Button>
          </Popover>
        </ConfigProvider>
        <Button
          loading={loading}
          type='primary'
          onClick={() => {
            const selectedDataWithoutItemID = data
              .filter((item: any) => item.selected)
              .filter((item: any) => !item.itemID);
            const searchOrderIds = selectedDataWithoutItemID.map(
              (item: any) => item.key.split('_')[0] as string,
            );
            const selectedDataWithItemID = data
              .filter((item: any) => item.selected)
              .filter((item: any) => item.itemID);
            const orderIdsWithItemID = [
              ...new Set(
                selectedDataWithItemID.map(
                  (item: any) => item.key.split('_')[0] as string,
                ),
              ),
            ];
            orderIdsWithItemID.forEach((orderIds) => {
              const items = data.filter(
                (item: any) => item.key.split('_')[0] === orderIds,
              );
              if (items.every((item: any) => item.selected)) {
                searchOrderIds.push(orderIds);
              } else {
                items.forEach((item: any) => {
                  if (item.selected) {
                    searchOrderIds.push(item.itemNo);
                  }
                });
              }
            });
            onSearch({
              paPvId: firstSelectValue ? firstSelectValue.value : paPvId,
              orderIds: searchOrderIds,
            });
          }}>
          {t('查询')}
        </Button>
        <Button
          style={{ border: '#E2E3E7 1px solid', color: '#D5D7DD' }}
          onClick={handleClickReset}>
          {t('重置')}
        </Button>
      </Flex>
      <Flex gap={'small'}>
        <Button
          icon={<ReloadOutlined />}
          color='primary'
          onClick={() => {
            onSearch({
              paPvId: firstSelectValue ? firstSelectValue.value : paPvId,
              orderIds: [],
            });
            setSecondSelectValue(null);
            setThirdSelectValue(null);
            setThirdSelectDisable(true);
            setRowData(null);
          }}></Button>
        <ConfigProvider
          theme={{
            components: {
              Popover: {
                borderRadiusLG: 20,
              },
            },
          }}>
          <Popover
            content={<IDSelected data={data} titles={rowTitles}></IDSelected>}
            trigger={'click'}
            arrow={false}
            placement='bottomRight'
            rootClassName='my-popover-class'>
            <Button>
              {t('已选择订单')}
              <div
                style={{
                  borderRadius: '50%',
                  width: '20px',
                  height: '20px',
                  backgroundColor: '#2F6BFF',
                  position: 'absolute',
                  top: '-10px',
                  right: '-10px',
                  textAlign: 'center',
                  lineHeight: '20px',
                  color: 'white',
                }}>
                {data ? data.filter((item: any) => item.selected).length : 0}
              </div>
            </Button>
          </Popover>
        </ConfigProvider>
      </Flex>
    </Flex>
  );
}

export default RightSelector;
