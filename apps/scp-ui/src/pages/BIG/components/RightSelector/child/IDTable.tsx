import {
  Table,
  ConfigProvider,
  Checkbox,
  Flex,
  Empty,
  AutoComplete,
} from 'antd';
import { useState, useEffect, useRef } from 'react';
import SearchIcon from '../../search.svg';
import { ColumnType } from 'antd/es/table';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSize } from 'ahooks';

function IDForm({
  types,
  titles,
  data,
  setData,
}: {
  types: string[];
  titles: { key: string; title: string }[];
  data: { selected: boolean; key: string; type: string; OrderID: string }[];
  setData: any;
}) {
  const { t } = useVoerkaI18n();
  const inputTheme = {
    activeBorderColor: 'none',
    hoverBorderColor: 'none',
    activeShadow: 'none',
    inputFontSize: 14,
    borderRadius: 6,
    colorBorder: '#fff',
    colorBgContainer: '#EFF2F3',
    paddingInline: 4,
  };

  const [columns, setColumns] = useState<any>([] as ColumnType<any>[]);
  const [dataSource, setDataSource] = useState<any>();
  const [autoOptions, setAutoOptions] = useState<any>({});
  const [searchValues, setSearchValues] = useState<any>({});

  useEffect(() => {
    const keys = Object.keys(searchValues);
    if (keys.length > 0) {
      let nextData = data;
      keys.forEach((key) => {
        nextData = nextData.filter((item) => {
          const dataValue: any = item[key as keyof typeof item];
          return dataValue && dataValue.match(searchValues[key]);
        });
      });
      setDataSource(nextData);
    }
  }, [searchValues]);

  useEffect(() => {
    if (!dataSource || !titles || titles.length === 0) {
      setColumns(null);
      return;
    }
    setColumns([
      {
        title: (
          <ConfigProvider
            theme={{
              token: inputTheme,
            }}>
            <AutoComplete
              options={
                Object.keys(autoOptions).includes('OrderID')
                  ? autoOptions['OrderID']
                  : []
              }
              placeholder={t('订单号')}
              prefix={<img src={SearchIcon} />}
              onFocus={() => {
                autoOptions['OrderID'] = [];
                setAutoOptions({ ...autoOptions });
              }}
              onChange={(e: any) => {
                const value = e;
                const key = 'OrderID';
                searchValues[key] = value;
                setSearchValues({ ...searchValues });
                if (value !== '') {
                  const nextData = dataSource
                    .map((item: any) => {
                      let searchShow = false;
                      const dataValue = item[key as keyof typeof item];
                      if (
                        typeof dataValue === 'string' &&
                        dataValue.match(value)
                      ) {
                        searchShow = true;
                      }
                      return {
                        ...item,
                        searchShow: searchShow,
                      };
                    })
                    .filter((item: any) => types.includes(item.type))
                    .filter((item: any) => item.searchShow);
                  const autoValues = new Set();
                  nextData.forEach((item: any) => {
                    autoValues.add(item[key as keyof typeof item]);
                  });
                  autoOptions[key] = [...autoValues]
                    .slice(0, 10)
                    .map((item) => ({
                      label: item,
                      value: item,
                    }));
                  setAutoOptions({ ...autoOptions });
                } else {
                  autoOptions[key] = [];
                  setAutoOptions({ ...autoOptions });
                }
              }}
              style={{
                width: '100%',
                textAlign: 'center',
              }}></AutoComplete>
          </ConfigProvider>
        ),
        width: '40px',
        onHeaderCell: () => {
          return {
            colSpan: 2,
          };
        },
        render: (_, record: any) => {
          const itemSlectList = dataSource.filter(
            (item: any) => item.OrderID === record.OrderID,
          );
          if (
            itemSlectList.length === 0 ||
            record.itemID !== itemSlectList[0].itemID
          ) {
            return <></>;
          }
          return (
            <Checkbox
              checked={record.selected}
              onChange={(e) => {
                if (itemSlectList.length > 1) {
                  if (
                    itemSlectList.every((item: any) => item.selected === true)
                  ) {
                    itemSlectList.forEach((item: any) => {
                      const dataItem = data.find(
                        (dItem) => dItem.key === item.key,
                      );
                      if (dataItem) {
                        dataItem.selected = false;
                      }
                    });
                  } else {
                    itemSlectList.forEach((item: any) => {
                      const dataItem = data.find(
                        (dItem) => dItem.key === item.key,
                      );
                      if (dataItem) {
                        dataItem.selected = true;
                      }
                    });
                  }
                  setData([...data]);
                  return;
                }
                const dataItem = data.find((item) => item.key === record.key);
                if (dataItem) {
                  dataItem.selected = e.target.checked;
                  setData([...data]);
                }
              }}
              indeterminate={
                itemSlectList.some((item: any) => item.selected === true)
                  ? !itemSlectList.every((item: any) => item.selected === true)
                  : false
              }></Checkbox>
          );
        },
      },
      ...titles.map((item) => {
        return {
          title: (
            <ConfigProvider
              theme={{
                token: inputTheme,
              }}>
              <AutoComplete
                options={
                  Object.keys(autoOptions).includes(item.title)
                    ? autoOptions[item.title]
                    : []
                }
                placeholder={
                  item.title === 'customer'
                    ? t('客户')
                    : item.title === 'factory'
                      ? t('工厂')
                      : item.title === 'material'
                        ? t('物料')
                        : item.title === 'departure'
                          ? t('出库')
                          : item.title === 'entry'
                            ? t('入库')
                            : item.title === 'productionSource'
                              ? t('生产源')
                              : item.title === 'store'
                                ? t('仓库')
                                : ''
                }
                prefix={<img src={SearchIcon} />}
                onFocus={() => {
                  autoOptions[item.title] = [];
                  setAutoOptions({ ...autoOptions });
                }}
                onChange={(e: any) => {
                  const key = item.title;
                  const value = e;
                  searchValues[key] = value;
                  setSearchValues({ ...searchValues });
                  if (value !== '') {
                    const nextData = dataSource
                      .map((item: any) => {
                        let searchShow = false;
                        const dataValue = item[key as keyof typeof item];
                        if (
                          typeof dataValue === 'string' &&
                          dataValue.match(value)
                        ) {
                          searchShow = true;
                        }
                        return {
                          ...item,
                          searchShow: searchShow,
                        };
                      })
                      .filter((item: any) => types.includes(item.type))
                      .filter((item: any) => item.searchShow);
                    const autoValues = new Set();
                    nextData.forEach((item: any) => {
                      autoValues.add(item[key as keyof typeof item]);
                    });
                    autoOptions[key] = [...autoValues]
                      .slice(0, 10)
                      .map((item) => ({
                        label: item,
                        value: item,
                      }));
                    setAutoOptions({ ...autoOptions });
                  } else {
                    autoOptions[key] = [];
                    setAutoOptions({ ...autoOptions });
                  }
                }}
                style={{
                  width: '100%',
                  textAlign: 'center',
                }}></AutoComplete>
            </ConfigProvider>
          ),
          dataIndex: item.key,
          key: item.key,
          onHeaderCell: (column: any) => {
            if (column.key === 'OrderID') {
              return {
                colSpan: 0,
              };
            }
            return {
              style: {
                height: '27px',
              },
            };
          },
          onCell: (record: any) => {
            return {
              style: {
                height: '22px',
                fontSize: '14px',
                lineHeight: '22px',
                color: record.selected ? '#0C40DE' : '#B9BCC6',
                cursor: 'pointer',
                userSelect: 'none',
              },
              onClick: () => {
                const dataItem = data.find((item) => item.key === record.key);
                if (dataItem) {
                  dataItem.selected = !dataItem.selected;
                  setData([...data]);
                }
              },
            };
          },
          ellipsis: true,
          render: (text: string, record: any, index: number) => {
            if (
              item.key === 'OrderID' &&
              dataSource.filter((item: any) => item.OrderID === record.OrderID)
                .length > 1
            ) {
              return (
                <Flex
                  gap={'middle'}
                  align='center'
                  style={{ height: '22px', lineHeight: '22px' }}>
                  {dataSource.findIndex(
                    (item: any) => item.OrderID === record.OrderID,
                  ) === index ? (
                    <p style={{ margin: 0 }}>{text}</p>
                  ) : (
                    <p style={{ margin: 0, color: 'transparent' }}>{text}</p>
                  )}
                  <Checkbox checked={record.selected}></Checkbox>
                  <p>{record.itemID}</p>
                </Flex>
              );
            }
            return <p style={{ margin: 0 }}>{text}</p>;
          },
        };
      }),
    ] as ColumnType<any>[]);
  }, [dataSource]);

  useEffect(() => {
    setSearchValues({});
  }, [types]);

  useEffect(() => {
    if (!data) {
      setDataSource(null);
      return;
    }
    const keys = Object.keys(searchValues);
    let nextData = data
      .map((item: any) => {
        return {
          ...item,
          searchShow: true,
        };
      })
      .filter((item) => types.includes(item.type))
      .filter((item) => item.searchShow);
    if (keys.length > 0) {
      keys.forEach((key) => {
        nextData = nextData.filter((item) => {
          const dataValue: any = item[key as keyof typeof item];
          return dataValue.match(searchValues[key]);
        });
      });
    }
    setDataSource(nextData);
  }, [types, data]);

  const containerRef = useRef(null);
  const containerSize = useSize(containerRef);

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: 'none',
            headerSplitColor: 'none',
            borderColor: 'none',
            cellPaddingBlock: 10,
            cellPaddingInline: 2.5,
            rowHoverBg: 'none',
            rowSelectedBg: 'none',
            rowSelectedHoverBg: 'none',
          },
        },
      }}>
      <div
        ref={containerRef}
        style={{
          position: 'relative',
          height: '100%',
          width: '100%',
        }}>
        {columns ? (
          <Table
            columns={columns}
            dataSource={data ? dataSource : []}
            rowKey='key'
            pagination={{
              pageSize: 10,
              position: ['bottomCenter'],
              hideOnSinglePage: true,
            }}
            scroll={{ y: (containerSize?.height || 0) - 50 }}
            style={{
              padding: '0 18px',
              width: containerSize?.width || 0,
            }}
            sticky={true}></Table>
        ) : (
          <Empty description={t('订单为空')}></Empty>
        )}
      </div>
    </ConfigProvider>
  );
}

export default IDForm;
