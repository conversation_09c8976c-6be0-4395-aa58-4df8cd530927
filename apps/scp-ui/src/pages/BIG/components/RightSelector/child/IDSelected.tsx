import { Divider, Flex, Empty, ConfigProvider } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';

type MappingType = {
  [key: string]: { label: string; value: string[] };
};
const mapping: MappingType = {
  production: { label: '生产', value: ['JPA', 'PA', 'FE'] },
  transportation: { label: '运输', value: ['BES', 'BAS', 'JBS'] },
  purchase: { label: '采购', value: ['BAP', 'BEP', 'JBP'] },
  inventory: { label: '库存', value: ['STK'] },
  order: { label: '预测订单', value: ['FCT'] },
  so: { label: 'SO', value: ['VC', 'VJ'] },
  ld: { label: '时序需求', value: ['LD'] },
};

const getLabeledType = (type: string) => {
  const result = Object.keys(mapping).filter((key) =>
    mapping[key].value.includes(type),
  );
  return result[0];
};

function IDSelected({
  data,
  titles,
}: {
  data: {
    selected: boolean;
    key: string;
    type: string;
    OrderID: string;
    itemID: string;
  }[];
  titles: any[];
}) {
  const { t } = useVoerkaI18n();
  if (!data) {
    return <Empty description={t('选择的订单为空')} />;
  }
  const orderTypes = [...new Set(data.map((item) => item.type))];
  const labeledTypes = [
    ...new Set(orderTypes.map((type) => getLabeledType(type))),
  ];
  if (data.filter((item) => item.selected).length === 0) {
    return <Empty description={t('选择的订单为空')} />;
  }
  return (
    <Flex vertical={true} style={{ padding: '20px', userSelect: 'none' }}>
      {labeledTypes.map((type) => {
        return (
          <TypeOrders key={type} data={data} type={type} titles={titles} />
        );
      })}
    </Flex>
  );
}

function TypeOrders({
  data,
  type,
  titles,
}: {
  data: {
    selected: boolean;
    key: string;
    type: string;
    OrderID: string;
    itemID: string;
  }[];
  type: string;
  titles: any[];
}) {
  const filteredData = data
    .filter((item) => mapping[type].value.includes(item.type))
    .filter((item) => item.selected);
  if (filteredData.length === 0) return;

  titles = titles.find((title) =>
    mapping[type].value.includes(title.type),
  ).titles;

  return (
    <ConfigProvider
      theme={{
        components: {
          Divider: {
            orientationMargin: 0,
            colorSplit: '#B9BCC6',
            colorTextHeading: '#B9BCC6',
            fontSize: 14,
          },
        },
      }}>
      <Divider orientation='left' style={{ fontWeight: 'bold' }}>
        {mapping[type].label}
      </Divider>
      {filteredData.map((item, index) => {
        const itemKeys = [
          ...new Set([
            'OrderID' as keyof typeof item,
            'itemID' as keyof typeof item,
            ...titles.map((title) => title.key as keyof typeof item),
          ]),
        ];
        return (
          <Flex
            key={item.key}
            vertical={false}
            gap={40}
            style={{
              marginBottom: 10,
              textAlign: 'center',
            }}>
            {itemKeys.map((key) => {
              if (
                key === 'OrderID' &&
                filteredData.findIndex(
                  (dItem) => dItem.OrderID === item.OrderID,
                ) !== index
              ) {
                return (
                  <p
                    key={key}
                    style={{ color: '#fff', flex: 1, whiteSpace: 'nowrap' }}>
                    {item[key]}
                  </p>
                );
              }
              if (item[key] === undefined) return null;
              return (
                <p
                  key={key}
                  style={{ color: '#0c40de', flex: 1, whiteSpace: 'nowrap' }}>
                  {item[key]}
                </p>
              );
            })}
          </Flex>
        );
      })}
    </ConfigProvider>
  );
}

export default IDSelected;
