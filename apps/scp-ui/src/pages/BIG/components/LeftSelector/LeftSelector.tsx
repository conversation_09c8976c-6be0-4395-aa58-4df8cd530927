import { ConfigProvider, Flex, Form, Select } from 'antd';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import RightArrow from '../right-arrow.svg';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSelector } from 'react-redux';

function LeftSelector({
  firstSelectValue,
  setFirstSelectValue,
  secondSelectValue,
  setSecondSelectValue,
  thirdSelectValue,
  setThirdSelectValue,
  thirdSelectDisable,
  setThirdSelectDisable,
}: {
  firstSelectValue: any;
  setFirstSelectValue: any;
  secondSelectValue: any;
  setSecondSelectValue: any;
  thirdSelectValue: any;
  setThirdSelectValue: any;
  thirdSelectDisable: boolean;
  setThirdSelectDisable: any;
}) {
  const { t } = useVoerkaI18n();
  const [form] = Form.useForm();
  const firstSelectOptions = useSelector(
    (state: any) => state.layout.pVersionList,
  );

  const user = useSelector((state: any) => state.login.user);

  useEffect(() => {
    setFirstSelectValue(
      firstSelectOptions.find((item: any) => item.key === user.preferPaPvId)
        ? firstSelectOptions.find((item: any) => item.key === user.preferPaPvId)
        : firstSelectOptions.find((item: any) => item.isBaseVersion),
    );
  }, []);

  const secondSelectOptions = [
    { value: 'ByQuestion', label: t('BI信息订单') },
    { value: 'ByBI', label: t('全部订单') },
  ];
  const thirdSelectOptionsByQuestion = [
    { value: 'risk', label: t('风险凭证') },
    { value: 'redun', label: t('冗余供应') },
    { value: 'delivery', label: t('交付风险') },
  ];
  const thirdSelectOptionsByBI = [
    { value: 'certification', label: t('供应订单') },
    { value: 'order', label: t('需求订单') },
  ];

  const selectCommonStyle = {
    width: '160px',
  };

  const selectedStyle = {
    color: '#0C40DE',
    borderBottom: '1px solid #0C40DE',
    height: '15px',
    lineHeight: '15px',
  };

  const firstSelectLabelRender = (props: any) => {
    const { label } = props;
    return (
      <Flex gap={'middle'} align='center'>
        <span>{t('计划版本')}</span>
        <span style={selectedStyle}>{label}</span>
      </Flex>
    );
  };

  const secondSelectLabelRender = (props: any) => {
    const { value, label } = props;
    return (
      <>
        {label ? (
          <span style={selectedStyle}>{label}</span>
        ) : (
          <span>{value}</span>
        )}
      </>
    );
  };

  const thirdSelectLabelRender = (props: any) => {
    const { value, label } = props;
    return (
      <>
        {label ? (
          <span style={selectedStyle}>{label}</span>
        ) : (
          <span>{value}</span>
        )}
      </>
    );
  };

  const dropDownStyle = {
    border: '#B9BCC6 1px solid',
    borderRadius: '10px',
    padding: '8px 0',
  };

  const arrowAnimations = {
    right: {
      rotate: 0,
    },
    down: {
      rotate: 90,
    },
  };

  const [firstSelectAnimation, setFirstSelectAnimation] = useState(
    arrowAnimations.right,
  );
  const [secondSelectAnimation, setSecondSelectAnimation] = useState(
    arrowAnimations.right,
  );
  const [thirdSelectAnimation, setThirdSelectAnimation] = useState(
    arrowAnimations.right,
  );

  useEffect(() => {
    if (!firstSelectValue) {
      return;
    }
    form.setFieldValue('firstSelect', firstSelectValue.value);
  }, [firstSelectValue]);

  useEffect(() => {
    if (!secondSelectValue) {
      form.setFieldValue('secondSelect', null);
      return;
    }
    form.setFieldValue('secondSelect', secondSelectValue);
    switch (secondSelectValue) {
      case 'ByQuestion': {
        setThirdSelectValue(thirdSelectOptionsByQuestion[0].value);
        setThirdSelectDisable(false);
        break;
      }
      case 'ByBI': {
        setThirdSelectValue(thirdSelectOptionsByBI[0].value);
        setThirdSelectDisable(false);
        break;
      }
    }
  }, [secondSelectValue]);

  useEffect(() => {
    if (!thirdSelectValue) {
      form.setFieldValue('thirdSelect', null);
      return;
    }
    form.setFieldValue('thirdSelect', thirdSelectValue);
  }, [thirdSelectValue]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            selectorBg: '#EFF2F3',
            colorBorder: 'none',
            borderRadius: 10,
            activeBorderColor: 'none',
            activeOutlineColor: 'none',
            optionSelectedBg: '#BFD0F8',
          },
        },
      }}>
      <Form form={form}>
        <Flex vertical={false} gap={'large'}>
          <Form.Item
            name={'firstSelect'}
            initialValue={
              firstSelectOptions.find(
                (item: any) => item.key === user.preferPaPvId,
              )
                ? firstSelectOptions.find(
                    (item: any) => item.key === user.preferPaPvId,
                  ).value
                : firstSelectOptions.find((item: any) => item.isBaseVersion)
                    .value
            }>
            <Select
              labelRender={firstSelectLabelRender}
              style={selectCommonStyle}
              options={firstSelectOptions}
              suffixIcon={
                <motion.img
                  src={RightArrow}
                  variants={arrowAnimations}
                  animate={firstSelectAnimation}
                />
              }
              onDropdownVisibleChange={(open) => {
                open
                  ? setFirstSelectAnimation(arrowAnimations.down)
                  : setFirstSelectAnimation(arrowAnimations.right);
              }}
              dropdownStyle={dropDownStyle}
              onChange={(value) => {
                setFirstSelectValue(
                  firstSelectOptions.find((item: any) => item.value === value),
                );
                setSecondSelectValue('');
                setThirdSelectValue('');
                setThirdSelectDisable(true);
              }}
            />
          </Form.Item>
          <Form.Item name={'secondSelect'}>
            <Select
              placeholder={t('按..筛选')}
              labelRender={secondSelectLabelRender}
              style={selectCommonStyle}
              options={secondSelectOptions}
              onChange={(value) => {
                setSecondSelectValue(value);
              }}
              suffixIcon={
                <motion.img
                  src={RightArrow}
                  variants={arrowAnimations}
                  animate={secondSelectAnimation}
                />
              }
              onDropdownVisibleChange={(open) => {
                open
                  ? setSecondSelectAnimation(arrowAnimations.down)
                  : setSecondSelectAnimation(arrowAnimations.right);
              }}
              dropdownStyle={dropDownStyle}
            />
          </Form.Item>
          <Form.Item name='thirdSelect'>
            <Select
              placeholder={t('请选择前置选项')}
              labelRender={thirdSelectLabelRender}
              style={selectCommonStyle}
              options={
                secondSelectValue === 'ByQuestion'
                  ? thirdSelectOptionsByQuestion
                  : thirdSelectOptionsByBI
              }
              onChange={(value) => {
                setThirdSelectValue(value);
              }}
              disabled={thirdSelectDisable}
              suffixIcon={
                <motion.img
                  src={RightArrow}
                  variants={arrowAnimations}
                  animate={thirdSelectAnimation}
                />
              }
              onDropdownVisibleChange={(open) => {
                open
                  ? setThirdSelectAnimation(arrowAnimations.down)
                  : setThirdSelectAnimation(arrowAnimations.right);
              }}
              dropdownStyle={dropDownStyle}
            />
          </Form.Item>
        </Flex>
      </Form>
    </ConfigProvider>
  );
}

export default LeftSelector;
