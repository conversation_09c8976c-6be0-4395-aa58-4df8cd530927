const isChooseOrder = () => {
  return (
    <svg
      width='1.14286rem'
      height='1.14286rem'
      viewBox='0 0 16 17'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g id='Group 405'>
        <path
          id='Vector 4'
          d='M12 5.5L6.84834 10.7687C6.45608 11.1699 5.81059 11.1699 5.41833 10.7687L4 9.31818'
          stroke='#0C40DE'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
        <rect
          id='Rectangle 251'
          x='0.5'
          y='1'
          width='15'
          height='15'
          rx='3.5'
          stroke='#1890FF'
        />
      </g>
    </svg>
  );
};

const unChooseOrder = () => {
  return (
    <svg
      width='1.14286rem'
      height='1.14286rem'
      viewBox='0 0 16 17'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g id='Group 405'>
        <rect
          id='Rectangle 251'
          x='0.5'
          y='1'
          width='15'
          height='15'
          rx='3.5'
          stroke='#B9BCC6'
        />
      </g>
    </svg>
  );
};
// 角标
// eslint-disable-next-line react-refresh/only-export-components
const SelectSubscript = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='1.5rem'
      height='1.42857rem'
      viewBox='0 0 15 15'
      fill='none'>
      <path
        d='M14.3135 6.6146C15.0271 6.98922 15.0271 8.01078 14.3135 8.3854L2.07833 14.8089C1.05731 15.3449 0.0897095 14.0125 0.915389 13.2075L6.03487 8.21597C6.43741 7.8235 6.43741 7.17644 6.03487 6.78397L0.915449 1.79255C0.0897663 0.98751 1.05735 -0.344891 2.07838 0.19115L14.3135 6.6146Z'
        fill='#BFD0F8'
      />
    </svg>
  );
};
// eslint-disable-next-line react-refresh/only-export-components
const Search = () => {
  return (
    <svg
      width='1.6rem'
      height='1.6rem'
      viewBox='0 0 17 17'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g id='search'>
        <path
          id='Rectangle 3124'
          d='M3 16L4.83333 14.1818M8.86667 14.1818C6.03161 14.1818 3.73333 11.9025 3.73333 9.09091C3.73333 6.27928 6.03161 4 8.86667 4C11.7017 4 14 6.27928 14 9.09091C14 11.9025 11.7017 14.1818 8.86667 14.1818Z'
          stroke='#B9BCC6'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
      </g>
    </svg>
  );
};
export default { isChooseOrder, unChooseOrder, SelectSubscript, Search };
