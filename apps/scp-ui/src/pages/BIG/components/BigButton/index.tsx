import { Form, message } from 'antd';
import { useEffect, useState } from 'react';
import BigModal from '../BigModal';
import BigIDListMdal from '../BigIDListModal';
import ajax from '@/api';
import './index.css';
import store from '@/store';
import { useVoerkaI18n } from '@voerkai18n/react';
type PropsType = {
  type: string;
  isChooseAllCondition?: boolean;
  sliderListData?: any;
  onClick?: any;
  pvId?: any;
  classify?: any;
};

const searchListData = ['单号', '物料', '工厂', '客户'];
const BigButton: React.FC<PropsType> = (props) => {
  const { t } = useVoerkaI18n();
  const {
    type,
    isChooseAllCondition,
    sliderListData,
    onClick,
    pvId,
    classify,
  } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [ischooseIdOpen, setIschooseIdOpen] = useState<boolean>(false);
  const [isIschooseIdOpen, setIsIschooseIdOpen] = useState<boolean>(false);
  const [chooseIdBtnClient, setChooseIdBtnClient] = useState<any[]>([]);
  const [orderListData, setOrderListData] = useState<any[]>([]);
  const [sliderList, setSliderList] = useState<any>([]);
  // 点击选择id按钮
  const chooseIdBtn = async () => {
    if (isChooseAllCondition) {
      if (!ischooseIdOpen) {
        if (localStorage.getItem('isHasOrderList')) {
          setIschooseIdOpen(!ischooseIdOpen);
        } else {
          getViewport();
          const params = {
            paPvId: pvId,
          };
          setIschooseIdOpen(!ischooseIdOpen);
          let res: any;
          console.log(classify, 'classify');

          if (classify === 'order') {
            res = await ajax.getAllOrder(params);
          } else if (classify === 'voucher') {
            res = await ajax.getAllCertificate(params);
          } else if (classify === 'riskVoucher') {
            res = await ajax.getDelayRiskOrder(params);
          } else if (classify === 'redundantSupply') {
            res = await ajax.getRedundancyRiskOrder(params);
          } else if (classify === 'deliveryRisk') {
            res = await ajax.getDeliveryRiskOrder(params);
          }

          if (res.code === 0) {
            const _orderListData: any[] = [];
            if (res.data) {
              res.data.map((item: any) => {
                _orderListData.push({
                  ...item,
                  ...item.content,
                  orderId: item.value,
                  isChoose: false,
                });
              });
              setOrderListData([..._orderListData]);
            } else {
              setOrderListData([]);
            }
            localStorage.setItem('isHasOrderList', 'true');
          }
        }
      } else {
        setIschooseIdOpen(!ischooseIdOpen);
      }
    } else {
      messageApi.open({
        type: 'error',
        content: t('请选择前置选项'),
      });
    }
  };
  // 点击选择id弹窗确认按钮
  const chooseIdOnOk = () => {
    setIschooseIdOpen(false);
  };
  // 点击选择id弹窗重置按钮
  const chooseIdOnRemake = () => {
    setIschooseIdOpen(false);
  };
  // 点击已选择id按钮
  const isChooseIdBtn = () => {
    if (store.getState().big.CHOOSE_LIST.length === 0) {
      messageApi.error('您还没有选择任何选项!');
      return;
    }
    getViewport();
    setIsIschooseIdOpen(!isIschooseIdOpen);
  };
  // 点击已选择id弹窗确认按钮
  const isChooseIdOnOk = (value: any) => {
    console.log(value, 'value');
    setIsIschooseIdOpen(false);
  };
  // 点击已选择id弹窗重置按钮
  const isChooseIdOnRemake = () => {
    setIsIschooseIdOpen(false);
  };

  // 获取弹窗在页面上的位置
  const getViewport = () => {
    function getBodyHeight() {
      return (
        document.body.clientHeight || document.documentElement.clientHeight
      );
    }
    const modal = document.getElementById('modal') as any;
    const height = getBodyHeight();
    const REM = 16 * (height / 1080);
    setChooseIdBtnClient([
      modal.getBoundingClientRect().x / REM,
      modal.getBoundingClientRect().y / REM,
    ]);
  };
  useEffect(() => {
    setSliderList(sliderListData);
  }, [sliderListData]);
  useEffect(() => {
    getViewport();
  }, [sliderListData]);

  if (type === 'chooseId') {
    return (
      <div>
        <Form.Item>
          <button
            type='submit'
            onClick={chooseIdBtn}
            className='chooseId'
            style={{
              display: `${isChooseAllCondition ? 'flex' : 'none'}`,
              border: `${
                ischooseIdOpen ? 'none' : '1px solid var(--Brand-2, #1890FF)'
              }`,
              backgroundColor: `${
                ischooseIdOpen ? 'var(--Brand-3, #0C40DE)' : 'white'
              }`,
              cursor: `${isChooseAllCondition ? 'pointer' : 'not-allowed'}`,
            }}>
            <span
              style={{
                color: `${
                  ischooseIdOpen ? 'white' : 'var(--Brand-2, #1890FF)'
                }`,
              }}>
              {t('选择ID')}
            </span>
          </button>
        </Form.Item>
        <div
          className='h-[100vh] w-[100vw] z-[99] absolute top-0 left-0'
          style={{ display: `${ischooseIdOpen ? 'block' : 'none'}` }}
          onClick={() => {
            setIschooseIdOpen(false);
          }}
        />
        <div id={'modal'}>
          <BigModal
            wrapStyle={{
              top: `${chooseIdBtnClient[1] - 6.9 + 'rem'}`,
              left: `${chooseIdBtnClient[0] - 37.5 + 'rem'}`,
              display: `${ischooseIdOpen ? 'block' : 'none'}`,
              zIndex: '999',
              alignContent: 'space-between',
              borderRadius: '1.42858rem',
              border: '1px solid var(--G6, #D5D7DD)',
            }}
            orderListData={orderListData}
            searchListData={searchListData}
            sliderListData={sliderList}
            onOk={chooseIdOnOk}
            onRemake={chooseIdOnRemake}
          />
        </div>
      </div>
    );
  } else if (type === 'search') {
    return (
      <Form.Item>
        <button type='submit' className='search' onClick={onClick}>
          <span>{t('查询')}</span>
        </button>
      </Form.Item>
    );
  } else if (type === 'remake') {
    return (
      <Form.Item>
        <button type='submit' className='remake' onClick={onClick}>
          <span>{t('重置')}</span>
        </button>
      </Form.Item>
    );
  } else if (type === 'isChoose') {
    return (
      <div>
        {contextHolder}
        <Form.Item>
          <button type='submit' onClick={isChooseIdBtn} className='isChoose'>
            <span>{t('已选择ID')}</span>
          </button>
        </Form.Item>
        <div className='subscript'>
          <div> {store.getState().big.CHOOSE_LIST.length} </div>
        </div>
        <div
          className='h-[100vh] w-[100vw] z-[99] absolute top-0 left-0 right-0'
          style={{ display: `${isIschooseIdOpen ? 'block' : 'none'}` }}
          onClick={() => {
            setIsIschooseIdOpen(false);
          }}
        />
        <div id={'modal1'}>
          <BigIDListMdal
            wrapStyle={{
              top: `${4.58143 + 'rem'}`,
              right: `${2.64 + 'rem'}`,
              display: `${isIschooseIdOpen ? 'block' : 'none'}`,
              zIndex: '999',
              alignContent: 'space-between',
              borderRadius: '1.42858rem',
              border: '1px solid var(--G6, #D5D7DD)',
            }}
            sliderListData={sliderList}
            onOk={isChooseIdOnOk}
            onRemake={isChooseIdOnRemake}
          />
        </div>
      </div>
    );
  } else {
    return <div></div>;
  }
};

export default BigButton;
