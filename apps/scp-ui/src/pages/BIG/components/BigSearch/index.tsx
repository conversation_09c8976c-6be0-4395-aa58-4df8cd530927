import { Input, ConfigProvider } from 'antd';
import icon from '../BigSvg';
type PropsType = {
  title: string;
  fillterSearch: any;
};
const BigSearch: React.FC<PropsType> = (props) => {
  const { title, fillterSearch } = props;
  const onSearch = (value: any) => {
    fillterSearch(title, value.target.value);
  };

  return (
    <div
      className='h-[2.57142rem] w-[9.5rem] leading-[2.57142rem] flex relative overflow-hidden'
      style={{
        backgroundColor: 'var(--G8, #EFF2F3)',
        borderRadius: '0.57142rem',
      }}>
      <ConfigProvider
        theme={{
          components: {
            Input: {
              activeBg: 'none',
              activeBorderColor: 'none',
              activeShadow: 'none',
            },
          },
        }}>
        <Input
          className='absolute w-[6.2rem] h-[2.57142rem] left-[2.8rem] border-none p-0'
          style={{ backgroundColor: 'var(--G8, #EFF2F3)' }}
          placeholder={title}
          onChange={onSearch}
        />
      </ConfigProvider>
      <div className='mt-[0.35rem] mx-[0.6rem]'>
        <icon.Search />
      </div>
      <div
        style={{
          color: 'var(--G5, #B9BCC6)',
          fontFamily: 'PingFang SC',
          fontStyle: 'normal',
          fontWeight: '500',
        }}></div>
    </div>
  );
};

export default BigSearch;
