import { Select, Form } from 'antd';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import selectStyles from './index.module.css';
import { SET_CHOOSE_LIST } from '@/store/features/bigSlice';
import store from '@/store';
import icon from '../BigSvg';
import { useVoerkaI18n } from '@voerkai18n/react';
type PropsType = {
  title: string;
  data?: any;
  getPvId?: any;
  getType?: any;
  isDetailsDisable?: boolean;
  getDetails?: any;
};
const BigSelect: React.FC<PropsType> = (props) => {
  const { t } = useVoerkaI18n();
  const { title, data, getPvId, getType, isDetailsDisable, getDetails } = props;
  // const [selectedValues, setSelectedValues] = useState([]);
  const user = useSelector((state: any) => state.login.user);
  const [selectTitle, setSelectTitle] = useState<string>('');
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isChoose, setIsChoose] = useState<boolean>(false);
  const [chooseList, setChooseList] = useState<any>({});
  const [list, setList] = useState<any[]>([]);
  const onChange = (values: any) => {
    localStorage.removeItem('isHasOrderList');
    localStorage.removeItem('bigChooseList');
    store.dispatch(SET_CHOOSE_LIST([]));
    setIsChoose(true);
    if (title === t('按...分类')) {
      getType(values);
    } else if (title !== t('计划版本') && title !== t('按...分类')) {
      getDetails(values);
    } else if (title === t('计划版本')) {
      getPvId(values.value);
    }
    if (chooseList && chooseList.key === values.key) {
      return;
    } else {
      setChooseList({ ...values });
    }
  };

  const changeSvg = () => {
    if (isDetailsDisable) {
      return;
    } else {
      setIsOpen(!isOpen);
    }
  };
  useEffect(() => {
    const bigSelect = document.getElementById(title + '-big-select') as any;
    document.addEventListener('click', function (event) {
      if (!bigSelect.contains(event.target)) {
        setIsOpen(false);
      }
    });
    setIsChoose(false);
    if (title === t('计划版本')) {
      const i = user;
      if (i) {
        getPvId(i.preferPaPvId);
      }
    }
  }, []);
  useEffect(() => {
    if (title !== t('计划版本') && title !== t('按...分类')) {
      setIsChoose(false);
    }
    setSelectTitle(title);
  }, [title]);

  useEffect(() => {
    if (title === t('计划版本')) {
      const i = user;
      if (i) {
        getPvId(i.preferPaPvId);
      }
    }
  }, []);
  useEffect(() => {
    setList(data);
  }, [data]);

  return (
    <div
      id={title + '-big-select'}
      className={selectStyles.wrap}
      style={{
        color: `${
          selectTitle === t('请选择前置选项')
            ? isDetailsDisable
              ? 'var(--G7, #E2E3E7)'
              : 'var(--G3, #555E70)'
            : 'var(--G3, #555E70)'
        }`,
        cursor: `${
          selectTitle === t('请选择前置选项')
            ? isDetailsDisable
              ? 'not-allowed'
              : 'pointer'
            : 'pointer'
        }`,
      }}
      onClick={changeSvg}>
      <div className='flex'>
        <div>
          {isChoose
            ? selectTitle === t('计划版本')
              ? t('计划版本')
              : ''
            : selectTitle}
        </div>
        <div
          className={
            selectTitle === t('计划版本')
              ? selectStyles.select1
              : selectStyles.select2
          }>
          {isChoose
            ? chooseList.label
            : selectTitle === t('计划版本')
              ? list.map((item: any) =>
                  item.key === user.preferPaPvId ? item.label : '',
                )
              : ''}
        </div>
        <Form.Item noStyle>
          <Select
            disabled={
              selectTitle === t('请选择前置选项') ? isDetailsDisable : false
            }
            labelInValue
            value={''}
            open={isOpen}
            className='absolute h-full w-full mt-[-0.35714rem] left-0'
            style={{ opacity: 0 }}
            onChange={onChange}
            options={list}
          />
        </Form.Item>
      </div>
      <div
        style={{
          transition: 'transform 0.15s ease-in-out',
          transform: `${isOpen ? 'rotate(90deg)' : 'rotate(0)'}`,
        }}>
        <icon.SelectSubscript />
      </div>
    </div>
  );
};

export default BigSelect;
