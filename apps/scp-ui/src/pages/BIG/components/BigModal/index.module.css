.wrap {
    position: absolute;
    padding: 1.5rem 2rem;
    min-width: 44rem;
    height: 27rem;
    background-color: #fff;
}

.listWrap {
    display: flex;
    justify-content: space-between;
    color: #B9BCC6;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
}

.segmented {
    border: 1px solid #E2E3E7;
    border-radius: 0.57142rem;
    color: #293147;
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    background-color: #fff;
    height: 2.57142rem;
}

.orderIsChoosed {
    min-height: 3.72rem;
    line-height: 3.72rem;
    display: flex;
    width: 9.5rem;
    color: #0c40de;
    text-align: center;
}

.orderUnChoosed {
    min-height: 3.72rem;
    line-height: 3.72rem;
    display: flex;
    width: 9.5rem;
    text-align: center;
}
.okBtn {
    margin-right: 0.75rem;
    display: flex;
    width: 8.4rem;
    height: 2.85714rem;
    padding: 0.21429rem 1.28571rem;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    border-radius: 1.42858rem;
    background: var(--Brand-2, #1890FF);
}

.okBtnSpan {
    color: var(--white, #FFF);
    font-family: "PingFang SC";
    font-size: 1.42858rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.remakeBtn {
    display: flex;
    width: 8.4rem;
    height: 2.85714rem;
    padding: 0.21429rem 1.28571rem;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    border-radius: 1.42858rem;
    background: #ffffff;
    border: 1px solid var(--Brand-2, #1890FF);
}

.remakeBtnSpan {
    color: var(--Brand-2, #1890FF);
    font-family: "PingFang SC";
    font-size: 1.42858rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}