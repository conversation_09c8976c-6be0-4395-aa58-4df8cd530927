import { Segmented, ConfigProvider } from 'antd';
import BigSearch from '../BigSearch';
import { useEffect, useState } from 'react';
import bigModalStyle from './index.module.css';
import { SET_CHOOSE_LIST } from '@/store/features/bigSlice';
import store from '@/store';
import icon from '../BigSvg';
type PropsType = {
  wrapStyle: any;
  orderListData: any;
  searchListData: any;
  onOk: any;
  onRemake: any;
  sliderListData: any;
};

const BigModal: React.FC<PropsType> = (props) => {
  const { wrapStyle, orderListData, onOk, onRemake, sliderListData } = props;
  const [orderList, setOrderList] = useState<any[]>([]);
  const [currentOrderList, setCurrentOrderList] = useState<any[]>([]);
  const [searchList, setSearchList] = useState<any[]>([]);
  const [sliderList, setSliderList] = useState<any[]>([]);
  const [sliderChooseList, setSliderChooseList] = useState<string>('');
  const [chooseList, setChooseList] = useState<any[]>([]);
  const chooseItem = (values: any, ind?: number) => {
    if (localStorage.getItem('BIGModalType') !== 'SO') {
      const newItem = {
        ...values,
        isChoose: !values.isChoose,
      };
      for (let i = 0; i < orderList.length; i++) {
        if (orderList[i].key === values.key) {
          orderList[i] = newItem;
        }
      }
      getOrderList(orderList);
      storeChooseList(values, newItem);
    } else {
      if (ind !== undefined) {
        const newItem = {
          ...values.item[ind],
          isChoose: !values.item[ind].isChoose,
          key: values.key,
          orderId: values.orderId,
          type: values.type,
          value: values.value,
        };
        for (let i = 0; i < orderList.length; i++) {
          if (
            orderList[i].key === values.key &&
            orderList[i].itemNo === values.item[ind].itemNo
          ) {
            orderList[i] = newItem;
          }
        }
        getOrderList(orderList, newItem);
      } else {
        const newItemList: any[] = [];
        const isChooseList: boolean[] = [];
        values.item.map((i: any) => {
          isChooseList.push(i.isChoose);
        });
        values.item.map((i: any) => {
          newItemList.push({
            ...i,
            isChoose: isChooseList.includes(false),
            key: values.key,
            orderId: values.orderId,
            type: values.type,
            value: values.value,
          });
        });
        const _orderList: any[] = [];
        for (let i = 0; i < orderList.length; i++) {
          if (orderList[i].key !== values.key) {
            _orderList.push(orderList[i]);
          }
        }
        _orderList.unshift(...newItemList);
        setOrderList([..._orderList]);
        getOrderList(_orderList, newItemList);
      }
    }
  };
  const storeChooseList = (values: any, newItem: any) => {
    if (values.type && values.type !== 'VC') {
      const _allChooseList: any = [...store.getState().big.CHOOSE_LIST];
      if (_allChooseList.length === 0) setChooseList([]);
      if (newItem.isChoose) {
        _allChooseList.push(newItem);
      } else {
        for (let i = 0; i < _allChooseList.length; i++) {
          if (_allChooseList[i].orderId === newItem.orderId) {
            _allChooseList.splice(i, 1);
          }
        }
      }
      store.dispatch(SET_CHOOSE_LIST([..._allChooseList]));
      const _chooseList = chooseList;
      let isHasBIGModalType = false,
        BIGModalTypeIndex = -1;
      if (_chooseList.length > 0) {
        for (let i = 0; i < _chooseList.length; i++) {
          if (_chooseList[i].type === localStorage.getItem('BIGModalType')) {
            isHasBIGModalType = true;
            BIGModalTypeIndex = i;
            break;
          } else {
            isHasBIGModalType = false;
            BIGModalTypeIndex = -1;
          }
        }
        if (isHasBIGModalType) {
          if (newItem.isChoose === true) {
            _chooseList[BIGModalTypeIndex].orderList.push(values.key);
          } else {
            if (_chooseList[BIGModalTypeIndex].orderList.length === 1) {
              _chooseList.splice(BIGModalTypeIndex, 1);
            } else {
              _chooseList[BIGModalTypeIndex].orderList = _chooseList[
                BIGModalTypeIndex
              ].orderList.filter(function (n: any) {
                return n !== values.key;
              });
            }
          }
        } else {
          _chooseList.push({
            type: localStorage.getItem('BIGModalType'),
            orderList: [values.key],
          });
        }
      } else {
        _chooseList.push({
          type: localStorage.getItem('BIGModalType'),
          orderList: [values.key],
        });
      }
      localStorage.setItem('bigChooseList', JSON.stringify([..._chooseList]));
      setChooseList([..._chooseList]);
      isHasBIGModalType = false;
      BIGModalTypeIndex = -1;
    } else {
      let fatherItem: any = {};
      values.map((item: any) => {
        if (item.orderId === newItem.orderId) {
          fatherItem = item;
        }
      });
      const _allChooseList: any = [...store.getState().big.CHOOSE_LIST];
      if (_allChooseList.length === 0) setChooseList([]);
      if (newItem.isChoose) {
        _allChooseList.push(newItem);
      } else {
        for (let i = 0; i < _allChooseList.length; i++) {
          if (_allChooseList[i].orderId === newItem.orderId) {
            _allChooseList.splice(i, 1);
          }
        }
      }
      store.dispatch(SET_CHOOSE_LIST(_allChooseList));

      const _chooseList = chooseList;
      let isHasBIGModalType = false;
      if (_chooseList.length > 0) {
        for (let i = 0; i < _chooseList.length; i++) {
          if (_chooseList[i].type === localStorage.getItem('BIGModalType')) {
            isHasBIGModalType = true;
            break;
          } else {
            isHasBIGModalType = false;
          }
        }

        if (isHasBIGModalType) {
          if (fatherItem.isAllChoose) {
            _chooseList.map((item: any) => {
              if (item.type === 'SO') {
                for (let i = 0; i < item.orderList.length; i++) {
                  if (item.orderList[i] === fatherItem.key) {
                    item.orderList.splice(i, 1);
                    break;
                  }
                }
                let list = [];
                list = item.orderList.filter((l: any) => {
                  fatherItem.item.map((v: any) => {
                    return l !== v.itemNo;
                  });
                });
                item.orderList = [...list];
                item.orderList.push(fatherItem.key);
              }
            });
          } else {
            _chooseList.map((item: any) => {
              if (item.type === 'SO') {
                for (let i = 0; i < item.orderList.length; i++) {
                  if (item.orderList[i] === fatherItem.key) {
                    item.orderList.splice(i, 1);
                    break;
                  }
                }
                for (let j = 0; j < item.orderList.length; j++) {
                  if (item.orderList[j] === newItem.itemNo) {
                    item.orderList.splice(j, 1);
                    break;
                  }
                }
                fatherItem.item.map((i: any) => {
                  if (i.isChoose) item.orderList.push(i.itemNo);
                });
              }
            });
          }
        } else {
          if (fatherItem.isAllChoose) {
            _chooseList.push({
              type: localStorage.getItem('BIGModalType'),
              orderList: [fatherItem.key],
            });
          } else {
            _chooseList.push({
              type: localStorage.getItem('BIGModalType'),
              orderList: [newItem.itemNo],
            });
          }
        }
      } else {
        if (fatherItem.isAllChoose) {
          _chooseList.push({
            type: localStorage.getItem('BIGModalType'),
            orderList: [fatherItem.key],
          });
        } else {
          _chooseList.push({
            type: localStorage.getItem('BIGModalType'),
            orderList: [newItem.itemNo],
          });
        }
      }
      localStorage.setItem('bigChooseList', JSON.stringify([..._chooseList]));
      setChooseList([..._chooseList]);
      isHasBIGModalType = false;
    }
  };

  const getOrderList = (value: any, otherItem?: any) => {
    let _orderListData: any[] = [];
    if (localStorage.getItem('BIGModalType') === 'transport') {
      value.map((item: any) => {
        if (item.type === 'BES' || item.type === 'BAS' || item.type === 'JBS') {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'departure',
          label: '出港地',
        },
        {
          type: 'entry',
          label: '入港地',
        },
      ]);
    } else if (localStorage.getItem('BIGModalType') === 'production') {
      value.map((item: any) => {
        if (item.type === 'JPA' || item.type === 'FE' || item.type === 'PA') {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'factory',
          label: '工厂',
        },
        {
          type: 'productionSource',
          label: '生产源',
        },
      ]);
    } else if (localStorage.getItem('BIGModalType') === 'purchase') {
      value.map((item: any) => {
        if (
          item.type === 'BAP' ||
          item.type === 'BEP' ||
          item.type === 'JBP' ||
          item.type === 'LA'
        ) {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'factory',
          label: '工厂',
        },
        {
          type: 'supplier',
          label: '供应商',
        },
      ]);
    } else if (localStorage.getItem('BIGModalType') === 'inventory') {
      value.map((item: any) => {
        if (item.type === 'STK') {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'factory',
          label: '工厂',
        },
        {
          type: 'store',
          label: '仓库',
        },
      ]);
    } else if (localStorage.getItem('BIGModalType') === 'forecastedOrders') {
      value.map((item: any) => {
        if (item.type === 'FCT') {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'factory',
          label: '工厂',
        },
        {
          type: 'customer',
          label: '客户',
        },
      ]);
    } else if (localStorage.getItem('BIGModalType') === 'SO') {
      value.map((item: any) => {
        if (item.type === 'VC') {
          _orderListData.push(item);
        }
      });
      setSearchList([
        {
          type: 'orderId',
          label: '单号',
        },
        {
          type: 'material',
          label: '物料',
        },
        {
          type: 'factory',
          label: '工厂',
        },
        {
          type: 'customer',
          label: '客户',
        },
      ]);
      const list = new Map();

      const SOOrderListData: any[] = [];
      _orderListData.map((v: any) => {
        if (list.has(v.orderId)) {
          list.set(v.orderId, [...list.get(v.orderId), v]);
        } else {
          list.set(v.orderId, [v]);
        }
      });
      list.forEach((k) => {
        if (k.length > 1) {
          const kArr: any[] = [];
          k.map((item: any) => {
            kArr.push({
              content: item.content,
              customer: item.customer.slice(0, 4),
              factory: item.factory.slice(0, 4),
              isChoose: item.isChoose,
              itemNo: item.itemNo,
              itemId: item.itemId,
              material:
                k[0].material.split('/')[k[0].material.split('/').length - 1],
            });
          });
          SOOrderListData.push({
            value: k[0].value,
            type: k[0].type,
            orderId: k[0].orderId,
            key: k[0].key,
            isAllChoose: k[0].isAllChoose,
            item: kArr,
          });
        } else {
          SOOrderListData.push({
            value: k[0].value,
            type: k[0].type,
            orderId: k[0].orderId,
            key: k[0].key,
            isAllChoose: k[0].isAllChoose,
            item: [
              {
                content: k[0].content,
                customer: k[0].customer.slice(0, 4),
                factory: k[0].factory.slice(0, 4),
                isChoose: k[0].isChoose,
                itemNo: k[0].itemNo,
                itemId: k[0].itemId,
                material:
                  k[0].material.split('/')[k[0].material.split('/').length - 1],
              },
            ],
          });
        }
      });
      SOOrderListData.map((item: any) => {
        item.isAllChoose = true;
        item.item.sort((a: any, b: any) => a.itemId - b.itemId);
        item.item.map((i: any) => {
          if (i.isChoose === false) {
            item.isAllChoose = false;
          }
        });
      });
      _orderListData = SOOrderListData;
      if (otherItem) {
        if (Array.isArray(otherItem)) {
          otherItem.map((item: any) => {
            storeChooseList(SOOrderListData, item);
          });
        } else {
          storeChooseList(SOOrderListData, otherItem);
        }
      }
    }
    setCurrentOrderList([..._orderListData]);
  };

  const fillterSearch = (title: string, value: any) => {
    if (title === '单号') {
      const _list: any[] = [];
      for (let i = 0; i < orderList.length; i++) {
        if (orderList[i].itemNo.includes(value)) {
          _list.push(orderList[i]);
        }
      }
      setOrderList([..._list]);
      getOrderList(_list);
    } else {
      return;
    }
  };
  const onChangeSegmented = (value: string) => {
    let type = '';
    switch (value) {
      case '运输':
        type = 'transport';
        break;
      case '生产':
        type = 'production';
        break;
      case '采购':
        type = 'purchase';
        break;
      case '库存':
        type = 'inventory';
        break;
      case '预测订单':
        type = 'forecastedOrders';
        break;
      case 'SO':
        type = 'SO';
        break;
    }
    localStorage.setItem('BIGModalType', type);
    getOrderList(orderList);
    setSliderChooseList(value);
  };
  const remake = () => {
    store.dispatch(SET_CHOOSE_LIST([]));
    onRemake();
    localStorage.removeItem('isHasOrderList');
  };
  useEffect(() => {
    setOrderList(orderListData);
    getOrderList(orderListData);
  }, [orderListData]);
  useEffect(() => {
    const _list: any = [];
    sliderListData.map((item: any) => {
      _list.push(item.label);
    });
    setSliderList([..._list]);
  }, [sliderListData]);
  useEffect(() => {
    onChangeSegmented(sliderList[0]);
  }, [sliderList]);
  useEffect(() => {
    if (!localStorage.getItem('bigChooseList')) setChooseList([]);
  }, [localStorage.getItem('bigChooseList')]);
  return (
    <div style={wrapStyle} className={bigModalStyle.wrap}>
      <div className='flex flex-col justify-between h-[24rem]'>
        {/* 滑块 */}
        <div className='h-[2.57142rem]'>
          <ConfigProvider
            theme={{
              components: {
                Segmented: {
                  itemSelectedBg: 'var(--Brand-2, #1890FF)',
                  itemSelectedColor: '#ffffff',
                  itemColor: 'var(--G2, #293147)',
                },
              },
            }}>
            <Segmented // @ts-ignore
              options={sliderList}
              value={sliderChooseList || sliderList[0]}
              className={bigModalStyle.segmented}
              onChange={(value: any) => onChangeSegmented(value)}
            />
          </ConfigProvider>
        </div>
        {/* 搜索框 */}
        <div className='h-[2.57142rem] flex justify-between'>
          {searchList.map((item: any, index: number) => {
            return (
              <BigSearch
                key={index}
                title={item.label}
                fillterSearch={fillterSearch}
              />
            );
          })}
        </div>
        {/* 列表 */}
        <div
          className='h-[12rem] flex flex-col overflow-y-auto'
          style={{ cursor: 'pointer' }}>
          {currentOrderList.map((item: any, index: number) => {
            if (localStorage.getItem('BIGModalType') === 'SO') {
              return item.item.map((i: any, ind: number) => {
                return (
                  <div key={ind} className={bigModalStyle.listWrap}>
                    <div
                      className={
                        i.isChoose
                          ? bigModalStyle.orderIsChoosed
                          : bigModalStyle.orderUnChoosed
                      }
                      onClick={() => chooseItem(item)}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {ind === 0 &&
                          (item.isAllChoose ? (
                            <icon.isChooseOrder />
                          ) : (
                            <icon.unChooseOrder />
                          ))}
                      </div>
                      <div>{ind === 0 ? item.orderId : ''}</div>
                    </div>
                    <div
                      className={
                        i.isChoose
                          ? bigModalStyle.orderIsChoosed
                          : bigModalStyle.orderUnChoosed
                      }
                      onClick={() => chooseItem(item, ind)}>
                      <div className='mt-[1.28857rem] mx-[1rem]'>
                        {i.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      {i.itemId}
                    </div>
                    <div
                      className={
                        i.isChoose
                          ? bigModalStyle.orderIsChoosed
                          : bigModalStyle.orderUnChoosed
                      }
                      onClick={() => chooseItem(item, ind)}>
                      {i.material}
                    </div>
                    <div
                      className={
                        i.isChoose
                          ? bigModalStyle.orderIsChoosed
                          : bigModalStyle.orderUnChoosed
                      }
                      onClick={() => chooseItem(item, ind)}>
                      {i.factory}
                    </div>
                    <div
                      className={
                        i.isChoose
                          ? bigModalStyle.orderIsChoosed
                          : bigModalStyle.orderUnChoosed
                      }
                      onClick={() => chooseItem(item, ind)}>
                      {i.customer}
                    </div>
                  </div>
                );
              });
            } else if (
              localStorage.getItem('BIGModalType') === 'forecastedOrders'
            ) {
              return (
                <div
                  key={index}
                  className={bigModalStyle.listWrap}
                  onClick={() => chooseItem(item)}>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    <div className='mt-[1.28857rem] mr-[1rem]'>
                      {item.isChoose ? (
                        <icon.isChooseOrder />
                      ) : (
                        <icon.unChooseOrder />
                      )}
                    </div>
                    <div> {item.orderId} </div>
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.material}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.factory}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.customer}
                  </div>
                </div>
              );
            } else if (localStorage.getItem('BIGModalType') === 'transport') {
              return (
                <div
                  key={index}
                  className={bigModalStyle.listWrap}
                  onClick={() => chooseItem(item)}>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    <div className='mt-[1.28857rem] mr-[1rem]'>
                      {item.isChoose ? (
                        <icon.isChooseOrder />
                      ) : (
                        <icon.unChooseOrder />
                      )}
                    </div>
                    <div> {item.orderId} </div>
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.material}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.departure}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.entry}
                  </div>
                </div>
              );
            } else if (localStorage.getItem('BIGModalType') === 'production') {
              return (
                <div
                  key={index}
                  className={bigModalStyle.listWrap}
                  onClick={() => chooseItem(item)}>
                  <div
                    className='min-h-[3.72rem] leading-[3.72rem] flex w-[9.5rem] '
                    style={{
                      color: `${
                        item.isChoose ? 'var(--Brand-3, #0C40DE)' : ''
                      }`,
                    }}>
                    <div className='mt-[1.28857rem] mr-[1rem]'>
                      {item.isChoose ? (
                        <icon.isChooseOrder />
                      ) : (
                        <icon.unChooseOrder />
                      )}
                    </div>
                    <div> {item.orderId} </div>
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.material}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.factory}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.productionSource}
                  </div>
                </div>
              );
            } else if (localStorage.getItem('BIGModalType') === 'purchase') {
              return (
                <div
                  key={index}
                  className={bigModalStyle.listWrap}
                  onClick={() => chooseItem(item)}>
                  <div
                    className='min-h-[3.72rem] leading-[3.72rem] flex w-[9.5rem]'
                    style={{
                      color: `${
                        item.isChoose ? 'var(--Brand-3, #0C40DE)' : ''
                      }`,
                    }}>
                    <div className='mt-[1.28857rem] mr-[1rem]'>
                      {item.isChoose ? (
                        <icon.isChooseOrder />
                      ) : (
                        <icon.unChooseOrder />
                      )}
                    </div>
                    <div> {item.orderId} </div>
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.material}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.factory}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.supplier}
                  </div>
                </div>
              );
            } else if (localStorage.getItem('BIGModalType') === 'inventory') {
              return (
                <div
                  key={index}
                  className={bigModalStyle.listWrap}
                  onClick={() => chooseItem(item)}>
                  <div
                    className='min-h-[3.72rem] leading-[3.72rem] flex w-[9.5rem]'
                    style={{
                      color: `${
                        item.isChoose ? 'var(--Brand-3, #0C40DE)' : ''
                      }`,
                    }}>
                    <div className='mt-[1.28857rem] mr-[1rem]'>
                      {item.isChoose ? (
                        <icon.isChooseOrder />
                      ) : (
                        <icon.unChooseOrder />
                      )}
                    </div>
                    <div> {item.orderId} </div>
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.material}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.factory}
                  </div>
                  <div
                    className={
                      item.isChoose
                        ? bigModalStyle.orderIsChoosed
                        : bigModalStyle.orderUnChoosed
                    }>
                    {item.store}
                  </div>
                </div>
              );
            }
          })}
        </div>
        {/* 按钮组 */}
        <div className='flex h-[2.85714rem] justify-center'>
          <button type='submit' className={bigModalStyle.okBtn} onClick={onOk}>
            <span className={bigModalStyle.okBtnSpan}>确认</span>
          </button>
          <button
            type='submit'
            className={bigModalStyle.remakeBtn}
            onClick={remake}>
            <span className={bigModalStyle.remakeBtnSpan}>重置</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BigModal;
