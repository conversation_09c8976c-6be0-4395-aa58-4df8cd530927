.idListmodalWrap {
    position: absolute;
    padding: 2rem 2rem;
    min-width: 44rem;
    height: 29rem;
    background-color: #fff;
}


.listWrap {
    height: 21.17286rem;
    display: flex;
    flex-flow: column;
    overflow-y: auto;
}

.title {
    color: var(--G5, #B9BCC6);
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    display: flex;
}

.line {
    height: 1px;
    width: 32rem;
    background-color: #E2E3E7;
}



.item {
    min-height: 3rem;
    display: flex;
    justify-content: space-between;
    color: var(--G5, #B9BCC6);
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    cursor: pointer;
}

.orderItemLabelChoosed {
    min-height: 3rem;
    line-height: 3.72rem;
    display: flex;
    width: 40%;
    color: #0C40DE;
    width: 9.5rem;
}

.orderItemLabelUnChoosed {
    min-height: 3rem;
    line-height: 3.72rem;
    display: flex;
    width: 40%;
    width: 9.5rem;
}

.orderLabelChoosed {
    min-height: 3rem;
    line-height: 3.72rem;
    display: flex;
    width: 25%;
    color: #0C40DE;
}

.orderLabelUnChoosed {
    min-height: 3rem;
    line-height: 3.72rem;
    display: flex;
    width: 25%;
}

.okBtn {
    margin-right: 0.75rem;
    display: flex;
    width: 8.4rem;
    height: 2.85714rem;
    padding: 0.21429rem 1.28571rem;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    border-radius: 1.42858rem;
    background: var(--Brand-2, #1890FF);
}

.okBtnSpan {
    color: var(--white, #FFF);
    font-family: "PingFang SC";
    font-size: 1.42858rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.remakeBtn {
    display: flex;
    width: 8.4rem;
    height: 2.85714rem;
    padding: 0.21429rem 1.28571rem;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    border-radius: 1.42858rem;
    background: #ffffff;
    border: 1px solid var(--Brand-2, #1890FF);
}

.remakeBtnSpan {
    color: var(--Brand-2, #1890FF);
    font-family: "PingFang SC";
    font-size: 1.42858rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}