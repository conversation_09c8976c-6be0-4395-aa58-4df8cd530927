import { useEffect, useState } from 'react';
import bigIDListModal from './index.module.css';
import store from '@/store';
import icon from '../BigSvg';
type PropsType = {
  wrapStyle: any;
  onOk: any;
  onRemake: any;
  sliderListData: any;
};

const BigIDListMdal: React.FC<PropsType> = (props) => {
  const { wrapStyle } = props;
  const [chooseOrderList, setChooseOrderList] = useState<any[]>([]);
  const [chooseSOOrderList, setChooseSOOrderList] = useState<any[]>([]);
  const [hasTransport, setHasTransport] = useState<boolean>(false);
  const [hasProduction, setHasProduction] = useState<boolean>(false);
  const [hasPurchase, setHasPurchase] = useState<boolean>(false);
  const [hasInventory, setHasInventory] = useState<boolean>(false);
  const [hasForecastedOrders, setHasForecastedOrders] =
    useState<boolean>(false);
  const [hasSO, setHasSO] = useState<boolean>(false);
  const chooseItem = (type: string, value: any) => {
    chooseOrderList.map((item: any) => {
      if (type === item.type) {
        item.orderIdList.map((i: any) => {
          if (i.orderId === value.orderId) {
            i.isChoose = !i.isChoose;
          }
        });
      }
    });
    setChooseOrderList([...chooseOrderList]);
  };

  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      setChooseOrderList([...store.getState().big.CHOOSE_LIST]);
      const list = store.getState().big.CHOOSE_LIST;
      setHasTransport(false);
      setHasProduction(false);
      setHasPurchase(false);
      setHasInventory(false);
      setHasForecastedOrders(false);
      setHasSO(false);
      list.map((item: any) => {
        if (item.type === 'BES' || item.type === 'BAS' || item.type === 'JBS')
          setHasTransport(true);
        if (item.type === 'JPA' || item.type === 'FE' || item.type === 'PA')
          setHasProduction(true);
        if (
          item.type === 'BAP' ||
          item.type === 'BEP' ||
          item.type === 'JBP' ||
          item.type === 'LA'
        )
          setHasPurchase(true);
        if (item.type === 'STK') setHasInventory(true);
        if (item.type === 'FCT') setHasForecastedOrders(true);
        if (item.type === 'VC') setHasSO(true);
      });
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const list = new Map();
    const SOOrderListData: any = [];
    chooseOrderList.map((v: any) => {
      if (v.type === 'VC') {
        if (list.has(v.orderId)) {
          list.set(v.orderId, [...list.get(v.orderId), v]);
        } else {
          list.set(v.orderId, [v]);
        }
      }
    });
    list.forEach((k) => {
      if (k.length > 1) {
        const kArr: any[] = [];
        k.map((item: any) => {
          kArr.push({
            content: item.content,
            customer: item.customer.slice(0, 4),
            factory: item.factory.slice(0, 4),
            isChoose: item.isChoose,
            itemNo: item.itemNo,
            itemId: item.itemId,
            material:
              k[0].material.split('/')[k[0].material.split('/').length - 1],
          });
        });
        SOOrderListData.push({
          value: k[0].value,
          type: k[0].type,
          orderId: k[0].orderId,
          key: k[0].key,
          isAllChoose: k[0].isAllChoose,
          item: kArr,
        });
      } else {
        SOOrderListData.push({
          value: k[0].value,
          type: k[0].type,
          orderId: k[0].orderId,
          key: k[0].key,
          isAllChoose: k[0].isAllChoose,
          item: [
            {
              content: k[0].content,
              customer: k[0].customer.slice(0, 4),
              factory: k[0].factory.slice(0, 4),
              isChoose: k[0].isChoose,
              itemNo: k[0].itemNo,
              itemId: k[0].itemId,
              material:
                k[0].material.split('/')[k[0].material.split('/').length - 1],
            },
          ],
        });
      }
    });
    SOOrderListData.map((item: any) => {
      item.isAllChoose = true;
      item.item.sort((a: any, b: any) => a.itemId - b.itemId);
      item.item.map((i: any) => {
        if (i.isChoose === false) {
          item.isAllChoose = false;
        }
      });
    });
    setChooseSOOrderList([...SOOrderListData]);
  }, [chooseOrderList]);
  return (
    <div style={wrapStyle} className={bigIDListModal.idListmodalWrap}>
      <div className='flex flex-col justify-between h-[26rem]'>
        {/* 列表 */}
        <div className={bigIDListModal.listWrap}>
          {/* 运输 */}
          <div style={{ display: `${hasTransport ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>运输</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseOrderList.map((item: any, index: number) => {
              if (
                item.type === 'BES' ||
                item.type === 'BAS' ||
                item.type === 'JBS'
              ) {
                return (
                  <div
                    key={index}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {item.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      <div> {item.orderId} </div>
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.material}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.departure}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.entry}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {/* 生产 */}
          <div style={{ display: `${hasProduction ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>生产</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseOrderList.map((item: any, index: number) => {
              if (
                item.type === 'JPA' ||
                item.type === 'FE' ||
                item.type === 'PA'
              ) {
                return (
                  <div
                    key={index}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {item.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      <div> {item.orderId} </div>
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.material}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.factory}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.productionSource}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {/* 采购 */}
          <div style={{ display: `${hasPurchase ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>采购</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseOrderList.map((item: any, index: number) => {
              if (
                item.type === 'BAP' ||
                item.type === 'BEP' ||
                item.type === 'JBP' ||
                item.type === 'LA'
              ) {
                return (
                  <div
                    key={index}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {item.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      <div> {item.orderId} </div>
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.material}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.factory}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.supplier}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {/* 库存 */}
          <div style={{ display: `${hasInventory ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>库存</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseOrderList.map((item: any, index: number) => {
              if (item.type === 'STK') {
                return (
                  <div
                    key={index}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {item.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      <div> {item.orderId} </div>
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.material}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.factory}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.store}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {/* 预测订单 */}
          <div style={{ display: `${hasForecastedOrders ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>预测订单</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseOrderList.map((item: any, index: number) => {
              if (item.type === 'FCT') {
                return (
                  <div
                    key={index}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {item.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      <div> {item.orderId} </div>
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.material}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.factory}
                    </div>
                    <div
                      className={`${
                        item.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {item.customer}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {/* SO */}
          <div style={{ display: `${hasSO ? 'block' : 'none'}` }}>
            <div className={bigIDListModal.title}>
              <div className='min-h-[3.72rem] leading-[3.72rem]'>SO</div>
              <div className='min-h-[3.72rem] leading-[3.72rem] pt-[1.86rem] ml-[0.57rem]'>
                <div className={bigIDListModal.line} />
              </div>
            </div>
            {chooseSOOrderList.map((item: any, index: number) => {
              return item.item.map((i: any, ind: number) => {
                return (
                  <div
                    key={ind}
                    className={bigIDListModal.item}
                    onClick={() => chooseItem(item, index)}>
                    <div
                      className={`${
                        i.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mr-[1rem]'>
                        {ind === 0 &&
                          (item.isAllChoose ? (
                            <icon.isChooseOrder />
                          ) : (
                            <icon.unChooseOrder />
                          ))}
                      </div>
                      <div>{ind === 0 ? item.orderId : ''}</div>
                    </div>
                    <div
                      className={`${
                        i.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      <div className='mt-[1.28857rem] mx-[1rem]'>
                        {i.isChoose ? (
                          <icon.isChooseOrder />
                        ) : (
                          <icon.unChooseOrder />
                        )}
                      </div>
                      {i.itemId}
                    </div>
                    <div
                      className={`${
                        i.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {i.material}
                    </div>
                    <div
                      className={`${
                        i.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {i.factory}
                    </div>
                    <div
                      className={`${
                        i.isChoose
                          ? bigIDListModal.orderItemLabelChoosed
                          : bigIDListModal.orderItemLabelUnChoosed
                      }`}>
                      {i.customer}
                    </div>
                  </div>
                );
              });
            })}
          </div>
        </div>
        {/* 按钮组 */}
        {/* <div className='flex h-[2.85714rem] justify-center'>
          <button
            type='submit'
            className={bigIDListModal.okBtn}
            onClick={() => onOk(chooseOrderList)}>
            <span className={bigIDListModal.okBtnSpan}>确认</span>
          </button>
          <button type='submit' className={bigIDListModal.remakeBtn} onClick={onRemake}>
            <span className={bigIDListModal.remakeBtnSpan}>重置</span>
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default BigIDListMdal;
