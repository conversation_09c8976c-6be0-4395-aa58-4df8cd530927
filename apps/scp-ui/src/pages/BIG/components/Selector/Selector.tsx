import { useState, useEffect } from 'react';
import LeftSelector from '../LeftSelector/LeftSelector';
import RightSelector from '../RightSelector/RightSelector';
import { Flex } from 'antd';
import './index.css';

function Selector({ paPvId, onSearch, loading }: any) {
  const [IDSelectButtonShow, setIDSelectButtonShow] = useState(false);
  const [firstSelectValue, setFirstSelectValue] = useState('');
  const [secondSelectValue, setSecondSelectValue] = useState('');
  const [thirdSelectValue, setThirdSelectValue] = useState('');
  const [thirdSelectDisable, setThirdSelectDisable] = useState(true);

  const [rowData, setRowData] = useState<any[]>([]);

  useEffect(() => {
    thirdSelectDisable
      ? setIDSelectButtonShow(false)
      : setIDSelectButtonShow(true);
  }, [thirdSelectDisable]);

  return (
    <Flex vertical={false} gap={'large'} justify='space-between'>
      <LeftSelector
        firstSelectValue={firstSelectValue}
        setFirstSelectValue={setFirstSelectValue}
        secondSelectValue={secondSelectValue}
        setSecondSelectValue={setSecondSelectValue}
        thirdSelectValue={thirdSelectValue}
        setThirdSelectValue={setThirdSelectValue}
        thirdSelectDisable={thirdSelectDisable}
        setThirdSelectDisable={setThirdSelectDisable}
      />
      <RightSelector
        paPvId={paPvId}
        rowData={rowData}
        firstSelectValue={firstSelectValue}
        setRowData={setRowData}
        setSecondSelectValue={setSecondSelectValue}
        thirdSelectValue={thirdSelectValue}
        setThirdSelectValue={setThirdSelectValue}
        IDSelectButtonShow={IDSelectButtonShow}
        onSearch={onSearch}
        setThirdSelectDisable={setThirdSelectDisable}
        loading={loading}
      />
    </Flex>
  );
}

export default Selector;
