import { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  setKfList as setStoreKfList,
  setAttributeList as setStoreAttributeList,
} from '@/store/features/planningViewSlice';
import ajax from '@/api';
import { Form, Select, Button, message, App, Flex, ConfigProvider } from 'antd';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { rowHeadTreeNode, colHeader, headDisabled } from './CharSheetType';

import { AttributeForm, ItemForm } from './components';
import { useSearchParams } from 'react-router-dom';
import type {
  GetAttributesRes,
  GetKeyFiguresRes,
} from '@/api/PlanningView/model';
import type { SelectProps } from 'antd';
import { getFormattedDate } from '@/utils/PlanningView';
import { isEmptyObject } from '@/utils';
import updateLocale from 'dayjs/plugin/updateLocale';
import MySheetNew from './components/MySheetNew/MySheetNew';
// import ChartButton from './components/ChartButton';
import AddPVObjectButton from './components/AddPVObjectButton';
import PerferButton from './components/PerferButton';
// import { useSize } from 'ahooks';
import { utils } from '@repo/configuration';
import { FilterCondition } from './components/FilterModal';
import BottomChat from './components/BottomChat';
import { ChartStarIcon } from './PlanningView';

const dayjs = utils.getDayjs('scp');
dayjs.extend(updateLocale);
dayjs.updateLocale('zh-cn', {
  weekStart: 1,
});
export interface viewType {
  label: string;
  value: string;
  saveUrl: string;
  getUrl: (params: any) => Promise<any>;
  head: colHeader[];
  disableRow: headDisabled[];
}
const DateFormat = new Map();
DateFormat.set('day', 'YYYY-MM-DD');
DateFormat.set('month', 'YYYY-MM');
const PlanningViewNew: React.FC = () => {
  const language = useSelector((state: any) => state.layout.language);
  console.log(language);
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();

  const [messageApi, contextHolder] = message.useMessage();

  const pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const user = useSelector((state: any) => state.login.user);
  const [currentPaPvId, setCurrentPaPvId] = useState<any>(
    user?.preferPaPvId === 0 ? [] : [user?.preferPaPvId],
  );
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [mySheetData, setMySheetData] = useState<any>(null);
  const [attributeOptions, setAttributeOptions] = useState<
    SelectProps['options']
  >([]);
  const [attributeList, setAttributeList] = useState<GetAttributesRes[]>([]);
  const [kfList, setKfList] = useState<SelectProps['options']>([]);

  const [conditionsAttr, setConditionsAttr] = useState<
    FilterCondition<string, string>[]
  >([]);

  const [filter, setFilter] = useState<any>(null);
  const [showChat, setShowChat] = useState(false);

  const { modal } = App.useApp();

  const search = async (values: any) => {
    try {
      setButtonLoading(true);
      const res = await ajax.getCalieData(values);
      if (res.code === 0) {
        const list = _.get(res, 'data.list', []) || [];
        renderSheet(list);
      } else {
        messageApi.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setButtonLoading(false);
    }
  };

  const getData = async (values: any) => {
    const { viewType } = values;
    let searchValues = {};
    console.log(values, 'testvalues');
    if (viewType === 1) {
      const { paPvId, time, md, kf, conditions, isOnlyWithData } = values;
      setCurrentPaPvId(paPvId);
      searchValues = {
        paPvId,
        conditions: (
          conditions?.filter(
            (item: any) => item !== void 0 || item === isEmptyObject(item),
          ) || []
        ).map((item: any) => ({
          ...item,
          key: conditionsAttr.find((attr: any) => attr.id === item.key)?.value,
        })),
        attributeList: md,
        kfList: kf,
        nonNotShow: isOnlyWithData,
        viewType,
        dateList: time.map(
          (item: {
            dateRange: any[];
            dateType: number;
            rollingType: number;
          }) => {
            return {
              tpLevel: item.dateType,
              rollingType: item.rollingType,
              startDate: getFormattedDate(item.dateRange[0], item.dateType),
              endDate: getFormattedDate(item.dateRange[1], item.dateType),
            };
          },
        ),
      };
    } else {
      const { viewType, md, kf, conditions, ...rest } = values;
      searchValues = {
        viewType,
        attributeList: md,
        kfList: kf,
        conditions: (
          conditions?.filter(
            (item: any) => item !== void 0 || item === isEmptyObject(item),
          ) || []
        ).map((item: any) => ({
          ...item,
          key: conditionsAttr.find((attr: any) => attr.id === item.key)?.value,
        })),
        ...rest,
      };
    }
    setSearchParams({ data: JSON.stringify(searchValues) });
    search(searchValues);
  };

  //查询方法
  const onFinish = (values: any, needConfirm: boolean = true) => {
    if (needConfirm) {
      const modifyLength = MySheetRef.current.getModifyLength();
      if (modifyLength > 0) {
        modal.confirm({
          title: t('刷新确认'),
          content: t('表中还有未保存的数据，确认刷新？'),
          okText: t('确认'),
          cancelText: t('取消'),
          onOk: () => {
            getData(values);
          },
        });
      } else {
        getData(values);
      }
    } else {
      getData(values);
    }
  };
  //渲染方法
  const renderSheet = (list: rowHeadTreeNode[]) => {
    setMySheetData(list);
    if (!list || list.length === 0) {
      messageApi.warning(t('没有查询到数据'));
    }
  };

  const uploadDataMySheet = async () => {
    const length = MySheetRef.current.getModifyLength();
    if (length === 0) {
      message.warning(t('无数据更改'));
      return;
    }
    const data = MySheetRef.current.getModifyData();
    if (!data) {
      return;
    }
    if (currentPaPvId.length > 1) {
      messageApi.error(t('多版本数据只支持查看'));
      return;
    }
    const res = await ajax.saveCooperateView({
      addCooperateViews: data,
      ...(currentPaPvId instanceof Array
        ? { paPvId: currentPaPvId[0] }
        : {
            paPvId: currentPaPvId,
          }),
    });
    if (res.code === 0) {
      messageApi.success(t('保存成功'));
      onFinish(form.getFieldsValue(), false);
    } else {
      messageApi.error(res.msg);
    }
  };
  /**
   * @description 获取主属性列表数据
   * */
  const getAttributeList = async () => {
    const res = await ajax.getArrtibuteList();
    if (res.code === 0) {
      const list = _.get(res, 'data.list', []);
      // const { list } = res.data;
      setAttributeList(list);
      const _temp = list.map((item: GetAttributesRes) => {
        return {
          label: item.label,
          value: item.id,
        };
      });
      setAttributeOptions(_temp);
      dispatch(setStoreAttributeList(_temp));
    } else {
      messageApi.error(res.msg);
    }
  };

  /**
   * @description 获取特征值列表数据
   */
  const getKfList = async () => {
    const res = await ajax.getKFigureList({ timeBasedFlag: true });
    if (res.code === 0) {
      const list = _.get(res, 'data.list', []);
      const _temp = list.map((item: GetKeyFiguresRes) => {
        return {
          label: item.label,
          value: item.kfId,
        };
      });
      setKfList(_temp);
      dispatch(setStoreKfList(_temp));
    } else {
      messageApi.error(res.msg);
    }
  };

  useEffect(() => {
    getAttributeList();
    getKfList();
    const data = JSON.parse(searchParams.get('data') as string);
    if (data !== null) {
      if (data.viewType === 1) {
        form.setFieldsValue({
          paPvId: data.paPvId,
          md: data.attributeList,
          kf: data.kfList,
          viewType: data.viewType,
          time: data.dateList.map((item: any) => {
            const fromDate = dayjs(item.startDate);
            const toDate = dayjs(item.endDate);
            return {
              dateRange: [fromDate, toDate],
              dateType: item.tpLevel,
              rollingType: item.rollingType,
            };
          }),
        });
      } else {
        form.setFieldsValue({
          paPvId: data.paPvId,
          md: data.attributeList,
          kf: data.kfList,
          viewType: data.viewType,
          ...data,
        });
      }
    }
  }, []);

  const MySheetRef = useRef<any>(null);

  return (
    <div className='h-[calc(100vh-9rem)]'>
      {contextHolder}
      <header
        style={{
          overflow: 'hidden',
          marginBottom: '12px',
        }}>
        <ConfigProvider
          theme={{
            components: {
              Form: {
                itemMarginBottom: 10,
              },
            },
          }}>
          <Form onFinish={onFinish} form={form}>
            <Form.Item noStyle>
              <div
                style={{
                  display: 'ruby',
                }}>
                <Form.Item
                  name='paPvId'
                  label={t('计划版本')}
                  rules={[{ required: true, message: t('请选择计划版本') }]}
                  initialValue={
                    user?.preferPaPvId === 0 ? null : user?.preferPaPvId
                  }>
                  <Select
                    mode='multiple'
                    className='w-24 '
                    style={{ width: '180px' }}
                    maxTagCount={'responsive'}
                    options={pVersionList.map((item: any) => {
                      return {
                        label: item.label,
                        value: item.key,
                      };
                    })}
                    onChange={() => {}}></Select>
                </Form.Item>
                <Form.Item
                  label={t('视图类型')}
                  name='viewType'
                  style={{
                    margin: '0 10px',
                  }}
                  initialValue={1}>
                  <Select
                    style={{ width: 120 }}
                    onChange={(value) => {
                      form.setFieldsValue({
                        md: [],
                        kf: [],
                        conditions: [],
                        itemTypeId: undefined,
                      });
                      setSearchParams({ type: value, ...searchParams });
                    }}
                    options={[
                      { label: t('条目'), value: 2 },
                      { label: t('时序'), value: 1 },
                    ]}
                  />
                </Form.Item>
              </div>
            </Form.Item>
            <Form.Item shouldUpdate noStyle>
              {({ getFieldValue }) => {
                const viewType = getFieldValue('viewType');
                if (viewType === 1) {
                  return (
                    <AttributeForm
                      {...{
                        t,
                        attributeList,
                        kfList,
                        attributeOptions,
                        currentPaPvId,
                        setFilter,
                      }}
                      conditions={conditionsAttr}
                      setConditions={setConditionsAttr}
                    />
                  );
                } else if (viewType === 2) {
                  return <ItemForm />;
                } else {
                  return null;
                }
              }}
            </Form.Item>
            <Form.Item noStyle>
              <div
                style={{
                  float: 'right',
                  marginBottom: 10,
                }}>
                <Flex gap={'10px'} justify='flex-end'>
                  {form.getFieldValue('viewType') === 1 ? (
                    <>
                      {language === 'zh' && (
                        <>
                          <PerferButton pForm={form} filter={filter} />
                          <AddPVObjectButton
                            paPvId={currentPaPvId}
                            kfList={kfList}></AddPVObjectButton>
                        </>
                      )}

                      <Button
                        icon={<ChartStarIcon />}
                        onClick={() => setShowChat(!showChat)}>
                        {showChat ? t('关闭助手') : t('助手')}
                      </Button>
                    </>
                  ) : null}
                  <Button
                    loading={buttonLoading}
                    type='primary'
                    htmlType='submit'>
                    {t('查询')}
                  </Button>
                  {language === 'zh' && (
                    <Button onClick={uploadDataMySheet}>{t('保存')}</Button>
                  )}
                </Flex>
              </div>
            </Form.Item>
          </Form>
        </ConfigProvider>
      </header>
      <main
        className='w-full bg-white rounded-lg h-[calc(94vh-10rem)]'
        style={{
          position: 'relative',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
          border: '1px solid #f0f0f0',
        }}>
        <div
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}>
          <div
            style={{
              height: showChat ? '33.333%' : '100%',
              transition: 'height 0.3s ease',
            }}>
            <MySheetNew
              rowData={mySheetData}
              isMultiVersion={currentPaPvId.length > 1}
              ref={MySheetRef}
            />
          </div>
          {showChat && <BottomChat form={form} />}
        </div>
      </main>
    </div>
  );
};

export default PlanningViewNew;
