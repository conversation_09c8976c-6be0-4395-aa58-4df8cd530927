import { useState, useEffect } from 'react';
import { Select, Modal, Form, Input, Space, Button, Badge, Switch } from 'antd';
import { FilterIcon } from '../PlanningView';
import ajax from '@/api';
import _ from 'lodash';

const { Item } = Form;

type FilterModalProps = {
  attributeList: FilterCondition<any, any>[];
  setFilter?: any;
};

export interface FilterCondition<T, K> {
  id: any;
  value: T;
  label: K;
  type: string;
}

/**
 * @description 操作的map
 */

const filterTypeSelect = new Map<string, { label: string; value: string }[]>();
const FilterModal: React.FC<FilterModalProps> = ({
  attributeList,
  setFilter = () => {},
}) => {
  const form = Form.useFormInstance();
  const [open, setOpen] = useState<boolean>(false);

  const conditions = Form.useWatch('conditions', form);
  const [count, setCount] = useState(1);

  const close = () => {
    setOpen(false);
  };
  /**
   * @description 获取筛选条件
   * */
  const getOperation = async () => {
    try {
      const res = await ajax.getFilterOperatorList();
      if (res.code === 0) {
        const list = _.get(res, 'data.list', {}) as Record<
          string,
          { label: string; opt: string }[]
        >;
        for (const key in list) {
          const value = list[key];
          filterTypeSelect.set(
            key,
            value.map((item) => ({ label: item.label, value: item.opt })),
          );
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const openModal = () => setOpen(true);

  useEffect(() => {
    setCount(conditions?.length || 0);
  }, [conditions]);
  useEffect(() => {
    getOperation();
  }, []);

  return (
    <>
      <span onClick={openModal} className='mr-4 text-[24px]'>
        <Badge count={count}>
          <FilterIcon className='cursor-pointer' />
        </Badge>
      </span>
      <Modal
        open={open}
        onCancel={close}
        forceRender
        onOk={() => {
          // form.setFieldValue(
          //   'conditions',
          //   form.getFieldValue('conditions').map((item: any) => ({
          //     ...item,
          //     content: item.content.replace(/\s+/g, ''),
          //   })),
          // );
          // form.validateFields().then(() => {
          //   close();
          // });
          const conditions = form.getFieldValue('conditions');
          if (
            !conditions ||
            Object.keys(conditions).some(
              (key: any) =>
                !conditions[key] || conditions[key].content.trim() === '',
            )
          ) {
            form.setFieldValue('conditions', null);
            close();
            return;
          }
          setFilter(
            conditions.map((item: any) => ({
              ...item,
              content: item.content.replace(/\s+/g, ''),
            })),
          );
          close();
        }}>
        <Form.List name='conditions'>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, ...field }) => (
                <FilterItem
                  key={key}
                  field={field}
                  remove={remove}
                  attributeList={attributeList}
                />
              ))}
              <Item>
                <Button onClick={() => add()}>添加主数据筛选选项</Button>
              </Item>
            </>
          )}
        </Form.List>
        <Form.Item
          name='isOnlyWithData'
          label='只显示有数据的计划对象'
          initialValue={false}>
          <Switch />
        </Form.Item>
      </Modal>
    </>
  );
};

type FilterItemProps = {
  field: any;
  attributeList: FilterCondition<any, any>[];
  remove: (name: number) => void;
};

function FilterItem(props: FilterItemProps) {
  const { field } = props;
  return (
    <Space style={{ display: 'flex', marginBottom: 8 }} align='baseline'>
      <Item
        {...props.field}
        name={[field.name, 'key']}
        rules={[{ required: true, message: '请输入过滤主属性' }]}>
        <Select
          style={{ width: 120 }}
          options={props.attributeList.map((attr) => ({
            label: attr.label, // Assuming attributeName exists
            value: attr.id,
          }))}
        />
      </Item>
      <Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const attributeName = getFieldValue([
            'conditions',
            field.name,
            'key',
          ]);
          const attributeType =
            props.attributeList.find((item) => item.value === attributeName)
              ?.type || 'string';

          const options = filterTypeSelect.get(attributeType) || [];

          return (
            <Item
              {...props.field}
              name={[field.name, 'type']}
              rules={[{ required: true, message: '请输入过滤条件' }]}>
              <Select style={{ width: 120 }} options={options} />
            </Item>
          );
        }}
      </Item>
      <Item {...props.field} name={[field.name, 'content']}>
        <Input />
      </Item>
      <Button type='link' onClick={() => props.remove(field.name)}>
        删除
      </Button>
    </Space>
  );
}

export default FilterModal;
