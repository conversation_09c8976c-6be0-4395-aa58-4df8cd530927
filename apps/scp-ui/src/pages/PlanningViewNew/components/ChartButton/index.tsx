import React, { useState } from 'react';
import { ChartButton as BaseChartButton } from '@/components';
import { Button } from 'antd';
import { ChartStarIcon } from '../../PlanningView'; // 确保这个图标路径正确
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';

const ChartButton: React.FC = () => {
  const { t } = useVoerkaI18n();

  const [visible, setVisible] = useState(false);

  /**
   * @description 获取AI状态
   * */
  const getAiStatus = async (): Promise<boolean> => {
    try {
      const res = await ajax.getAiSwitch();
      return res.data.isAi;
    } catch (error) {
      console.error(error);
    }
    return false;
  };
  const showModal = async () => {
    const _isShowChartButton = await getAiStatus();
    if (_isShowChartButton) {
      setVisible(true);
    }
  };
  const closeModal = () => setVisible(false);

  return (
    <div>
      <BaseChartButton open={visible} closeModal={closeModal} isCommand={true}>
        <Button icon={<ChartStarIcon />} onClick={showModal}>
          {t('助手')}
        </Button>
      </BaseChartButton>
    </div>
  );
};

export default ChartButton;
