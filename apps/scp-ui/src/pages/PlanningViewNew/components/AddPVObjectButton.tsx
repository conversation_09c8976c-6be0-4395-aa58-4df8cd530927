import { useState, useEffect } from 'react';
import { Button, Modal, Select, SelectProps, Form, message } from 'antd';
import { GetKfAttributeListRes } from '@/api/PlanningView/model';
import ajax from '@/api';
import _ from 'lodash';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useSelector } from 'react-redux';

interface AddPVObjectButtonProps {
  kfList: SelectProps['options'];
  paPvId: number | null;
}

const { Item } = Form;

const AddPVObjectButton: React.FC<AddPVObjectButtonProps> = ({ kfList }) => {
  const { t } = useVoerkaI18n();

  const pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [kfId, setKfId] = useState<number | undefined>(undefined);
  const [attributeList, setAttributeList] = useState<GetKfAttributeListRes[]>(
    [],
  );
  const [messageApi, contextHolder] = message.useMessage();
  const paPvId = Form.useWatch('paPvId', form);

  const openModal = () => {
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const saveObject = () => {
    const props = form.getFieldsValue();
    const { values } = props;
    const _values = Object.entries(values).map(([key, value]) => {
      return {
        fieldCode: key,
        fieldKey: value,
        fieldValue: attributeList
          .find((item) => item.fieldCode === key)
          ?.values.find((item: { key: unknown }) => item.key === value)?.name,
      };
    });

    const params = {
      ...props,
      paPvId,
      values: _values,
      isDelete: false,
    };
    form.validateFields().then(() => handleSaveOrDeleteObject(params));
  };

  const deleteObject = () => {
    const props = form.getFieldsValue();
    const { values } = props;
    const _values = Object.entries(values).map(([key, value]) => {
      return {
        fieldCode: key,
        fieldKey: value,
        fieldValue: attributeList
          .find((item) => item.fieldCode === key)
          ?.values.find((item: { key: unknown }) => item.key === value)?.name,
      };
    });

    const params = {
      ...props,
      paPvId,
      values: _values,
      isDelete: true,
    };
    form.validateFields().then(() => handleSaveOrDeleteObject(params));
  };

  /**
   * @description 保存或者删除Object
   * */

  const handleSaveOrDeleteObject = async (values: any) => {
    try {
      const res = await ajax.handlePlanObject(values);
      if (res.code === 0) {
        console.log('保存成功');
        closeModal();
      } else {
        messageApi.error(res.msg);
        throw new Error(res.msg);
      }
    } catch (error) {
      console.error('Error saving or deleting object:', error);
    }
  };

  /**
   * @description 获取kf对应的属性列表
   * */
  const getAttributeList = async (paPvId: number, kfId: number) => {
    try {
      const res = await ajax.getKfAttributeList({ paPvId, kfId });
      if (res.code === 0) {
        const list: GetKfAttributeListRes[] = _.get(res, 'data.list', []) || [];
        setAttributeList(list);
      } else {
        setAttributeList([]);
        messageApi.error(res.msg);
        throw new Error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching attribute list:', error);
    }
  };

  useEffect(() => {
    if (kfId !== undefined) {
      form.setFieldValue('values', {});
      if (paPvId !== null) getAttributeList(paPvId, kfId);
    }
  }, [kfId]);

  return (
    <>
      <Button onClick={openModal}>{t('计划对象')}</Button>
      <Modal
        destroyOnClose
        title={t('计划对象管理')}
        open={modalOpen}
        onOk={saveObject}
        okText={t('添加')}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <Button
              danger
              onClick={deleteObject}
              disabled={attributeList.length === 0}>
              {t('删除')}
            </Button>
            <OkBtn />
            <CancelBtn />
          </>
        )}
        okButtonProps={{
          disabled: attributeList.length === 0,
        }}
        onCancel={closeModal}>
        {/* kfList的选择框 */}
        <Form
          labelCol={{ span: 4 }}
          form={form}
          onValuesChange={(_, values) => setKfId(values.kfId)}>
          <Item
            label={t('计划版本')}
            name='paPvId'
            rules={[{ required: true }]}>
            <Select options={pVersionList} style={{ width: '200px' }} />
          </Item>
          <Item label={t('关键指标')} name='kfId' rules={[{ required: true }]}>
            <Select options={kfList} style={{ width: '200px' }} />
          </Item>
          {attributeList.map((item) => {
            return (
              <>
                <Item
                  name={['values', item.fieldCode]}
                  rules={[{ required: true }]}
                  label={item.fieldName}>
                  <Select
                    style={{ width: '200px' }}
                    options={item.values}
                    fieldNames={{ label: 'name', value: 'key' }}
                  />
                </Item>
              </>
            );
          })}
        </Form>
      </Modal>
      {contextHolder}
    </>
  );
};

export default AddPVObjectButton;
