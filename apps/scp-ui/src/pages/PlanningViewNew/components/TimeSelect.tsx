import React, { useState, useEffect } from 'react';
import {
  Form,
  Button,
  Select,
  DatePicker,
  Space,
  Modal,
  SelectProps,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import updateLocale from 'dayjs/plugin/updateLocale';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
const { Option } = Select;
const { RangePicker } = DatePicker;

dayjs.extend(weekOfYear);
dayjs.extend(quarterOfYear);
dayjs.extend(updateLocale);
dayjs.updateLocale('zh-cn', {
  weekStart: 1,
});

interface DateRange {
  dateType: number;
  dateRange: Dayjs[];
}

interface FormValues {
  time: DateRange[];
}

interface TimeSelectProps extends SelectProps {
  onChange?: (values: FormValues) => void;
}

export type DateType = 'date' | 'week' | 'month' | 'quarter' | 'year';
/**
 * @description 日期映射Set
 */
export const timeMap = new Map<number, DateType>([
  [1, 'date'],
  // [2, 'date'],
  [3, 'week'],
  [4, 'month'],
  [5, 'quarter'],
  [6, 'year'],
]);
const TimeSelect: React.FC<TimeSelectProps> = ({ onChange, ...rest }) => {
  const form = Form.useFormInstance();
  const time = Form.useWatch('time', form);
  const { t } = useVoerkaI18n();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string[]>([]);
  const [dateTypeList, setDateTypeList] = useState<SelectProps['options']>([]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    const values = form.getFieldsValue();
    onFinish(values);
  };

  function formatDate(date: Dayjs, type: DateType): string {
    switch (type) {
      case 'date':
        return dayjs(date).format('YYYY-MM-DD');
      case 'week':
        return `${dayjs(date).year()}-${dayjs(date).week()}周`;
      case 'month':
        return dayjs(date).format('YYYY-MM');
      case 'quarter':
        return `${dayjs(date).year()}-Q${dayjs(date).quarter()}`;
      case 'year':
        return dayjs(date).format('YYYY');
      default:
        return dayjs(date).format('YYYY-MM-DD');
    }
  }
  function getFormattedDate(date: Dayjs, formatType: number): string {
    const type = timeMap.get(formatType);
    if (!type) {
      throw new Error(`Unsupported format type: ${formatType}`);
    }
    return formatDate(date, type);
  }

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  //
  const onFinish = (values: FormValues) => {
    const formattedRanges = values.time.map((range) => ({
      dateType: range.dateType,
      dateRange: range.dateRange.map((date) =>
        getFormattedDate(date, range.dateType),
      ),
    }));

    const displayValue = formattedRanges.map(
      (range) =>
        `${dateTypeList?.find((item) => item.value === range.dateType)?.label}: ${range.dateRange.join(' - ')}`,
    );

    setSelectedValue(displayValue);
    setIsModalVisible(false);

    if (onChange) {
      onChange(values);
    }
  };

  /**
   * @description 获取时间配置信息
   * */
  const getTimeProfileList = async () => {
    const res = await ajax.getTimeProfileList();
    const { list } = res.data;
    const tpLevelMap = list && list[0]?.tpLevelMap;

    if (tpLevelMap !== null && tpLevelMap !== undefined) {
      const _temp: SelectProps['options'] = Object.keys(tpLevelMap).map(
        (key) => ({
          label: tpLevelMap[key],
          value: Number(key),
        }),
      );
      setDateTypeList(_temp);
    }
  };

  useEffect(() => {
    const selectedDateType =
      time
        ?.map((item: any) => item?.dateType)
        .filter((item: any) => item !== void 0) || [];
    const _temp =
      dateTypeList
        ?.map((item: any) => {
          if (selectedDateType?.includes(item.value as number)) {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
          return item;
        })
        .filter((item: any) => item !== undefined) || [];
    setDateTypeList(_temp as SelectProps['options']);
  }, [time]);

  useEffect(() => {
    if (time === undefined) {
      return;
    }
    const formattedRanges = time
      .filter((item: any) => item !== undefined)
      .map((range: any) => ({
        dateType: range.dateType,
        dateRange: range.dateRange?.map((date: any) =>
          getFormattedDate(date, range.dateType),
        ),
      }));

    const displayValue = formattedRanges.map(
      (range: any) =>
        `${dateTypeList?.find((item) => item.value === range.dateType)?.label}: ${range.dateRange?.join(' - ')}`,
    );
    setSelectedValue(displayValue);
  }, [time, dateTypeList]);

  useEffect(() => {
    getTimeProfileList();
  }, []);

  return (
    <>
      <Select
        {...rest}
        mode='tags'
        style={{ width: 350 }}
        value={selectedValue}
        placeholder={t('点击选择日期范围')}
        onClick={showModal}
        open={false} // 使 Select 不能下拉，只能点击触发 Modal
      >
        {selectedValue.map((val, index) => (
          <Option key={index} value={val}>
            {val}
          </Option>
        ))}
      </Select>
      <Modal
        title={t('选择日期范围')}
        forceRender={true}
        width={700}
        open={isModalVisible}
        footer={null}
        onCancel={handleCancel}>
        <Form.List name='time'>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Space
                  key={key}
                  style={{ display: 'flex', marginBottom: 8 }}
                  align='baseline'>
                  <Form.Item
                    {...restField}
                    name={[name, 'dateType']}
                    rules={[{ required: true, message: t('请选择日期类型') }]}>
                    <Select style={{ width: 105 }} options={dateTypeList} />
                  </Form.Item>
                  <Form.Item
                    name={[name, 'rollingType']}
                    rules={[
                      { required: true, message: t('请选择日期范围类型') },
                    ]}
                    noStyle
                    shouldUpdate>
                    <Select
                      style={{ width: 105 }}
                      options={[
                        { label: 'Fixed', value: 0 },
                        { label: 'Rolling', value: 1 },
                        { label: 'From Rolls', value: 2 },
                        { label: 'To Rolls', value: 3 },
                      ]}
                    />
                  </Form.Item>
                  <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => {
                      return (
                        <Form.Item
                          {...restField}
                          name={[name, 'dateRange']}
                          rules={[
                            { required: true, message: t('请选择日期范围') },
                          ]}>
                          <RangePicker
                            picker={timeMap.get(
                              Number(
                                getFieldValue(['time', name, 'dateType']) || -1,
                              ),
                            )}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                  <Button danger onClick={() => remove(name)}>
                    {t('删除')}
                  </Button>
                </Space>
              ))}
              <Form.Item>
                <div className='flex justify-between'>
                  <Button
                    type='dashed'
                    onClick={() => add()}
                    style={{ width: 126 }}>
                    {t(' + 添加日期范围')}
                  </Button>
                  <div>
                    <Button type='primary' className='mr-4' onClick={handleOk}>
                      {t('确定')}
                    </Button>
                  </div>
                </div>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Modal>
    </>
  );
};

export default TimeSelect;
