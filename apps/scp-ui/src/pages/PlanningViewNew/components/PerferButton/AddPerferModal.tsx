import { App, Modal, Form, Select, Input, Button } from 'antd';
import { SelectProps } from 'antd/lib';
import ajax from '@/api';
type AddPerferModalProps = {
  parentId: number;
  open: boolean;
  close: () => void;
  addScheme: (group: number, id: number | null, name: string) => void;
};
const options: SelectProps['options'] = [
  { label: '分组', value: 1 },
  { label: '偏好', value: 2 },
];
const AddPerferModal: React.FC<AddPerferModalProps> = ({
  parentId,
  open,
  addScheme,
  close,
}) => {
  const { message } = App.useApp();

  /**
   * @description 新建分组
   * */
  const addSchemeGroup = async (values: any) => {
    try {
      const res = await ajax.addSchemeGroupTreeNode(values);
      if (res.code === 0) {
        console.log('添加成功');
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      message.error(error as string);
      console.log(error);
    } finally {
      close();
    }
  };
  const onFinish = (values: any) => {
    const { type, name } = values;
    if (type === 1) {
      const props = {
        parentId,
        groupName: name,
        menuTag: 'COLLAB_VIEW',
      };
      addSchemeGroup(props);
    } else {
      addScheme(parentId, null, name);
    }
  };

  return (
    <Modal
      title='Basic Modal'
      open={open}
      destroyOnClose
      onOk={() => {}}
      onCancel={close}
      footer={null}
      zIndex={10000}>
      <Form onFinish={onFinish}>
        <Form.Item label={'类型'} name='type' rules={[{ required: true }]}>
          <Select options={options} />
        </Form.Item>
        <Form.Item label={'名称'} name='name' rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item>
          <Button htmlType='submit' type='primary'>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddPerferModal;
