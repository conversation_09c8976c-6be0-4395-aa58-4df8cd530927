import { useState, useEffect, useRef } from 'react';
import {
  App,
  Button,
  Dropdown,
  Form,
  Tree,
  type TreeDataNode,
  Popconfirm,
  FormInstance,
} from 'antd';
import AddPerferModal from './AddPerferModal';
import type { OperationData } from './model';
import _ from 'lodash';
import ajax from '@/api';
import { getFormattedDate } from '@/utils/PlanningView';
import {
  GetPerferTreeDataRes,
  SaveCollabSchemeReq,
} from '@/api/PlanningView/model';

interface PerferButtonProps extends React.PropsWithChildren {
  pForm: FormInstance;
  filter: any;
}
import { utils } from '@repo/configuration';
const dayjs = utils.getDayjs('scp');

const PerferButton: React.FC<PerferButtonProps> = ({ pForm, filter }) => {
  const form = Form.useFormInstance();
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  const { message } = App.useApp();
  const [disableDatas, setDisableDatas] = useState<{
    showDisable: boolean;
    saveDisable: boolean;
    addDisable: boolean;
    deleteDisable: boolean;
  }>({
    showDisable: true,
    saveDisable: true,
    addDisable: true,
    deleteDisable: false,
  });
  //控制dropdown的显示隐藏
  const [open, setOpen] = useState<boolean>(false);
  const dropdownRef = useRef<any>(null);
  //控制modal的显示隐藏
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  //当前选择的节点
  const [selectedNode, setSelectedNode] = useState<{
    key: number;
    title: string;
    childrenLength?: number;
  }>({
    key: -1,
    title: '',
  });
  const [selectedNodeIsPerfer, setSelectedNodeIsPerfer] =
    useState<boolean>(false);

  //扁平化schemeList
  const [schemeList, setSchemeList] = useState<SaveCollabSchemeReq[]>([]);

  //最下方操作的数据
  const operationData: OperationData[] = [
    {
      label: t('显示'),
      key: 'show',
      disabled: disableDatas.showDisable,
      operationFunction: () => {
        const perferItem = schemeList.find((item: SaveCollabSchemeReq) => {
          return item.schemeId === selectedNode.key;
        });
        pForm.setFieldValue('md', perferItem?.attributeList);
        pForm.setFieldValue('kf', perferItem?.kfList);
        const dateList = perferItem?.dateList.map((item) => {
          const fromDate =
            item.fromRolls !== null
              ? dayjs().add(item.fromRolls, 'day')
              : dayjs(item.startDate);
          const toDate =
            item.toRolls !== null
              ? dayjs().add(item.toRolls, 'day')
              : dayjs(item.endDate);
          console.log(item.rollingType, 'item.rollingType');
          return {
            dateRange: [fromDate, toDate],
            dateType: item.tpLevel,
            rollingType: item.rollingType,
          };
        });
        pForm.setFieldValue('time', dateList);
        pForm.setFieldValue(
          'conditions',
          perferItem?.conditionList.map((item) => ({
            key: item.id,
            type: item.preferOperator,
            content: item.preferValue,
          })),
        );
        pForm.setFieldValue('isOnlyWithData', perferItem?.isOnlyWithData);
        console.log(pForm.getFieldValue('conditions'), 'pForm.getFieldValue');
        console.log(perferItem, 'perferItem');
        setOpen(false);
      },
    },
    {
      label: t('新增'),
      key: 'add',
      disabled: disableDatas.addDisable,
      operationFunction: () => {
        setModalOpen(true);
      },
    },
    {
      label: t('更新'),
      key: 'save',
      disabled: disableDatas.saveDisable,
      operationFunction: () => {
        console.log('save');
        console.log(selectedNode);
        SaveCollabScheme(
          getParentKey(treeData, selectedNode.key),
          selectedNode.key,
          selectedNode.title,
        );
        setOpen(false);
      },
    },
    {
      label: t('删除'),
      key: 'delete',
      className: 'text-red-500',
      disabled: disableDatas.deleteDisable,
      render: (label: JSX.Element) => {
        return (
          <Popconfirm
            key='delete'
            title={t('确定删除吗?')}
            onConfirm={() => {
              if (selectedNodeIsPerfer) {
                deleteScheme(selectedNode.key);
              } else {
                deleteSchemeGroup(selectedNode.key);
              }
            }}>
            {label}
          </Popconfirm>
        );
      },
    },
  ];

  /**
   * @description 获取偏好
   * */
  const getPerferTreeData = async () => {
    try {
      const res = await ajax.getPerferTreeData({ menuTag: 'COLLAB_VIEW' });
      if (!res.code) {
        const result = _.get(res, 'data.result', []);
        const _treeData = result
          .map((item: any) => transformTreeData(item))
          .filter((item): item is TreeDataNode => item !== null);
        const _schemeList = transformTreeDataToArr(result[0] || []);
        setSchemeList(_schemeList);
        setTreeData(_treeData);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      message.error(t('获取偏好失败'));
      console.log(error);
    }
  };
  /**
   * @description 将后端返回的树形数据转换成TreeDataNode
   * */
  const transformTreeData = (
    treeNode: GetPerferTreeDataRes | SaveCollabSchemeReq,
  ): TreeDataNode | null => {
    if (!treeNode) return null;

    if ('schemeId' in treeNode && 'schemeName' in treeNode) {
      // treeNode is of type SaveCollabSchemeReq
      const _treeNode: TreeDataNode = {
        key: treeNode.schemeId as number,
        title: treeNode.schemeName,
      };
      return _treeNode;
    } else if (treeNode.children !== void 0) {
      // treeNode is of type GetPerferTreeDataRes
      const _treeNode: TreeDataNode = {
        key: treeNode.id,
        title: treeNode.groupName,
      };
      let _children: any[] = treeNode.children || [];
      if (treeNode.schemeList && treeNode.schemeList.length > 0) {
        _children = [...treeNode.schemeList, ..._children];
      }
      _treeNode.children =
        (_children.map((item) => transformTreeData(item)) as TreeDataNode[]) ||
        null;
      console.log(_treeNode, '_treeNode');
      return _treeNode;
    }
    return null;
  };
  /**
   * @description 将租中的所有偏好进行保存为一个数组
   * */
  const transformTreeDataToArr = (
    treeData: GetPerferTreeDataRes | SaveCollabSchemeReq,
  ) => {
    const _temp: SaveCollabSchemeReq[] = [];
    if (!treeData) return _temp;
    if (
      !('schemeId' in treeData && 'schemeName' in treeData) &&
      treeData.schemeList !== void 0
    ) {
      const _schemeList: any[] = treeData.schemeList || [];
      _schemeList.forEach((item) => {
        _temp.push(...transformTreeDataToArr(item));
      });
    }
    if ('schemeId' in treeData && 'schemeName' in treeData) {
      // treeData is of type SaveCollabSchemeReq
      _temp.push(treeData);
    } else if (treeData.children !== void 0 && treeData.children !== null) {
      // treeData is of type GetPerferTreeDataRes
      const _children: any[] = treeData.children || [];
      _children.forEach((item) => {
        _temp.push(...transformTreeDataToArr(item));
      });
    }
    return _temp;
  };

  /**
   * @description 删除分组
   * */
  const deleteSchemeGroup = async (id: number) => {
    if (id === -1) return;
    try {
      const res = await ajax.deleteSchemeGroupTreeNode({ groupId: id });
      if (res.code === 0) {
        console.log(t('删除成功'));
        getPerferTreeData();
      }
    } catch (error) {
      message.error(t('删除失败'));
      console.log(error);
    }
  };

  /**
   * @description 删除偏好
   * */
  const deleteScheme = async (id: number) => {
    if (id === -1) return;
    try {
      const res = await ajax.deleteCollabScheme({ schemeId: id });
      if (res.code === 0) {
        console.log(t('删除成功'));
        getPerferTreeData();
      }
    } catch (error) {
      message.error(t('删除失败'));
      console.log(error);
    }
  };

  /**
   * @description 保存和新增偏好
   * */
  const saveAndAddPerfer = async (props: SaveCollabSchemeReq) => {
    try {
      const res = await ajax.saveCollabScheme(props);
      if (res.code === 0) {
        console.log(t('保存成功'));
        setModalOpen(false);
        getPerferTreeData();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      message.error(t('保存失败'));
      console.log(error);
    } finally {
      setModalOpen(false);
    }
  };
  /**
   * @description 通过子节点的key获取父节点的key
   * */
  const getParentKey = (treeData: TreeDataNode[], key: number): number => {
    let parentKey = -1;
    for (let i = 0; i < treeData.length; i++) {
      const item = treeData[i];
      if (item.children) {
        if (item.children.some((child) => child.key === key)) {
          parentKey = item.key as number;
        } else if (getParentKey(item.children, key) !== -1) {
          parentKey = getParentKey(item.children, key);
        }
      }
    }
    return parentKey;
  };

  const handleClickOutside = (event: any) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setOpen(false);
    }
  };

  const SaveCollabScheme = (
    groupId: number,
    schemeId: number | null,
    schemeName: string,
  ) => {
    const value = form.getFieldsValue();
    const props: SaveCollabSchemeReq = {
      schemeId: schemeId,
      menuTag: 'COLLAB_VIEW',
      groupId: groupId,
      isDefault: false,
      schemeName: schemeName,
      isOnlyWithData: value.isOnlyWithData,
      conditionList: filter
        ? filter.map((item: any) => ({
            conditionId: item.key,
            preferOperator: item.type,
            preferValue: item.content,
          }))
        : [],
      attributeList: value.md,
      kfList: value.kf,
      dateList: value.time.map((item: any) => {
        let fromRolls: number | null = null;
        let toRolls: number | null = null;
        const rollingType = item.rollingType;
        switch (rollingType) {
          case 1:
            fromRolls = item.dateRange[0].diff(dayjs(), 'day');
            toRolls = item.dateRange[1].diff(dayjs(), 'day');
            break;
          case 2:
            fromRolls = item.dateRange[0].diff(dayjs(), 'day');
            toRolls = null;
            break;
          case 3:
            toRolls = item.dateRange[1].diff(dayjs(), 'day');
            fromRolls = null;
            break;
          default:
            toRolls = null;
            fromRolls = null;
            break;
        }
        return {
          endDate: getFormattedDate(item.dateRange[1], item.dateType),
          startDate: getFormattedDate(item.dateRange[0], item.dateType),
          rollingType: rollingType,
          tpLevel: item.dateType,
          fromRolls: fromRolls,
          toRolls: toRolls,
        };
      }),
    };
    saveAndAddPerfer(props);
  };

  useEffect(() => {
    getPerferTreeData();
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <>
      <AddPerferModal
        open={modalOpen}
        parentId={selectedNode.key}
        close={() => {
          setModalOpen(false);
          getPerferTreeData();
        }}
        addScheme={SaveCollabScheme}
      />
      <Dropdown
        trigger={['click']}
        open={open}
        dropdownRender={() => {
          return (
            <div ref={dropdownRef} className='w-[200px] bg-white'>
              <Tree
                treeData={treeData}
                showLine={true}
                onSelect={(selectKeys, info) => {
                  const node = (info.selectedNodes[0] as {
                    key: number;
                    title: string;
                    childrenLength?: number;
                  }) || { key: -1, title: '', childrenLength: 0 };
                  node.childrenLength = info.node.children?.length || 0;
                  setSelectedNode(node);
                  if (selectKeys.length === 1) {
                    console.log(info.node.children);
                    if (info.node.children !== void 0) {
                      setSelectedNodeIsPerfer(false);
                      setDisableDatas({
                        deleteDisable: info.node.children.length > 0,
                        showDisable: true,
                        addDisable: false,
                        saveDisable: true,
                      });
                    } else {
                      setSelectedNodeIsPerfer(true);
                      setDisableDatas({
                        deleteDisable: false,
                        showDisable: false,
                        addDisable: true,
                        saveDisable: false,
                      });
                    }
                  } else {
                    setDisableDatas({
                      showDisable: true,
                      addDisable: true,
                      saveDisable: true,
                      deleteDisable: true,
                    });
                  }
                }}
              />
              <div>
                {operationData.map((item: OperationData) => {
                  const title = (
                    <a
                      key={item.key}
                      className={`mx-[4px] ${item.disabled && 'disabled-link'} ${item.className && item.className}`}
                      onClick={() =>
                        item.operationFunction && item.operationFunction()
                      }>
                      {item.label}
                    </a>
                  );

                  if (item.render) {
                    return item.render(title);
                  }
                  return title;
                })}
              </div>
            </div>
          );
        }}>
        <Button
          onClick={() => {
            setOpen(!open);
          }}>
          {t('偏好')}
        </Button>
      </Dropdown>
    </>
  );
};

export default PerferButton;
