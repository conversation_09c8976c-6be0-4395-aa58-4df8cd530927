import { useState, useEffect } from 'react';
import { useUpdateEffect } from 'ahooks';
import { Form, Select, SelectProps } from 'antd';
import _ from 'lodash';
import { FilterModal, DraggableSelect } from '.';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
const { Item } = Form;

interface ItemFormProps {}
const ItemForm: React.FC<ItemFormProps> = () => {
  const form = Form.useFormInstance();
  const itemTypeId = Form.useWatch('itemTypeId', form);
  const [itemTypeList, setItemTypeList] = useState<SelectProps['options']>([]);
  const [kfList, setKfList] = useState<SelectProps['options']>([]);
  const [attributeList, setAttributeList] = useState<SelectProps['options']>(
    [],
  );
  const [filterData, setFilterData] = useState<any>([]);

  const { t } = useVoerkaI18n();

  const getItemTypeList = async () => {
    try {
      const res = await ajax.getItemTyepList({});
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setItemTypeList(
          list.map((item) => ({ label: item.label, value: item.id })),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getItemTypeKFList = async (itemTypeId: number) => {
    try {
      const res = await ajax.getItemTypeKF({ itemTypeId });
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setKfList(
          list.map((item) => {
            return {
              label: item.kfLabel,
              value: item.kfId,
            };
          }),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getAttributeList = async (itemTypeId: number) => {
    try {
      const res = await ajax.getItemTypeRootAttribute({ itemTypeId });
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setFilterData(
          list.map((item) => {
            return {
              value: item.attributeName,
              label: item.label,
              type: 'string',
            };
          }),
        );
        setAttributeList(
          list.map((item) => {
            return {
              label: item.label,
              value: item.id,
            };
          }),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getItemTypeList();
  }, []);

  useUpdateEffect(() => {
    getItemTypeKFList(itemTypeId);
    getAttributeList(itemTypeId);
  }, [itemTypeId]);

  return (
    <div
      style={{
        display: 'ruby',
      }}>
      <Item noStyle>
        <Item label={t('类型')} name='itemTypeId'>
          <Select style={{ width: 120 }} options={itemTypeList} />
        </Item>
        <FilterModal attributeList={filterData} />
      </Item>
      <Item label={t('主属性')} name='md'>
        <DraggableSelect options={attributeList} maxTagCount={'responsive'} />
      </Item>
      <Item label={t('关键指标')} name='kf'>
        <DraggableSelect options={kfList} maxTagCount={'responsive'} />
      </Item>
    </div>
  );
};
export default ItemForm;
