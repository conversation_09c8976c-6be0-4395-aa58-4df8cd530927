import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useVoerkaI18n } from '@voerkai18n/react';
import {
  BackgroundCondition,
  CornerCell,
  DataCell,
  PivotSheet,
  RowCell,
  S2Event,
  TextCondition,
  copyToClipboard,
} from '@antv/s2';
import { Input, App } from 'antd';
import { debounce } from 'lodash';
import { useSize } from 'ahooks';
import './index.css';

class EditableDataCell extends DataCell {
  protected initCell(): void {
    super.initCell();
    this.backgroundShape.style.cursor = 'pointer';
    this.textShape.style.cursor = 'pointer';
  }
}

interface fields {
  rows: any[];
  columns: any[];
  values: any[];
}

interface metaItem {
  field: any;
  name: any;
}

const getTextWidth = (text: string, font: string) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) {
    return 0;
  }
  context.font = font;
  return context.measureText(text).width;
};

const getSheetData = (
  t: any,
  rowData: any,
  fields: fields,
  data: any[],
  meta: metaItem[],
  idQuery: any = {},
  kfIdQuery: any = {},
  addtionalData: any = {},
  temp: any = {},
) => {
  if (rowData.label && rowData.label !== 'root') {
    if (!fields.rows.includes(rowData.field)) {
      fields.rows.push(rowData.field);
    }
    if (!meta.some((item: any) => item.field === rowData.field)) {
      meta.push({
        field: rowData.field,
        name: rowData.label,
      });
    }
    temp[rowData.field] = rowData.name;
  }
  if (rowData.values && rowData.values.length > 0) {
    temp['keyFigure'] = rowData.name;
    if (!Object.keys(kfIdQuery).includes(rowData.name)) {
      kfIdQuery[rowData.name] = rowData.key;
    }
    rowData.values.forEach((value: any) => {
      if (!fields.values.includes((value.periodId | value.key) + '')) {
        fields.values.push((value.periodId | value.key) + '');
        const title = value.date ? value.date : value.name;
        fields.columns.push({
          field: (value.periodId | value.key) + '',
          title: title,
          width: getTextWidth(title, '12px Arial') + 10,
        });
        addtionalData[(value.periodId | value.key) + ''] = {};
      }
      addtionalData[(value.periodId | value.key) + ''][data.length] = {
        disabledLevel: value.disabledLevel ? value.disabledLevel : 4,
        tpLevel: value.tpLevel ? value.tpLevel : 0,
      };
      temp[(value.periodId | value.key) + ''] =
        value.value !== null && value.value ? value.value : value.itemValue;
    });
    data.push({
      ...temp,
      index: data.length + 1,
    });
  }
  if (rowData.children && rowData.children.length > 0) {
    if (!Object.keys(idQuery).includes(rowData.name)) {
      idQuery[rowData.name] = {
        id: rowData.id,
        children: {},
      };
    }
    rowData.children.forEach((child: any) => {
      getSheetData(
        t,
        child,
        fields,
        data,
        meta,
        idQuery[rowData.name].children,
        kfIdQuery,
        addtionalData,
        temp,
      );
    });
  }
  if (rowData.keys && rowData.keys.length > 0) {
    if (!Object.keys(idQuery).includes(rowData.name)) {
      idQuery[rowData.name] = {
        id: rowData.id,
        children: {},
      };
    }
    if (!fields.rows.includes('keyFigure')) {
      fields.rows.push('keyFigure');
    }
    if (!meta.some((item: any) => item.field === 'keyFigure')) {
      meta.push({
        field: 'keyFigure',
        name: t('关键指标'),
      });
    }
    rowData.keys.forEach((key: any) => {
      getSheetData(
        t,
        key,
        fields,
        data,
        meta,
        idQuery,
        kfIdQuery,
        addtionalData,
        temp,
      );
    });
  }
};

const MySheetNew = forwardRef(function MySheetNew(
  {
    rowData,
    isMultiVersion,
  }: {
    rowData: any;
    isMultiVersion: boolean;
  },
  ref: React.Ref<any>,
) {
  const { message } = App.useApp();

  const { t } = useVoerkaI18n();
  const [fields, setFields] = useState<fields>({
    rows: [],
    columns: [],
    values: [],
  });
  const [meta, setMeta] = useState<metaItem[]>([]);
  const [data, setData] = useState<any[]>([]);
  const [oringinData, setOriginData] = useState<any[]>([]);
  const [addtionalData, setAddtionalData] = useState<any>({});
  const [idQuery, setIdQuery] = useState<any>({});
  const [kfIdQuery, setKfIdQuery] = useState<any>({});
  const [scroll, setScroll] = useState<any>({ x: 0, y: 0 });
  const indexWidth = 50;
  const fakeFields: fields = {
    rows: ['index'],
    columns: new Array(26).fill('').map((_, index) => {
      return {
        field: String.fromCharCode(65 + index),
        title: String.fromCharCode(65 + index),
      };
    }),
    values: new Array(26).fill('').map((_, index) => {
      return String.fromCharCode(65 + index);
    }),
  };
  const fakeMeta: metaItem[] = [
    { field: 'index', name: '' },
    ...new Array(26).fill('').map((_, index) => {
      return {
        field: String.fromCharCode(65 + index),
        name: String.fromCharCode(65 + index),
      };
    }),
  ];
  const fakeData: any[] = new Array(50).fill('').map((_, index) => {
    return { index: index + 1 };
  });
  const [isFakeData, setIsFakeData] = useState(false);
  const [rowPos, setRowPos] = useState<any>({ index: 0 });
  const [cornerPos, setCornerPos] = useState<any>({ index: 0 });

  const [inputShow, setInputShow] = useState(false);
  const [inputBaseInfo, setInputBaseInfo] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const [inputValue, setInputValue] = useState<string>('');
  const [editingCell, setEditingCell] = useState<{
    rowIndex: number;
    field: string;
  } | null>(null);
  const [modifyCells, setModifyCells] = useState<
    {
      rowIndex: number;
      field: string;
    }[]
  >([]);

  useImperativeHandle(ref, () => {
    return {
      getModifyData: () => {
        return getFormatUpdateData();
      },
      getModifyLength: () => {
        return modifyCells.length;
      },
    };
  }, [data]);

  useEffect(() => {
    if (!rowData || rowData.length === 0) {
      setFields(fakeFields);
      setMeta(fakeMeta);
      setData(fakeData);
      setIsFakeData(true);
      return;
    }
    setIsFakeData(false);
    const tempFields: fields = {
      rows: [],
      columns: [],
      values: [],
    };
    const tempMeta: metaItem[] = [
      {
        field: 'index',
        name: t(''),
      },
    ];
    const tempData: any[] = [];
    const tempIdQuery: any = {};
    const tempKfIdQuery: any = {};
    const tempAddtionalData: any = {};
    getSheetData(
      t,
      {
        id: 0,
        field: 'root',
        label: 'root',
        name: 'root',
        children: rowData,
      },
      tempFields,
      tempData,
      tempMeta,
      tempIdQuery,
      tempKfIdQuery,
      tempAddtionalData,
    );
    setIdQuery(tempIdQuery);
    setKfIdQuery(tempKfIdQuery);
    tempFields.rows.push('index');
    setFields(tempFields);
    setMeta(tempMeta);
    setOriginData(
      tempData.map((item) => {
        return {
          ...item,
        };
      }),
    );
    setAddtionalData(tempAddtionalData);
    const fData: any = {};
    tempFields.rows.forEach((row: any) => {
      fData[row] = '';
    });
    tempFields.values.forEach((value: any) => {
      fData[value] = '';
    });
    while (tempData.length < 50) {
      tempData.push({
        ...fData,
        index: tempData.length + 1,
      });
    }
    setData(tempData);
    setRowPos({ index: 0 });
    setCornerPos({ index: 0 });
    setInputShow(false);
    setEditingCell(null);
    setModifyCells([]);
  }, [rowData]);

  useEffect(() => {
    renderSheet();
  }, [fields, meta, data]);

  const showInput = (
    x: number,
    y: number,
    width: number,
    height: number,
    field: string,
    rowIndex: number,
  ) => {
    setInputShow(true);
    setInputBaseInfo({
      x,
      y,
      width,
      height,
    });
    setEditingCell({
      rowIndex: rowIndex,
      field: field,
    });
  };

  const hideInput = () => {
    setInputShow(false);
    // setEditingCell(null);
  };

  const tryAddModifyCell = (rowIndex: number, field: string) => {
    if (
      !modifyCells.some(
        (item) => item.rowIndex === rowIndex && item.field === field,
      )
    ) {
      modifyCells.push({
        rowIndex: rowIndex,
        field: field,
      });
      setModifyCells([...modifyCells]);
    }
  };

  const tryRemoveModifyCell = (rowIndex: number, field: string) => {
    const index = modifyCells.findIndex(
      (item) => item.rowIndex === rowIndex && item.field === field,
    );
    if (index >= 0) {
      modifyCells.splice(index, 1);
      setModifyCells([...modifyCells]);
    }
  };

  const isNumber = (value: any) => {
    if (!value || value === '') {
      return false;
    }
    const reg = /(-)?\d+(\.\d+)?$/g;
    const regResult = value.match(reg);
    if (regResult && regResult[0] === value) {
      return true;
    } else {
      return false;
    }
  };

  useEffect(() => {
    if (!inputShow && editingCell) {
      if (!(isNumber(inputValue) || inputValue === '')) {
        message.error(t('修改的值只能为数字'));
        return;
      }
      const oringinValue = oringinData[editingCell.rowIndex][editingCell.field];
      if (
        (!inputValue || inputValue.trim() === '') &&
        (!oringinValue || oringinValue === '')
      ) {
        tryRemoveModifyCell(editingCell.rowIndex, editingCell.field);
      } else {
        if (inputValue !== oringinValue + '') {
          tryAddModifyCell(editingCell.rowIndex, editingCell.field);
        } else {
          tryRemoveModifyCell(editingCell.rowIndex, editingCell.field);
        }
      }
      data[editingCell.rowIndex][editingCell.field] = inputValue;
      setData([...data]);
      setEditingCell(null);
      setInputValue('');
    }
  }, [inputShow]);

  const getFormatUpdateData = () => {
    if (modifyCells.length === 0) {
      return [];
    }
    const result = modifyCells.map((item) => {
      const record = {
        ...data[item.rowIndex],
        root: 'root',
      };
      const queryList = [
        'root',
        ...fields.rows.filter((row) => row !== 'index' && row !== 'keyFigure'),
      ];
      let temp: any;
      return {
        col:
          fields.rows.length +
          fields.values.findIndex((value) => value === item.field),
        row: item.rowIndex + 1,
        periodId: Number(item.field),
        kfId: kfIdQuery[record.keyFigure],
        ids: queryList
          .map((row, index) => {
            if (index === 0) {
              temp = idQuery[record[row]];
            } else {
              temp = temp.children[record[row]];
            }
            return {
              field: row,
              id: temp.id,
              name: record[row],
            };
          })
          .filter((item) => item.name !== 'root'),
        tpLevel: addtionalData[item.field][item.rowIndex].tpLevel,
        value: record[item.field] === '' ? null : Number(record[item.field]),
      };
    });

    if (ref && 'current' in ref) {
      ref.current.updateData = result;
    }
    return result;
  };

  const sheetContainer = document.querySelector(
    '.sheet-container',
  ) as HTMLDivElement;

  const sheetSize = useSize(sheetContainer);

  useEffect(() => {
    renderSheet();
  }, [sheetSize, window.innerWidth, window.innerHeight]);

  const renderSheet = async () => {
    if (sheetContainer) {
      const sheet = new PivotSheet(
        sheetContainer,
        {
          fields: fields,
          meta: meta,
          data: data,
        },
        {
          width: sheetContainer.offsetWidth,
          height: sheetContainer.offsetHeight,
          placeholder: {
            cell: '',
          },
          style: {
            rowCell: {
              width: (node) => {
                if (node && node.field === 'index') {
                  return indexWidth;
                }
                return null;
              },
            },
            colCell: {
              width: (node) => {
                const totalColWidth = fields.columns
                  .map((item) => item.width)
                  .reduce((acc, val) => acc + val, 0);
                if (
                  node &&
                  !isFakeData &&
                  sheetContainer.offsetWidth < totalColWidth
                ) {
                  return getTextWidth(node.value, '14px Arial') + 10;
                }
                return null;
              },
            },
          },
          rowCell: (node, s2, headConfig) => {
            if (!Object.keys(rowPos).includes(node.field)) {
              rowPos[node.field] = node.x + indexWidth;
            }
            node.x = rowPos[node.field];
            return new RowCell(node, s2, headConfig);
          },
          cornerCell: (node, s2, cornerConfig) => {
            if (!Object.keys(cornerPos).includes(node.field)) {
              cornerPos[node.field] = node.x + indexWidth;
            }
            node.x = cornerPos[node.field];
            return new CornerCell(node, s2, cornerConfig);
          },
          dataCell: (node, s2, ...restOptions) => {
            if (
              addtionalData[node.valueField] &&
              addtionalData[node.valueField][node.rowIndex] &&
              addtionalData[node.valueField][node.rowIndex].disabledLevel === 2
            ) {
              return new EditableDataCell(node, s2, restOptions);
            }
            return new DataCell(node, s2, ...restOptions);
          },
          interaction: {
            resize: {
              rowCellVertical: false,
            },
            hoverHighlight: false,
            copy: {
              enable: true,
            },
          },
          conditions: {
            background: [
              ...fields.values.map((item) => {
                return {
                  field: item,
                  mapping: (_, __, cell) => {
                    if (!Object.keys(cell.getMeta()).includes('rowIndex')) {
                      return {
                        fill: '#fff',
                      };
                    }
                    if (Object.keys(addtionalData).length === 0) {
                      return {
                        fill: '#f3f3f4',
                      };
                    }
                    const disabledLevel = addtionalData[Number(item)][
                      cell.getMeta().rowIndex
                    ]
                      ? addtionalData[Number(item)][cell.getMeta().rowIndex]
                          .disabledLevel
                      : 0;
                    return {
                      fill: modifyCells.some(
                        (i) =>
                          i.rowIndex === cell.getMeta().rowIndex &&
                          i.field === item,
                      )
                        ? '#c9b4a6'
                        : disabledLevel === 2
                          ? '#fff'
                          : '#f3f3f4',
                    };
                  },
                } as BackgroundCondition;
              }),
              ...fields.rows.map((item) => {
                return {
                  field: item,
                  mapping: (_, __, cell) => {
                    if (Object.keys(cell.getMeta()).includes('cornerType')) {
                      if (item !== 'index') {
                        return {
                          fill: '#5d6cf5',
                        };
                      } else {
                        return {
                          fill: '#fff',
                        };
                      }
                    } else {
                      if (item === 'index') {
                        return {
                          fill:
                            cell.getMeta().rowIndex % 2 === 0
                              ? '#f6fbf5'
                              : '#fff',
                        };
                      }
                    }
                  },
                } as BackgroundCondition;
              }),
            ],
            text: [
              ...fields.rows.map((item) => {
                return {
                  field: item,
                  mapping: (_, __, cell) => {
                    if (Object.keys(cell.getMeta()).includes('cornerType')) {
                      if (item !== 'index') {
                        return {
                          fill: '#fff',
                        };
                      }
                    }
                  },
                } as TextCondition;
              }),
            ],
          },
        },
      );
      sheet.setTheme({
        rowCell: {
          text: {
            textAlign: 'center',
          },
          cell: {
            horizontalBorderColor: '#e5e7eb',
            verticalBorderColor: '#e5e7eb',
            interactionState: {
              // selected: {
              //   backgroundColor: 'transparent',
              //   borderColor: 'transparent',
              // },
            },
          },
          bolderText: {
            fontWeight: 'normal',
            textAlign: 'center',
          },
        },
        dataCell: {
          text: {
            textAlign: 'center',
          },
          cell: {
            horizontalBorderColor: '#e5e7eb',
            verticalBorderColor: '#e5e7eb',
            interactionState: {
              // selected: {
              //   backgroundColor: 'transparent',
              //   borderColor: 'transparent',
              // },
            },
          },
        },
        cornerCell: {
          text: {
            textAlign: 'center',
          },
          cell: {
            horizontalBorderColor: '#e5e7eb',
            verticalBorderColor: '#e5e7eb',
            interactionState: {
              // selected: {
              //   backgroundColor: 'transparent',
              //   borderColor: 'transparent',
              // },
            },
          },
        },
        colCell: {
          text: {
            textAlign: 'center',
          },
          cell: {
            horizontalBorderColor: '#e5e7eb',
            verticalBorderColor: '#e5e7eb',
            interactionState: {
              // selected: {
              //   backgroundColor: 'transparent',
              //   borderColor: 'transparent',
              // },
            },
          },
        },
      });
      sheet.on(S2Event.LAYOUT_RESIZE_ROW_WIDTH, (event) => {
        const widthChange = event.info.resizedWidth
          ? event.info.resizedWidth - event.info.width
          : 0;
        let index =
          fields.rows.findIndex((item) => item === event.info.meta.field) + 1;
        for (index; index < fields.rows.length; index++) {
          if (fields.rows[index] === 'index') {
            continue;
          }
          rowPos[fields.rows[index]] += widthChange;
          cornerPos[fields.rows[index]] += widthChange;
        }
      });
      sheet.on(S2Event.DATA_CELL_CLICK, (event) => {
        const cell = sheet.getCell(event.target);
        if (!cell) {
          return;
        }
        if (isMultiVersion) {
          message.warning(t('多版本模式下，无法编辑单元格'));
          return;
        }
        const disabledLevel = addtionalData[Number(cell.getMeta().valueField)][
          cell.getMeta().rowIndex
        ]
          ? addtionalData[Number(cell.getMeta().valueField)][
              cell.getMeta().rowIndex
            ].disabledLevel
          : 0;
        if (disabledLevel !== 2) {
          return;
        }
        const x = cell.getRenderBounds().getMin()[0];
        const y = cell.getRenderBounds().getMin()[1];
        setTimeout(() => {
          showInput(
            x,
            y,
            cell.getMeta().width,
            cell.getMeta().height,
            cell.getMeta().valueField,
            cell.getMeta().rowIndex,
          );
          setInputValue(
            cell.getMeta().fieldValue ? cell.getMeta().fieldValue + '' : '',
          );
        }, 4);
      });
      sheet.on(S2Event.GLOBAL_CLICK, () => {
        hideInput();
      });
      sheet.on(S2Event.GLOBAL_SCROLL, (event) => {
        setScroll({
          x: event.scrollX,
          y: event.scrollY,
        });
      });
      sheet.on(S2Event.LAYOUT_AFTER_RENDER, () => {
        sheet.interaction.scrollTo({
          offsetX: {
            value: scroll.x,
            animate: false,
          },
          offsetY: {
            value: scroll.y,
            animate: false,
          },
        });
      });
      sheet.on(S2Event.GLOBAL_COPIED, (event) => {
        if (event && event.length > 0) {
          setTimeout(() => {
            copyToClipboard(event[0].content.split('\r\n').join(','));
          }, 100);
        }
      });

      const debounceRender = debounce(async (width: number, height: number) => {
        sheet.changeSheetSize(width, height);
        await sheet.render(false); // 不重新加载数据
      }, 200);

      const resizeObserver = new ResizeObserver(([entry] = []) => {
        const [size] = entry.borderBoxSize || [];
        debounceRender(size.inlineSize, size.blockSize);
      });

      resizeObserver.observe(sheetContainer);

      sheet.render();
    }
  };

  const handlePaste = (e: any) => {
    const pasteData = e.clipboardData
      .getData('text')
      .split('\t')
      .filter((item: any) => isNumber(item));
    setInputValue(pasteData[0]);
    let rowIndex = editingCell ? editingCell.rowIndex : 0;
    let fieldIndex = editingCell
      ? fields.values.findIndex((item) => item === editingCell.field)
      : 0;
    for (let i = 0; i < pasteData.length; i++) {
      if (
        addtionalData[fields.values[fieldIndex]][rowIndex].disabledLevel === 2
      ) {
        if (
          oringinData[rowIndex][fields.values[fieldIndex]] + '' !==
          pasteData[i] + ''
        ) {
          modifyCells.push({
            rowIndex: rowIndex,
            field: fields.values[fieldIndex],
          });
        } else {
          if (
            modifyCells.some(
              (item) =>
                item.rowIndex === rowIndex &&
                item.field === fields.values[fieldIndex],
            )
          ) {
            modifyCells.splice(
              modifyCells.findIndex(
                (item) =>
                  item.rowIndex === rowIndex &&
                  item.field === fields.values[fieldIndex],
              ),
              1,
            );
          }
        }
        setModifyCells([...modifyCells]);
        data[rowIndex][fields.values[fieldIndex]] = pasteData[i];
        setData([...data]);
        fieldIndex++;
        if (fieldIndex >= fields.values.length) {
          rowIndex++;
          fieldIndex = 0;
        }
        if (rowIndex >= oringinData.length) {
          return;
        }
      }
    }
  };

  return (
    <div
      ref={ref}
      className='sheet-container'
      style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden',
      }}>
      {inputShow ? (
        <Input
          autoFocus
          value={inputValue}
          style={{
            position: 'absolute',
            top: inputBaseInfo.y,
            left: inputBaseInfo.x,
            width: inputBaseInfo.width,
            height: inputBaseInfo.height,
            boxSizing: 'border-box',
            borderRadius: '0',
            textAlign: 'center',
          }}
          onChange={(e) => {
            setInputValue(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              hideInput();
            }
          }}
          onPaste={(e) => {
            e.preventDefault();
            handlePaste(e);
          }}
        />
      ) : (
        <></>
      )}
    </div>
  );
});

export default MySheetNew;
