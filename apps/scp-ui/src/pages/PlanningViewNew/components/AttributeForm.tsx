import React, { useEffect } from 'react';
import { Form, SelectProps, Flex } from 'antd';
import { DraggableSelect } from './DraggableSelect';
import FilterModal from './FilterModal';
import TimeSelect from './TimeSelect';
import type { GetAttributesRes } from '@/api/PlanningView/model';
import _ from 'lodash';
import ajax from '@/api';

/**
 * @ description 主属性和特征值表单
 * */
interface AttributeFormProps {
  t: any;
  attributeOptions: SelectProps['options'];
  kfList: SelectProps['options'];
  attributeList: GetAttributesRes[];
  currentPaPvId: number | null;
  conditions: any;
  setConditions: any;
  setFilter: any;
}

const AttributeForm: React.FC<AttributeFormProps> = (props) => {
  const { t, attributeOptions, kfList, conditions, setConditions, setFilter } =
    props;

  const getAttributeList = async () => {
    try {
      const res = await ajax.getAllFilterConditionsByMenu({
        menuTag: 'COLLAB_VIEW',
      });
      if (res.code === 0) {
        const _attributeList = (_.get(res, 'data.list', []) || []).map(
          (item) => {
            return {
              value: item.conditionContent.field,
              label: item.conditionContent.label,
              type: item.conditionContent.type,
              id: item.id,
            };
          },
        );
        setConditions(_attributeList);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getAttributeList();
  }, []);

  return (
    <div
      style={{
        display: 'ruby',
      }}>
      <Form.Item noStyle>
        <Flex gap={'10px'}>
          <Form.Item label={t('属性')} name='md' rules={[{ required: true }]}>
            <DraggableSelect
              // suffix={<SortIcon />}
              style={{ width: 250 }}
              options={attributeOptions}
              maxTagCount={'responsive'}
            />
          </Form.Item>
          <FilterModal attributeList={conditions} setFilter={setFilter} />
        </Flex>
      </Form.Item>
      <Form.Item label={t('关键指标')} name='kf' rules={[{ required: true }]}>
        <DraggableSelect
          style={{ width: 250 }}
          options={kfList}
          // suffix={<PencileIcon />}
          maxTagCount={'responsive'}
        />
      </Form.Item>
      <Form.Item
        label={t('时间')}
        rules={[{ required: true }]}
        style={{
          marginLeft: '10px',
          marginRight: '10px',
        }}>
        <TimeSelect maxTagCount={'responsive'} />
      </Form.Item>
    </div>
  );
};
export default AttributeForm;
