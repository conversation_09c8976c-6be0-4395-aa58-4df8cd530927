import React, { useState, useEffect, useCallback } from 'react';
import { Select, SelectProps } from 'antd';
import {
  DndContext,
  closestCenter,
  useSensor,
  useSensors,
  PointerSensor,
} from '@dnd-kit/core';
import { arrayMove, SortableContext } from '@dnd-kit/sortable';
import { useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Sortable item component
interface SortableItemProps extends React.PropsWithChildren {
  id: string;
  children: React.ReactNode;
  handleChange: (isSelect: boolean, value: string) => void;
  isSelect: boolean;
}

const SortableItem: React.FC<SortableItemProps> = React.memo(
  ({ id, children, handleChange, isSelect }) => {
    const { attributes, listeners, setNodeRef, transform, transition } =
      useSortable({ id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    return (
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        onClickCapture={() => handleChange(isSelect, id)}>
        {children}
      </div>
    );
  },
);

interface NewDraggableSelectProps extends SelectProps<any> {
  options: SelectProps<any>['options'];
  value?: SelectProps<any>['value'];
  onChange?: (value: any) => void;
}

export const DraggableSelect: React.FC<NewDraggableSelectProps> = React.memo(
  ({ options, value, onChange = () => {}, ...rest }) => {
    const [items, setItems] = useState<any[]>([]);
    const [unSelectedItems, setUnSelectedItems] = useState<any[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');

    const sensors = useSensors(
      useSensor(PointerSensor, {
        activationConstraint: {
          distance: 5,
          // delay: 250,
        },
      }),
    );

    // Update state when options or value changes
    useEffect(() => {
      //TODO: 此处数据顺序有问题，要进行修改！！！
      // const selectedItems =
      //   options?.filter((option) => value?.includes(option.value)) || [];
      const selectedItems =
        value?.map((item: any) =>
          options?.find((option) => option.value === item),
        ) || [];
      options?.filter((option) => value?.includes(option.value)) || [];

      const unselectedItems =
        options?.filter((option) => !value?.includes(option.value)) || [];
      setItems(selectedItems);
      setUnSelectedItems(unselectedItems);
    }, [options, value]);

    const allItems = [...items, ...unSelectedItems];

    const handleDragEnd = useCallback(
      (event: any) => {
        const { active, over } = event;
        if (active.id !== over.id) {
          const isActiveInItems = items.some(
            (item) => item?.value === active.id,
          );
          const isOverInItems = items.some((item) => item?.value === over.id);
          if (isActiveInItems && isOverInItems) {
            setItems((prevItems) => {
              const oldIndex = prevItems.findIndex(
                (item) => item?.value === active.id,
              );
              const newIndex = prevItems.findIndex(
                (item) => item?.value === over.id,
              );
              const newItems = arrayMove(prevItems, oldIndex, newIndex);
              onChange(newItems.map((item) => item?.value));
              return newItems;
            });
          }
        }
      },
      [items, onChange],
    );

    const handleChange = useCallback(
      (isSelect: boolean, value: any) => {
        if (isSelect) {
          const selectedOption = options?.find((item) => item?.value === value);
          if (selectedOption) {
            setItems((prevItems) => {
              const newItems = [...prevItems, selectedOption];
              onChange(newItems.map((item) => item?.value));
              return newItems;
            });
            setUnSelectedItems((prevItems) =>
              prevItems.filter((item) => item?.value !== value),
            );
          }
        } else {
          const deselectedOption = options?.find(
            (item) => item.value === value,
          );
          if (deselectedOption) {
            setItems((prevItems) =>
              prevItems.filter((item) => item?.value !== value),
            );
            setUnSelectedItems((prevItems) => [...prevItems, deselectedOption]);
            onChange(
              items
                .filter((item) => item?.value !== value)
                .map((item) => item?.value),
            );
          }
        }
      },
      [items, options, onChange],
    );

    return (
      <DndContext
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        sensors={sensors}>
        <SortableContext
          items={allItems.map((item) => item?.value)}
          strategy={verticalListSortingStrategy}>
          <Select
            {...rest}
            style={{ width: 300 }}
            mode='multiple'
            onDeselect={(value) => {
              if (value === undefined) {
                const filteredItems = [...items].filter(
                  (item) => item?.value !== undefined,
                );
                setItems(filteredItems);
                onChange(filteredItems);
                return;
              }
              const isSelectId =
                items.find((item) => item?.label === value).value || -1;
              if (isSelectId === -1) return;
              handleChange(false, isSelectId);
            }}
            value={items.map((item) => item?.label)}
            onSearch={(input) => setSearchValue(input)}
            searchValue={searchValue}
            dropdownRender={() => (
              <div className='max-h-[200px] overflow-auto'>
                <div>
                  {items
                    .filter((item) =>
                      (item?.label as string)
                        ?.toLowerCase()
                        .includes(searchValue.toLowerCase()),
                    )
                    .map((item) => (
                      <SortableItem
                        key={item?.value}
                        id={item?.value}
                        isSelect={false}
                        handleChange={handleChange}>
                        <div key={item?.value} className='bg-sky-50'>
                          {item?.label}
                        </div>
                      </SortableItem>
                    ))}
                </div>
                <div>
                  {unSelectedItems
                    .filter((item) =>
                      (item?.label as string)
                        ?.toLowerCase()
                        .includes(searchValue.toLowerCase()),
                    )
                    .map((item) => (
                      <SortableItem
                        key={item?.value}
                        id={item?.value}
                        isSelect={true}
                        handleChange={handleChange}>
                        <div key={item?.value}>{item?.label}</div>
                      </SortableItem>
                    ))}
                </div>
              </div>
            )}
          />
        </SortableContext>
      </DndContext>
    );
  },
);
