import PlanningView from '@/api/PlanningView';
import {
  RunRAGFlowAttributeReq,
  RunRAGFlowAttributeRes,
} from '@/api/PlanningView/model';

interface Conversation {
  id: number;
  createdAt: number;
  createdBy: number;
  updatedAt: number;
  updatedBy: number;
  userId: number;
  title: string;
  convAppBuilderId: string;
  convType: number;
}

interface ConversationGroup {
  label: string;
  convList: Conversation[] | null;
}

export const getConversations = async (
  data: any,
): Promise<ConversationGroup[]> => {
  try {
    const res = await PlanningView.getUserDialogList(data);
    if (!res.code) {
      return res.data.list;
    } else {
    }
  } catch (err) {
    console.error(err);
  }
  return [];
};

//TODO: 错误处理
export const runRAGFlowChatConfigApiForAttribute = async (
  data: RunRAGFlowAttributeReq,
): Promise<RunRAGFlowAttributeRes['result'] | null> => {
  try {
    const res = await PlanningView.runRAGFlowChatConfigApiForAttribute(data);
    if (!res.code) {
      return res.data.result || null;
    } else throw new Error(res.msg);
  } catch (err) {
    console.error(err);
  }
  return null;
};

interface Message {
  id: number;
  convAppBuilderId: string;
  senderId: number;
  messageText: string;
  answerUuid: string;
}

export const getAiMessagees = async (data: {
  convAppBuilderId: string;
}): Promise<Message[]> => {
  try {
    const res = await PlanningView.getAiMessagees(data);
    if (!res.code) {
      return res.data.list;
    }
  } catch (err) {
    console.error(err);
  }
  return [];
};
