import {
  Bubble,
  Conversations,
  Sender,
  useXAgent,
  useXChat,
} from '@ant-design/x';
import React, { useEffect, useState, useRef } from 'react';
import { Button, type GetProp, theme } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { timeMap, DateType } from '../TimeSelect';
import { RenderAttribute } from './components';
import {
  getConversations,
  runRAGFlowChatConfigApiForAttribute,
  getAiMessagees,
} from './api';
import { ConversationsProps } from '@ant-design/x/es/conversations';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';

type AgentUserMessage = {
  role: 'user';
  content: string;
};

type AgentAiMessage = {
  role: 'ai';
  content: string;
};

type AgentErrorMessage = {
  role: 'error';
  content: string;
};

type AgentMessage = AgentAiMessage | AgentUserMessage | AgentErrorMessage;

type BubbleMessage = {
  role: string;
};

interface AttributeInfo {
  keyFigure: number[];
  noRootAttribute: number[];
  rootAttribute: number[];
}

interface ConfigurationType {
  keyFigure: string[];
  from: string;
  to: string;
  dimensionality: string;
  conversationId: string;
  attributeInfoMap: {
    [key: string]: AttributeInfo;
  };
  relationMap: Record<string, unknown>;
}

interface FormDataType {
  kf: number[];
  md: number[];
  time: {
    dateType: number;
    rollingType: number;
    dateRange: [string, string];
  }[];
}

//TODO: 当首页进行搜索之后，下面的select框会发生重置

const BottomChat = ({ form }: { form: any }) => {
  /**-----------------------------const -------------------------- */
  const { token } = theme.useToken();
  const [conversationList, setConversationList] = useState<
    GetProp<ConversationsProps, 'items'>
  >([]);
  const user = useSelector((state: any) => state.login.user);

  const [currentConversationId, setCurrentConversationId] =
    useState<string>('');
  const [content, setContent] = useState<string>('');

  const conversationIdRef = useRef('');

  const [agent] = useXAgent<AgentMessage>({
    request: async ({ message }, { onSuccess }) => {
      const result = await runRAGFlowChatConfigApiForAttribute({
        conversationId: conversationIdRef.current,
        query: message?.content || '',
        stream: false,
      });
      if (result === null) {
        onSuccess({
          role: 'error',
          content: t('服务器发生错误'),
        });
      }

      if (result !== null) {
        onSuccess({
          role: 'ai',
          content: JSON.stringify(result),
        });

        if (conversationIdRef.current === '') {
          getHistoryList();
        }
        setCurrentConversationId(result.conversationId);
      }
    },
  });

  const { onRequest, parsedMessages, setMessages } = useXChat<
    AgentMessage,
    BubbleMessage
  >({
    agent,
    requestPlaceholder: {
      role: 'ai',
      content: 'Waiting...',
    },
  });

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      avatar: { icon: <UserOutlined />, style: { background: '#fde3cf' } },
      //   typing: { step: 5, interval: 20 },
      style: {
        maxWidth: 600,
      },
      messageRender: renderMessageContent,
    },
    user: {
      placement: 'end',
      avatar: {
        icon: <img src={user.headerImg} alt='userHeaderImg' />,
        style: { background: '#87d068' },
      },
      messageRender: (message) => message,
    },
    error: {
      placement: 'start',
      avatar: { icon: <UserOutlined />, style: { background: '#fde3cf' } },
      style: {
        maxWidth: 600,
      },
      messageRender: renderErrorMessageContent,
    },
  };

  const style = {
    width: 256,
    background: token.colorBgContainer,
    borderRadius: token.borderRadius,
  };
  /**-----------------------------const end-------------------------- */

  /*--------------------------function----------------------------------*/

  //TODO: 停止生成的逻辑进行编写。

  /**
   * @description 增加新对话处理逻辑
   */
  function addNewMessage() {
    setCurrentConversationId('');
    setMessages([]);
  }

  /**
   * @description 处理conversion 点击事件
   */
  async function showHistoryChat(convAppBuilderId: string) {
    const list = await getAiMessagees({ convAppBuilderId });
    const _currentMessages: AgentMessage[] = list.map((item) => {
      return {
        role: item.senderId === 0 ? 'ai' : 'user',
        content: item.messageText,
      };
    });
    setMessages(
      _currentMessages.map((message, index) => ({
        id: `${convAppBuilderId}-${index}`,
        message: message,
        status: 'success',
      })),
    );
  }

  /**
   * @description 处理显示内容
   */

  function renderMessageContent(messgae: string) {
    try {
      const obj: ConfigurationType = JSON.parse(messgae) || null;
      if (obj == null) return <div>{t('返回格式错误')}</div>;
      return <MessageContent form={form} obj={obj} />;
    } catch (error) {
      return messgae;
    }
  }

  /**
   * @description 处理错误展示内容
   */
  function renderErrorMessageContent(message: string) {
    return <div className='text-red-400'>{message}</div>;
  }

  /**
   * @description 获取对话的list
   */

  function getHistoryList() {
    getConversations({ convType: '1' }).then((res) => {
      const _conversationList: GetProp<ConversationsProps, 'items'> = [];
      res.map((item) => {
        item.convList?.forEach((conv) => {
          _conversationList.push({
            key: conv.convAppBuilderId,
            label: conv.title,
            group: item.label,
          });
        });
      });
      setConversationList(_conversationList);
      //   _conversationList.length >= 1
      //     ? setCurrentConversationId(_conversationList[0].key)
      //     : setCurrentConversationId('');
    });
  }

  /*-----------------------------end function--------------------------*/

  useEffect(() => {
    getHistoryList();
  }, []);

  useEffect(() => {
    conversationIdRef.current = currentConversationId;

    showHistoryChat(currentConversationId);
  }, [currentConversationId]);

  return (
    <div style={{ display: 'flex', height: '66%' }}>
      <div
        className='overflow-auto'
        style={{ borderRight: `1px solid ${token.colorBorder}` }}>
        <Button className='ml-4' type='primary' onClick={addNewMessage}>
          {t('新增对话')}
        </Button>
        <Conversations
          items={conversationList}
          activeKey={currentConversationId}
          style={style}
          onActiveChange={(convAppBuilderId) => {
            setCurrentConversationId(convAppBuilderId);
          }}
          groupable
        />
      </div>
      <div
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          padding: '16px',
        }}>
        <div style={{ flex: 1, overflow: 'auto' }}>
          <Bubble.List
            roles={roles}
            items={parsedMessages.map(({ id, message, status }) => {
              return {
                key: id,
                loading: status === 'loading',
                ...message,
              };
            })}
          />
        </div>
        <div style={{ marginTop: '16px' }}>
          <Sender
            loading={agent.isRequesting()}
            value={content}
            onChange={setContent}
            onSubmit={(content) => {
              onRequest({
                role: 'user',
                content: content,
              });
              setContent('');
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default BottomChat;

function MessageContent({ obj, form }: any) {
  const [formData, setFormData] = useState<FormDataType>({
    kf: [],
    md: [],
    time: [],
  });

  // 创建反向映射
  const reverseTimeMap = new Map<DateType, number>(
    Array.from(timeMap.entries()).map(([key, value]) => [value, key]),
  );

  // 获取函数
  const getTimeTypeByValue = (value: DateType): number | undefined => {
    return reverseTimeMap.get(value);
  };
  function onAdopt() {
    form.setFieldsValue({
      kf: formData.kf,
      md: formData.md,
      time: [
        {
          dateType: getTimeTypeByValue(obj.dimensionality),
          rollingType: 0,
          dateRange: [dayjs(obj.from), dayjs(obj.to)],
        },
      ],
    });
  }
  return (
    <div className='flex flex-col gap-2'>
      <div>{t('AI辅助的计划视图配置：')}</div>
      <div>
        <span className='text-slate-400'></span>
        {/* <span>{renderAttributeInfo(obj.attributeInfoMap)}</span> */}
        <RenderAttribute
          attributeInfoMap={obj.attributeInfoMap}
          onChange={(kf, ab) => {
            setFormData({
              ...formData,
              kf: kf,
              md: ab,
            });
          }}
        />
      </div>
      <div id='time'>
        <span className='text-slate-500'>{t('时间范围')}：</span>
        <span>{`${dayjs(obj.from).format('YYYY/MM/DD')} - ${dayjs(obj.to).format('YYYY/MM/DD')}`}</span>
      </div>
      <div>
        <span className='text-slate-500'>{t('时间颗粒度')}：</span>
        <span>{obj.dimensionality}</span>
      </div>
      <div>
        <Button
          onClick={() => {
            onAdopt();
          }}>
          {t('使用')}
        </Button>
      </div>
    </div>
  );
}
