import { SelectProps } from 'antd';
import { DraggableSelect } from '../../../DraggableSelect';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVoerkaI18n } from '@voerkai18n/react';
interface AttributeInfo {
  keyFigure: number[];
  noRootAttribute: number[];
  rootAttribute: number[];
}

interface ConfigurationType {
  keyFigure: string[];
  from: string;
  to: string;
  dimensionality: string;
  conversationId: string;
  attributeInfoMap: {
    [key: string]: AttributeInfo;
  };
  relationMap: Record<string, unknown>;
}

interface RenderAttributeProps {
  attributeInfoMap: ConfigurationType['attributeInfoMap'];
  onChange: (kfList: number[], abList: number[]) => void;
}

export default function RenderAttribute({
  attributeInfoMap,
  onChange,
}: RenderAttributeProps) {
  const kfList = useSelector((state: any) => state.planningView.kfList);
  const abList = useSelector((state: any) => state.planningView.attributeList);

  const { t } = useVoerkaI18n();

  const [keyFigureList, setKeyFigureList] = useState<SelectProps['options']>(
    [],
  );
  const [rootAttributeList, setRootAttributeList] = useState<
    SelectProps['options']
  >([]);
  const [nonRootAttributeList, setNonRootAttributeList] = useState<
    SelectProps['options']
  >([]);

  const [kf, setKf] = useState<number[]>([]);
  const [rootAttr, setRootAttr] = useState<number[]>([]);
  const [nonRootAttr, setNonRootAttr] = useState<number[]>([]);

  const convertKf = (kf: number) => {
    return kfList?.find((item: any) => item.value === kf)?.label || '';
  };

  const convertMd = (md: number) => {
    return abList?.find((item: any) => item.value === md)?.label || '';
  };

  const hasCommonElements = (arr1: number[], arr2: number[]): boolean => {
    const set = new Set(arr1);
    return arr2.some((item) => set.has(item));
  };

  useEffect(() => {
    let _keyFigureList: number[] = [];
    if (attributeInfoMap === undefined || attributeInfoMap === null) {
      return;
    }
    Object.entries(attributeInfoMap).map(([_, value]) => {
      _keyFigureList = _keyFigureList.concat(value.keyFigure);
    });
    setKeyFigureList(
      _keyFigureList
        .filter((item) => convertKf(item) !== '')
        .map((item) => {
          return {
            value: item,
            label: convertKf(item),
          };
        }),
    );
    setKf(_keyFigureList.filter((item) => convertKf(item) !== ''));
  }, [attributeInfoMap]);

  useEffect(() => {
    setRootAttr([]);
    setNonRootAttr([]);
    let _nonRootAttributeList: number[] = [];
    let _rootAttributeList: number[] = [];
    if (attributeInfoMap === undefined || attributeInfoMap === null) {
      return;
    }
    Object.entries(attributeInfoMap).map(([_, value]) => {
      if (hasCommonElements(value.keyFigure, kf)) {
        _nonRootAttributeList = _nonRootAttributeList.concat(
          value.noRootAttribute,
        );
        _rootAttributeList = _rootAttributeList.concat(value.rootAttribute);
      }
    });
    setNonRootAttributeList(
      Array.from(new Set(_nonRootAttributeList))
        .filter((item) => convertMd(item) !== '')
        .map((item) => {
          return {
            value: item,
            label: convertMd(item),
          };
        }),
    );
    setRootAttributeList(
      Array.from(new Set(_rootAttributeList))
        .filter((item) => convertMd(item) !== '')
        .map((item) => {
          return {
            value: item,
            label: convertMd(item),
          };
        }),
    );
    setRootAttr(_rootAttributeList.filter((item) => convertMd(item) !== ''));
  }, [kf]);

  useEffect(() => {
    onChange(kf, [...rootAttr, ...nonRootAttr]);
  }, [kf, rootAttr, nonRootAttr]);

  type ItemType = {
    title: string;
    options: SelectProps['options'];
    onChange: (value: number[]) => void;
    value: number[];
  };

  const item: ItemType[] = [
    {
      title: t('关键指标：'),
      options: keyFigureList,
      onChange: (value) => setKf(value),
      value: kf,
    },
    {
      title: t('关键属性：'),
      options: rootAttributeList,
      onChange: (value) => setRootAttr(value),
      value: rootAttr,
    },
    {
      title: t('参考属性：'),
      options: nonRootAttributeList,
      onChange: (value) => setNonRootAttr(value),
      value: nonRootAttr,
    },
  ];

  const RenderItem = ({ title, options, onChange, value }: ItemType) => (
    <div className='flex flex-col gap-2'>
      <div className='text-slate-500'>{title}</div>
      <DraggableSelect options={options} onChange={onChange} value={value} />
    </div>
  );
  return <div>{item.map(RenderItem)}</div>;
}
