import * as GC from '@grapecity/spread-sheets';

GC.Spread.Sheets.LicenseKey =
  'cloud.scmify.com,959321789442376#B13lZTRd6NtxkaKNFOCFEThNVMlN6byVVVUpkekdVNiZGcHRFOUN4YwBnR6RUWOdUZ6Y7VPJDRWRmaOZmM4djWGBzU9RVQFVWSZV5VN3yRiJnQYBFVxV5bZtWUm9EcYt6V9FUNsR5d9AFdxNGRVFlU8wWTsVXZv3SYFZjNvJmamJDROxkNN5kV5QVe9F7RHFzRatSRSdFTVNTNxF7MFF5aN3yNV9WSylTOjZjNKRFeFJkVUtGM6kHSl5EN9UXUwtEWatmW7tSYyR6SNRXR9cESEFWdSdmar2ENityMONDTHRXZaVHWyp6bvJVNFF5MRJmSvl4bFljYiRVezsmTVlDdRpETtpmI0IyUiwiI5QkNyYTRBZjI0ICSiwCO5QDNygTOyEjM0IicfJye35XX3JyVUNkWiojIDJCLiYTMuYHITpEIkFWZyB7UiojIOJyebpjIkJHUiwiI8UTNwgDMgUjMyEzMyAjMiojI4J7QiwiI4IjMxQjMwIjI0ICc8VkIsISbvNmL9ZWatN6cuQWdvx6YiojIz5GRiwiI8+Y9sWY9QmZ0Jyp96uL9v6L0Jy11eeb9t6p9Iy117ib9A009iojIh94QiwiI6czMyQDN9gzNxIzM9UTOiojIklkIs4XXbpjInxmZiwSZzxWYmpjIyNHZisnOiwmbBJye0ICRiwiI34TUyJkTNljNiVXbzZGa5Q6U5QlM93ScGJEa6NjaSJWUMlFS9UEWFVnUCNUSZllT8J5cLRWUVRlaEhFURZFaVRHRq3WRFZFeTZnV7I4dtR6KhJ9c';
export interface CharSheetType {
  sheet: GC.Spread.Sheets.Worksheet;
  editorStyle: GC.Spread.Sheets.Style;
  returnData: uploadData | null;
  paPvId: number;
  head: colHeader[];
  rowItems: rowHeadTreeNode[];
  timeMap: Map<string, [number, number]>;
  renderHead: (headers: colHeader[]) => void;
  addContentSpan: (
    row: number,
    col: number,
    rowCount: number,
    colCount: number,
    color: string,
    title: string,
  ) => void;
  setRows: (rowItems: rowHeadTreeNode[], fields: string[]) => void;
  setPaPvId: (paPvId: number) => void;
  //临时数据转换方法
  // transformData: (data: tempData[]) => rowData[];
  listenChange: (
    style: GC.Spread.Sheets.Style,
    rowItems: rowData[],
    paPvId: number,
  ) => void;
  //清空监视器
  clearListener: () => void;

  clear: () => void;
}

export type rowHeadTreeNode = {
  id: number;
  key: number;
  name: string;
  field: string;
  label: string;
  color: string;
  disabled?: headDisabled;
  children: rowHeadTreeNode[] | null;
  keys: rowHeadKeys[] | null;
  values: rowHeadKeys[] | null;
};
type rowHeadKeys = {
  disabledLevel: number;
  key: number;
  name: string;
  values: rowHeadValue[] | null;
  itemValue: string;
};

type rowHeadValue = {
  periodId: number;
  tpLevel: number;
  date: string;
  value: number;
  disabledLevel: number;
};

export type colHeader = {
  name: string;
  width: number;
  isShowDateType?: string;
};

export type rowHead = {
  name: string;
  Field: string;
  color: string;
  key: number;
  id: number;
  disabled?: headDisabled;
};
export type headDisabled = {
  disableRow: number;
  level: number;
};

export type rowValue = {
  date: string;
  key: number;
  value: number;
};
export type rowData = {
  heads: rowHead[];
  keys: rowHead[];
  values: rowValue[];
};
//临时数据格式
export type tempData = {
  date: string;
  kfName_id: number;
  locno: string;
  matnr: string;
  ppSosId: string;
  quantity: number;
  sourceId: number;
  time: number;
};
//返回修改的数据格式
export type uploadData = {
  paPvId: number;
  addCooperateViews: uploadDataItem[];
};
export type uploadDataItem = {
  col: number;
  row: number;
  kfId: number;
  ids: { field: string; id: string }[];
  periodId: number;
  tpLevel: number;
  value: number;
};

export type SaveViewParams = {
  paPvId: number;
  groupType: string;
  addCooperateViews: uploadData[];
};
