import Icon from '@ant-design/icons';
import Sort from './Sort';
import Pencile from './Pencile';
import Filter from './Filter';
import ChartStar from './ChartStar';

const SortIcon = (props: any) => <Icon component={Sort} {...props} />;
const PencileIcon = (props: any) => <Icon component={Pencile} {...props} />;
const FilterIcon = (props: any) => <Icon component={Filter} {...props} />;
const ChartStarIcon = (props: any) => <Icon component={ChartStar} {...props} />;
export { SortIcon, PencileIcon, FilterIcon, ChartStarIcon };
