import React from 'react';
import { memo, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import UserSettingModal from './UserSettingModal';
import ShowButtonModal from './ShowButtonModal';
import UploadAvatar from '@/components/UploadAvatar';
import { Button } from 'antd';
import LeftArrow from '@/assets/LeftArrow.svg';
import { useVoerkaI18n } from '@voerkai18n/react';

const UserPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const token = useSelector((state: any) => state.login.token);
  const user = useSelector((state: any) => state.login.user);
  const [avatar, setAvatar] = useState<string | undefined>(user?.headerImg);
  const navigate = useNavigate();
  const { t } = useVoerkaI18n();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleUpload = async (imageUrl: string): Promise<boolean> => {
    let flag = false;
    // 这里可以添加将头像上传到服务器的逻辑
    if (imageUrl) {
      // Convert base64 to Blob
      const fetchBlob = async () => {
        try {
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          const file = new File([blob], 'avatar.png', { type: 'image/png' });
          // Prepare form data
          const formData = new FormData();
          formData.append('photo', file);

          // Upload to backend
          const res = await fetch('/api/file/uploadUserProfilePhoto', {
            method: 'POST',
            body: formData,
            headers: {
              'X-Token': token,
            },
          }).then((res) => res.json());

          if (res.code === 0) {
            flag = true;
          } else {
            console.error('Upload failed');
          }
        } catch (error) {
          console.error('Error:', error);
        }
      };
      await fetchBlob();
    }
    return new Promise((resolve) => {
      resolve(flag);
    });
  };

  const [userSettingModalVisible, setUserSettingModalVisible] =
    useState<boolean>(false);
  const [showButtonModalVisible, setShowButtonModalVisible] =
    useState<boolean>(false);

  const openUserSettingModal = () => {
    setUserSettingModalVisible(true);
  };
  const openShowButtonModal = () => {
    setShowButtonModalVisible(true);
  };

  useEffect(() => {
    console.log('user:', user);
    if (user) {
      setAvatar(user.headerImg);
    }
  }, [user]);

  const pareaInfo = localStorage.getItem('pareaInfo');

  return (
    <div className='container'>
      <section className='mb-8'>
        <div className='flex mb-4 items-center gap-4'>
          <div className='flex h-4 items-center'>
            <img src={LeftArrow}></img>
            <span
              onClick={() => navigate(-1)}
              className='cursor-pointer text-s1'
              style={{
                color: '#2F6BFF',
              }}>
              {t('返回')}
            </span>
          </div>
          <h2 className='text-xl font-semibold '>设置</h2>
        </div>

        <div className='bg-white p-4 rounded shadow'>
          <div className='flex items-center mb-[20px]'>
            <strong>头像：</strong>
            <img
              src={avatar}
              alt='User Avatar'
              className='w-12 h-12 rounded-full mr-4'
            />
            <Button onClick={handleOpenModal}>上传</Button>
            <UploadAvatar
              isOpen={isModalOpen}
              onClose={handleCloseModal}
              onUpload={handleUpload}
            />
          </div>
          <p>
            <strong>姓名：</strong>
            {user?.nickName}
          </p>
          <p>
            <strong>邮箱：</strong>
            {user?.email}
          </p>
          <p>
            <strong>公司：</strong>
            {user?.coCode}
          </p>
          <p>
            <strong>计划范围：</strong>
            {pareaInfo ? JSON.parse(pareaInfo).parea : ''}
          </p>
        </div>
      </section>

      <section className='mb-8'>
        <div className='bg-white p-4 rounded shadow'>
          <form></form>
          <span
            className='mr-4 text-blue-500 cursor-pointer'
            onClick={openShowButtonModal}>
            AI助手设置
          </span>
        </div>
      </section>

      <section className='mb-8'>
        <div className='bg-white p-4 rounded shadow'>
          <form></form>
          <span
            className='text-blue-500 cursor-pointer'
            onClick={openUserSettingModal}>
            修改密码
          </span>
        </div>
      </section>

      <UserSettingModal
        visible={userSettingModalVisible}
        close={() => setUserSettingModalVisible(false)}
      />
      <ShowButtonModal
        open={showButtonModalVisible}
        close={() => setShowButtonModalVisible(false)}
      />
    </div>
  );
};
const MemoUserPage = memo(UserPage);
export default MemoUserPage;
