import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import MemoUserPage from '../index';
import store from '@/store';

describe('UserPage', () => {
  it('should render user information', async () => {
    render(
      <Provider store={store}>
        <Router>
          <MemoUserPage />
        </Router>
      </Provider>,
    );

    await waitFor(() => {
      expect(screen.findAllByText('个人信息')).toBeDefined();
      expect(screen.findAllByText('偏好设置')).toBeDefined();
      expect(screen.findAllByText('安全设置')).toBeDefined();
    });
  });

  it('should open and close the upload avatar modal', async () => {
    render(
      <Provider store={store}>
        <Router>
          <MemoUserPage />
        </Router>
      </Provider>,
    );

    fireEvent.click(screen.getByText('上 传'));

    await waitFor(() => {
      expect(screen.findAllByText('上传头像')).toBeDefined();
    });

    fireEvent.click(screen.getByText('取 消'));

    await waitFor(() => {
      expect(screen.queryByText('上传头像')).not.toBeInTheDocument();
    });
  });

  it('should open and close the user setting modal', async () => {
    render(
      <Provider store={store}>
        <Router>
          <MemoUserPage />
        </Router>
      </Provider>,
    );

    fireEvent.click(
      screen.getByText((content, element) => {
        return (
          element?.tagName.toLowerCase() === 'span' && content === '安全设置'
        );
      }),
    );

    await waitFor(() => {
      expect(screen.getByText('修改密码')).toBeInTheDocument();
    });
  });

  // it('should open and close the show button modal', async () => {
  //   render(
  //     <Provider store={store}>
  //       <Router>
  //         <MemoUserPage />
  //       </Router>
  //     </Provider>,
  //   );

  //   fireEvent.click(screen.getByText('ai助手'));

  //   await waitFor(() => {
  //     expect(screen.getByText('ai助手')).toBeInTheDocument();
  //   });

  //   fireEvent.click(screen.getByText('取消'));

  //   await waitFor(() => {
  //     expect(screen.queryByText('ai助手')).not.toBeInTheDocument();
  //   });
  // });
});
