import { Modal, Form, Input, Button, message } from 'antd';
import ajax from '@/api';
import { memo } from 'react';
type UserSettingModalProps = {
  visible: boolean;
  close: () => void;
};
type ChangePasswordParams = {
  password: string;
  newPassword: string;
  newPasswordAgain: string;
};
const UserSettingModal: React.FC<UserSettingModalProps> = ({
  visible,
  close,
}) => {
  const changePassword = async (params: ChangePasswordParams) => {
    const { password, newPassword } = params;
    const res = await ajax.changePassword({
      password,
      newPassword,
    });
    if (res.code === 0) {
      message.success('修改成功');
      close();
    }
  };
  const onFinish = (values: ChangePasswordParams) => {
    if (values.newPassword !== values.newPasswordAgain) {
      return;
    }
    changePassword(values);
  };
  return (
    <Modal title='修改密码' open={visible} onCancel={close} footer={false}>
      <Form onFinish={onFinish} labelCol={{ span: 8 }}>
        <Form.Item
          label='请输入密码'
          name='password'
          rules={[{ required: true }]}>
          <Input></Input>
        </Form.Item>
        <Form.Item
          label='请输入新密码'
          name='newPassword'
          rules={[{ required: true }]}>
          <Input type='password'></Input>
        </Form.Item>
        <Form.Item
          label='请再次输入新密码'
          name='newPasswordAgain'
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error('The new password that you entered do not match!'),
                );
              },
            }),
          ]}>
          <Input type='password'></Input>
        </Form.Item>
        <Form.Item>
          <Button type='primary' htmlType='submit'>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};
const MemoUserSettingModal = memo(UserSettingModal);
export default MemoUserSettingModal;
