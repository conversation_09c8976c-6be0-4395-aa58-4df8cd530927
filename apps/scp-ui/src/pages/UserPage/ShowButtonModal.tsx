import { useState } from 'react';
import { App, Modal, Input, Form, Button } from 'antd';
import { useSelector } from 'react-redux';
import ajax from '@/api';

interface ShowButtonModalProps {
  open: boolean;
  close: () => void;
}
const userCode = '7758258';
const ShowButtonModal: React.FC<ShowButtonModalProps> = ({ open, close }) => {
  const { message } = App.useApp();
  const [inputValue, setInputValue] = useState('');
  const isShowChartButton = useSelector(
    (state: any) => state.layout.isShowChartButton,
  );

  const onFinish = async (values: any) => {
    try {
      const { code } = values;
      if (code === userCode) {
        const res = await ajax.changeChartButton();
        if (res.code === 0) {
          console.log('res', res);
        }
      } else {
        message.error('请输入正确的个人信息代码');
      }
    } catch (error) {
      console.error(error);
    } finally {
      close();
    }
  };
  return (
    <App>
      <Modal open={open} onCancel={close} footer={false}>
        <Form onFinish={onFinish}>
          <Form.Item label='请输入个人信息代码:' name='code'>
            <Input
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
              }}
            />
          </Form.Item>
          <Button htmlType='submit'>
            {isShowChartButton ? '关闭ai助手' : '开启ai助手'}
          </Button>
        </Form>
      </Modal>
    </App>
  );
};

export default ShowButtonModal;
