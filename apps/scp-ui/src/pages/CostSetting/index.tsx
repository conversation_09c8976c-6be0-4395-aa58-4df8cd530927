import React, { useEffect, useState } from 'react';
import { DeleteButton } from '@repo/ui';
import {
  Table,
  Button,
  Descriptions,
  Tabs,
  Modal,
  Form,
  Input,
  App,
} from 'antd';
import { motion } from 'framer-motion';
import { LeftOutlined, PlusOutlined } from '@ant-design/icons';
import ajax from '@/api';
import _ from 'lodash';
import DetailContent from './components/DetailContent';
import { useVoerkaI18n } from '@voerkai18n/react';

const columns = (
  showDetail: (record: any) => void,
  deleteProfile: (profileUuid: string) => void,
  t: any,
) => [
  { title: t('名字'), dataIndex: 'name', key: 'name' },
  {
    title: t('操作'),
    key: 'action',
    render: (_: any, record: any) => (
      <>
        <Button onClick={() => showDetail(record)}>{t('配置')}</Button>
        <DeleteButton
          deleteTitle={t('确认删除这个配置吗?')}
          onClick={() => deleteProfile(record.profileUuid)}
          style={{ marginLeft: 8 }}
        />
      </>
    ),
  },
];

export default function CostSetting() {
  const { t } = useVoerkaI18n();
  const { message } = App.useApp();
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [profileList, setProfileList] = useState<any[]>([]);
  const [tagList, setTagList] = useState<any[]>([]);
  const [currentFileUuid, setCurrentProfileUuid] = useState<string>('');
  const showDetail = async (record: any) => {
    await getTagListByProfile(record.profileUuid);
    setSelectedItem(record);
    setCurrentProfileUuid(record.profileUuid);
  };

  const getPlanProfileList = async () => {
    try {
      const res = await ajax.getPlanProfileList();
      const list = _.get(res, 'data.list', []) || [];
      setProfileList(list);
    } catch (e) {
      console.error(e);
    }
  };

  const hideDetail = () => {
    setSelectedItem(null);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    const values = form.getFieldsValue();
    try {
      const res = await ajax.createPlanProfile(values);
      if (!res.code) {
        message.success(t('创建成功'));
        setIsModalVisible(false);
        form.resetFields();
        getPlanProfileList();
      } else {
        message.error(res.msg);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const deleteProfile = async (profileUuid: string) => {
    try {
      const res = await ajax.deletePlanProfile({ profileUuid });
      if (!res.code) {
        message.success(t('删除成功'));
        getPlanProfileList();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * getTagListByProfile
   */
  const getTagListByProfile = async (profileUuid: string) => {
    try {
      const res = await ajax.getTagListByProfile({ profileUuid });
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setTagList(list);
      }
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    getPlanProfileList();
  }, []);

  return (
    <div className='w-full h-[calc(100vh-138px)] rounded-2xl overflow-auto'>
      <div className=' flex h-full'>
        <motion.div
          className='flex-shrink-0 border-r border-gray-100 bg-white'
          animate={{ width: selectedItem ? '20%' : '100%' }}
          transition={{ duration: 0.3 }}>
          {selectedItem ? (
            <>
              <div className='flex items-center my-4 bg-white'>
                <LeftOutlined
                  onClick={hideDetail}
                  style={{
                    fontSize: '24px',
                    cursor: 'pointer',
                    marginRight: '8px',
                  }}
                />
                <span>{t('选择的信息')}</span>
              </div>
              <Descriptions bordered column={1}>
                <Descriptions.Item label={t('名称')}>
                  {selectedItem.name}
                </Descriptions.Item>
                <Descriptions.Item label={t('描述')}>
                  {selectedItem.description}
                </Descriptions.Item>
              </Descriptions>
            </>
          ) : (
            <>
              <Table
                bordered
                title={() => (
                  <div className='flex justify-between'>
                    <span className='text-h3'>{t('计划参数')}</span>
                    <Button
                      type='primary'
                      icon={<PlusOutlined />}
                      onClick={showModal}>
                      {t('新增计划参数')}
                    </Button>
                  </div>
                )}
                dataSource={profileList}
                columns={columns(showDetail, deleteProfile, t)}
                pagination={false}
              />
            </>
          )}
        </motion.div>
        {selectedItem && (
          <motion.div
            className='flex-grow p-4 border-l border-gray-100 h-full bg-white'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}>
            <Tabs
              key={currentFileUuid}
              items={tagList.map((item) => {
                return {
                  key: item.tagUuid,
                  label: item.label,
                  children: (
                    <DetailContent
                      tagUuid={item.tagUuid}
                      profileUuid={currentFileUuid}
                    />
                  ),
                };
              })}
            />
          </motion.div>
        )}
        <Modal
          title={t('新增')}
          open={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}>
          <Form form={form} layout='vertical'>
            <Form.Item
              name='name'
              label={t('名称')}
              rules={[{ required: true, message: t('请输入名称') }]}>
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
}
