import { useEffect, useState } from 'react';
import { But<PERSON>, App, Popconfirm } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import { useNavigate } from 'react-router-dom';
import { JobTemplatePage as JobTemplateComponent } from '@repo/ui';
import { type FormItemType } from './types';
import ajax from '@/api';
import _ from 'lodash';
import type { TableColumnsType } from 'antd';

interface DataType {
  id: React.Key;
  templateName: string;
  templateUuid: string;
  stepsNum: number;
  isOrigin: boolean;
  isSystem: boolean;
  isSystemName: string;
  sourceCode: number;
  sourceName: string;
}

const JobTemplatePage = () => {
  const { message } = App.useApp();
  const { t } = useVoerkaI18n();
  const navigate = useNavigate();
  const [tableData, setTableData] = useState<DataType[]>([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [shareUserList, setShareUserList] = useState([]);
  const [shareUserId, setShareUserId] = useState<number>(-1);
  const [shareTemplateUuid, setShareTemplateUuid] = useState<string>('');
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [pageData, setPageData] = useState({
    page: 1,
    total: 0,
    pageSize: 10,
  });

  const [currentShareTemplate, setCurrentShareTemplate] = useState({
    templateUuid: '',
    sharedTo: -1,
    templateName: '',
  });

  const closeShareModal = () => setShareModalOpen(false);
  const openShareModal = () => setShareModalOpen(true);

  const InputFormItems: FormItemType[] = [
    {
      type: 'text',
      field: 'name',
      label: t('模板名称'),
      required: false,
      value: '',
    },
    {
      type: 'number',
      field: 'stepNums',
      label: t('步骤数量'),
      required: false,
      min: 0,
      max: 100,
    },
  ];

  const getUserListByCoId = async () => {
    try {
      const res = await ajax.getUserListByCoId();
      const list = _.get(res, 'data.list', []) || [];
      setShareUserList(
        list.map((item: any) => ({
          label: item.nickName,
          value: item.id,
        })),
      );
    } catch (error) {
      console.log(error);
    }
  };

  const getShareUserList = async (templateUuid: string) => {
    try {
      const result = await ajax.getJobTemplateShareableUsers({
        templateUuid: templateUuid,
      });
      const list = _.get(result, 'data.list', []) || [];
      setShareUserList(
        list.map((item: any) => ({
          label: item.nickName,
          value: item.id,
        })),
      );
    } catch (error) {
      console.log(error);
    }
  };

  const shareJobTemplateTo = async (templateUuid: string, sharedTo: number) => {
    try {
      const res = await ajax.shareJobTemplateTo({ templateUuid, sharedTo });
      if (res.code === 0) {
        message.success(t('分享成功'));
        closeShareModal();
      } else {
        message.error(t('分享失败'));
        console.log(res.msg);
      }
    } catch (error) {
      message.error(t('分享失败'));
      console.log(error);
    }
  };

  const deleteJobTemplate = async (templateUuid: string) => {
    try {
      const res = await ajax.deleteJobTemplate(templateUuid);
      if (res.code === 0) {
        message.success(t('删除成功'));
        getJobTemplateByUserUpdate(pageData);
      }
    } catch (error) {
      message.error(t('删除失败'));
      console.log(error);
    }
  };
  //判断这个id的模版是否可以进行编辑
  const isCanEdit = async (id: string) => {
    try {
      const res = await ajax.getJobTemplateByUserUpdate({});
      const list = _.get(res, 'data.list', []);
      const idList = list.map((item: any) => item.templateUuid);
      return !!idList.includes(id);
    } catch (error) {
      console.log(error);
    }
  };

  const columns: TableColumnsType<DataType> = [
    // { title: t('ID'), dataIndex: 'id', key: 'id', width: 80 },
    { title: t('模版名称'), dataIndex: 'templateName', key: 'templateName' },
    { title: t('步骤数量'), dataIndex: 'stepsNum', key: 'stepsNum' },
    {
      title: t('来源'),
      dataIndex: 'sourceName',
      key: 'sourceName',
      // render: (text) => (text ? 'global' : 'share'),
    },
    {
      title: t('系统内置'),
      dataIndex: 'isSystem',
      key: 'isSystem',
      render: (_, record) => record.isSystemName,
    },
    {
      title: t('操作'),
      align: 'center',
      key: 'operation',
      width: 300,
      render: (_: any, record: DataType) => (
        <div>
          <Button
            type={'link'}
            onClick={() => {
              isCanEdit(record.templateUuid).then((res) => {
                let type: string = 'view';
                if (res) {
                  type = 'edit';
                }
                navigate(
                  `/editJobTemplate?id=${record.templateUuid}&name=${record.templateName}&type=${type}&isCreated=${record.sourceCode === 4}`,
                );
              });
            }}>
            {t('查看')}
          </Button>
          <Button
            style={{
              display:
                record.sourceCode === 1 ||
                record.sourceCode === 2 ||
                record.sourceCode === 3
                  ? 'none'
                  : 'inline-block',
            }}
            type='link'
            onClick={() => {
              setShareTemplateUuid(record.templateUuid);
              openShareModal();
              setCurrentShareTemplate((prev) => ({
                ...prev,
                templateUuid: record.templateUuid,
                templateName: record.templateName,
              }));
              console.log(record);
              getShareUserList(record.templateUuid);
            }}>
            {t('分享')}
          </Button>
          <Popconfirm
            title={t('确认删除该模版？')}
            onConfirm={() => deleteJobTemplate(record.templateUuid)}
            okText={t('确认')}
            cancelText={t('取消')}>
            <Button
              type='link'
              danger
              style={{
                display:
                  record.sourceCode === 1 || record.sourceCode === 2
                    ? 'none'
                    : 'inline-block',
              }}>
              {t('删除')}
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  //获取可查看的jobTemplate
  const getJobTemplateByUserUpdate = async (data?: any) => {
    setTableLoading(true);
    try {
      const res = await ajax.getJobTemplateByUserOnlyRead(data);
      const list = _.get(res, 'data.list', []);
      setPageData((prev) => ({ ...prev, total: res.data.total || 0 }));
      setTableData(list);
      setTableLoading(false);
      console.log(list);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getJobTemplateByUserUpdate(pageData);
    getUserListByCoId();
  }, []);

  return (
    <JobTemplateComponent
      InputFormItems={InputFormItems}
      onFinish={(values: any) => {
        getJobTemplateByUserUpdate({
          templateName: values.name,
          steps: values.stepNums,
        });
      }}
      onClickNewTemplate={() => navigate(`/editJobTemplate?type=edit`)}
      mainTableData={{
        columns,
        dataSource: tableData,
        loading: tableLoading,
      }}
      pageData={{
        current: pageData.page,
        total: pageData.total,
        pageSize: pageData.pageSize,
        onPageChange: (page: number) => {
          setPageData((prev) => ({ ...prev, page }));
          getJobTemplateByUserUpdate({ ...pageData, page });
        },
      }}
      modalData={{
        title: t('共享模版') + `：${currentShareTemplate.templateName}`,
        open: shareModalOpen,
        onCancel: closeShareModal,
        onFinish: () => {
          shareJobTemplateTo(shareTemplateUuid, shareUserId);
        },
        okButtonDisabled: shareUserId === -1,
        select: {
          placeholder: t('请选择共享对象'),
          options: shareUserList,
          onChange: (value: any) => {
            setShareUserId(value);
          },
        },
      }}
    />
  );
};

export default JobTemplatePage;
