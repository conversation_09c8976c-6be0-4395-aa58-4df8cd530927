import { render, screen, waitFor } from '@/test/setup';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import JobTemplate from '@/pages/JobTemplate/JobTemplate';
import store from '@/store/index';

describe('jobtemplate page', () => {
  it('jobtemplate page', async () => {
    render(
      <Provider store={store}>
        <Router>
          <JobTemplate />
        </Router>
      </Provider>,
    );
    await waitFor(() => {
      expect(screen.getByText('复制计划版本')).toBeInTheDocument();
    });

    expect(screen.getByText('查询')).toBeInTheDocument();
    screen.debug();
  });
});
