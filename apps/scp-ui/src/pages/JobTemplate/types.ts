type FormInputTypes = 'input' | 'select' | 'number' | 'date';
// 公共部分
type BaseFormItem<T extends FormInputTypes> = {
  type: T;
  label: string;
  name: string;
  isEdit?: boolean;
  isRequired?: boolean;
  initialValue?: any;
};

// Input类型选项
type InputOptions = {
  defaultValue?: string;
};

// Select类型选项
type SelectOptions<T> = { options: { label: string; value: T }[] };

// Select类型表单项
type SelectFormItem<T> = BaseFormItem<'select'> & {
  options?: SelectOptions<T>;
};

// Input类型表单项
type InputFormItem = BaseFormItem<'input'> & {
  options?: InputOptions;
};

// 组合类型
export type TemplateFormItem<T> = SelectFormItem<T> | InputFormItem;

type BaseFormItemType<T> = {
  type: T;
  field: string;
  label: string;
  required: boolean;
};

type DateFormItemType = BaseFormItemType<'date'> & {
  value: string;
};

type NumberFormItemType = BaseFormItemType<'number'> & {
  min: number;
  max: number;
};

type SelectFormItemType = BaseFormItemType<'select'> & {
  options: { value: number | string; label: string }[];
};

type CheckBoxFormItemType = BaseFormItemType<'checkbox-group'> & {
  options: { value: number | string; label: string }[];
};

type TextFormItemType = BaseFormItemType<'text'> & {
  value: string;
};

export type FormItemType =
  | DateFormItemType
  | NumberFormItemType
  | SelectFormItemType
  | CheckBoxFormItemType
  | TextFormItemType;
