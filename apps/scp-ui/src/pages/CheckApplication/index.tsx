import { useState } from 'react';
import { CheckApplication } from '@repo/ui';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import ajax from '@/api';
import _ from 'lodash';

export default function CheckApplication_() {
  const { message } = App.useApp();
  const navigate = useNavigate();

  const [jobFrameValue, setJobFrameValue] = useState([]);
  const [templateFrameValue, setTemplateFrameValue] = useState([]);
  const [copyTaskList, setCopyTaskList] = useState([]);

  // 获取计划任务模板
  const getJobTemplateFrameAndValue = async (
    templateUuid: string,
    jobId: string,
  ) => {
    try {
      const res = await ajax.getJobTemplateFrameAndValueForCopy({
        taskUuid: templateUuid,
        jobId: jobId,
      });
      const jobFrameValue = _.get(res, 'data.jobFrameValue', []);
      const templateFrameValue = _.get(res, 'data.templateFrameValue', []);
      setJobFrameValue(jobFrameValue);
      setTemplateFrameValue(templateFrameValue);
    } catch (error) {
      console.error('错误:', error);
    }
  };

  // 获取计划任务模板步骤列表
  const getCronTaskDetailContainsParent = async (
    taskUuid: string,
    jobId: string,
  ) => {
    try {
      const res = await ajax.getCronTaskDetailContainsParent(taskUuid, jobId);
      const list = _.get(res, 'data.list', []);
      setCopyTaskList(
        list.map((item: any) => ({
          label: item.templateName,
          value: item.seq || 0,
          templateUuid: item.templateUuid,
        })),
      );
    } catch (error) {
      console.error('错误:', error);
    }
  };

  //取消未执行的定时任务

  const deleteUnExecutedJob = async (jobId: string) => {
    try {
      const res = await ajax.deleteUnExecutedJob(jobId);
      if (res.code === 0) {
        message.success('删除成功');
        navigate(-1);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('错误:', error);
    }
  };

  return (
    <CheckApplication
      navigate={navigate}
      token={useSelector((state: any) => state.login.token)}
      getJobTemplateFrameAndValue={getJobTemplateFrameAndValue}
      getCronTaskDetailContainsParent={getCronTaskDetailContainsParent}
      deleteUnExecutedJob={deleteUnExecutedJob}
      jobFrameValue={jobFrameValue}
      templateFrameValue={templateFrameValue}
      copyTaskList={copyTaskList}
    />
  );
}
