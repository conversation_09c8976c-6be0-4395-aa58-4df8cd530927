import ReactEcharts from 'echarts-for-react';
import icon from '@/components/SVG';
const HomePie: React.FC<{
  value: { x: (string | number)[]; y: (string | number)[] };
}> = ({ value }) => {
  const option: any = {
    xAxis: {
      type: 'category',
      data: value.x,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: value.y,
        type: 'bar',
      },
    ],
  };
  return value.y.length === 0 ? (
    <icon.noSearchDataIcon isCollapse={false}></icon.noSearchDataIcon>
  ) : (
    <ReactEcharts option={option} style={{ height: '100%' }} />
  );
};
export default HomePie;
