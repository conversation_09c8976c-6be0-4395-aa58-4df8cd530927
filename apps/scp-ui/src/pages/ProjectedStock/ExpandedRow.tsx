import { useEffect, useState } from 'react';
import { Spin, Table } from 'antd';
import type { TableColumnsType } from 'antd';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
interface ExpandedRowType {
  record: any;
  paPvId: number;
}
interface ExpandedDataType {
  key: React.Key;
  locno: string;
  matnr: string;
  quantity: number;
  itemDesc: string;
  date: string;
  dependentLoc: string;
  dependentMat: string;
}
const ExpandedRow: React.FC<ExpandedRowType> = ({ record, paPvId }) => {
  const { t } = useVoerkaI18n();
  const columns: TableColumnsType<ExpandedDataType> = [
    {
      title: t('地点'),
      dataIndex: 'locno',
      key: 'locno',
    },
    {
      title: t('物料'),
      dataIndex: 'matnr',
      key: 'matnr',
    },
    {
      title: t('数量'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('预计库存'),
      dataIndex: 'projectedStock',
      key: 'projectedStock',
    },
    {
      title: t('条目信息'),
      dataIndex: 'itemDesc',
      key: 'itemDesc',
    },
    {
      title: t('需求日期'),
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: t('相关地点'),
      dataIndex: 'dependentLoc',
      key: 'dependentLoc',
    },
    {
      title: t('相关物料'),
      dataIndex: 'dependentMat',
      key: 'dependentMat',
    },
  ];

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const res = await ajax.getProjectView({
        parea: 'MyECar',
        paPvId: paPvId,
        locno: record.locno,
        matnr: record.matnr,
      });
      if (!res.code) {
        setData(res.data.list);
      }
      setLoading(false);
    };

    fetchData();
  }, [record]);

  return loading ? (
    <Spin />
  ) : (
    <Table columns={columns} dataSource={data} pagination={false} />
  );
};
export default ExpandedRow;
