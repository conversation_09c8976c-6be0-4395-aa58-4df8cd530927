import { useVoerkaI18n } from '@voerkai18n/react';
import JWForm from '@/components/JWForm';
import type { TableColumnsType } from 'antd';

const Demandful: React.FC = () => {
  const { t } = useVoerkaI18n();
  const columns: TableColumnsType = [
    {
      title: 'ID',
      dataIndex: 'demandName',
      key: 'demandName',
      sorter: (a: any, b: any) => a.demandName.localeCompare(b.demandName),
    },
    {
      title: t('地点'),
      dataIndex: 'locno',
      key: 'locno',
      sorter: (a: any, b: any) => a.locno.localeCompare(b.locno),
    },
    {
      title: t('物料'),
      dataIndex: 'matnr',
      key: 'matnr',
      sorter: (a: any, b: any) => a.matnr.localeCompare(b.matnr),
    },
    {
      title: t('客户'),
      dataIndex: 'customer',
      key: 'customer',
      sorter: (a: any, b: any) => a.customer.localeCompare(b.customer),
    },
    {
      title: t('数量'),
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a: any, b: any) => a.Quantity - b.Quantity,
    },
    {
      title: t('需求日期'),
      dataIndex: 'requestedDate',
      key: 'requestedDate',
    },
    {
      title: t('按时交付'),
      dataIndex: 'qtyInTime',
      key: 'qtyInTime',
      sorter: (a: any, b: any) => a.QtyInTime - b.QtyInTime,
    },
    {
      title: t('延迟交付'),
      dataIndex: 'qtyDelay',
      key: 'qtyDelay',
      sorter: (a: any, b: any) => a.QtyDelay - b.QtyDelay,
    },
    {
      title: t('未交付'),
      dataIndex: 'qtyNondel',
      key: 'qtyNondel',
      sorter: (a: any, b: any) => a.QtyNondel - b.QtyNondel,
    },
    {
      title: t('条目信息'),
      dataIndex: 'itemDesc',
      key: 'itemDesc',

      sorter: (a: any, b: any) => a.itemDesc.localeCompare(b.itemDesc),
    },
  ];

  return <JWForm pageName={'DS_ANAL'} columns={columns} />;
};

export default Demandful;
