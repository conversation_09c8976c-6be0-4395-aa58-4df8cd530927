import React, { useState, ReactNode, useEffect } from 'react';
import { Table, Space, Drawer, Descriptions } from 'antd';
import { EditOutlined, CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import type { ColumnsType } from 'antd/es/table';
import type { DescriptionsProps } from 'antd';
import ajax from '@/api';
import _ from 'lodash';

interface DataType {
  key: string;
  label: string;
  kfType: number;
  kfId: number;
  planLevelName: string;
  status: string;
  isSystemBuiltIn: boolean;
  basePlanLevel: string;
  isTimeRelated: boolean;
  category: string;
  aggregationMode: string;
  decompositionMode: string;
  decompositionDependencyRatio: string;
  calculationRule: string;
}

interface DetailDataType {
  aggOperationMode: string; // 聚合操作模式
  disaggregatedMode: string; // 分解模式
  exp: string | null; // 额外信息
  isCustom: boolean; // 是否自定义
  isDisaggregation: boolean; // 是否分解
  kfId: number; // 特征值ID
  kfTypeName: string; // 特征值类型名称
  label: string; // 标签
  otherKfProportion: string; // 其他特征值比例
  periodWork: boolean; // 是否周期工作
  planLevelName: string; // 计划等级名称
  proportionality: string; // 比例
}
interface CustomTableRowProps {
  children: ReactNode;
  'data-row-key': string;
  [key: string]: any;
}

export default function CustomTable() {
  const [data, setData] = useState<DataType[]>([]);
  const [detailData, setDetailData] = useState<DetailDataType | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);
  const [pageData, setPageData] = useState({
    total: 0,
    page: 1,
    pageSize: 20,
  });
  const [loading, setLoading] = useState(false);
  const [tableHeight, setTableHeight] = useState(400);

  const handleEdit = (e: React.MouseEvent, key: string) => {
    e.stopPropagation();
    console.log('编辑', key);
  };

  const handleCopy = (e: React.MouseEvent, key: string) => {
    e.stopPropagation();
    console.log('复制', key);
  };

  const handleDelete = (e: React.MouseEvent, key: string) => {
    e.stopPropagation();
    setData(data.filter((item) => item.key !== key));
  };

  const getKeyFigureList = async () => {
    try {
      setLoading(true);
      const res = await ajax.getKeyFigureList({
        timeBasedFlag: false,
        page: pageData.page,
        pageSize: pageData.pageSize,
      });
      const list = _.get(res, 'data.list', []);
      const total = _.get(res, 'data.total', 0);
      setData(list);
      setPageData((prevState) => ({ ...prevState, total }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const getKeyFigureDetail = async (id: number | string) => {
    try {
      const res = await ajax.getKeyFigureDetail({ kfId: id });
      const data = _.get(res, 'data', null) as DetailDataType | null;
      setDetailData(data);
      console.log(detailData);

      console.log(res);
    } catch (error) {
      console.error(error);
    }
  };

  const columns: ColumnsType<DataType> = [
    {
      title: '名称',
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: '类型',
      dataIndex: 'kfType',
      key: 'kfType',
      render: (kfType: number) => (kfType === 1 ? '前端输入' : '存储'),
    },
    {
      title: '关联PL-名称',
      dataIndex: 'planLevelName',
      key: 'planLevelName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size='middle'>
          <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.8 }}>
            <EditOutlined
              onClick={(e) => handleEdit(e, record.key)}
              style={{ cursor: 'pointer', fontSize: '18px' }}
            />
          </motion.div>
          <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.8 }}>
            <CopyOutlined
              onClick={(e) => handleCopy(e, record.key)}
              style={{ cursor: 'pointer', fontSize: '18px' }}
            />
          </motion.div>
          <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.8 }}>
            <DeleteOutlined
              onClick={(e) => handleDelete(e, record.key)}
              style={{ cursor: 'pointer', fontSize: '18px' }}
            />
          </motion.div>
        </Space>
      ),
    },
  ];

  const onRowClick = async (record: DataType) => {
    setSelectedRowKey(record.key);
    await getKeyFigureDetail(record.kfId);
    setDrawerVisible(true);
  };

  const onCloseDrawer = () => {
    setDrawerVisible(false);
    setSelectedRowKey(null);
  };

  const getSelectedRowData = () => {
    return data.find((item) => item.key === selectedRowKey);
  };

  const getDescriptionItems = (): DescriptionsProps['items'] => {
    const selectedData = getSelectedRowData();
    return [
      { key: '1', label: '名称', children: selectedData?.label },
      {
        key: '2',
        label: '是否系统内置',
        children: selectedData?.isSystemBuiltIn ? '是' : '否',
      },
      {
        key: '3',
        label: '基础计划等级',
        children: selectedData?.basePlanLevel,
      },
      {
        key: '4',
        label: '是否与时间相关',
        children: selectedData?.isTimeRelated ? '是' : '否',
      },
      { key: '5', span: 2, label: '类别', children: selectedData?.category },
      {
        key: '6',
        span: 2,
        label: '聚合模式',
        children: selectedData?.aggregationMode,
      },
      {
        key: '7',
        span: 2,
        label: '分解模式',
        children: selectedData?.decompositionMode,
      },
      {
        key: '8',
        label: '分解依赖比例',
        children: selectedData?.decompositionDependencyRatio,
      },
      { key: '9', label: '计算规则', children: selectedData?.calculationRule },
    ];
  };

  useEffect(() => {
    getKeyFigureList();
  }, [pageData.page, pageData.pageSize]);

  useEffect(() => {
    const updateTableHeight = () => {
      const windowHeight = window.innerHeight;
      const maxTableHeight = windowHeight * 0.65;
      setTableHeight(maxTableHeight);
    };

    updateTableHeight();
    window.addEventListener('resize', updateTableHeight);

    return () => {
      window.removeEventListener('resize', updateTableHeight);
    };
  }, []);

  return (
    <div className='custom-table-container'>
      <Table
        loading={loading}
        columns={columns}
        dataSource={data}
        onRow={(record) => ({
          onClick: () => onRowClick(record),
          style: {
            cursor: 'pointer',
          },
        })}
        components={{
          body: {
            row: ({ children, ...props }: CustomTableRowProps) => (
              <motion.tr
                {...props}
                initial={false}
                transition={{ duration: 0.3 }}>
                {children}
              </motion.tr>
            ),
          },
        }}
        pagination={{
          total: pageData.total,
          position: ['bottomCenter'],
          current: pageData.page,
          pageSize: pageData.pageSize,
          onChange: (page, pageSize) =>
            setPageData({ ...pageData, page, pageSize }),
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
        }}
        scroll={{ y: tableHeight }}
      />
      <Drawer
        title={`行 ID: ${selectedRowKey}`}
        placement='right'
        closable={true}
        onClose={onCloseDrawer}
        open={drawerVisible}
        width={800}>
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 50 }}
          transition={{ duration: 0.3 }}>
          <Descriptions bordered column={2} items={getDescriptionItems()} />
        </motion.div>
      </Drawer>
    </div>
  );
}
