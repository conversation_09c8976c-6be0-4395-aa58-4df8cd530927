import { useEffect, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import { Table, type TableColumnsType, Tag, Drawer } from 'antd';
import _ from 'lodash';
import ajax from '@/api';
interface TableData {
  id: number;
  name: string;
  isCustom: boolean;
  uniqueList: { label: string; code: string }[];
}

interface ItemTypeDetail {
  itemTypeId: number;
  itemTypeLabel: string;
  itemTypeCode: string;
  kfId: number;
  kfLabel: string;
  kfCode: string;
}

const columns: TableColumnsType<TableData> = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '是否自定义',
    dataIndex: 'isCustom',
    key: 'isCustom',
    render: (text) => (text ? '是' : '否'),
  },
  {
    title: '唯一标识名称',
    dataIndex: 'uniqueListLabel',
    key: 'uniqueListLabel',
    render: (_, record) => {
      return record.uniqueList.map((item: { label: string; code: string }) => (
        <Tag key={item.label}>{item?.label}</Tag>
      ));
    },
  },
  {
    title: '唯一标识编码',
    dataIndex: 'uniqueListCode',
    key: 'uniqueListCode',
    render: (_, record) =>
      record.uniqueList.map((item: { label: string; code: string }) => (
        <Tag key={item.code}>{item?.code}</Tag>
      )),
  },
];

const detailColumns: TableColumnsType<ItemTypeDetail> = [
  {
    title: 'ID',
    dataIndex: 'itemTypeId',
    key: 'itemTypeId',
  },
  {
    title: 'item名称',
    dataIndex: 'itemTypeLabel',
    key: 'itemTypeLabel',
  },
  {
    title: '编码',
    dataIndex: 'itemTypeCode',
    key: 'itemTypeCode',
  },
  {
    title: 'KF ID',
    dataIndex: 'kfId',
    key: 'kfId',
  },
  {
    title: 'KF 名称',
    dataIndex: 'kfLabel',
    key: 'kfLabel',
  },
  {
    title: 'KF 编码',
    dataIndex: 'kfCode',
    key: 'kfCode',
  },
];
const ItemType: React.FC = () => {
  const [tableData, setTableData] = useState<TableData[]>([]);
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [drawerLoading, setDrawerLoading] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TableData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [detailData, setDetailData] = useState<ItemTypeDetail[]>([]);

  const getItemTypeList = async () => {
    setLoading(true);
    try {
      const res = await ajax.getItemTypeList();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setTableData(list);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getItemTypeDetail = async (id: number) => {
    try {
      setDrawerLoading(true);
      const res = await ajax.getItemTypeDetails({ itemTypeId: id });
      if (!res.code) {
        const data = (_.get(res, 'data', []) || []) as ItemTypeDetail[];
        setDetailData(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setDrawerLoading(false);
    }
  };
  useUpdateEffect(() => {
    if (currentRecord) {
      getItemTypeDetail(currentRecord.id);
    }
  }, [currentRecord]);

  useEffect(() => {
    getItemTypeList();
  }, []);

  return (
    <>
      <Table<TableData>
        onRow={(record) => {
          return {
            onClick: () => {
              setCurrentRecord(record);
              setDrawerVisible(true);
            },
          };
        }}
        rowKey={(record) => record.id.toString() + record.name}
        loading={loading}
        columns={columns}
        dataSource={tableData}
        pagination={{
          position: ['bottomCenter'],
        }}
      />
      <Drawer
        loading={drawerLoading}
        open={drawerVisible}
        width={800}
        title='详情'
        onClose={() => setDrawerVisible(false)}>
        <Table<ItemTypeDetail>
          rowKey='kfId'
          columns={detailColumns}
          dataSource={detailData}
        />
      </Drawer>
    </>
  );
};

export default ItemType;
