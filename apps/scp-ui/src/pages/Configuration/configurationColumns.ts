export default {
  Attribute: {
    columns: [
      // {
      //   title: 'ID',
      //   dataIndex: 'attributeId',
      //   key: 'attributeId',
      //   isFilter: true,
      // },
      {
        title: '名称',
        dataIndex: 'label',
        key: 'label',
        isFilter: true,
      },
      // {
      //   title: '描述',
      //   dataIndex: 'label',
      //   key: 'label',
      // },
      // {
      //   title: '类型',
      //   dataIndex: 'valueType',
      //   key: 'valueType',
      //   isFilter: true,
      // },
      {
        title: '是否为系统内置',
        dataIndex: 'isCustom',
        key: 'isCustom',
        isFilter: true,
        render: (i: boolean) => !i + '',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        isFilter: true,
        render: () => 'active',
      },
    ],
    detailsList: [
      {
        label: 'ID',
        value: 'attributeId',
      },
      {
        label: '名称',
        value: 'label',
      },
      // {
      //   label: '描述',
      //   value: 'label',
      // },
      {
        label: '类型',
        value: 'valueType',
      },
      {
        label: '是否为系统内置',
        value: 'isCustom',
      },
      {
        label: '状态',
        value: 'status',
      },
    ],
    addList: [
      {
        label: '名称',
        value: 'label',
      },
      {
        label: '类型',
        value: 'valueType',
      },
    ],
  },
  MasterDataType: {
    columns: [
      // {
      //   title: 'ID',
      //   dataIndex: 'masterDataTypeId',
      //   key: 'masterDataTypeId',
      //   isFilter: true,
      // },
      {
        title: '名称',
        dataIndex: 'label',
        key: 'label',
        isFilter: true,
      },
      // {
      //   title: '描述',
      //   dataIndex: 'label',
      //   key: 'label',
      // },
      {
        title: '是否为系统内置',
        dataIndex: 'isCustom',
        key: 'isCustom',
        isFilter: true,
        render: (i: boolean) => !i + '',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        isFilter: true,
        render: () => 'active',
      },
    ],
    detailsList: [
      {
        label: 'ID',
        value: 'masterDataTypeId',
      },
      {
        label: '名称',
        value: 'label',
      },
      // {
      //   label: '描述',
      //   value: 'label',
      // },
      {
        label: '是否为系统内置',
        value: 'isCustom',
      },
      {
        label: '状态',
        value: 'status',
      },
    ],
    drawerColumns: [
      {
        title: '名称',
        dataIndex: 'attributeLabel',
        key: 'attributeLabel',
        isFilter: true,
      },
      // {
      //   title: '描述',
      //   dataIndex: 'attributeLabel',
      //   key: 'attributeLabel',
      // },
      {
        title: '是否为必须',
        dataIndex: 'IsRoot',
        key: 'IsRoot',
        isFilter: true,
        render: (i: boolean) => i + '',
      },
      {
        title: 'key',
        dataIndex: 'isUnique',
        key: 'isUnique',
        isFilter: true,
        render: (i: boolean) => i + '',
      },
    ],
    addList: [
      {
        label: '名称',
        value: 'label',
      },
    ],
  },
  PlanLevel: {
    columns: [
      // {
      //   title: 'ID',
      //   dataIndex: 'planningLevelId',
      //   key: 'planningLevelId',
      //   isFilter: true,
      // },
      {
        title: '名称',
        dataIndex: 'planningLevelName',
        key: 'planningLevelName',
        isFilter: true,
      },
      // {
      //   title: '描述',
      //   dataIndex: 'descript',
      //   key: 'descript',
      // },
      {
        title: '时间维度',
        dataIndex: 'tpLevel',
        key: 'tpLevel',
        isFilter: true,
      },
      {
        title: '是否为系统内置',
        dataIndex: 'isCustom',
        key: 'isCustom',
        isFilter: true,
        render: (i: boolean) => !i + '',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        isFilter: true,
        render: () => 'active',
      },
    ],
    detailsList: [
      {
        label: 'ID',
        value: 'planningLevelId',
      },
      {
        label: '名称',
        value: 'planningLevelName',
      },
      // {
      //   label: '描述',
      //   value: 'descript',
      // },
      {
        label: '时间维度',
        value: 'tpLevel',
      },
      {
        label: '是否为系统内置',
        value: 'isCustom',
      },
      {
        label: '状态',
        value: 'status',
      },
    ],
    drawerColumns: [
      {
        title: '名称',
        dataIndex: 'attributeLabel',
        key: 'attributeLabel',
        isFilter: true,
      },
      // {
      //   title: '描述',
      //   dataIndex: 'attributeLabel',
      //   key: 'attributeLabel',
      // },
      {
        title: '是否为Root',
        dataIndex: 'IsRoot',
        key: 'IsRoot',
        isFilter: true,
        render: (i: boolean) => i + '',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        isFilter: true,
        render: () => 'active',
      },
    ],
    addList: [
      {
        label: '名称',
        value: 'planningLevelName',
      },
      {
        label: '时间维度',
        value: 'tpLevel',
      },
    ],
  },
  KeyFigure: {
    columns: [
      // {
      //   title: 'ID',
      //   dataIndex: 'kfId',
      //   key: 'kfId',
      //   isFilter: true,
      // },
      {
        title: '名称',
        dataIndex: 'label',
        key: 'label',
        isFilter: true,
      },
      {
        title: '类型',
        dataIndex: 'kfType',
        key: 'kfType',
        isFilter: true,
        render: (v: number) => {
          if (v === 1) {
            return '前端输入';
          } else if (v === 2) {
            return '存储';
          } else if (v === 3) {
            return '计算';
          }
        },
      },
      // {
      //   title: '关联pl-ID',
      //   dataIndex: 'planLevelID',
      //   key: 'planLevelID',
      //   isFilter: true,
      // },
      {
        title: '关联pl-名称',
        dataIndex: 'planLevelName',
        key: 'planLevelName',
        isFilter: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        isFilter: true,
        render: () => 'active',
      },
      // {
      //   title: '是否为系统内置',
      //   dataIndex: 'IsCustom',
      //   key: 'IsCustom',
      //   isFilter: true,
      // },
      // {
      //   title: '是否被使用',
      //   dataIndex: 'isComponent',
      //   key: 'isComponent',
      //   isFilter: true,
      // },
    ],
    detailsList: [
      {
        label: 'ID',
        value: 'kfId',
      },
      {
        label: '名称',
        value: 'label',
      },
      {
        label: '类型',
        value: 'kfType',
      },
      {
        label: '关联pl-ID',
        value: 'planLevelID',
      },
      {
        label: '关联pl-名称',
        value: 'planLevelName',
      },
      // {
      //   label: '是否可见',
      //   value: 'isShow',
      // },
      {
        label: '是否为系统内置',
        value: 'isCustom',
      },
      // {
      //   label: '是否被使用',
      //   value: 'isComponent',
      // },
      {
        label: '状态',
        value: 'status',
      },
    ],
    addList: [
      {
        label: '名称',
        value: 'planningLevelName',
      },
      {
        label: '类型',
        value: 'kfType',
      },
      {
        label: '关联pl-名称',
        value: 'planLevelName',
      },
      {
        label: '操作',
        value: 'operation',
      },
    ],
  },
};
