import ConfiguationFrom from '@/components/ConfiguationFrom';
import configurationStyles from './index.module.css';
import { Badge, Descriptions, message } from 'antd';
import ajax from '@/api';
import { useState, useEffect } from 'react';
import type { DescriptionsProps } from 'antd';
import { useVoerkaI18n } from '@voerkai18n/react';
import React from 'react';
type propsTypes = {
  pageType: string;
};

const ShowPage: React.FC<propsTypes> = React.memo((props) => {
  const { t } = useVoerkaI18n();
  const co = {
    Attribute: {
      columns: [
        {
          title: t('名称'),
          dataIndex: 'label',
          key: 'label',
          isFilter: true,
        },
        {
          title: t('是否为系统内置'),
          dataIndex: 'isCustom',
          key: 'isCustom',
          isFilter: true,
          render: (i: boolean) => !i + '',
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => 'active',
        },
      ],
      detailsList: [
        {
          label: 'ID',
          value: 'attributeId',
        },
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('类型'),
          value: 'valueType',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'label',
        },
        {
          label: t('类型'),
          value: 'valueType',
        },
      ],
    },
    MasterDataType: {
      columns: [
        {
          title: t('名称'),
          dataIndex: 'label',
          key: 'label',
          isFilter: true,
        },
        {
          title: t('是否为系统内置'),
          dataIndex: 'isCustom',
          key: 'isCustom',
          isFilter: true,
          render: (i: boolean) => !i + '',
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => 'active',
        },
      ],
      detailsList: [
        {
          label: 'ID',
          value: 'masterDataTypeId',
        },
        {
          label: '名称',
          value: 'label',
        },
        {
          label: '是否为系统内置',
          value: 'isCustom',
        },
        {
          label: '状态',
          value: 'status',
        },
      ],
      drawerColumns: [
        {
          title: t('名称'),
          dataIndex: 'attributeLabel',
          key: 'attributeLabel',
          isFilter: true,
        },
        {
          title: 'IsRoot',
          dataIndex: 'isRequired',
          key: 'isRequired',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
        {
          title: 'key',
          dataIndex: 'isUnique',
          key: 'isUnique',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'label',
        },
      ],
    },
    PlanLevel: {
      columns: [
        {
          title: t('名称'),
          dataIndex: 'planningLevelName',
          key: 'planningLevelName',
          isFilter: true,
        },
        {
          title: t('时间维度'),
          dataIndex: 'tpLevel',
          key: 'tpLevel',
          isFilter: true,
        },
        {
          title: t('是否为系统内置'),
          dataIndex: 'isCustom',
          key: 'isCustom',
          isFilter: true,
          render: (i: boolean) => !i + '',
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => 'active',
        },
      ],
      detailsList: [
        {
          label: 'ID',
          value: 'planningLevelId',
        },
        {
          label: t('名称'),
          value: 'planningLevelName',
        },
        {
          label: t('时间维度'),
          value: 'tpLevel',
        },
        {
          label: t('是否为系统内置'),
          value: 'isCustom',
        },
        {
          label: t('状态'),
          value: 'status',
        },
      ],
      drawerColumns: [
        {
          title: t('名称'),
          dataIndex: 'attributeLabel',
          key: 'attributeLabel',
          isFilter: true,
        },
        {
          title: '是否为Root',
          dataIndex: 'isRoot',
          key: 'isRoot',
          isFilter: true,
          render: (i: boolean) => i + '',
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => 'active',
        },
      ],
      addList: [
        {
          label: t('名称'),
          value: 'planningLevelName',
        },
        {
          label: t('时间维度'),
          value: 'tpLevel',
        },
      ],
    },
    KeyFigure: {
      columns: [
        {
          title: t('名称'),
          dataIndex: 'label',
          key: 'label',
          isFilter: true,
        },
        {
          title: t('类型'),
          dataIndex: 'kfType',
          key: 'kfType',
          isFilter: true,
          render: (v: number) => {
            if (v === 1) {
              return '前端输入';
            } else if (v === 2) {
              return '存储';
            } else if (v === 3) {
              return '计算';
            }
          },
        },
        {
          title: t('关联pl-名称'),
          dataIndex: 'planLevelName',
          key: 'planLevelName',
          isFilter: true,
        },
        {
          title: t('状态'),
          dataIndex: 'status',
          key: 'status',
          isFilter: true,
          render: () => 'active',
        },
      ],
      detailsList: [
        {
          label: 'ID',
          value: 'kfId',
        },
        {
          label: '名称',
          value: 'label',
        },
        {
          label: '类型',
          value: 'kfType',
        },
        {
          label: '关联pl-ID',
          value: 'planLevelID',
        },
        {
          label: '关联pl-名称',
          value: 'planLevelName',
        },
        {
          label: '是否为系统内置',
          value: 'isCustom',
        },
        {
          label: '状态',
          value: 'status',
        },
      ],
      addList: [
        {
          label: '名称',
          value: 'planningLevelName',
        },
        {
          label: '类型',
          value: 'kfType',
        },
        {
          label: '关联pl-名称',
          value: 'planLevelName',
        },
        {
          label: '操作',
          value: 'operation',
        },
      ],
    },
  };
  const { pageType } = props;
  const [items, setItems] = useState<DescriptionsProps['items']>([]);
  const [currentData, setCurrentData] = useState<any[]>([]);
  const [columns, setColumns] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [drawerColumns, setDrawerColumns] = useState<any[]>([]);
  const [detailsList, setDetailsList] = useState<any[]>([]);
  const [pageData, setPageData] = useState({
    page: 1,
    pageSize: 10,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [isListNull, setIsListNull] = useState<boolean>(false);
  const onSearch = async (type: string, searchValue?: string) => {
    setLoading(true);
    let res: any = [];
    switch (type) {
      case 'Attribute':
        res = await ajax.getAttribute({
          ...pageData,
          attributeName: searchValue || '',
        });
        if (res.data !== null && res.code === 0) {
          if (res.data.list.length === 0 || res.data.list === null) {
            message.info('暂无数据');
            setIsListNull(true);
          }
          setCurrentData(res.data.list || []);
          setTotal(res.data.total);
        }
        break;
      case 'Master Data Type':
        res = await ajax.getMasterDataTypeList({
          ...pageData,
          label: searchValue || '',
        });
        if (res.data !== null && res.code === 0) {
          if (res.data.list.length === 0 || res.data.list === null) {
            message.info('暂无数据');
            setIsListNull(true);
          }

          setCurrentData(res.data.list || []);
          setTotal(res.data.total);
        }
        break;
      case 'Time Profile':
        {
          res = await ajax.getTimeProfileList();
          const { list }: { list: any } = res.data;
          if (list !== null && res.code === 0) {
            setCurrentData(list || []);
            const data = list[0];
            setItems([
              {
                key: '1',
                label: t('名称'),
                children: data.name,
              },
              {
                key: '2',
                label: t('描述'),
                children: data.profileDesc,
              },
              {
                key: '3',
                label: t('时间维度'),
                children: (
                  <div>
                    {data.tpLevelList.map((item: string, index: number) => {
                      if (index === 5) return `${item}`;
                      return `${item}` + ',';
                    })}
                  </div>
                ),
              },
              {
                key: '4',
                label: t('开始时间'),
                children: data.periodStart,
              },
              {
                key: '5',
                label: t('结束时间'),
                children: data.periodEnd,
                span: 2,
              },
              {
                key: '6',
                label: t('状态'),
                children: <Badge status='success' text='active' />,
                span: 3,
              },
              {
                key: '7',
                label: t('当前PA'),
                children: data.pareaId,
              },

              {
                key: '8',
                label: t('其他'),
                children: data.details,
              },
            ]);
          }
          if (res.data.list.length === 0 || res.data.list === null) {
            message.info('暂无数据');
            setIsListNull(true);
          }
        }
        break;
      case 'Plan Level':
        res = await ajax.getPlanningLevelList({
          ...pageData,
          planLevelName: searchValue || '',
        });
        if (res.data !== null && res.code === 0) {
          setCurrentData(res.data.list || []);
          setTotal(res.data.total);
        }
        if (res.data.list.length === 0 || res.data.list === null) {
          message.info('暂无数据');
          setIsListNull(true);
        }

        break;
      case 'Key Figure':
        res = await ajax.getKeyFigureList({
          timeBasedFlag: false,
          ...pageData,
          keyFigureName: searchValue || '',
        });
        if (res.data !== null && res.code === 0) {
          setCurrentData(res.data.list || []);
          setTotal(res.data.total);
        }
        if (res.data.list.length === 0 || res.data.list === null) {
          message.info('暂无数据');
          setIsListNull(true);
        }

        break;
    }
    setLoading(false);
  };
  const getPageData = (value: any) => {
    setPageData({ ...value });
  };
  useEffect(() => {
    if (pageType) {
      switch (pageType) {
        case 'Attribute':
          setColumns(co.Attribute.columns);
          setDetailsList(co.Attribute.detailsList);
          break;
        case 'Master Data Type':
          setColumns(co.MasterDataType.columns);
          setDetailsList(co.MasterDataType.detailsList);
          setDrawerColumns(co.MasterDataType.drawerColumns);
          break;
        case 'Time Profile':
          break;
        case 'Plan Level':
          setColumns(co.PlanLevel.columns);
          setDetailsList(co.PlanLevel.detailsList);
          setDrawerColumns(co.PlanLevel.drawerColumns);
          break;
        case 'Key Figure':
          setColumns(co.KeyFigure.columns);
          setDetailsList(co.KeyFigure.detailsList);
          break;
      }
      onSearch(pageType);
    }
  }, [pageData, pageType, localStorage.getItem('language')]);

  return (
    <div>
      {pageType === 'Time Profile' ? (
        <div className={configurationStyles.wrap}>
          <main className={configurationStyles.main}>
            <Descriptions title={t('当前时间配置')} bordered items={items} />
          </main>
        </div>
      ) : (
        <ConfiguationFrom
          pageType={pageType}
          loading={loading}
          columns={columns}
          detailsList={detailsList}
          data={currentData}
          onSearch={onSearch}
          drawerColumns={drawerColumns}
          total={total}
          getPageData={getPageData}
          isListNull={isListNull}
        />
      )}
    </div>
  );
});
export default ShowPage;
