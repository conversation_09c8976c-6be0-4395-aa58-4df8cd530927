import { useState } from 'react';
import { Modal, Select, message } from 'antd';
import { useSelector } from 'react-redux';
import { setUser } from '@/store/features/loginSlice';
import store from '@/store';
import ajax from '@/api';
type PerferPversionModalProps = {
  visible: boolean;
  close: () => void;
};
const PerferPversionModal: React.FC<PerferPversionModalProps> = ({
  visible,
  close,
}) => {
  const _pVersionList = useSelector((state: any) => state.layout.pVersionList);
  const pVersionList = (function () {
    const list = _pVersionList || [];
    if (list.length === 0) {
      return JSON.parse(localStorage.getItem('pVersionList') || '[]');
    } else {
      return list;
    }
  })();
  const user = useSelector((state: any) => state.login.user);
  const [currentPversion, setCurrentPversion] = useState<number>(
    user?.preferPaPvId == 0 ? null : user?.preferPaPvId,
  );
  /**
   *@description 提交偏好计划版本
   *@param
   *@return {void}
   *@auther liujinxu
   */
  const commitPerferPversion = async () => {
    const obj = {
      userId: user?.id,
      preferType: 2,
      preferPaPvId: currentPversion,
    };
    const res = await ajax.setUserPerferVersion(obj);
    if (res.code !== 0) {
      message.error(res.msg);
    } else {
      message.success('设置成功');
      store.dispatch(
        setUser({
          ...user,
          preferPaPvId: currentPversion,
        }),
      );
      localStorage.setItem('user', JSON.stringify(user));
      close();
    }
  };

  return (
    <Modal
      title='偏好计划版本设置'
      open={visible}
      onCancel={close}
      onOk={commitPerferPversion}>
      <Select
        value={currentPversion}
        onChange={(value) => setCurrentPversion(value)}
        style={{ width: '200px' }}
        options={pVersionList}
        defaultValue={user?.preferPaPvId == 0 ? null : user?.preferPaPvId}
      />
    </Modal>
  );
};

export default PerferPversionModal;
