import { useState, useEffect } from 'react';
import { Table, Flex, Button, Modal, Form, Input, App, Select } from 'antd';
import { DeleteButton } from '@repo/ui';
import PerferPversionModal from './components/PerferPversionSettingModal';
import ajax from '@/api';
import _ from 'lodash';
import { PlusOutlined } from '@ant-design/icons';

const PversionSetting = () => {
  const { message } = App.useApp();

  const [modalType, setModalType] = useState('create');
  const [modalOpen, setModalOpen] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [mdOptions, setMdOptions] = useState([]);
  const [shareParams, setShareParams] = useState({});
  const [originSharedUserIds, setOriginSharedUserIds] = useState([]);
  const [shareUserList, setShareUserList] = useState([]);

  const [perferModalOpen, setPerferModalOpen] = useState(false);

  const [form] = Form.useForm();

  const columns = [
    {
      title: '名称',
      dataIndex: 'pversion',
    },
    {
      title: '主数据',
      dataIndex: 'mdId',
    },
    {
      title: '来源',
      dataIndex: 'authTypeName',
    },
    {
      title: '操作',
      render: (record: any) => {
        return (
          <Flex gap={10} justify='center'>
            <Button
              disabled={
                record.authType === 'Default' || record.authType === 'Shared'
              }
              onClick={() => {
                setShareParams({
                  versionName: record.pversion,
                  pareaId: Number(localStorage.getItem('pareaId')),
                  mdId: record.mdId,
                  paPvId: record.paPvId,
                });
                form.setFieldValue('userIds', record.userIds);
                setOriginSharedUserIds(record.userIds);
                setModalType('share');
                setModalOpen(true);
              }}>
              {'设置分享'}
            </Button>
            <DeleteButton
              disabled={
                record.authType === 'Default' || record.authType === 'Shared'
              }
              deleteTitle={'删除'}
              onClick={() => {
                ajax
                  .deletePversion({
                    paPvId: record.paPvId,
                  })
                  .then(() => {
                    message.success('删除计划版本成功');
                    getVersions();
                  })
                  .catch((error) => {
                    console.error(error);
                  });
              }}></DeleteButton>
          </Flex>
        );
      },
    },
  ];
  columns.forEach((col: any) => {
    col.onCell = () => {
      return {
        style: {
          textAlign: 'center',
        },
      };
    };
    col.onHeaderCell = () => {
      return {
        style: {
          textAlign: 'center',
        },
      };
    };
  });

  const getVersions = () => {
    ajax
      .getAreaAndVersion()
      .then((res: any) => {
        const data = _.get(res, 'data.list', []) || [];
        setDataSource(
          data.map((item: any) => {
            return {
              paPvId: item.paPvId,
              pversion: item.pversion,
              authType: item.authType,
              authTypeName: item.authTypeName,
              mdId: item.mdId,
              userIds: item.userList,
            };
          }),
        );
      })
      .then((err) => {
        console.error(err);
      });
  };

  const getUserListByCoId = async () => {
    try {
      const res = await ajax.getUserListByCoId();
      const list = _.get(res, 'data.list', []) || [];
      setShareUserList(
        list.map((item: any) => ({
          ...item,
          label: item.nickName,
          value: item.id,
        })),
      );
    } catch (error) {
      console.error(error);
    }
  };

  const getMdOptions = () => {
    ajax.getMdIdList().then((res) => {
      const data = _.get(res, 'data.list', []) || [];
      setMdOptions(
        data.map((item: any) => {
          return {
            label: item.mdId,
            value: item.mdId,
          };
        }),
      );
    });
  };

  useEffect(() => {
    getVersions();
    getMdOptions();
    getUserListByCoId();
  }, []);

  const handleCreateOrShare = (params?: any) => {
    setModalLoading(true);
    const newSharedUserIds = form.getFieldValue('userIds');
    const addSharedUserIds = _.difference(
      newSharedUserIds,
      originSharedUserIds,
    );
    const deleteSharedUserIds = _.difference(
      originSharedUserIds,
      newSharedUserIds,
    );
    modalType === 'create'
      ? (params = {
          versionName: form.getFieldValue('versionName'),
          pareaId: Number(localStorage.getItem('pareaId')),
          mdId: form.getFieldValue('mdId'),
          userIds: newSharedUserIds,
        })
      : (params = {
          ...shareParams,
          userIds: addSharedUserIds,
        });
    if (modalType === 'share' && shareUserList.length === 0) {
      message.warning('不存在可分享的用户');
      setModalLoading(false);
      return;
    }
    if (
      Object.keys(params)
        .filter((key) => (modalType === 'create' ? key !== 'userIds' : true))
        .some((key) => !params[key])
    ) {
      message.warning('请填写完整信息');
      setModalLoading(false);
      return;
    }
    ajax
      .createPversion(params)
      .then(() => {
        if (modalType === 'create') {
          setModalLoading(false);
          setModalOpen(false);
          message.success('新增计划版本成功');
          getVersions();
          form.resetFields();
        } else {
          if (deleteSharedUserIds.length > 0) {
            ajax
              .cancelSharedPversion({
                ...shareParams,
                userIds: deleteSharedUserIds,
              })
              .then(() => {
                setModalLoading(false);
                setModalOpen(false);
                message.success('设置分享计划版本成功');
                getVersions();
                form.resetFields();
              })
              .catch((error) => {
                console.error(error);
              });
          } else {
            setModalLoading(false);
            setModalOpen(false);
            message.success('设置分享计划版本成功');
            getVersions();
            form.resetFields();
          }
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };

  return (
    <Flex
      style={{
        width: '100%',
        height: '100%',
      }}
      className='bg-white px-10 rounded-2xl'
      vertical={true}
      gap={10}>
      <Table
        title={() => (
          <Flex justify='flex-end' gap={10}>
            <Button
              type='primary'
              onClick={() => {
                setPerferModalOpen(true);
              }}>
              {'设置偏好计划版本'}
            </Button>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => {
                form.resetFields();
                setModalType('create');
                setModalOpen(true);
              }}>
              {'新增计划版本'}
            </Button>
          </Flex>
        )}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={'paPvId'}
      />
      <Modal
        open={modalOpen}
        title={modalType === 'create' ? '新增计划版本' : '设置分享计划版本'}
        okText={'确定'}
        okButtonProps={{
          loading: modalLoading,
        }}
        cancelText={'取消'}
        centered={true}
        onCancel={() => setModalOpen(false)}
        onOk={handleCreateOrShare}>
        <Form form={form}>
          <Form.Item
            label={'计划版本名称'}
            name={'versionName'}
            style={{ display: modalType === 'create' ? 'block' : 'none' }}
            required>
            <Input placeholder='请输入计划版本名称'></Input>
          </Form.Item>
          <Form.Item
            label={'选择主数据'}
            name={'mdId'}
            style={{ display: modalType === 'create' ? 'block' : 'none' }}
            required>
            <Select placeholder={'请选择主数据'} options={mdOptions}></Select>
          </Form.Item>
          <Form.Item
            label={'设置要分享的用户'}
            name={'userIds'}
            required={modalType === 'share'}>
            <Select
              options={shareUserList}
              mode={'multiple'}
              placeholder={'请设置要分享的用户'}></Select>
          </Form.Item>
        </Form>
      </Modal>
      <PerferPversionModal
        visible={perferModalOpen}
        close={() => {
          setPerferModalOpen(false);
        }}></PerferPversionModal>
    </Flex>
  );
};

export default PversionSetting;
