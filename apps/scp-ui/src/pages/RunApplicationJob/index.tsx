import { useState } from 'react';
import { RunPlanCard } from '@repo/ui';
import { useNavigate } from 'react-router-dom';
import { useUpdateEffect } from 'ahooks';
import { useSelector } from 'react-redux';
import { FormField } from '@repo/ui';
import ajax from '@/api';
import _ from 'lodash';

interface TemplateFrameValue {
  key: string;
  title: string;
  remark?: string;
  sourceName: string;
  value: FormField[];
}

const RunApplicationJob = () => {
  const [planTemplateList, setPlanTemplateList] = useState<any[]>([]);
  const [jobFrameValue, setJobFrameValue] = useState<FormField[]>([]);
  const [templateFrameValue, setTemplateFrameValue] = useState<
    TemplateFrameValue[]
  >([]);

  const getPlanTemplate = async () => {
    try {
      const res = await ajax.getJobTemplateByUserOnlyRead({});
      const list = _.get(res, 'data.list', []) || [];
      setPlanTemplateList(
        list.map((item: any) => {
          return {
            label: item.templateName,
            value: item.templateUuid,
          };
        }),
      );
    } catch (error) {
      console.error('错误:', error);
    }
  };

  // 获取计划任务模板
  const getJobTemplateFrameAndValue = async (templateUuid: string) => {
    try {
      const res = await ajax.getJobTemplateFrameAndValue(templateUuid);
      const jobFrameValue = _.get(res, 'data.jobFrameValue', []);
      const templateFrameValue = _.get(res, 'data.templateFrameValue', []);
      setJobFrameValue(jobFrameValue);
      setTemplateFrameValue(templateFrameValue);
    } catch (error) {
      console.error('错误:', error);
    }
  };

  //获取复制的计划任务模板
  const getJobTemplateFrameAndValueByCopy = async (
    templateUuid: string,
    jobId: string,
    seq: number,
  ) => {
    try {
      const res = await ajax.getJobTemplateFrameAndValueForCopy({
        taskUuid: templateUuid,
        jobId,
        seq,
      });
      const jobFrameValue = _.get(res, 'data.jobFrameValue', []);
      const templateFrameValue = _.get(res, 'data.templateFrameValue', []);
      setJobFrameValue(jobFrameValue);
      setTemplateFrameValue(templateFrameValue);
    } catch (error) {
      console.error('错误:', error);
    }
  };

  return (
    <RunPlanCard
      startCronJob={ajax.startCronJob}
      getPlanTemplate={getPlanTemplate}
      getJobTemplateFrameAndValue={getJobTemplateFrameAndValue}
      getJobTemplateFrameAndValueByCopy={getJobTemplateFrameAndValueByCopy}
      useNavigate={useNavigate}
      useUpdateEffect={useUpdateEffect}
      token={useSelector((state: any) => state.login.token)}
      planTemplateList={planTemplateList}
      jobFrameValue={jobFrameValue}
      templateFrameValue={templateFrameValue}
      setPlanTemplateList={setPlanTemplateList}></RunPlanCard>
  );
};

export default RunApplicationJob;
