import { DynamicForm } from '@repo/ui';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Form, Button, App } from 'antd';
import ajax from '@/api';
import _ from 'lodash';

interface DetailContentProps {
  profileUuid: string;
  tagUuid: string;
}

export default function DetailContent({
  tagUuid,
  profileUuid,
}: DetailContentProps) {
  const { message } = App.useApp();
  const [subTagList, setSubTagList] = useState<any[]>([]);

  const token = useSelector((state: any) => state.login.token);
  const getSubTagFrameByTag = async (tagUuid: string) => {
    try {
      const res = await ajax.getSubTagFrameByTag({ profileUuid, tagUuid });
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setSubTagList(list);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const saveCostsByProfileTagId = async (data: any) => {
    try {
      const res = await ajax.saveCostsByProfileTagId(data);
      if (!res.code) {
        message.success('保存成功');
        getSubTagFrameByTag(tagUuid);
      } else {
        message.error(res.msg);
        console.error(res.msg);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const onFinish = (values: any, subTagUuid: string) => {
    const _values = {
      profileUuid,
      tagUuid,
      subTagUuid,
      costItem: Object.values(values).map((item: any) => {
        return { ...item, subTagUuid, profileUuid };
      }),
    };
    saveCostsByProfileTagId(_values);
  };

  useEffect(() => {
    getSubTagFrameByTag(tagUuid);
  }, [tagUuid]);

  return (
    <div className='overflow-auto w-full h-[calc(100vh-228px)]'>
      {subTagList.map((item) => {
        return (
          <div className='p-4' key={item.name + new Date().getTime()}>
            <Form
              onFinish={(values) => {
                onFinish(values, item.tagUuid);
              }}>
              <div className='text-2xl border-b-2 pb-2 mb-2 flex justify-between'>
                {item.label}
                <Button
                  type='link'
                  htmlType='submit'
                  disabled={item.value === null}>
                  保存
                </Button>
              </div>
              <DynamicForm
                fields={item.value || []}
                formName={item.name}
                token={token}
              />
            </Form>
          </div>
        );
      })}
    </div>
  );
}
