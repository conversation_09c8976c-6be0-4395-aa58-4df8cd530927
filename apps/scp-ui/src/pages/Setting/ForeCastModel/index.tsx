import React, { useState, useEffect } from 'react';
import { Card, App, Form, Input, Button, Table, Descriptions } from 'antd';
import { motion } from 'framer-motion';
import { PlusOutlined, LeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useActivate } from 'react-activation';
import { useVoerkaI18n } from '@voerkai18n/react';
import { DeleteButton } from '@repo/ui';
import ajax from '@/api';
import _ from 'lodash';
import AddPage from './AddPage';

const ForeCastModel: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useVoerkaI18n();
  const { message } = App.useApp();
  const [pageData, setPageData] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });

  const [selectedItem, setSelectedItem] = useState<any>(null);

  const [dataSource, setDataSource] = useState([]);

  const deleteModelProfile = async (modelUuid: string) => {
    try {
      const res = await ajax.deleteModelProfile({ modelUuid });
      if (!res.code) {
        message.info(res.msg);
        getForecastModelProfileList();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const columns = [
    {
      key: 'forecastModelName',
      title: t('模型名称'),
      dataIndex: 'forecastModelName',
    },
    {
      key: 'forecastModelDesc',
      title: t('模型描述'),
      dataIndex: 'forecastModelDesc',
    },
    {
      key: 'options',
      title: t('操作'),
      dataIndex: 'options',
      render: (_: any, record: any) => {
        return (
          <div>
            <Button
              type='primary'
              onClick={() => {
                // navigate('/forecastModalAdd', {
                //   state: {
                //     record: record,
                //   },
                // });
                setSelectedItem(record);
              }}>
              {t('查看')}
            </Button>
            <DeleteButton
              type='link'
              deleteTitle={t('是否删除')}
              onClick={() => {
                deleteModelProfile(record.modelUuid);
              }}
            />
          </div>
        );
      },
    },
  ];

  const getForecastModelProfileList = async () => {
    try {
      const res = await ajax.getForecastModelProfileList({
        page: pageData.page,
        pageSize: pageData.pageSize,
      });
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setDataSource(list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const hideDetail = () => {
    setSelectedItem(null);
    setPageData({
      ...pageData,
      page: 1,
    });
  };

  useEffect(() => {
    getForecastModelProfileList();
  }, [pageData]);

  useActivate(() => {
    getForecastModelProfileList();
  });
  return (
    <div className='flex h-full rounded-2xl overflow-hidden'>
      <motion.div
        animate={{ width: selectedItem ? '20%' : '100%' }}
        transition={{ duration: 0.3 }}>
        {selectedItem ? (
          <div className='bg-white h-full'>
            <div className='flex items-center p-4  bg-white'>
              <LeftOutlined
                onClick={hideDetail}
                style={{
                  fontSize: '24px',
                  cursor: 'pointer',
                  marginRight: '8px',
                }}
              />
              <span>{t('选择的信息')}</span>
            </div>
            <Descriptions bordered column={1}>
              <Descriptions.Item label={t('名称')}>
                {selectedItem.forecastModelName}
              </Descriptions.Item>
              <Descriptions.Item label={t('描述')}>
                {selectedItem.forecastModelDesc}
              </Descriptions.Item>
            </Descriptions>
          </div>
        ) : (
          <>
            <header>
              <Card>
                <Form layout='inline'>
                  <Form.Item
                    label={<div className='text-s1'>{t('名称')}</div>}
                    name='name'>
                    <Input />
                  </Form.Item>
                  <div>
                    <Button type='primary' className='mr-4' htmlType='submit'>
                      {t('查询')}
                    </Button>
                    <Button htmlType='reset'>{t('重置')}</Button>
                  </div>
                </Form>
              </Card>
            </header>
            <main>
              <Table
                bordered
                rowKey={'id'}
                title={() => {
                  return (
                    <div className='flex justify-between'>
                      <div className='text-h3'>{t('预测模型列表')}</div>
                      <Button
                        icon={<PlusOutlined />}
                        type='primary'
                        onClick={() => {
                          navigate('/forecastModalAdd');
                        }}>
                        {t('新增预测模型')}
                      </Button>
                    </div>
                  );
                }}
                dataSource={dataSource}
                columns={columns}
                pagination={{
                  position: ['bottomCenter'],
                  current: pageData.page,
                  pageSize: pageData.pageSize,
                  total: pageData.total,
                  onChange: (page, pageSize) => {
                    setPageData({
                      ...pageData,
                      page,
                      pageSize,
                    });
                  },
                }}
              />
            </main>
          </>
        )}
      </motion.div>
      {selectedItem && (
        <motion.div
          className='flex-grow p-4 border-l border-gray-100 h-full bg-white'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}>
          <AddPage record={selectedItem} />
        </motion.div>
      )}
    </div>
  );
};

export default ForeCastModel;
