import React, { useEffect, useState } from 'react';
import {
  App,
  Form,
  Input,
  Button,
  Card,
  Radio,
  Select,
  InputNumber,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import ajax from '@/api';
import { t } from '@/languages';
import _ from 'lodash';

type AddPageProps = {
  record: any;
};
const AddPage: React.FC<AddPageProps> = ({ record }) => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [kfList, setKfList] = useState([]);

  // const record = useLocation().state?.record || {};
  const isEdit = !_.isEmpty(record);

  const onFinish = (values: any) => {
    if (isEdit) {
      const newValues = { ...values, modelUuid: record.modelUuid };
      updateForecastModel(newValues);
    } else {
      createForecastModel(values);
    }
  };

  /**
   * @description 提交接口
   */
  const createForecastModel = async (values: any) => {
    try {
      const res = await ajax.createForecastModelProfile(values);
      if (!res.code) {
        message.success(res.msg);
        navigate('/setting/forecastModel');
      }
    } catch (error) {
      console.error(error);
    }
  };

  const updateForecastModel = async (values: any) => {
    try {
      const res = await ajax.updateModelProfile(values);
      if (!res.code) {
        message.success(res.msg);
        // navigate('/setting/forecastModel');
      }
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * @description 获取关键指标列表
   */
  const getKyeFigureList = async () => {
    try {
      const res = await ajax.getKeyFigureList({});
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setKfList(list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getKyeFigureList();
  }, []);

  return (
    <div style={{ margin: '0 auto' }}>
      <Form
        form={form}
        layout='vertical'
        onFinish={onFinish}
        initialValues={record}>
        {/* 第一部分：概览 */}
        <Card title={t('概览')} style={{ marginBottom: 24 }}>
          <Form.Item
            name='forecastModelName'
            label={t('模型名字')}
            rules={[{ required: true, message: t('请输入模型名字') + '!' }]}>
            <Input placeholder={t('请输入模型名字')} />
          </Form.Item>
          <Form.Item name='forecastModelDesc' label={t('模型描述')}>
            <Input.TextArea placeholder={t('请输入模型描述')} />
          </Form.Item>
        </Card>

        {/* 第二部分：预测模型参数 */}
        <Card title={t('预测模型参数')} style={{ marginBottom: 24 }}>
          {/* 关键指标部分 */}
          <Card type='inner' title={t('关键指标')} style={{ marginBottom: 24 }}>
            <Form.Item
              name='historicalKfId'
              label={t('历史数据关键指标')}
              rules={[
                { required: true, message: t('请输入输入关键指标') + '!' },
              ]}>
              <Select
                options={kfList}
                fieldNames={{
                  value: 'kfId',
                }}
              />
            </Form.Item>
            <Form.Item
              name='forecastKfId'
              label={t('未来预测关键指标')}
              rules={[
                { required: true, message: t('请输入未来预测关键指标') + '!' },
              ]}>
              <Select
                options={kfList}
                fieldNames={{
                  value: 'kfId',
                }}
              />
            </Form.Item>
            <Form.Item
              name='expostKfId'
              label={t('ExPost数据关键指标')}
              rules={[
                {
                  required: true,
                  message: t('请输入ExPost数据关键指标') + '!',
                },
              ]}>
              <Select
                options={kfList}
                fieldNames={{
                  value: 'kfId',
                }}
              />
            </Form.Item>
            <Form.Item
              name='forecastErrorKfId'
              label={t('预测误差数据关键指标')}
              rules={[
                {
                  required: true,
                  message: t('请输入预测误差数据关键指标') + '!',
                },
              ]}>
              <Select
                options={kfList}
                fieldNames={{
                  value: 'kfId',
                }}
              />
            </Form.Item>
          </Card>
          {/* 更新预测算法部分 */}
          <Card type='inner' title={t('预测算法')}>
            {/* 使用单选框替换复选框 */}
            <Form.Item
              name='forecastAlgorithmType'
              label={t('选择预测算法')}
              rules={[{ required: true, message: t('请选择预测算法') }]}>
              <Radio.Group>
                <Radio value={1}>{t('简单平均移动')}</Radio>
                <Radio value={2}>{t('加权平均移动')}</Radio>
              </Radio.Group>
            </Form.Item>
            {/* 根据选择的算法显示对应参数 */}
            <Form.Item
              shouldUpdate={(prev, curr) =>
                prev.forecastAlgorithmType !== curr.forecastAlgorithmType
              }
              noStyle>
              {() => {
                const algorithm = form.getFieldValue('forecastAlgorithmType');
                if (algorithm === 1) {
                  return (
                    <Form.Item
                      name='simpleAvgPeriod'
                      label={
                        t('平均值计算窗口') + '（' + t('简单平均移动') + '）'
                      }
                      rules={[
                        {
                          required: true,
                          message: t('请输入平均值计算窗口') + '!',
                        },
                      ]}>
                      <InputNumber placeholder={t('请输入平均值计算窗口')} />
                    </Form.Item>
                  );
                }
                if (algorithm === 2) {
                  return (
                    <>
                      <Form.Item
                        name='weightedAvgPeriod'
                        label={
                          t('平均值计算窗口') + '（' + t('加权平均移动') + '）'
                        }
                        rules={[
                          {
                            required: true,
                            message: t('请输入平均值计算窗口') + '!',
                          },
                        ]}>
                        <InputNumber placeholder={t('请输入平均值计算窗口')} />
                      </Form.Item>
                      <Form.Item
                        name='weightedAvgCoeType'
                        label={t('加权值')}
                        rules={[
                          { required: true, message: t('请输入加权值') },
                        ]}>
                        <InputNumber placeholder={t('请输入加权值')} />
                      </Form.Item>
                    </>
                  );
                }
                return null;
              }}
            </Form.Item>
          </Card>
        </Card>
        {/* 第三部分：预测后处理 */}
        <Card title={t('预测后处理')} style={{ marginBottom: 24 }}>
          <Form.Item
            name='forecastErrorPostType'
            label={t('模型误差计算方式选择')}
            rules={[
              { required: true, message: t('请选择模型误差计算方式') + '!' },
            ]}>
            <Radio.Group>
              <Radio value={1}>{t('平均绝对百分比误差/MAPE')}</Radio>
              <Radio value={2}>{t('平均百分比误差/MPE')}</Radio>
              <Radio value={3}>{t('均方根误差/RMSE')}</Radio>
            </Radio.Group>
          </Form.Item>
        </Card>
        <Form.Item>
          <Button type='primary' htmlType='submit'>
            {isEdit ? t('保存') : t('提交')}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddPage;
