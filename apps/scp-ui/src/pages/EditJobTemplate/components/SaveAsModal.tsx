import React, { useState } from 'react';
import { Modal, Input, Button, Form, message } from 'antd';
import ajax from '@/api';
import { useVoerkaI18n } from '@voerkai18n/react';
export interface SaveAsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaveAs: (name: string) => void;
}

const SaveAsModal: React.FC<SaveAsModalProps> = ({
  isOpen,
  onClose,
  onSaveAs,
}) => {
  const { t } = useVoerkaI18n();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSaveAs = async () => {
    try {
      const values = await form.validateFields();
      onSaveAs(values.templateName);
      onClose();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const checkoutNameIsAvailable = async () => {
    try {
      const templateName = form.getFieldValue('templateName');
      if (!templateName) {
        message.error('Please input the template name!');
        return;
      }
      setLoading(true);
      const response = await ajax.checkTemplateName(templateName);

      if (response.data.isExists === 'false') {
        message.success('Template name is available!');
      } else {
        form.setFields([
          {
            name: 'templateName',
            errors: ['Template name is already taken!'],
          },
        ]);
      }
    } catch (error) {
      console.error('Error checking template name:', error);
      message.error('Failed to check template name!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={isOpen} onCancel={onClose} footer={null}>
      <header>{t('另存为')}</header>
      <main>
        <Form form={form} onFinish={handleSaveAs}>
          <Form.Item
            label={t('模版名称')}
            name='templateName'
            rules={[{ required: true, message: t('请输入模版名称') }]}>
            <Input />
          </Form.Item>
          <Button onClick={checkoutNameIsAvailable} loading={loading}>
            {t('检查模版名字是否可用')}
          </Button>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              Save
            </Button>
          </Form.Item>
        </Form>
      </main>
    </Modal>
  );
};

export default SaveAsModal;
