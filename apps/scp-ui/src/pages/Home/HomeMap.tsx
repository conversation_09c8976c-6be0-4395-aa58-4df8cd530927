import { Map } from '@/components';
import * as mapboxgl from 'maplibre-gl';
import { CustomerData } from './index';
import Marker from '@/components/Map/Marker';
const HomeMap: React.FC<{
  initMap: (map: mapboxgl.Map, marker: Marker) => void;
  pointData: CustomerData[];
}> = ({ initMap, pointData }) => {
  return (
    <div className='h-full w-full relative'>
      {/* <div className=' absolute z-10 top-0 left-0 w-1/5 h-1/2 bg-white m-4 rounded-lg'>
        <header className=' border-b border-b-slate-200 h-1/6 flex items-center bg-slate-200 rounded-lg justify-center'>
          <h1 className='text-center text-sm '>地图标识</h1>
        </header>
        <main className='w-4/5 flex justify-center'>
          <ul className='flex flex-col items-center justify-between h-full'>
            <li className='flex items-center justify-between w-full h-1/6 '>
              <img
                className='h-10'
                src='/map/greenMapMarker.png'
                alt='success'
              />
              <span className='text-sm'>按时交付</span>
            </li>
            <li className='flex items-center justify-between w-full h-1/6 '>
              <img
                className='h-10'
                src='/map/warnningMapMarker.png'
                alt='warnning'
              />

              <span className='text-sm'>推迟交付</span>
            </li>
            <li className='flex items-center justify-between w-full h-1/6 '>
              <img
                className='h-10'
                src='/map/dangerMapMarker.png'
                alt='danger'
              />
              <span className='text-sm'>未交付</span>
            </li>
          </ul>
        </main>
      </div> */}

      <Map
        setMap={(map, marker) => {
          initMap(map, marker);
        }}
        data={pointData}
      />
    </div>
  );
};

export default HomeMap;
