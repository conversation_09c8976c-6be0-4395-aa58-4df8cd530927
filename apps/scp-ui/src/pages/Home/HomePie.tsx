import { useEffect, useState } from 'react';
import ReactEcharts from 'echarts-for-react';

const getOption = (data: any, type: string) => {
  switch (type) {
    case 'bar':
      return {
        xAxis: {
          data: Object.keys(data),
        },
        yAxis: {},
        series: [
          {
            type: 'bar',
            data: Object.values(data),
          },
        ],
      };
    case 'line':
      return {
        xAxis: {
          type: 'category',
          data: Object.keys(data),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: Object.values(data),
            type: 'line',
          },
        ],
      };
    case 'pie':
      return {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
        },
        legend: {
          bottom: 10,
          left: 'center',
          textStyle: {
            color: '#718EBF',
          },
          itemWidth: 14,
        },
        series: [
          {
            color: ['#1890ff', '#2f6bff', '#0c40de'],
            name: '访问来源',
            type: 'pie',
            radius: '70%',
            avoidLabelOverlap: false,
            labelLine: {
              show: false,
            },
            label: {
              normal: {
                position: 'inside',
                show: true,
                formatter: '{b} {c}',
                color: '#F5F7FA',
              },
            },
            data: Object.entries(data).map(([name, value]) => ({
              value: value,
              name: name,
            })),
          },
        ],
      };
  }
};

type HomePieProps = {
  data: any;
  type?: string;
};

const HomePie: React.FC<HomePieProps> = ({ data, type = 'pie' }) => {
  const fakeData = {
    客户1: 15,
    客户2: 25,
    客户3: 35,
  };

  const [xData, setXData] = useState<any[]>([]);

  const init = (data: any) => {
    const temp: any[] = [];
    data.forEach((item: any) => {
      const index = temp.findIndex((i) => i.customer === item.name);
      if (index === -1) {
        temp.push({
          name: item.customer,
          value: item.amount,
        });
      } else {
        temp[index].value += item.amount;
      }
      setXData(temp);
    });
  };

  useEffect(() => {
    console.log(xData);

    if (data.length === 0) return;
    init(data);
  }, [data]);

  return (
    <ReactEcharts
      option={getOption(fakeData, type) as any}
      style={{ height: '100%' }}
    />
  );
};
export default HomePie;
