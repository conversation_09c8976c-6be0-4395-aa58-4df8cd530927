import { useState, useEffect } from 'react';
import { Splitter, Button } from 'antd';
import SelectModal from '../SelectModal';
import { useSelector } from 'react-redux';
import { t } from '@/languages';

interface HomeSplitterProps {
  id?: any;
  layout?: any;
  content?: any;
  contentMeta?: any;
  splitTree: any;
  setSplitTree: any;
}

const HomeSplitter = (props: HomeSplitterProps) => {
  const { id, layout, content, contentMeta, splitTree, setSplitTree } = props;

  const editable = useSelector((state: any) => state.home.editable);

  const filterLeaves = splitTree.filter((item: any) => item.pid === id);
  const children =
    filterLeaves.length === 0
      ? content
      : filterLeaves.map((item: any) => {
          return (
            <Splitter.Panel
              key={item.id}
              resizable={editable}
              size={item.size + '%'}>
              <HomeSplitter
                id={item.id}
                layout={item.layout}
                content={item.content}
                contentMeta={item.contentMeta}
                splitTree={splitTree}
                setSplitTree={setSplitTree}></HomeSplitter>
            </Splitter.Panel>
          );
        });

  const getIndex = () => {
    return splitTree.findIndex((item: any) => item.id === id);
  };
  const getOnlyLeafIndex = () => {
    return splitTree.findIndex((item: any) => item.id === filterLeaves[0].id);
  };

  const [selectModalOpen, setSelectModalOpen] = useState<boolean>(false);

  useEffect(() => {
    if (filterLeaves.length === 1 && getIndex() >= 0) {
      const onlyLeafIndex = getOnlyLeafIndex();
      splitTree[getIndex()] = {
        ...filterLeaves[0],
        pid: splitTree[getIndex()].pid,
      };
      splitTree.splice(onlyLeafIndex, 1);
      setSplitTree([...splitTree]);
    }
  }, [filterLeaves]);

  return (
    <div
      style={{
        position: 'relative',
        height: '100%',
        width: '100%',
      }}>
      <Splitter
        layout={layout}
        onResize={(sizes: any) => {
          const sum = sizes.reduce((acc: any, cur: any) => acc + cur, 0);
          const percentC1 = (sizes[0] / sum) * 100;
          const percentC2 = (sizes[1] / sum) * 100;
          const indexC1 = splitTree.findIndex(
            (item: any) => item.id === filterLeaves[0].id,
          );
          const indexC2 = splitTree.findIndex(
            (item: any) => item.id === filterLeaves[1].id,
          );
          splitTree[indexC1].size = percentC1;
          splitTree[indexC2].size = percentC2;
          setSplitTree([...splitTree]);
        }}>
        {children}
      </Splitter>
      <Button
        style={{
          position: 'absolute',
          left: '50%',
          transform: 'translateX(-50%)',
          top: 5,
          width: 40,
          height: 20,
          lineHeight: '10px',
          padding: '0',
          display: editable && filterLeaves.length === 0 ? 'block' : 'none',
        }}
        onClick={() => {
          splitTree[getIndex()].layout = 'vertical';
          splitTree.push({
            id: Date.now() + 'c1',
            pid: id,
            layout: null,
            size: 50,
            content: null,
            contentMeta: null,
          });
          splitTree.push({
            id: Date.now() + 'c2',
            pid: id,
            layout: null,
            size: 50,
            ...(content
              ? {
                  content: content,
                  contentMeta: splitTree[getIndex()].contentMeta,
                }
              : {
                  content: null,
                  contentMeta: null,
                }),
          });
          setSplitTree([...splitTree]);
        }}>
        {'+'}
      </Button>
      <Button
        style={{
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          left: 5,
          width: 20,
          height: 40,
          padding: '0',
          display: editable && filterLeaves.length === 0 ? 'block' : 'none',
        }}
        onClick={() => {
          const index = splitTree.findIndex((item: any) => item.id === id);
          splitTree[index].layout = 'horizontal';
          splitTree.push({
            id: Date.now() + 'c1',
            pid: id,
            layout: null,
            size: 50,
            content: null,
            contentMeta: null,
          });
          splitTree.push({
            id: Date.now() + 'c2',
            pid: id,
            layout: null,
            size: 50,
            ...(content
              ? {
                  content: content,
                  contentMeta: splitTree[getIndex()].contentMeta,
                }
              : {
                  content: null,
                  contentMeta: null,
                }),
          });
          setSplitTree([...splitTree]);
        }}>
        {'+'}
      </Button>
      <Button
        style={{
          position: 'absolute',
          top: 5,
          right: 5,
          display:
            (editable && filterLeaves.length === 0 && splitTree.length > 1) ||
            (splitTree.length === 1 && content)
              ? 'block'
              : 'none',
        }}
        onClick={() => {
          splitTree.length === 1 && content
            ? ((splitTree[0].content = null),
              (splitTree[0].contentMeta = null),
              setSplitTree([...splitTree]))
            : (splitTree.splice(getIndex(), 1), setSplitTree([...splitTree]));
        }}>
        {'X'}
      </Button>
      <Button
        style={{
          position: 'absolute',
          bottom: 5,
          left: '50%',
          transform: 'translateX(-50%)',
          width: 'auto',
          height: 20,
          lineHeight: '15px',
          padding: '0 5px',
          display: editable && filterLeaves.length === 0 ? 'block' : 'none',
        }}
        onClick={() => {
          setSelectModalOpen(true);
        }}>
        {t('选择组件')}
      </Button>
      <SelectModal
        ref={(ref: any) => {
          if (ref && ref.render && contentMeta && !content) {
            splitTree[getIndex()].content = <div>{ref.render()}</div>;
            setSplitTree([...splitTree]);
          }
        }}
        open={selectModalOpen}
        setOpen={setSelectModalOpen}
        render={(component: any, contentMeta: any) => {
          splitTree[getIndex()] = {
            ...splitTree[getIndex()],
            content: <div>{component}</div>,
            contentMeta: contentMeta,
          };
          setSplitTree([...splitTree]);
        }}
        contentMeta={splitTree[getIndex()].contentMeta}></SelectModal>
    </div>
  );
};

export default HomeSplitter;
