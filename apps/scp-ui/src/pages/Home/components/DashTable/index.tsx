import { useState, useEffect, useRef } from 'react';
import { ConfigProvider, Table, App } from 'antd';
import { useSelector } from 'react-redux';
import { useSize } from 'ahooks';
import { t } from '@/languages';
import ajax from '@/api';

interface DashTableProps {}

interface CustomerData {
  customer: string;
  quantity: number;
  customerGroup: string;
  matnr: string;
  material_category: string;
  amount: number;
  lng: number;
  lat: number;
  address: string;
  turnover: string;
  qty_in_time: string;
  qty_delay: string;
  qty_nondel: string;
}

const DashTable = (props: DashTableProps) => {
  props;
  const { message } = App.useApp();

  const user = useSelector((state: any) => state.login.user);
  const pVersionList = useSelector((state: any) => state.layout.pVersionList);

  const baseVersion = pVersionList?.find(
    (item: any) => item.isBaseVersion,
  ).value;

  const columns = [
    {
      title: t('客户'),
      dataIndex: 'customer',
      key: 'customer',
      ellipsis: true,
    },
    {
      title: t('客户分组'),
      dataIndex: 'customerGroup',
      key: 'customerGroup',
      ellipsis: true,
    },
    {
      title: t('地址'),
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
      render: (text: string) => (
        <span className='text-blue-500 cursor-pointer'>{text}</span>
      ),
    },
    {
      title: t('营业额'),
      dataIndex: 'amount',
      key: 'amount',
      ellipsis: true,
    },
    {
      title: t('按时交付'),
      dataIndex: 'qty_in_time',
      key: 'qty_in_time',
      ellipsis: true,
      render: (text: string) => (
        <span className={`${parseInt(text) > 0 ? 'text-success' : ''}`}>
          {text}
        </span>
      ),
    },
    {
      title: t('推迟交付'),
      dataIndex: 'qty_delay',
      key: 'qty_delay',
      ellipsis: true,
      render: (text: string) => (
        <span className={`${parseInt(text) > 0 ? 'text-warning' : ''}`}>
          {text}
        </span>
      ),
    },
    {
      title: t('未交付'),
      dataIndex: 'qty_nondel',
      key: 'qty_nondel',
      ellipsis: true,
      render: (text: string) => (
        <span className={`${parseInt(text) > 0 ? 'text-error' : ''}`}>
          {text}
        </span>
      ),
    },
  ];

  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<CustomerData[]>([]);

  const getCustomerData = async () => {
    try {
      setLoading(true);
      const paPvId = Number(user?.preferPaPvId) || baseVersion;

      const [res, coreRes, hotRes] = await Promise.all([
        ajax.getCustomerData({ paPvId }),
        ajax.getCoreData({ paPvId }),
        ajax.getDetPegData({ paPvId }),
      ]);

      if (res.code !== 0 || coreRes.code !== 0 || hotRes.code !== 0) {
        message.error('获取数据失败');
        return;
      }
      const { list } = res.data;
      const _tableData = list?.map((item: any) => {
        const [lng, lat] = item.cus_lng_lat.split(',');
        return {
          ...item,
          customer: item.customer,
          customerGroup: item.cus_group || '',
          lng: Number(lng),
          lat: Number(lat),
          address: item.locno,
          turnover: item.turnover || '',
          qty_in_time: item.qty_in_time,
        } as CustomerData;
      });
      setTableData(_tableData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCustomerData();
  }, []);

  const containerRef = useRef<any>();
  const containerSize = useSize(containerRef);

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            borderColor: '#EFF2F3',
            headerBg: '#ffffff',
            headerColor: '#293147',
            headerSplitColor: '#ffffff',
            cellFontSize: 16,
            cellPaddingBlock: (containerSize?.height || 150) > 500 ? 20 : 9,
          },
          Pagination: {
            colorPrimary: '#0C40DE',
            colorPrimaryHover: '#1890FF',
          },
        },
      }}>
      <div style={{ height: '100%' }} ref={containerRef}>
        <Table
          style={{ height: '100%' }}
          columns={columns}
          loading={loading}
          dataSource={tableData}
          rowKey={(_: any, index: any) => index as React.Key}
          scroll={{ y: (containerSize?.height || 120) - 120 }}
          pagination={{
            total: tableData.length,
            pageSize: 10,
            position: ['bottomCenter'],
          }}
        />
      </div>
    </ConfigProvider>
  );
};

export default DashTable;
