import ReactEcharts from 'echarts-for-react';
import { t } from '@/languages';

interface PieChartProps {
  chart: any;
}

const PieChart = ({ chart }: PieChartProps) => {
  const { data } = chart;

  const option: any = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      bottom: 10,
      left: 'center',
      textStyle: {
        color: '#718EBF',
      },
      itemWidth: 14,
    },
    series: [
      {
        color: ['#1890ff', '#2f6bff', '#0c40de'],
        name: t('访问来源'),
        type: 'pie',
        radius: '70%',
        avoidLabelOverlap: false,
        labelLine: {
          show: false,
        },
        label: {
          normal: {
            position: 'inside',
            show: true,
            formatter: '{b} {c}',
            color: '#F5F7FA',
          },
        },
        data: Object.entries(data).map(([name, value]) => ({
          value: value,
          name: t(name),
        })),
      },
    ],
  };

  return <ReactEcharts option={option} style={{ height: '100%' }} />;
};

export default Pie<PERSON>hart;
