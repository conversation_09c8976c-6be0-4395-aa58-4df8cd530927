import ReactEcharts from 'echarts-for-react';
import { t } from '@/languages';

interface BarChartProps {
  chart: any;
}

const BarChart = ({ chart }: BarChartProps) => {
  const { data } = chart;

  const group = Object.keys(chart).includes('dataGroup');

  const option: any = {
    ...(group
      ? {
          legend: {
            data: chart.dataGroup.map((item: any) => t(item)),
            left: 'center',
            bottom: 10,
            textStyle: {
              color: '#718EBF',
            },
            itemWidth: 14,
          },
        }
      : {
          legend: {},
        }),
    tooltip: {
      trigger: 'item',
      formatter: group ? '{a} <br/>{b} : {c}' : '{b} : {c}',
    },
    grid: {
      left: '20%',
      top: '20%',
    },
    xAxis: [
      {
        type: 'category',
        data: Object.keys(data).map((item) => t(item)),
        axisTick: {
          alignWithLabel: true,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: t('数量(个)'),
        position: 'left',
      },
    ],
    series: Array.isArray(Object.values(data)[0])
      ? (Object.values(data)[0] as any[]).map((_, index) => ({
          ...(group
            ? {
                name: t(chart.dataGroup[index]),
              }
            : {}),
          type: 'bar',
          barWidth: 15,
          data: Object.values(data).map((item: any) => item[index]),
          itemStyle: {
            barBorderRadius: [7, 7, 0, 0],
          },
        }))
      : {
          ...(group ? { name: chart.dataGroup } : {}),
          type: 'bar',
          barWidth: 15,
          data: Object.values(data),
          itemStyle: {
            barBorderRadius: [7, 7, 0, 0],
          },
        },
  };

  return (
    <ReactEcharts option={option} style={{ height: '100%' }} notMerge={true} />
  );
};

export default BarChart;
