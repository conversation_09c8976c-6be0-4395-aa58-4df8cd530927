import { Flex } from 'antd';
import <PERSON><PERSON><PERSON> from '../PieChart';
import <PERSON><PERSON><PERSON> from '../BarChart';
import { t } from '@/languages';

interface ChartCardProps {
  chart: any;
}

const ChartCard = ({ chart }: ChartCardProps) => {
  const { title, type } = chart;

  return (
    <Flex
      vertical={true}
      style={{
        position: 'absolute',
        height: '100%',
        width: '100%',
        padding: '20px',
      }}>
      <span
        style={{
          fontSize: '22px',
          fontWeight: 500,
          color: '#0d152d',
          padding: '10px 0',
        }}>
        {t(title)}
      </span>
      <div
        style={{
          background: '#fff',
          position: 'relative',
          borderRadius: '16px',
          flex: 1,
        }}>
        {(() => {
          switch (type) {
            case 'pie':
              return <PieChart chart={chart} />;
            case 'bar':
              return <BarChart chart={chart} />;
          }
        })()}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '16px',
            cursor: 'pointer',
          }}></div>
      </div>
    </Flex>
  );
};

export default ChartCard;
