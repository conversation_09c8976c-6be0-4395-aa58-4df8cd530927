import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Modal, Table, Switch, Select, Flex } from 'antd';
import { useSelector } from 'react-redux';
import ChartCard from '../ChartCard';
import DashTable from '../DashTable';
import { t } from '@/languages';

interface SelectModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  render: (component: any, contentMeta?: any) => void;
  contentMeta: any;
}

const SelectModal = forwardRef((props: SelectModalProps, ref: any) => {
  const { open, setOpen, render, contentMeta } = props;
  const [meta, setMeta] = useState<any>(null);

  const charts = useSelector((state: any) => state.home.charts);
  const tables = useSelector((state: any) => state.home.tables);

  useEffect(() => {
    setMeta(contentMeta);
  }, [contentMeta]);

  const getConponent = (meta: any) => {
    switch (meta.type) {
      case 'chart':
        return <ChartCard chart={charts[meta.key]} />;
      case 'table':
        return <DashTable />;
    }
  };

  useImperativeHandle(
    ref,
    () => ({
      render: () => {
        if (contentMeta) {
          return getConponent(contentMeta);
        }
      },
    }),
    [contentMeta],
  );

  const Options = [
    {
      label: t('图表'),
      value: 'chart',
    },
    {
      label: t('表格'),
      value: 'table',
    },
  ];

  const [currentType, setCurrentType] = useState(
    contentMeta ? contentMeta.type : 'chart',
  );

  const chartColumns = [
    {
      title: t('图表名称'),
      dataIndex: 'title',
    },
    {
      title: t('图表类型'),
      dataIndex: 'type',
      render: (value: any, record: any) => {
        return t(record.typeOptions[value]);
      },
    },
    {
      title: t('图表显示'),
      dataIndex: 'display',
      render: (_: any, record: any) => {
        return (
          <Switch
            checked={meta && meta.type === 'chart' && meta.key === record.key}
            onChange={(checked) => {
              if (checked) {
                setMeta({
                  type: 'chart',
                  key: record.key,
                });
              } else {
                setMeta(undefined);
              }
            }}></Switch>
        );
      },
    },
  ];

  const chartDataSource = charts.map((chart: any) => {
    return {
      key: chart.key,
      title: t(chart.title),
      typeOptions: chart.typeOptions,
      type: chart.type,
    };
  });

  const tableColumns = [
    {
      title: t('表格名称'),
      dataIndex: 'title',
    },
    {
      title: t('表格显示'),
      dataIndex: 'display',
      render: (_: any, record: any) => {
        return (
          <Switch
            checked={meta && meta.type === 'table' && meta.key === record.key}
            onChange={(checked) => {
              if (checked) {
                setMeta({
                  type: 'table',
                  key: record.key,
                });
              } else {
                setMeta(undefined);
              }
            }}></Switch>
        );
      },
    },
  ];

  const tableDataSource = tables.map((table: any) => {
    return {
      key: table.key,
      title: t(table.title),
    };
  });

  return (
    <Modal
      title={
        <Flex align='center' gap={'10px'}>
          <span>{t('选择组件')}</span>
          <Select
            options={Options}
            defaultValue={currentType}
            onChange={(value) => {
              setCurrentType(value);
            }}
            style={{
              width: 100,
            }}></Select>
        </Flex>
      }
      forceRender={true}
      open={open}
      onCancel={() => setOpen(false)}
      centered={true}
      onOk={() => {
        if (!meta) {
          render(null);
          setOpen(false);
          return;
        }
        render(getConponent(meta), meta);
        setOpen(false);
      }}>
      <Table
        ref={ref}
        columns={currentType === 'chart' ? chartColumns : tableColumns}
        dataSource={currentType === 'chart' ? chartDataSource : tableDataSource}
        pagination={false}></Table>
    </Modal>
  );
});

export default SelectModal;
