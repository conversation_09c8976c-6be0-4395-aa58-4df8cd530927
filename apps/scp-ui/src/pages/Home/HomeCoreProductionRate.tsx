import ReactEcharts from 'echarts-for-react';
import { useEffect } from 'react';

type HomeCoreProductionRateProps = {
  data: any;
};
const HomeCoreProductionRate: React.FC<HomeCoreProductionRateProps> = ({
  data,
}) => {
  useEffect(() => {
    if (data.length === 0) return;
    // init(data);
  }, [data]);

  const option: any = {
    // color: ["#D5D7DD", "#2F6BFF", "#0C40DE"],
    color: ['#2F6BFF'],
    radius: '120%',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}',
    },
    grid: {
      left: '20%',
      top: '10%',
      bottom: '20%',
    },
    xAxis: {
      type: 'category',
      data: ['TR_长1', 'TR_珠1', 'TR_长2', 'TR_珠2', 'TR_长3'],
    },
    yAxis: {},
    series: [
      {
        data: [120, 200, 150, 80, 70],
        barWidth: 15,
        itemStyle: {
          barBorderRadius: [7, 7, 0, 0],
        },
        type: 'bar',
      },
    ],
  };
  return <ReactEcharts option={option} style={{ height: '100%' }} />;
};
export default HomeCoreProductionRate;
