import React from 'react';
import ReactEcharts from 'echarts-for-react';
import { CustomerData } from './index';
import { useEffect, useState } from 'react';

type HomeProductionBarProps = {
  data: any;
};
const HomeProductionBar: React.FC<HomeProductionBarProps> = React.memo(
  ({ data }) => {
    const [xData, setXData] = useState<string[]>([]);
    const init = (data: CustomerData[]) => {
      const temp: any[] = [];
      data.forEach((item: CustomerData) => {
        const index = temp.findIndex(
          (i) => i.material_category === item.material_category,
        );
        if (index === -1) {
          temp.push({
            material_category: item.material_category,
            qty_delay: item.qty_delay,
            qty_in_time: item.qty_in_time,
            qty_nondel: item.qty_nondel,
          });
        } else {
          temp[index].qty_delay += item.qty_delay;
          temp[index].qty_in_time += item.qty_in_time;
          temp[index].qty_nondel += item.qty_nondel;
        }
      });
      const xData = temp.map((item: any) => item.material_category);
      // const yData1 = temp.map((item: any) => item.qty_in_time);
      // const yData2 = temp.map((item: any) => item.qty_delay);
      // const yData3 = temp.map((item: any) => item.qty_nondel);
      setXData(xData);
      // setYData1(yData1);
      // setYData2(yData2);
      // setYData3(yData3);
    };
    useEffect(() => {
      if (data.length === 0) return;
      init(data);
    }, [data]);

    const option: any = {
      color: ['#26cc68', '#ffd565', '#f25445'],
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}',
      },
      // legend: {
      //   data: ['按时交付', '推迟交付', '未交付'],
      //   left: 'center',
      //   bottom: 10,
      //   textStyle: {
      //     color: '#718EBF',
      //   },
      //   itemWidth: 14,
      // },
      grid: {
        left: '20%',
        top: '20%',
      },
      xAxis: [
        {
          type: 'category',
          data: xData,
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '数量(个)',
          position: 'left',
          // axisLabel: {
          //   formatter: "{value} 个"
          // }
        },
      ],

      series: [
        {
          name: '按时交付',
          type: 'bar',
          barWidth: 15,
          data: [15, 9, 26],
          itemStyle: {
            barBorderRadius: [7, 7, 0, 0],
          },
        },
        {
          name: '推迟交付',
          type: 'bar',
          barWidth: 15,
          data: [12, 17, 21],
          itemStyle: {
            barBorderRadius: [7, 7, 0, 0],
          },
        },
        {
          name: '未交付',
          type: 'bar',
          barWidth: 15,
          data: [24, 7, 18],
          itemStyle: {
            barBorderRadius: [7, 7, 0, 0],
          },
        },
      ],
    };

    return <ReactEcharts option={option} style={{ height: '100%' }} />;
  },
);
export default HomeProductionBar;
