import ReactEcharts from 'echarts-for-react';
import { useEffect } from 'react';

type HomeHotPicProps = {
  data: any;
};
const HomeHotPic: React.FC<HomeHotPicProps> = ({ data }) => {
  // const hours = data[1].map((item: any) => item.name);
  // const days = data[0].map((item: any) => item.name);
  // const [matrial, setMatrial] = useState<string[]>([]);
  // const [customer, setCustomer] = useState<string[]>([]);
  // const temp = data[1].map((item: any) => {});
  // const [hotData, setHotData] = useState<any[]>([]);
  // function processData(data: any[]) {
  //   if (data === null) return [[], [], []];
  //   const customers: string[] = [];
  //   const materials: string[] = [];
  //   const coordinates: [number, number, number][] = [];
  //   const materialTotals: { [key: string]: number } = {};
  //
  //   data.forEach((item) => {
  //     const { s_matnr, total_quantity, customer } = item;
  //
  //     if (!customers.includes(customer)) {
  //       customers.push(customer);
  //     }
  //
  //     if (!materials.includes(s_matnr)) {
  //       materials.push(s_matnr);
  //     }
  //
  //     // 计算每种材料的总数量
  //     if (materialTotals[s_matnr]) {
  //       materialTotals[s_matnr] += total_quantity;
  //     } else {
  //       materialTotals[s_matnr] = total_quantity;
  //     }
  //   });
  //
  //   data.forEach((item) => {
  //     const { s_matnr, total_quantity, customer } = item;
  //     // 计算每个客户对每种材料的占比
  //     const ratio = total_quantity / materialTotals[s_matnr];
  //     coordinates.push([
  //       materials.indexOf(s_matnr),
  //       customers.indexOf(customer),
  //       ratio,
  //     ]);
  //   });
  //
  //   return [customers, materials, coordinates];
  // }
  useEffect(() => {
    if (data.length === 0) return;
    // const [customer, matrial, hotData] = processData(data);
    // setCustomer(customer as string[]);
    // setMatrial(matrial as string[]);
    // setHotData(hotData);
  }, [data]);

  const option: any = {
    color: ['#26CC68', '#1890FF'],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}',
    },
    legend: {
      data: ['客户1', '客户2'],
      left: 'center',
      bottom: 10,
      textStyle: {
        color: '#718EBF',
      },
      itemWidth: 14,
    },
    grid: {
      left: '15%',
      top: '15%',
    },
    xAxis: [
      {
        type: 'category',
        data: ['半成品1', '半成品2', '半成品3'],
        axisTick: {
          alignWithLabel: true,
          length: 1,
        },
      },
    ],
    yAxis: {},

    series: [
      {
        name: '客户1',
        type: 'bar',
        barWidth: 15,
        data: [15, 9, 13],
        itemStyle: {
          barBorderRadius: [7, 7, 0, 0],
        },
      },
      {
        name: '客户2',
        type: 'bar',
        barWidth: 15,
        data: [12, 17, 28],
        itemStyle: {
          barBorderRadius: [7, 7, 0, 0],
        },
      },
    ],
  };
  return (
    <ReactEcharts option={option} style={{ height: '100%', width: '100%' }} />
  );
};

export default HomeHotPic;
