import { Table, Flex, Button, Input, Select, Form, Modal, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

import { useSelector } from 'react-redux';
import {
  ReloadOutlined,
  SettingOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { setCharts } from '@/store/features/homeSlice';
import { setEditable } from '@/store/features/homeSlice';
import HomeSplitter from './components/HomeSplitter';
import store from '@/store';

export interface CustomerData {
  customer: string;
  quantity: number;
  customerGroup: string;
  matnr: string;
  material_category: string;
  amount: number;
  lng: number;
  lat: number;
  address: string;
  turnover: string;
  qty_in_time: string;
  qty_delay: string;
  qty_nondel: string;
}

export interface MessageProps {
  title: string;
  content: string;
  fromUser: string;
  toUser: string;
  isRead: boolean;
  time: string;
}

const Home: React.FC = () => {
  // throw new Error('Function not implemented.');

  const [homeSetting, setHomeSetting] = useState(false);

  const [form] = useForm();
  const [formDisabled, setFormDisabled] = useState(true);
  const [formTypeOptions, setFormTypeOptions] = useState<any>({});
  const [formEditKey, setFormEditKey] = useState<number>(0);

  const resetForm = () => {
    form.resetFields();
    setFormDisabled(true);
    setFormTypeOptions({});
    setFormEditKey(0);
  };

  const processForm = () => {
    const { name, type } = form.getFieldsValue();
    store.dispatch(
      setCharts(
        charts.map((item: any, index: number) => {
          if (index === formEditKey) {
            return {
              ...item,
              title: name,
              type: type,
            };
          } else {
            return item;
          }
        }),
      ),
    );
    resetForm();
  };

  const charts = useSelector((state: any) => state.home.charts);

  const [splitTree, setSplitTree] = useState<any[]>([
    {
      id: 1,
      pid: 0,
      layout: 'horizontal',
      content: null,
      contentMeta: null,
    },
    {
      id: 2,
      pid: 1,
      layout: 'vertical',
      size: 70,
      content: null,
      contentMeta: null,
    },
    {
      id: 3,
      pid: 1,
      layout: null,
      size: 30,
      content: null,
      contentMeta: {
        type: 'chart',
        key: charts[1].key,
      },
    },
    {
      id: 4,
      pid: 2,
      layout: null,
      size: 50,
      content: null,
      contentMeta: {
        type: 'table',
        key: charts[0].key,
      },
    },
    {
      id: 5,
      pid: 2,
      layout: 'horizontal',
      size: 50,
      content: null,
      contentMeta: null,
    },
    {
      id: 6,
      pid: 5,
      layout: null,
      size: 50,
      content: null,
      contentMeta: {
        type: 'chart',
        key: charts[2].key,
      },
    },
    {
      id: 7,
      pid: 5,
      layout: null,
      size: 50,
      content: null,
      contentMeta: {
        type: 'chart',
        key: charts[3].key,
      },
    },
  ]);

  const user = useSelector((state: any) => state.login.user);

  const saveLayout = () => {
    const uuid = user.uuid;
    localStorage.setItem(
      'layout-' + uuid,
      JSON.stringify(
        splitTree.map((item: any) => ({
          ...item,
          content: null,
        })),
      ),
    );
  };

  const getLayout = () => {
    const uuid = user.uuid;
    const layout = localStorage.getItem('layout-' + uuid);
    if (layout) {
      setSplitTree(JSON.parse(layout));
    }
  };

  useEffect(() => {
    getLayout();
  }, []);

  const editable = useSelector((state: any) => state.home.editable);

  const [componentSettingLoading, setComponentSettingLoading] = useState(false);

  return (
    <Flex
      vertical={true}
      gap={5}
      style={{
        width: '100%',
        height: '100%',
      }}>
      <Flex vertical={false} justify={'space-between'}>
        <span
          style={{
            fontFamily: 'PingFang SC',
            fontSize: 22,
            fontWeight: 500,
          }}>
          {t('首页')}
        </span>
        <Flex gap={5}>
          <Button
            icon={<SettingOutlined />}
            color='primary'
            variant={homeSetting ? 'outlined' : undefined}
            onClick={() => {
              setHomeSetting(!homeSetting);
            }}></Button>
          <Button
            icon={<EditOutlined />}
            color='primary'
            variant={editable ? 'outlined' : undefined}
            onClick={() => {
              if (editable) [saveLayout()];
              store.dispatch(setEditable(!editable));
            }}></Button>
          <Button
            icon={<ReloadOutlined />}
            color='primary'
            onClick={() => {
              setSplitTree(
                splitTree.map((item: any) => ({
                  ...item,
                  content: null,
                })),
              );
            }}></Button>
        </Flex>
      </Flex>
      <div
        style={{
          flex: 1,
          backgroundColor: '#fff',
          borderRadius: '20px',
          position: 'relative',
          overflow: 'hidden',
        }}>
        <HomeSplitter
          id={splitTree[0].id}
          layout={splitTree[0].layout}
          content={splitTree[0].content}
          contentMeta={splitTree[0].contentMeta}
          splitTree={splitTree}
          setSplitTree={setSplitTree}></HomeSplitter>
      </div>
      <Modal
        open={homeSetting}
        onCancel={() => {
          resetForm();
          setHomeSetting(false);
        }}
        onOk={() => {
          if (
            Object.keys(form.getFieldsValue()).every((key) =>
              form.getFieldValue(key),
            )
          ) {
            processForm();
          }
          setHomeSetting(false);
        }}
        title={t('设置')}
        centered={true}
        width={'auto'}>
        <Flex vertical={false}>
          <Flex vertical={true}>
            <Table
              columns={[
                {
                  title: t('图表名称'),
                  dataIndex: 'title',
                },
                {
                  title: t('图表类型'),
                  dataIndex: 'type',
                  render: (value, record) => {
                    return t(record.typeOptions[value]);
                  },
                },
                {
                  title: t('操作'),
                  dataIndex: 'action',
                  render: (_: any, record: any) => {
                    return (
                      <Flex vertical={false} gap={5}>
                        <Button
                          onClick={() => {
                            form.setFieldsValue({
                              name: record.title,
                              type: record.type,
                            });
                            setFormTypeOptions(record.typeOptions);
                            setFormDisabled(false);
                            setFormEditKey(record.key);
                            setComponentSettingLoading(true);
                            setTimeout(() => {
                              setComponentSettingLoading(false);
                            }, 200);
                          }}>
                          {t('编辑')}
                        </Button>
                        <Button danger={true}>{t('删除')}</Button>
                      </Flex>
                    );
                  },
                },
              ]}
              dataSource={charts.map((chart: any) => {
                return {
                  key: chart.key,
                  title: t(chart.title),
                  typeOptions: chart.typeOptions,
                  type: chart.type,
                  display: chart.display,
                };
              })}
              pagination={false}></Table>
          </Flex>
          <Flex
            vertical={true}
            flex={1}
            style={{
              position: 'relative',
            }}>
            <Form
              form={form}
              disabled={formDisabled}
              style={{
                flex: 1,
              }}>
              <Form.Item noStyle>
                <Flex
                  vertical={true}
                  style={{
                    padding: '16px',
                  }}
                  gap={10}
                  align='center'>
                  <Flex gap={10} align='center'>
                    <span
                      style={{
                        width: '80px',
                      }}>
                      {t('图表名称') + ' :'}
                    </span>
                    <Form.Item noStyle name={'name'}>
                      <Input
                        style={{
                          width: '200px',
                        }}></Input>
                    </Form.Item>
                  </Flex>
                  <Flex gap={10} align='center'>
                    <span
                      style={{
                        width: '80px',
                      }}>
                      {t('图表类型') + ' :'}
                    </span>
                    <Form.Item noStyle name={'type'}>
                      <Select
                        style={{
                          width: '200px',
                        }}
                        options={Object.keys(formTypeOptions).map(
                          (key: any) => {
                            return {
                              label: formTypeOptions[key],
                              value: key,
                            };
                          },
                        )}></Select>
                    </Form.Item>
                  </Flex>
                </Flex>
              </Form.Item>
            </Form>
            <Spin
              style={{
                position: 'absolute',
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                display: componentSettingLoading ? 'flex' : 'none',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fff',
              }}
              spinning={true}></Spin>
          </Flex>
        </Flex>
      </Modal>
    </Flex>
  );
};
export default Home;

// export default function Home() {
//   return <div>Home</div>;
// }
