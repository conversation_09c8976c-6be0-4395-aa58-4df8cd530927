import { Table, Form, Button, ConfigProvider } from 'antd';
import {
  FormTableFilter,
  FormTableInput,
  FormText,
  type FormTableColumnsDataProps,
} from './';

const columnsData: FormTableColumnsDataProps[] = [
  { name: 'age', valueType: 'input', key: 'age' },
  { name: 'name', valueType: 'input', key: 'name' },
  { name: 'filter', valueType: 'filter', key: 'filter' },
  { name: 'desc', valueType: 'text', key: 'desc' },
];

const dataSource = [
  {
    id: 1,
    filter: null,
    name: 'test1',
    age: 555222,
    desc: 'des22222c',
  },
  {
    id: 2,
    filter: [
      {
        andOr: 'and',
        field: 'daf',
        leftType: '(',
        type: 'include',
        rightType: ')',
        value: '111',
      },
    ],
    name: 'test2',
    age: 123,
    desc: 'des111c',
  },
  {
    id: 3,
    filter: [
      {
        andOr: 'and',
        field: 'daf',
        leftType: '(',
        type: 'include',
        rightType: ')',
        value: '111',
      },
    ],
    name: 'test2',
    age: 123,
    desc: 'des111c',
  },
];

export default function FormTable() {
  const renderColumns = columnsData.map((item: FormTableColumnsDataProps) => {
    let render = null;
    switch (item.valueType) {
      case 'input':
        render = (text: string, record: any) => (
          <FormTableInput
            prefix={['table', record.id]}
            name={item.key}
            defaultValue={text}
          />
        );
        break;
      case 'filter':
        render = (value: any, record: any) => (
          <FormTableFilter initValue={value} prefix={['table', record.id]} />
        );
        break;
      case 'text':
        render = (text: string) => <FormText text={text} />;
        break;
      default:
        render = (text: string) => <FormText text={text} />;
        break;
    }

    return {
      title: item.name,
      key: item.key,
      dataIndex: item.key,
      render: render,
    };
  });

  return (
    <div>
      <ConfigProvider
        theme={{
          components: {
            Form: {
              itemMarginBottom: 0,
            },
          },
        }}>
        <Form
          onFinish={(values) => {
            console.log(values);
          }}>
          <Table
            columns={renderColumns}
            dataSource={dataSource}
            pagination={false}
          />
          <Button type='primary' htmlType='submit'>
            test
          </Button>
        </Form>
      </ConfigProvider>
    </div>
  );
}
