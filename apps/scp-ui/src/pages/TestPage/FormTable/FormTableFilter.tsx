import { useEffect } from 'react';
import { Button, Input, Form, Popover, Space, Select } from 'antd';

import React from 'react';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

interface FromTableFilterProps {
  prefix: string[];
  initValue: any;
}

export default function FormTableFilter({
  prefix,
  initValue,
}: FromTableFilterProps) {
  return <FilterButton prefix={prefix} initValue={initValue} />;
}

function FilterButton({
  prefix,
  initValue,
}: {
  prefix: string[];
  initValue: any;
}) {
  const form = Form.useFormInstance();
  const value = Form.useWatch([prefix, 'filter'], form);
  useEffect(() => {
    console.log(value);
  }, [value]);
  return (
    <Popover
      placement='bottom'
      content={
        <div className='w-[800px] h-[200px] overflow-auto p-2'>
          <Form.List name={[...prefix, 'filter']} initialValue={initValue}>
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space
                    key={key}
                    style={{ display: 'flex', marginBottom: 8 }}
                    align='baseline'>
                    <Form.Item {...restField} name={[name, 'leftType']}>
                      <Select
                        placeholder='类型'
                        style={{ width: 60 }}
                        allowClear
                        options={[
                          {
                            label: '(',
                            value: '(',
                          },
                          {
                            label: '((',
                            value: '((',
                          },
                          {
                            label: '(((',
                            value: '(((',
                          },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'field']}
                      rules={[{ required: true, message: '请输入过滤条件' }]}>
                      <Input placeholder='过滤条件' />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'type']}
                      rules={[{ required: true, message: '请输入类型' }]}>
                      <Select
                        placeholder='类型'
                        style={{ width: 120 }}
                        options={[
                          {
                            label: '包含',
                            value: 'include',
                          },
                          {
                            label: '不包含',
                            value: 'exclude',
                          },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'value']}
                      rules={[{ required: true, message: '请输入值' }]}>
                      <Input placeholder='值' />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'rightType']}>
                      <Select
                        placeholder='类型'
                        allowClear
                        style={{ width: 60 }}
                        options={[
                          {
                            label: ')',
                            value: ')',
                          },
                          {
                            label: '))',
                            value: '))',
                          },
                          {
                            label: ')))',
                            value: ')))',
                          },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'andOr']}
                      rules={[{ required: true, message: '请输入类型' }]}>
                      <Select
                        placeholder='类型'
                        style={{ width: 120 }}
                        options={[
                          {
                            label: '并且',
                            value: 'and',
                          },
                          {
                            label: '或者',
                            value: 'or',
                          },
                        ]}
                      />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type='dashed'
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}>
                    添加过滤条件
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </div>
      }
      trigger={'click'}>
      <Button type='link'>过滤条件</Button>
    </Popover>
  );
}
