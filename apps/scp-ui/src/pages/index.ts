import React from 'react';

import ProcessMagement from './ProcessMagement';
import Setting from './Setting';
const Home = React.lazy(() => import('./Home'));
const Login = React.lazy(() => import('./Login'));
const SSOLogin = React.lazy(() => import('./Login/SSOLogin'));
const Demandful = React.lazy(() => import('./Demandful'));
const PlanningViewNew = React.lazy(() => import('./PlanningViewNew'));
const ProjectedStock = React.lazy(() => import('./ProjectedStock'));
const Supplyusage = React.lazy(() => import('./Supplyusage'));
const ApplicationLog = React.lazy(() => import('./ApplicationLog'));
const ViewMasterData = React.lazy(() => import('./ViewMasterData'));
const TestPage = React.lazy(() => import('./TestPage'));
const UserPage = React.lazy(() => import('./UserPage'));
const ApplicationJob = React.lazy(() => import('./ApplicationJob'));
const BIG = React.lazy(() => import('./BIG'));
const Mobile = React.lazy(() => import('./MobileHome'));
const Chart = React.lazy(() => import('./ChartPlus'));
const Error = React.lazy(() => import('./Error'));
const Configuation = React.lazy(() => import('./Configuration'));
const JobTemplate = React.lazy(() => import('./JobTemplate'));
const JobTemplatePage = React.lazy(() => import('./JobTemplate/JobTemplate'));
const EditJobTemplate = React.lazy(() => import('./EditJobTemplate'));
const RunApplicationJob = React.lazy(() => import('./RunApplicationJob'));
const CheckApplication = React.lazy(() => import('./CheckApplication'));
// const PversionSetting = React.lazy(() => import('./PversionSetting'));
const NotFound = React.lazy(() => import('./NotFound'));
// const CostSetting = React.lazy(() => import('./CostSetting'));
// const ForeCastModel = React.lazy(() => import('./Setting/ForeCastModel'));
const ForeCastAdd = React.lazy(() => import('./Setting/ForeCastModel/AddPage'));

export default {
  Home,
  Login,
  SSOLogin,
  Demandful,
  PlanningViewNew,
  ProjectedStock,
  ApplicationLog,
  ViewMasterData,
  Supplyusage,
  TestPage,
  UserPage,
  ApplicationJob,
  BIG,
  Error,
  Mobile,
  Chart,
  Configuation,
  JobTemplate,
  JobTemplatePage,
  EditJobTemplate,
  RunApplicationJob,
  CheckApplication,
  NotFound,
  // CostSetting,
  // PversionSetting,
  // ForeCastModel,
  ForeCastAdd,
  ...ProcessMagement,
  ...Setting,
};
