import {
  OwnerListType,
  TableDataType,
} from '@/pages/ProcessMagement/ProcessTemplateManager/types';
import type { ProcessListDataType } from '@/pages/ProcessMagement/ManagerProcess/types';
import request, { returnData } from '@/services';
import { Task } from '@/pages/ProcessMagement/MyTaskPage/type';
import { StepDetail } from '@/pages/ProcessMagement/RunProcess/type';

export default {
  /**
   * @description get owner list
   * @return {Promise<returnData<OwnerListType>>}
   */
  getOwnerList: (): Promise<returnData<OwnerListType[]>> =>
    request.get('/api/processTemplate/getOwnerList'),

  /**
   * @description get table data
   * @return {Promise<returnData<TableDataType>>}
   */

  getProcessTalbeData: (data: any): Promise<returnData<TableDataType[]>> =>
    request.get('/api/process/getProcessTemplateList', { params: data }),

  /**
   * @description 获取任务列表
   * @return {Promise<returnData<Task[]>>}
   */
  getTasks: (): Promise<returnData<Task[]>> => request.get('/api/tasks'),

  /**
   * @description 获取运行中的流程列表
   * @return {Promise<returnData<RunProcessType[]>>}
   */
  getRunProcessList: (): Promise<returnData<StepDetail[]>> =>
    request.get('/api/runProcess/getRunProcessList'),

  /**
   * @description 获取流程列表
   * @return {Promise<returnData<TableDataType[]>>}
   */
  getProcessList: (): Promise<returnData<ProcessListDataType[]>> =>
    request.get('/api/processTemplate/getTableData'),

  /**
   * @description 获取流程详情
   * @return
   */
  getTaskList: () => request.get('/api/editProcessTemplate/getTaskList'),

  /**
   * @description 创建流程实例
   * @return
   */
  createProcessInstance: (data: any) =>
    request.post('/api/process/createProcessInstance', data),

  /**
   * @description 启动流程实例
   * @return
   */
  startProcessInstance: (data: any) =>
    request.post('/api/process/startProcessInstance', data),

  /**
   * @description 查询流程实例列表
   * @return
   */
  getProcessInstanceList: (data?: any) =>
    request.get('/api/process/getProcessInstanceList', {
      params: data,
    }),

  /**
   * @description 获取所有流程模板
   * @return
   */
  getAllProcessTemplate: () =>
    request.get('/api/process/getAllProcessTemplate'),

  /**
   * @description 获取同公司所有用户
   * @return
   */
  getUserListByCoIdContainsSelf: () =>
    request.get('/api/user/getUserListByCoIdContainsSelf'),

  /**
   * @description 获取流程模板详情
   * @return
   */
  getProcessTemplateInfoByTemplateUuid: (data: any) =>
    request.get('/api/process/getProcessTemplateInfoByTemplateUuid', {
      params: data,
    }),

  /**
   * @description 获取流程实例详情（根据实例uuid）
   * @return
   */
  getProcessInstanceInfoByInstanceUuid: (data: any) =>
    request.get('/api/process/getProcessInstanceInfoByInstanceUuid', {
      params: data,
    }),

  /**
   * @description 开始实例任务
   * @return
   */
  startInstanceTask: (data: any) =>
    request.post('/api/process/startInstanceTask', data),

  /**
   * @description 完成实例任务
   * @return
   */
  completeInstanceTask: (data: any) =>
    request.post('/api/process/completeInstanceTask', data),

  /**
   * @description 根据taskUuid获取流程任务详情
   * @return
   */
  getProcessTaskInfoByTaskUuid: (data: any) =>
    request.get('/api/process/getProcessTaskInfoByTaskUuid', {
      params: data,
    }),

  /**
   * @description 获取所有流程任务
   */
  getTaskTemplateList: () => request.get('/api/process/getAllProcessTask'),

  /**
   * @description 创建流程任务
   */
  createProcess: (data: any) =>
    request.post('/api/process/createProcessTask', data),

  /**
   * @description 根据taskUuid获取任务详情
   */
  getTaskDetail: (taskUuid: string) =>
    request.get('/api/process/getProcessTaskInfoByTaskUuid', {
      params: { taskUuid },
    }),

  /**
   * @description 创建流程模板列表
   */
  createProcessTemplate: (data: any) => {
    return request.post('/api/process/createProcessTemplate', data);
  },

  /**
   * @description 修改流程模板列表
   */
  updateProcessTemplate: (data: any) => {
    return request.post('/api/process/editProcessTemplate', data);
  },

  /**
   * @description 删除单个流程模板
   * @api /process/deleteProcessTemplate
   */
  deleteProcessTemplate: (templateUuid: string) =>
    request.post('/api/process/deleteProcessTemplate', {
      templateUuid,
    }),

  /**
   * @description 获取个人任务列表
   * @api /process/getProcessInstanceTaskListByCurUser
   */
  getProcessInstanceTaskListByCurUser: (data: any) =>
    request.get('/api/process/getProcessInstanceTaskListByCurUser', {
      params: data,
    }),

  /**
   * @description 审阅实例步骤
   * @api /process/reviewInstanceStep
   */
  reviewInstanceStep: (data: any) =>
    request.post('/api/process/reviewInstanceStep', data),

  /**
   * @description 删除流程实例
   * @api /process/deleteProcessInstance
   */
  deleteProcessInstance: (data: any) =>
    request.post('/api/process/deleteProcessInstance', data),
};
