import request from '@/services';

const Setting = {
  createPversion: (data: any) =>
    request.post('/api/parea/createPversion', data),
  deletePversion: (data: any) =>
    request.post('/api/parea/deletePversion', data),
  cancelSharedPversion: (data: any) =>
    request.post('/api/parea/cancelSharedPversion', data),
  /**
   * @description 获取关键指标列表
   * @api /statisticalForecast/getKeyFigureList
   */
  getSettingKeyFigureList: () =>
    request.get('/api/statisticalForecast/getKeyFigureList'),

  /**
   * @description 创建预测模型 profile
   * @api/statisticalForecast/createForecastModelProfile
   */
  createForecastModelProfile: (data: any) =>
    request.post('/api/statisticalForecast/createForecastModelProfile', data),

  /**
   * @description 查询 pa 下所有 forecast model profiles
   * @api /statisticalForecast/getForecastModelProfileList
   */

  getForecastModelProfileList: (data: any) =>
    request.get('/api/statisticalForecast/getForecastModelProfileList', {
      params: data,
    }),

  /**
   * @description 修改forecastModel
   * @api /statisticalForecast/updateModelProfile
   */
  updateModelProfile: (data: any) =>
    request.post('/api/statisticalForecast/updateModelProfile', data),

  /**
   * @description 删除forecastModel
   * @api /statisticalForecast/deleteModelProfile
   */
  deleteModelProfile: (data: any) =>
    request.post('/api/statisticalForecast/deleteModelProfile', data),
};

export default Setting;
