//获取所有Attribute的返回数据
export type GetAttributesRes = {
  id: number;
  attributeName: string;
  valueType: string;
  queryType: string[];
  label: string;
  isCustom: boolean;
};

//获取所有KeyFigure的返回数据
export type GetKeyFiguresRes = {
  kfId: number;
  label: string;
  kfName: string;
  planLevelID: number;
  planLevelName: string;
  IsCustom: boolean;
  kfType: number;
};

//获取所有时间配置信息的返回数据
export type GetTimeProfileRes = {
  name: string;
  profileDesc: string;
  profileStart: string;
  profileEnd: string;
  pareaId: number;
  tpLevelList: string[];
  details: string | null;
};

//获取所有偏好树形结构接口的入参
export type GetPerferTreeDataReq = {
  menuTag: string;
};
//获取所有偏好树形结构接口的出参
export interface GetPerferTreeDataRes {
  id: number;
  groupName: string;
  isSystem: boolean;
  menuId: number;
  userId: number;
  parentId: number;
  schemeList: SaveCollabSchemeReq[];
  children: GetPerferTreeDataRes[] | null;
}

export type AddSchemeGroupTreeNodeReq = {
  parentId: number;
  groupName: string;
  menuTag: string;
};

export type DeleteSchemeGroupTreeNodeReq = {
  groupId: number;
};

interface Condition {
  id: number;
  conditionId: number;
  preferOperator: string;
  preferValue: string;
}

interface DateRange {
  endDate: string;
  tpLevel: number;
  startDate: string;
  rollingType: number;
  fromRolls: number | null;
  toRolls: number | null;
}

export interface SaveCollabSchemeReq {
  schemeId: number | null;
  menuTag: string;
  groupId: number;
  isDefault: boolean;
  schemeName: string;
  conditionList: Condition[];
  attributeList: number[];
  kfList: number[];
  dateList: DateRange[];
  isOnlyWithData: any;
}

export interface GetKfAttributeListRes {
  kfId?: number;
  paPvId?: number;
  [property: string]: any;
}

export interface HandlePlanObjectByKfIdReq {
  isDelete: string;
  kfId: number;
  paPvId: number;
  values: Value[];
  [property: string]: any;
}

interface Value {
  fieldCode: string;
  fieldKey: number;
  fieldValue: string;
  [property: string]: any;
}

export interface GetItemTyepListRes {
  id: number;
  code: string;
  label: string;
}

export interface GetItemTypeKFListRes {
  kfId: number;
  kfCode: string;
  kfLabel: string;
}

export interface RunModelResponse {
  code: number;
  data: RunModelData;
  msg: string;
  [property: string]: any;
}

export interface RunModelData {
  agentType: string;
  attribute: string[];
  conversationId: string;
  from: string;
  keyFigure: string[];
  message: string;
  planArea: string;
  to: string;
  [property: string]: any;
}

interface ConversationItem {
  id: number;
  createdAt: number;
  createdBy: number;
  updatedAt: number;
  updatedBy: number;
  userId: number;
  title: string;
  convAppBuilderId: string;
  convType: number;
}

interface ConversationGroup {
  label: string;
  convList: ConversationItem[] | null;
}

export interface GetUserDialogListRes {
  list: ConversationGroup[];
}

export interface GetPlanningViewDataReq {
  /**
   * []int
   */
  attributeList?: string[];
  /**
   * []jsonObejct
   */
  conditions?: string[];
  /**
   * []JsonObject
   */
  dateList?: string[];
  /**
   * viewType=2必填
   */
  itemTypeId?: number;
  /**
   * []int
   */
  kfList?: string[];
  paPvId?: number;
  /**
   * 1:timebased 2:itembased
   */
  viewType?: number;
  [property: string]: any;
}

/**
 * Request
 */
export interface DeleteCollabSchemeReq {
  /**
   * 方案id
   */
  schemeId: number;
  [property: string]: any;
}

export interface GetKfAttributeListReq {
  kfId?: number;
  paPvId?: number;
  [property: string]: any;
}

export interface GetItemTypeKeyFigureReq {
  itemTypeId?: number;
  [property: string]: any;
}

export interface GetItemTypeRootAttributeReq {
  itemTypeId?: number;
  [property: string]: any;
}

export interface GetConversationsReq {
  /**
   * 1 配置 2 问答
   */
  convType: number;
  [property: string]: any;
}

export interface GetMessagesReq {
  convAppBuilderId: string;
  [property: string]: any;
}

/**
 * RAGFlow属性配置聊天请求参数
 */
export interface RunRAGFlowAttributeReq {
  /** 用户输入的查询文本 */
  query: string;
  /** 会话ID */
  conversationId: string;
  /** 是否使用流式返回 */
  stream: boolean;
}

/**
 * RAGFlow属性配置聊天返回结果
 */
export interface RunRAGFlowAttributeRes {
  result: {
    /** 第一组属性配置ID列表 */
    attributeConfigFirst: number[];
    /** 第二组属性配置ID列表 */
    attributeConfigSecond: number[];
    /** 关键指标配置ID列表 */
    keyFigureConfig: number[];
    /** 关键指标名称列表 */
    keyFigure: string[];
    /** 开始日期 */
    from: string;
    /** 结束日期 */
    to: string;
    /** 时间维度 */
    dimensionality: string;
    /** 会话消息 */
    message: string;
    /** 会话ID */
    conversationId: string;
  };
}
