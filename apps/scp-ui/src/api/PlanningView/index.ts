import request, { returnData } from '@/services';
import type { uploadData } from '@/pages/PlanningViewNew/CharSheetType';
import type {
  GetAttributesRes,
  GetKeyFiguresRes,
  GetPerferTreeDataReq,
  GetPerferTreeDataRes,
  AddSchemeGroupTreeNodeReq,
  DeleteSchemeGroupTreeNodeReq,
  SaveCollabSchemeReq,
  GetKfAttributeListRes,
  HandlePlanObjectByKfIdReq,
  GetItemTyepListRes,
  GetItemTypeKFListRes,
  RunRAGFlowAttributeReq,
  RunRAGFlowAttributeRes,
} from './model';
const PlanningView = {
  //产能去向接口/cooperateView/getTSCapaDemandList
  getTSCapaDemandList: (data: object) =>
    request.get('/api/cooperateView/getTSCapaDemandList', { params: data }),
  //卡涞excel的接口
  getCalieData: (data: object) =>
    request.get(
      'api/cooperateView/getPlanningViewData',
      { params: data },
      // params: { ...data, ...replace },
    ),
  //卡涞excel的接口
  getSaasData: (data: object) =>
    request.get('api/cooperateView/getSubscriptPriceData', {
      params: data,
    }),
  //获取所有属性列表
  getArrtibuteList: (): Promise<returnData<GetAttributesRes[] | null>> =>
    request.get('/api/cooperateView/getPvAttributeList'),
  //获取所有KeyFigure
  getKFigureList: (
    data: object,
  ): Promise<returnData<GetKeyFiguresRes[] | null>> =>
    request.get('/api/eigenvalue/getKeyFigureList', { params: data }),
  //获取时间配置信息
  getTimeProfileList: () => request.get('/api/eigenvalue/getTimeProfileList'),
  //保存协同视图接口
  saveCooperateView: (data: uploadData) =>
    request.post('/api/cooperateView/savePlanningViewData', data),

  /**
   * @description 获取偏好树形结构的接口
   * */
  getPerferTreeData: (
    data: GetPerferTreeDataReq,
  ): Promise<returnData<GetPerferTreeDataRes[]>> =>
    request.get('/api/filter/getSchemeGroupTree', { params: data }),

  /**
   * @description 添加偏好组节点
   */
  addSchemeGroupTreeNode: (data: AddSchemeGroupTreeNodeReq) =>
    request.post('/api/filter/addSchemeGroupTreeNode', data),

  /**
   * @description 删除偏好组节点
   * */
  deleteSchemeGroupTreeNode: (data: DeleteSchemeGroupTreeNodeReq) =>
    request.post('/api/filter/deleteSchemeGroupTreeNode', data),

  /**
   * @description 新增或修改偏好接口
   * */
  saveCollabScheme: (data: SaveCollabSchemeReq) =>
    request.post('/api/filter/saveCollabScheme', data),

  /**
   * @description 删除偏好接口
   * */
  deleteCollabScheme: (data: { schemeId: number }) =>
    request.post('/api/filter/deleteCollabScheme', data),

  /**
   *  @description 获取kf对应的属性列表
   * */
  getKfAttributeList: (data: {
    kfId: number;
    paPvId: number;
  }): Promise<returnData<GetKfAttributeListRes[] | null>> =>
    request.get('/api/cooperateView/getKfAttributeList', { params: data }),

  /**
   * @description 保存或者删除PlanObject
   * */

  handlePlanObject: (data: HandlePlanObjectByKfIdReq) =>
    request.post('/api/cooperateView/handlePlanObjectByKfId', data),

  /**
   * @description 获取ItemType列表
   *   */

  getItemTyepList: (
    data: any,
  ): Promise<returnData<GetItemTyepListRes[] | null>> =>
    request.get('/api/eigenvalue/getItemTypeList', { params: data }),
  /**
   * @description 获取ItemType对应的KF列表
   * */
  getItemTypeKF: (data: {
    itemTypeId: number;
  }): Promise<returnData<GetItemTypeKFListRes[] | null>> =>
    request.get('/api/cooperateView/getItemTypeKeyFigure', { params: data }),

  getItemTypeRootAttribute: (data: {
    itemTypeId: number;
  }): Promise<returnData<GetAttributesRes[] | null>> =>
    request.get('api/cooperateView/getItemTypeRootAttribute', { params: data }),

  /**
   * @description 获取AI推荐的数据
   * */

  runModel: (data: object) =>
    request.post('/api/ragFlow/runRAGFlowChatConfigApi', data),

  /**
   * @description 获取用户对话列表
   * */

  getUserDialogList: (data: object) =>
    request.get('/api/appBuilder/getConversations', { params: data }),

  /**
   * @description 获取列表详细信息
   * */

  getAiMessagees: (data: object) =>
    request.get('/api/appBuilder/getMessages', { params: data }),

  /**
   * @description 新版根据keyFigure获取全部数据协同配置AI进行会话（ByRagFlow）
   * @api /ragFlow/runRAGFlowChatConfigApiForAttribute
   * @param data RunRAGFlowAttributeReq 聊天请求参数
   * @returns Promise<returnData<RunRAGFlowAttributeRes>> 聊天响应结果
   */
  runRAGFlowChatConfigApiForAttribute: (
    data: RunRAGFlowAttributeReq,
  ): Promise<returnData<RunRAGFlowAttributeRes['result']>> =>
    request.post('/api/ragFlow/runRAGFlowChatConfigApiForAttribute', data),
};
export default PlanningView;
