import request from '@/services';

const BIGApi = {
  getBIGData: (data: any) => request.post('/api/big/getBig', data),
  //获取五种类型的节点
  // 订单
  getAllOrder: (data: any) =>
    request.get('/api/big/AllOrder?paPvId=' + `${data.paPvId}`),
  // 凭证
  getAllCertificate: (data: any) =>
    request.get('/api/big/AllCertificate?paPvId=' + `${data.paPvId}`),
  // 风险凭证
  getDelayRiskOrder: (data: any) =>
    request.get('/api/big/DelayRiskOrder?paPvId=' + `${data.paPvId}`),
  // 冗余供应
  getRedundancyRiskOrder: (data: any) =>
    request.get('/api/big/RedundancyRiskOrder?paPvId=' + `${data.paPvId}`),
  // 交付风险
  getDeliveryRiskOrder: (data: any) =>
    request.get('/api/big/DeliveryRiskOrder?paPvId=' + `${data.paPvId}`),
};

export default BIGApi;
