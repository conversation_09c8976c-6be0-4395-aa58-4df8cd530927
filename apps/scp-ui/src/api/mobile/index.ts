import request from '@/services';

const Mobile = {
  getInitViewType: () => request.get('api/phone/getInitViewType'),
  getViewList: (params: any) =>
    request.get('/api/phone/getViewList?paPvId=' + `${params}`),
  getParams: (params: any) =>
    request.get(
      '/api/phone/getParams?paPvId=' +
        `${params.paPvId}` +
        '&viewType=' +
        `${params.viewType}`,
    ),
  // getViewList: () => request.get('/api/phone/getViewList?paPvId=' + `3`),
  // getParams: (params: any) =>
  //     request.get('/api/phone/getParams?paPvId=' + `3` + '&viewType=' + `${params.viewType}`),
  updateCharts: (params: any) =>
    request.post('/api/phone/updateCharts', { ...params }),
};

export default Mobile;
