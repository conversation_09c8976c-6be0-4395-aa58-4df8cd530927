import request from '@/services';

const EditJobTemplateApi = {
  ///plan/getJobSelectedTemplate
  getJobSelectedTemplate: () => request.get('/api/plan/getJobSelectedTemplate'),
  ///plan/getJobTemplateByUserUpdate
  getJobTemplateByUserUpdate: (data: any) =>
    request.get('/api/plan/getJobTemplateByUserUpdate', { params: data }),

  //创建作业模板
  ///plan/createJobTemplate
  createJobTemplate: (data: any) =>
    request.post('/api/plan/createJobTemplate', data),

  //修改作业模板
  ///plan/updateJobTemplateJobTemplate
  updateJobTemplate: (data: any) =>
    request.post('/api/plan/updateJobTemplate', data),

  //分享模版给他人
  ///plan/shareJobTemplateTo
  shareJobTemplateTo: (data: any) =>
    request.post('/api/plan/shareJobTemplateTo', data),
  ///plan/deleteJobTemplate
  deleteJobTemplate: (templateUuid: string) =>
    request.post('/api/plan/deleteJobTemplate', { templateUuid }),

  ///plan/checkTemplateName
  //检查模版名是否可用
  checkTemplateName: (templateName: string) =>
    request.get('/api/plan/checkTemplateName', { params: { templateName } }),
};

export default EditJobTemplateApi;
