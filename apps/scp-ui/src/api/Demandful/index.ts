import request from '@/services';
const DemandfulApi = {
  getDemandful: (data: object) =>
    request.get('/api/demandFulfill/getDemandFulfillList', {
      params: data,
    }),
  getMyTag: (data: object) =>
    request.get('/api/demandFulfill/getMyTag', {
      params: data,
    }),
  saveProgramme: (data: object) =>
    request.post('/api/demandFulfill/saveProgramme', {
      params: data,
    }),
  addNewTag: (data: object) =>
    request.post('/api/demandFulfill/addNewTag', {
      params: data,
    }),
  deleteTag: (data: object) =>
    request.post('/api/demandFulfill/deleteTag', {
      params: data,
    }),
};
export default DemandfulApi;
