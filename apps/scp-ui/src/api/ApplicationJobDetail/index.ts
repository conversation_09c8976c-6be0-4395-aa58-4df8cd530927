import request from '@/services';
import { returnData } from '@/services';
import {
  GetLogsScpLoggerReq,
  GetLogsScpLoggerRes,
  GetCronJobTaskReq,
  GetCronJobTaskRes,
  GetJobTemplateFrameAndValueForCopyReq,
} from './model';

const ApplicationJobDetailApi = {
  //跑计划接口/plan/startPlanningRun
  startPlanningRun: (data: any) =>
    request.post('/api/plan/startPlanningRun', data),
  getCronJobTask: (
    data: GetCronJobTaskReq,
  ): Promise<returnData<GetCronJobTaskRes[] | null>> =>
    request.get('/api/cron/getCronJobTask', { params: data }),
  //获取scp日志
  getLogsScpLogger: (
    data: GetLogsScpLoggerReq,
  ): Promise<returnData<GetLogsScpLoggerRes[] | null>> =>
    request.get('/api/scpLog/getLogsScpLogger', { params: data }),

  //获取cron任务详情
  //cron/getCronTaskDetail
  getCronTaskDetail: (data: any) =>
    request.get('/api/cron/getCronTaskDetail', { params: data }),
  /**
   * @description
   * @api /plan/getJobTemplateFrameAndValueForCopy
   * */
  getJobTemplateFrameAndValueForCopy: (
    data: GetJobTemplateFrameAndValueForCopyReq,
  ) =>
    request.get('/api/plan/getJobTemplateFrameAndValueForCopy', {
      params: data,
    }),

  /**
   * @description 获取cron任务详情
   * @api /cron/getCronTaskDetailContainsParent
   * */
  getCronTaskDetailContainsParent: (taskUuid: string, jobId: string) =>
    request.get('/api/cron/getCronTaskDetailContainsParent', {
      params: { taskUuid, jobId },
    }),

  /**
   * @description 重新执行
   * @api /cron/manualRestartJobChain
   * */
  manualRestartJobChain: (taskUuid: string, restartStrategy: number) =>
    request.post('/api/cron/manualRestartJobChain', {
      taskUuid,
      restartStrategy,
    }),

  /**
   * @description
   * @api /user/getUserListByCoIdContainsSelf
   */
  getUserListByCoIdContainsSelf: () =>
    request.get('/api/user/getUserListByCoIdContainsSelf'),

  /**
   * @description 取消未执行的定时任务
   * @api /cron/deleteUnExecutedJob
   */

  deleteUnExecutedJob: (jobId: string) =>
    request.post('/api/cron/deleteUnExecutedJob', { jobId: jobId }),
};

export default ApplicationJobDetailApi;
