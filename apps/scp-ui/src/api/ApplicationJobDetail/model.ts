export type GetLogsScpLoggerReq = {
  taskUuid: string;
  seq: number;
};

export type GetLogsScpLoggerRes = {
  api: string;
  args: string;
  details: string;
  file: string;
  func: string;
  level: string;
  line: number;
  msg: string;
  statusCode: number;
  taskId: string;
  time: string;
  traceId: string;
};

export type GetCronJobTaskReq = {
  paPvId: number;
  startTime: string;
  endTime: string;
  jobId?: Array<string>;
  taskState?: Array<number>;
  taskTag?: string;
  jobName?: string;
  jobMode?: string;
  page: number;
  pageSize: number;
};

export type GetCronJobTaskRes = {
  jobId: string;
  jobName: string;
  jobMode: string;
  jobType: string;
  taskStartTime: number;
  taskEndTime: number;
  taskStateCode: number;
  taskStateName: string;
  taskTag: string;
  taskUuid: string;
  message: string;
  templateTypeCode: number;
  templateTypeName: string;
  createdByName: string;
};

export type GetJobTemplateFrameAndValueForCopyReq = {
  taskUuid: string;
  jobId: string;
  seq?: number;
};
