//获取所有时间配置信息的返回数据
export type GetTimeProfileRes = {
  name: string;
  profileDesc: string;
  profileStart: string;
  profileEnd: string;
  pareaId: number;
  tpLevelList: string[];
  tpLevelMap: { [key: string]: string };
  details: string | null;
};

export interface GetItemTyepListReq {
  itemTypeId?: number;
  itemTypeName?: string;
  label?: string;
  isCustom?: boolean;
}

export interface GetItemTyepListRes {
  id: number;
  name: string;
  label: string;
  isCustom: boolean;
  uniqueList: { label: string; code: string }[];
}

export interface GetAttributeReq {
  /**
   * 属性ID
   */
  attributeId?: number;
  /**
   * 属性名称
   */
  attributeName?: string;
  /**
   * false 系统内置 true 用户自定义
   */
  isCustom?: string;
  [property: string]: any;
}

export interface GetMasterDataTypeListReq {
  isCustom?: string;
  label?: string;
  name?: string;
  [property: string]: any;
}

export interface GetMasterDataTypeDetailsReq {
  masterDataTypeId: string;
  [property: string]: any;
}

export interface GetPlanningLevelListReq {
  /**
   * false 系统内置 true 用户自定义
   */
  isCustom?: string;
  /**
   * 计划等级ID
   */
  planLevelId?: number;
  /**
   * 计划等级名称
   */
  planLevelName?: string;
  [property: string]: any;
}

export interface GetPlanningLevelDetailReq {
  /**
   * 计划等级ID
   */
  planLevelId?: number;
  [property: string]: any;
}

export interface GetKeyFigureListReq {
  /**
   * false 系统内置 true 用户自定义
   */
  isCustom?: string;
  /**
   * kf名称
   */
  keyFigureId?: string;
  /**
   * kfId
   */
  keyFigureName?: string;
  /**
   * 计划等级ID
   */
  planLevelId?: number;
  timeBasedFlag?: boolean;
  [property: string]: any;
}

export interface GetKeyFigureDetailReq {
  /**
   * kf名称
   */
  kfId?: string;
  [property: string]: any;
}

export interface GetItemTypeDetailsReq {
  /**
   * 类别Id
   */
  itemTypeId?: number;
  [property: string]: any;
}

export interface SaveHedgeInfoReq {
  backwardDays: number;
  forwardDays: number;
  hedgeStrategy: number;
  isHedged: boolean;
  kfId: number;
  [property: string]: any;
}
