import request, { returnData } from '@/services';
import type {
  GetTimeProfileRes,
  GetItemTyepListRes,
  SaveHedgeInfoReq,
} from './model';

const ConfigurationApi = {
  getAttribute: (data: object) =>
    request.get('/api/eigenvalue/getAttribute', { params: data }),

  createOrUpdateAttribute: (data: any) =>
    request.post('/api/eigenvalue/createOrUpdateAttribute', data),

  getMasterDataTypeList: (data: object) =>
    request.get('/api/eigenvalue/getMasterDataTypeList', { params: data }),

  getMasterDataTypeDetails: (data: object) =>
    request.get('/api/eigenvalue/getMasterDataTypeDetails', { params: data }),

  getTimeProfileList: (): Promise<returnData<GetTimeProfileRes[] | null>> =>
    request.get('/api/eigenvalue/getTimeProfileList'),

  getPlanningLevelList: (data: object) =>
    request.get('/api/eigenvalue/getPlanningLevelList', { params: data }),

  getPlanningLevelDetail: (data: object) =>
    request.get('/api/eigenvalue/getPlanningLevelDetail', { params: data }),

  createOrUpdatePlanLevel: (data: any) =>
    request.post('/api/eigenvalue/createOrUpdatePlanLevel', data),

  getKeyFigureList: (data: object) =>
    request.get('/api/eigenvalue/getKeyFigureList', { params: data }),

  getKeyFigureDetail: (data: object) =>
    request.get('/api/eigenvalue/getKeyFigureDetail', { params: data }),

  createOrUpdateKeyFigure: (data: any) =>
    request.post('/api/eigenvalue/createOrUpdateKeyFigure', data),

  getItemTypeList: (
    data?: any,
  ): Promise<returnData<GetItemTyepListRes[] | null>> =>
    request.get('/api/eigenvalue/getItemTypeList', { params: data }),

  /**
   * @description 获取ItemType详情
   * */
  getItemTypeDetails: (data: { itemTypeId: number }) =>
    request.get('/api/eigenvalue/getItemTypeDetails', { params: data }),

  /**
   * @description 查询冲销策略
   * @api /eigenvalue/getHedgeStrategy
   */
  getHedgeStrategy: () => request.get('/api/eigenvalue/getHedgeStrategy'),

  /**
   * @description 配置冲销策略
   * @api /eigenvalue/saveHedgeInfo
   */
  saveHedgeInfo: (data: SaveHedgeInfoReq) =>
    request.post('/api/eigenvalue/saveHedgeInfo', data),
  // 获得当前可见的主数据列表
  getMdIdList: () => request.get('/api/mdDataView/getMdIdList'),
  // 查询预测冲销配置
  getHedgeInfo: (data: any) =>
    request.get('/api/eigenvalue/getHedgeInfo', { params: data }),
};

export default ConfigurationApi;
