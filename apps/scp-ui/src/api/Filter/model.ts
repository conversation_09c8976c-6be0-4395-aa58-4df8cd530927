export interface GetFilterOperatorListRes {
  [map: string]: { opt: string; label: string; value: string }[];
}
export type GetAllFilterConditionsByMenuRes = {
  conditionContent: ConditionContent;
  id: number;
  preferOperator: string;
  preferValue: string;
};
export type ConditionContent = {
  field: string;
  label: string;
  type: string;
};

export type FilterSchemeInfo = {
  schemeName: string;
  isDefault: boolean;
  isSystem: boolean;
  schemeId: number;
  conditionList: GetAllFilterConditionsByMenuRes[];
};

export type SaveSchemeReq = {
  conditionList: GetAllFilterConditionsByMenuRes[];
  isDefault: boolean;
  menuTag: string;
  schemeName: string;
  schemeId: number | null;
};

export interface GetFilterSchemeInfoByMenuAndUserReq {
  menuTag: string;
  [property: string]: any;
}

export interface GetAllFilterConditionsByMenuReq {
  menuTag: string;
  [property: string]: any;
}

export interface GetFilterResultReq {
  /**
   * 筛选条件
   */
  filter: string[];
  /**
   * 菜单页面唯一标识
   */
  menuTag: string;
  /**
   * 分页页码
   */
  page: string;
  /**
   * 分页大小
   */
  pageSize: string;
  /**
   * 计划域、计划版本唯一标识id
   */
  paPvId: number;
  [property: string]: any;
}

/**
 * Request
 */
export interface DeleteSchemeReq {
  /**
   * 方案id
   */
  schemeId: number;
  [property: string]: any;
}
