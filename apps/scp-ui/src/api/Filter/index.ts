import request, { returnData } from '@/services';
import {
  GetAllFilterConditionsByMenuRes,
  FilterSchemeInfo,
  SaveSchemeReq,
  GetFilterOperatorListRes,
} from './model';
import { AxiosRequestConfig } from 'axios';
const FilterApi = {
  getFilterSchemeInfoByMenuAndUser: (
    data: object,
  ): Promise<returnData<FilterSchemeInfo[] | null>> =>
    request.get('/api/filter/getFilterSchemeInfoByMenuAndUser', {
      params: data,
    }),
  getAllFilterConditionsByMenu: (
    data: object,
  ): Promise<returnData<GetAllFilterConditionsByMenuRes[] | null>> =>
    request.get('/api/filter/getAllFilterConditionsByMenu', {
      params: data,
    }),
  saveScheme: (data: SaveSchemeReq) =>
    request.post('/api/filter/saveScheme', data),
  // 查询
  getFilterResult: (data: any, rest: AxiosRequestConfig) =>
    request.get('/api/filter/getFilterResult', {
      params: {
        filter: data.filterArr,
        ...data,
      },
      ...rest,
    }),
  //获取操作符列表接口
  getFilterOperatorList: (): Promise<returnData<GetFilterOperatorListRes>> =>
    request.get('/api/filter/getFilterOperatorList'),

  // 设为默认方案
  setDefaultScheme: (data: { schemeId: number }) =>
    request.post('/api/filter/setDefaultScheme', data),

  // 删除方案
  deleteScheme: (data: object) =>
    request.post('/api/filter/deleteScheme', data),
};
export default FilterApi;
