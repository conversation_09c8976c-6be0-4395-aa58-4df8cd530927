import request from '@/services';
import { type CreatePlanProfileReq } from './types';
const CostApi = {
  /**
   * @description 获取当前PA的配置文件参数
   * @api /profile/getPlanProfileList
   */
  getPlanProfileList: () =>
    request.get('/api/profile/getPlanProfileList', {}, false),

  /**
   * @description 创建PA配置文件
   * /profile/createPlanProfile
   */
  createPlanProfile: (data: CreatePlanProfileReq) =>
    request.post('/api/profile/createPlanProfile', data),

  /**
   * @description 获取配置文件下的父标签列表
   * @api /profile/getTagListByProfile
   */
  getTagListByProfile: (data: { profileUuid: string }) =>
    request.get('/api/profile/getTagListByProfile', { params: data }, false),

  /**
   * @description 获取标签下的子标签及内容
   * @api /profile/getSubTagFrameByTag
   */
  getSubTagFrameByTag: (data: { profileUuid: string; tagUuid: string }) =>
    request.get('/api/profile/getSubTagFrameByTag', { params: data }, false),

  /**
   * @description 获取系统所有标签页
   * @api /profile/getAllTagList
   */
  getAllTagList: () => request.get('/api/profile/getAllTagList', {}, false),

  /**
   * @description 给profile新增tag
   * @api /profile/addTagToPlanProfile
   */
  addTag: (data: any) => request.post('/api/profile/addTagToPlanProfile', data),

  /**
   * @description 删除tag
   * @api /profile/deleteTagToPlanProfile
   */
  deleteTag: (data: any) =>
    request.post('/api/profile/deleteTagToPlanProfile', data),

  /**
   * @description 某个标签下保存成本规则
   * @api /profile/saveCostsByProfileTagId
   */
  saveCostsByProfileTagId: (data: any) =>
    request.post('/api/profile/saveCostsByProfileTagId', data),

  /**
   * @description 删除计划配置文件
   * @api /profile/deletePlanProfile
   */
  deletePlanProfile: (data: { profileUuid: string }) =>
    request.post('/api/profile/deletePlanProfile', data),

  // /**
  //  * @description 查询带组合条件的筛选条件（顺序）
  //  * @api /filter/buildQueryItems
  //  */
  // buildQueryItems: (data: { schemeId: number }) =>
  //   request.post('/api/filter/buildQueryItems', data),
};

export default CostApi;
