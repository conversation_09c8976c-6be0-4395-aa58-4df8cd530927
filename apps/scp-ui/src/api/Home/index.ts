import request from '@/services';
import { GetCoreDataReq, GetCustomerDataReq } from './model';

const HomeApi = {
  getCustomerData: (data: GetCustomerDataReq) =>
    request.post('/api/dashboard/dashboardInfo', data),

  getCoreData: (data: GetCoreDataReq) =>
    request.get('/api/dashboard/dashBoardResource', { params: data }),

  // 获取客户与物料信息接口
  // GET /dashboard/getDetPeg
  getDetPegData: (data: object) =>
    request.get('/api/dashboard/getDetPeg', { params: data }),

  //获取首页消息数组
  getHomeMessage: (data: object) =>
    request.get('/api/home/<USER>', { params: data }),
  //获取计划域和计划版本
  getAreaAndVersion: () =>
    request.get('/api/mdDataView/getQualifiedAreaVersion'),
  /**
   * @description 获取AI开关状态
   * */
  getAiSwitch: () => request.get('/api/appBuilder/getIsAiStatus'),

  /**
   * @description 发送AppBuilder的问答应用对话
   * */
  runQueryModel: (data: object) =>
    request.post('/api/appBuilder/runQueryModel', data),
};

export default HomeApi;
