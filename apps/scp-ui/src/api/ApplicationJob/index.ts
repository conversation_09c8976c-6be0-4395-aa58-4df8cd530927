import request from '@/services';
import { StartCopyVersionReq } from './model';

const ApplicationJobApi = {
  //跑计划接口/plan/startPlanningRun
  startCronJob: (data: object) => request.post('/api/cron/startCronJob', data),

  /**
   * @description: 复制计划版本数据
   * */

  startCopyVersion: (obj: StartCopyVersionReq) =>
    request.post('/api/run/startCopyVersion', obj),

  /**
   * @description:planObject同步（仅新增）
   * */

  regeneratingPlanningObjects: (paPvId: number) =>
    request.post('/api/run/regeneratingPlanningObjects', { paPvId }),

  ///plan/getJobTemplateByUserOnlyRead
  //获取用户只读的作业模板
  getJobTemplateByUserOnlyRead: (data: any) =>
    request.get('/api/plan/getJobTemplateByUserOnlyRead', { params: data }),
  ///plan/getJobTemplateFrameAndValue
  getJobTemplateFrameAndValue: (templateUuid: string) =>
    request.get('/api/plan/getJobTemplateFrameAndValue', {
      params: { templateUuid, cronFlag: true },
    }),
  //只获取时间的模版
  getJobTemplateFrame: () =>
    request.get('/api/plan/getJobTemplateFrameAndValue', {
      params: { cronFlag: true },
    }),
  ///plan/getJobSelectedTemplate
  getJobSelectedTemplate: () => request.get('/api/plan/getJobSelectedTemplate'),
};

export default ApplicationJobApi;
