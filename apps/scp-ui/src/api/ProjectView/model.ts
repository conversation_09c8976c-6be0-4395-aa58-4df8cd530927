export interface GetPstockReq {
  /**
   * 地点名称
   */
  locno: string;
  /**
   * 物料名称
   */
  matnr: string;
  /**
   * 计划域、计划版本唯一id
   */
  paPvId: number;
  [property: string]: any;
}

export interface GetStockBaseInfoReq {
  /**
   * 筛选条件
   */
  filter: string;
  /**
   * 菜单页面唯一标识
   */
  menuTag: string;
  /**
   * 分页页码
   */
  page: string;
  /**
   * 分页大小
   */
  pageSize: string;
  /**
   * 计划域、计划版本唯一标识id
   */
  paPvId: number;
  [property: string]: any;
}
