import request from '@/services';
const ProjectViewApi = {
  getProjectView: (data: object) =>
    request.get('/api/inventoryAnaly/getPstock', {
      params: data,
    }),
  getStockBaseInfo: (data: any) =>
    request.get('/api/inventoryAnaly/getStockBaseInfo', {
      params: {
        filter: JSON.stringify(data.filterArr),
        menuTag: data.menuTag,
        paPvId: data.paPvId,
        pageSize: data.pageSize,
        page: data.page,
      },
    }),
};
export default ProjectViewApi;
