import request from '@/services';

const GraphApi = {
  getGraphData: (data: any) =>
    request.get('/api/graph/getGraphData', { params: data }),
  getInfoByNodeId: (data: any) =>
    request.get('/mock/graph/getInfoById', { params: data }),
  //获取所有的OrderId
  getAllOrderId: (data: any) =>
    request.get('/api/graph/getAllOrderId', { params: data }),
  //获取detailData
  getDetailData: (data: any) =>
    request.get('/api/graph/getDetailOrderData', { params: data }),
};

export default GraphApi;
