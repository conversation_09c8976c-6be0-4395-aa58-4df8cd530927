/*
 * @Author: jinx<PERSON><PERSON><PERSON>
 * @Date: 2023-11-27 11:11:38
 * @LastEditors: jinxu<PERSON>iu
 * @LastEditTime: 2023-12-18 10:48:23
 * @Description: 请填写简介
 */
import HomeApi from './Home';
import LoginApi from './Login';
import ViewApi from './ViewMasterData';
import ProjectViewApi from './ProjectView';
import Demandful<PERSON><PERSON> from './Demandful';
import GraphApi from './Graph';
import PlanningView from './PlanningView';
import Supplyusage from './Supplyusage';
import ApplicationJobApi from './ApplicationJob';
import ApplicationLog from './ApplicationLog';
import UserPage from './UserPage';
import CommApi from './Common';
import Mobile from '@/api/mobile';
import FilterApi from '@/api/Filter';
import BigApi from '@/api/BIG';
import ApplicationJobDetailApi from '@/api/ApplicationJobDetail';
import ConfigurationApi from '@/api/Configuration';
import <PERSON>A<PERSON> from './Authority';
import JobTemplate from './JobTemplate';
import EditJobTemplateApi from './EditJobTemplate';
import SettingApi from './Setting';
import CostApi from './Cost';
import ProcessManagement from './ProcessManagement';

export default {
  ...HomeApi,
  ...UserPage,
  ...ApplicationJobApi,
  ...ApplicationLog,
  ...CommApi,
  ...GraphApi,
  ...ViewApi,
  ...LoginApi,
  ...ProjectViewApi,
  ...DemandfulApi,
  ...PlanningView,
  ...Supplyusage,
  ...Mobile,
  ...FilterApi,
  ...BigApi,
  ...ApplicationJobDetailApi,
  ...ConfigurationApi,
  ...AuthorityApi,
  ...JobTemplate,
  ...EditJobTemplateApi,
  ...SettingApi,
  ...CostApi,
  ...ProcessManagement,
};
