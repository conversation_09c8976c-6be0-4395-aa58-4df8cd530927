import { useState, useEffect } from 'react';
import { route_info, type RouteInfoParams } from '@/router';
import { AllowedRoute } from '@/router/type.ts';
import ajax from '@/api';
import _ from 'lodash';

export default function useAuth(initMenus: AllowedRoute[]) {
  const [authMenus, setAuthMenus] = useState<Array<AllowedRoute>>(initMenus);
  const [loading, setLoading] = useState(true); // 初始状态设置为 true
  const [menuList, setMenuList] = useState<RouteInfoParams[]>([]);

  const getAuth = async () => {
    try {
      const res = await ajax.getTierMenus();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];

        // 辅助函数，递归构建子菜单（多层路由匹配）
        const buildTree = (parentPath: string, list: any[]): any[] => {
          const parentSegments = parentPath.split('/').filter(Boolean);
          return list
            .filter((item) => {
              if (!item.path.startsWith(parentPath + '/')) return false;
              const itemSegments = item.path.split('/').filter(Boolean);
              return itemSegments.length === parentSegments.length + 1;
            })
            .map((item) => {
              const subtree = buildTree(item.path, list);
              const node: any = { name: item.menuName, path: item.path };
              if (subtree.length > 0) node.children = subtree;
              return node;
            });
        };

        // 构建菜单树，使用 route_info 作为基础
        setMenuList(() => {
          const _list = route_info.filter((item: any) => {
            return list.some((route: any) => route.path.startsWith(item.path));
          });
          _list.forEach((item: any) => {
            const match = list.find((route: any) => route.path === item.path);
            if (match) {
              item.name = match.menuName;
            }
            const children = buildTree(item.path, list);
            if (children.length > 0) {
              item.children = children;
            }
          });
          return _list;
        });
        setAuthMenus(list);
      }
    } catch (error) {
      console.error('错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!window.location.pathname.includes('/login')) {
      getAuth();
    }
  }, [window.location.pathname]);

  return {
    authMenus,
    loading,
    menuList,
  };
}
