import { message } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';

function useJwMessage(): readonly [
  MessageInstance,
  React.ReactElement<any, string | React.JSXElementConstructor<any>>,
] {
  message.config({
    top: 100,
    duration: 1,
    maxCount: 2,
    rtl: true,
    prefixCls: 'jw-message',
  });
  const [messageApi, contextHolder] = message.useMessage();
  return [messageApi, contextHolder];
}

export default useJwMessage;
