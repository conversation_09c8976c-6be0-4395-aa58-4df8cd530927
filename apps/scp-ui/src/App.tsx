import React, { useEffect, useState } from 'react';
import { useRoutes } from 'react-router-dom';
import { App as AntdApp } from 'antd';
import 'scmify-components/styles.css';
import { ErrorBoundary } from '@ant-design/pro-components';
import { Suspense } from 'react';
import { type RouteObject, Navigate, matchPath } from 'react-router-dom';
import { Layout } from '@/components';
import Page from '@/pages';
import { AllowedRoute } from './router/type';
import { createNestedRoutes } from './router';
import { useAuth } from '@/hooks';
const { Login, NotFound, SSOLogin } = Page;

function App() {
  function findBestMatch(path: string, allowedRoutes: Array<AllowedRoute>) {
    let bestMatch = null;
    let highestScore = 0;

    for (const route of allowedRoutes) {
      const match = matchPath(route.path, path);
      if (match) {
        const score = match.pathname.split('/').filter(Boolean).length;
        if (score > highestScore) {
          highestScore = score;
          bestMatch = route;
        }
      }
    }
    return bestMatch;
  }

  const [dynamicRoutes, setDynamicRoutes] = useState<RouteObject[]>([]);
  const { loading, authMenus } = useAuth([]);

  const staticRoutes: RouteObject[] = [
    {
      path: '/',
      element: <Layout />,
      children: [
        {
          index: true,
          errorElement: <ErrorBoundary />,
          element: <Navigate to='/home' />, // 将这里改为一个有效的路由
        },
        ...dynamicRoutes, // 在这里展开动态路由
        {
          path: '*',
          element: (
            <Suspense fallback={<div>Loading...</div>}>
              <NotFound
                findBestMatch={(path: any) => findBestMatch(path, [])}
              />
            </Suspense>
          ),
        },
      ],
    },
    {
      path: '/login',
      children: [
        {
          index: true,
          element: <Login />,
        },
        {
          path: 'DingTalk',
          element: <SSOLogin />,
        },
        {
          path: 'EnterpriseWeChat',
          element: <SSOLogin />,
        },
        {
          path: 'Feishu',
          element: <SSOLogin />,
        },
      ],
    },
  ];

  useEffect(() => {
    if (!window.location.pathname.includes('/login')) {
      const _routes = createNestedRoutes(authMenus);
      // console.log(_routes, 'routes');
      setDynamicRoutes(_routes);
    }
  }, [window.location.pathname, loading]);

  const router = useRoutes(staticRoutes);

  if (loading && !window.location.pathname.includes('/login')) {
    return <div>Loading...</div>;
  }

  return (
    <AntdApp>
      <ErrorBoundary>
        <Suspense fallback={<div>Loading...</div>}>{router}</Suspense>
      </ErrorBoundary>
    </AntdApp>
  );
}

export default App;
