import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import autoMergeLevel2 from 'redux-persist/lib/stateReconciler/autoMergeLevel2';

import messageSlice from './features/messageSlice';
import navSlice from './features/navSlice';
import layoutSlice from './features/layoutSlice';
import loginSlice from './features/loginSlice';
import demandfulSlice from './features/demandfulSlice';
import bigSlice from './features/bigSlice';
import applicationJobSlice from './features/applicationJobSlice';
import planningViewSlice from './features/planningViewSlice';
import homeSlice from './features/homeSlice';

const persistConfig = {
  key: 'root',
  storage,
  stateReconciler: autoMergeLevel2,
};

const rootReducer = combineReducers({
  nav: navSlice, // 修正 navSlice 名称
  message: messageSlice,
  layout: layoutSlice,
  login: loginSlice,
  demandful: demandfulSlice,
  big: bigSlice, // 修正 bigSlice 名称
  applicationJob: applicationJobSlice, // 修正 applicationJobSlice 名称
  planningView: planningViewSlice, // 修正 planningViewSlice 名称
  home: homeSlice, // 修正 homeSlice 名称
});
export type RootState = ReturnType<typeof rootReducer>;

const persistedReducer = persistReducer<RootState>(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store;

export const persistor = persistStore(store);
