import { createSlice } from '@reduxjs/toolkit';

export interface MessageProps {
  title: string;
  content: string;
  fromUser: string;
  toUser: string;
  isRead: boolean;
  time: string;
}
const initialState: { message: MessageProps[] } = {
  message: [],
};

export const messageSlice = createSlice({
  name: 'message',
  initialState,
  reducers: {
    setMessage: (state, { payload }) => {
      state.message = payload;
    },
  },
});

export const { setMessage } = messageSlice.actions;

export default messageSlice.reducer;
