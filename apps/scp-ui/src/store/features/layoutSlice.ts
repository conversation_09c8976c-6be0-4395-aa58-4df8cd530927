import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  pAreaList: [],
  pVersionList: [],
  againLoginVisible: false,
  language: 'zh',
  masterList: [],
  showBeLoginCovered: false,
};

const layoutSlice = createSlice({
  name: 'layout',
  initialState,
  reducers: {
    setPAreaList: (state, { payload }) => {
      state.pAreaList = payload;
    },
    setPVersionList: (state, { payload }) => {
      state.pVersionList = payload;
    },
    setAgainLoginVisible: (state, { payload }) => {
      if (window.location.pathname !== '/login') {
        state.againLoginVisible = payload;
      } else {
        state.againLoginVisible = false;
      }
    },
    setLanguage: (state, { payload }) => {
      state.language = payload;
    },
    setMasterList: (state, { payload }) => {
      state.masterList = payload;
    },
    setShowbeLoginCovered: (state, { payload }) => {
      state.showBeLoginCovered = payload;
    },
  },
});

export const {
  setPAreaList,
  setPVersionList,
  setAgainLoginVisible,
  setLanguage,
  setMasterList,
  setShowbeLoginCovered,
} = layoutSlice.actions;

export default layoutSlice.reducer;
