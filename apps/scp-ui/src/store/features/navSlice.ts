import { createSlice } from '@reduxjs/toolkit';
export interface NavProps {
  key: string;
  label: string;
}
export interface NavState {
  navs: Array<NavProps>;
}

const initialState: NavState = {
  navs: [],
};

export const NavSlice = createSlice({
  name: 'navs',
  initialState,
  reducers: {
    addNav: (state, { payload }) => {
      state.navs.push(payload);
    },
    subNav: (state, { payload }) => {
      const index = state.navs.map((item) => item.key).indexOf(payload);
      state.navs.splice(index, 1);
    },
  },
});

export const { addNav, subNav } = NavSlice.actions;

export default NavSlice.reducer;
