import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  TABLE_DATA: [],
  TOTAL: 0,
  LOADING: false,
  FORM_VLAUE: {
    paPvId: 1,
  },
  FILTER_FORM_LIST: [
    {
      optionName: '需求',
      type: '包含',
      value: '',
    },
  ],
  CHOOSE_TAG_DATA: {},
  IS_SAVE_MODAL_OPEN: false,
  IS_ADD_MODAL_OPEN: false,
};

const demandfulSlice = createSlice({
  name: 'demandful',
  initialState,
  reducers: {
    SET_TABLE_DATA: (state, { payload }) => {
      state.TABLE_DATA = payload;
    },
    SET_TOTAL: (state, { payload }) => {
      state.TOTAL = payload;
    },
    SET_LOADING: (state, { payload }) => {
      state.LOADING = payload;
    },
    SET_FORM_VLAUE: (state, { payload }) => {
      state.FORM_VLAUE = payload;
    },
    SET_FILTER_FORM_LIST: (state, { payload }) => {
      state.FILTER_FORM_LIST = payload;
    },
    SET_CHOOSE_TAG_DATA: (state, { payload }) => {
      state.CHOOSE_TAG_DATA = payload;
    },
    SET_IS_SAVE_MODAL_OPEN: (state, { payload }) => {
      state.IS_SAVE_MODAL_OPEN = payload;
    },
    SET_IS_ADD_MODAL_OPEN: (state, { payload }) => {
      state.IS_ADD_MODAL_OPEN = payload;
    },
  },
});

export const {
  SET_TABLE_DATA,
  SET_TOTAL,
  SET_LOADING,
  SET_FORM_VLAUE,
  SET_FILTER_FORM_LIST,
  SET_CHOOSE_TAG_DATA,
  SET_IS_SAVE_MODAL_OPEN,
  SET_IS_ADD_MODAL_OPEN,
} = demandfulSlice.actions;

export default demandfulSlice.reducer;
