import { createSlice } from '@reduxjs/toolkit';

export interface CounterState {
  CHOOSE_LIST: any;
}

const initialState: CounterState = {
  CHOOSE_LIST: [],
};

export const bigSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    SET_CHOOSE_LIST: (state, { payload }) => {
      state.CHOOSE_LIST = payload;
    },
  },
});

export const { SET_CHOOSE_LIST } = bigSlice.actions;

export default bigSlice.reducer;
