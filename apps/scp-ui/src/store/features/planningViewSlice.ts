import { createSlice } from '@reduxjs/toolkit';
import { SelectProps } from 'antd';
export interface PlanningViewState {
  kfList: SelectProps['options'];
  attributeList: SelectProps['options'];
}

const initialState: PlanningViewState = {
  kfList: [],
  attributeList: [],
};

export const PlanningViewSlice = createSlice({
  name: 'planningView',
  initialState,
  reducers: {
    setKfList: (state, { payload }) => {
      state.kfList = payload;
    },
    setAttributeList: (state, { payload }) => {
      state.attributeList = payload;
    },
  },
});

export const { setKfList, setAttributeList } = PlanningViewSlice.actions;

export default PlanningViewSlice.reducer;
