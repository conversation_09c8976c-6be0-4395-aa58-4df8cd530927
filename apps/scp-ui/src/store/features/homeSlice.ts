import { createSlice } from '@reduxjs/toolkit';
import { t } from '@/languages';

export interface CounterState {
  charts: any[];
  tables: any[];
  editable: boolean;
}

const initialState: CounterState = {
  charts: [
    {
      key: 0,
      title: t('订单信息-饼状图'),
      data: {
        客户1: 15,
        客户2: 25,
        客户3: 35,
      },
      typeOptions: { bar: t('柱状图'), line: t('折线图'), pie: t('饼状图') },
      type: 'pie',
      percent: 25,
    },
    {
      key: 1,
      title: t('订单信息-柱状图'),
      data: {
        按时交付: [15, 9, 26],
        推迟交付: [12, 17, 21],
        未交付: [24, 7, 18],
      },
      dataGroup: ['订单一', '订单二', '订单三'],
      typeOptions: { bar: t('柱状图'), line: t('折线图'), pie: t('饼状图') },
      type: 'bar',
      percent: 25,
    },
    {
      key: 2,
      title: t('核心产线使用率'),
      data: {
        TR_长1: 120,
        TR_珠1: 200,
        TR_长2: 150,
        TR_珠2: 80,
        TR_长3: 70,
      },
      typeOptions: { bar: t('柱状图'), line: t('折线图'), pie: t('饼状图') },
      type: 'bar',
      percent: 25,
    },
    {
      key: 3,
      title: t('供应追溯'),
      data: {
        半成品1: [15, 12],
        半成品2: [9, 17],
        半成品3: [13, 28],
      },
      dataGroup: ['客户一', '客户二'],
      typeOptions: { bar: t('柱状图'), line: t('折线图'), pie: t('饼状图') },
      type: 'bar',
      percent: 25,
    },
  ],
  tables: [
    {
      key: 0,
      title: t('客户信息表'),
    },
  ],
  editable: false,
};

export const homeSlice = createSlice({
  name: 'home',
  initialState,
  reducers: {
    setCharts: (state, { payload }) => {
      state.charts = payload;
    },
    setTables: (state, { payload }) => {
      state.tables = payload;
    },
    setEditable: (state, { payload }) => {
      state.editable = payload;
    },
  },
});

export const { setCharts, setTables, setEditable } = homeSlice.actions;

export default homeSlice.reducer;
