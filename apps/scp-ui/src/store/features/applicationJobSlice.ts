import { createSlice } from '@reduxjs/toolkit';

export interface CounterState {
  PARAMS: any;
}

const initialState: CounterState = {
  PARAMS: {},
};

export const applicationJobSlice = createSlice({
  name: 'applicationJob',
  initialState,
  reducers: {
    SET_PARAMS: (state, { payload }) => {
      state.PARAMS = { ...payload };
      console.log(state.PARAMS, 'state.PARAMS');
    },
  },
});

export const { SET_PARAMS } = applicationJobSlice.actions;

export default applicationJobSlice.reducer;
