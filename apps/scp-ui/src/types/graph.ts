type baseNodeAndEdge = {
  id: string;
  status: 'normal' | 'warning' | 'error';
};

export type CommonGNode = {
  data: Record<string, any> &
    baseNodeAndEdge & {
      type: 'matnr' | 'order' | 'transportation' | 'production';
    };
};

export type CommonGEdge = {
  data: Record<string, any> &
    baseNodeAndEdge & { source: string; target: string };
};

export type Elements = (CommonGNode | CommonGEdge)[];
