import '@testing-library/jest-dom';
import 'vitest-canvas-mock';
import { render } from '@testing-library/react';
import { server } from '@/mock/server';

beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
  Object.defineProperty(window, 'getComputedStyle', {
    value: () => ({
      getPropertyValue: (prop: any) => {
        if (prop === 'display') return 'block';
        if (prop === 'visibility') return 'visible';
        if (prop === 'height') return '100px';
        return '';
      },
    }),
  });
});
afterAll(() => server.close());
afterEach(() => server.resetHandlers());
//@ts-expect-error  Mocking createObjectURL for tests
global.URL.createObjectURL = function () {};
window.matchMedia =
  window.matchMedia ||
  function () {
    return {
      matches: false,
      addListener: function () {},
      removeListener: function () {},
    };
  };

function customRender(ui: React.ReactElement, options = {}) {
  return render(ui, {
    // wrap provider(s) here if needed
    wrapper: ({ children }) => children,
    ...options,
  });
}

export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
// override render export
export { customRender as render };
