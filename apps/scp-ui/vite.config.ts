/// <reference types="vitest" />
import { defineConfig } from 'vite';
// import { Plugin as importToCDN } from 'vite-plugin-cdn-import';
import react from '@vitejs/plugin-react';
import path from 'path';
import compress from 'vite-plugin-compress';
import svgr from 'vite-plugin-svgr';
import i18nPlugin from '@voerkai18n/plugins/vite';
import AutoImport from 'unplugin-auto-import/vite';
// import Voerkai18nPlugin from "@voerkai18n/vite"
export default defineConfig({
  plugins: [
    i18nPlugin(),
    react(),
    svgr({
      include: '**/*.svg?react',
    }),
    AutoImport({
      dts: true,
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
      ],
      imports: [
        {
          '@/languages': ['t'],
        },
        {
          zod: ['z'],
        },
      ],
    }),
    // importToCDN({
    //   modules: [
    //     {
    //       name: 'react',
    //       var: 'React',
    //       path: 'https://scp-static-js.oss-cn-hangzhou.aliyuncs.com/react.production.min.js',
    //     },
    //     {
    //       name: 'react-dom',
    //       var: 'ReactDOM',
    //       path: 'https://scp-static-js.oss-cn-hangzhou.aliyuncs.com/react-dom.production.min.js',
    //     },
    //     {
    //       name: '@grapecity/spread-sheets',
    //       var: 'GC',
    //       path: 'https://scp-static-js.oss-cn-hangzhou.aliyuncs.com/gc.spread.sheets.all.min.js',
    //     },
    //     {
    //       name: 'echarts',
    //       var: 'echarts',
    //       path: 'https://scp-static-js.oss-cn-hangzhou.aliyuncs.com/echarts.min.js',
    //     },
    //   ],
    // }),
    compress as any,
  ],
  server: {
    host: '0.0.0.0',
    hmr: true,
    open: false,
    proxy: {
      '/api': {
        target: 'http://**************:50151',
        // target: 'http://localhost:50052',
        // target: 'http://*************:50052',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/jump/scpJumpToInt': {
        target: 'http://localhost:50151',
        changeOrigin: true,
      },
      '/mock': {
        target: 'http://127.0.0.1:4523/m1/3469938-0-default',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/mock\/api/, ''),
      },
    },
  },
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        // drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      // external: [
      //   'echarts',
      //   'react',
      //   'react-dom',
      //   '@grapecity/spread-sheets',
      //   '@grapecity/spread-sheets-react',
      // ],
      output: {
        manualChunks: {
          antd: ['antd', '@ant-design/icons'],
        },
      },
    },
    chunkSizeWarningLimit: 3000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
    css: true,
  },
});
