{"name": "scp-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "VITE_ENABLE_MSW=false vite --force --mode development", "dev:mock": "VITE_ENABLE_MSW=true vite --force --mode development", "prod": "vite --mode production", "build": "tsc  && vite build", "build:dev": "tsc && vite build --mode development", "build:prod": "tsc && vite build --mode production", "test": "vitest", "eslint": "eslint . --max-warnings 0 --fix", "preview": "vite preview", "lint": "lint-staged", "install:husky": "husky install", "commitlint": "commitlint --config commitlint.config.cjs -e -V"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.3", "@ant-design/x": "1.0.5", "@antv/s2": "^2.1.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@grapecity/spread-sheets": "16.2.5", "@grapecity/spread-sheets-react": "16.2.5", "@internationalized/date": "^3.6.0", "@nextui-org/react": "^2.6.11", "@nutui/nutui-react": "^2.7.5", "@react-aria/i18n": "^3.12.4", "@reduxjs/toolkit": "^1.9.7", "@repo/configuration": "workspace:*", "@repo/ui": "workspace:^", "@rollup/plugin-inject": "^5.0.5", "@voerkai18n/formatters": "^3.0.10", "@voerkai18n/react": "^3.0.10", "@voerkai18n/runtime": "^3.0.10", "ahooks": "^3.8.4", "antd": "^5.23.0", "antd-style": "^3.7.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "crypto-js": "^4.2.0", "cytoscape": "^3.30.4", "cytoscape-canvas": "^3.0.1", "cytoscape-dagre": "^2.5.0", "cytoscape-html": "^0.1.2", "cytoscape-klay": "^3.1.4", "cytoscape-popper": "^2.0.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "dotenv-expand": "^11.0.7", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^11.16.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "maplibre-gl": "^3.6.2", "moment": "^2.30.1", "nprogress": "^0.2.0", "postcss-antd-fixes": "^0.2.0", "process": "^0.11.10", "qs": "^6.13.1", "quarterOfYear": "link:dayjs/plugin/quarterOfYear", "re-resizable": "^6.10.3", "react": "^18.3.1", "react-activation": "^0.13.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-i18next": "^13.5.0", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.3", "react-redux": "^8.1.3", "react-router-dom": "^6.28.1", "redux-persist": "^6.0.0", "scmify-components": "^0.1.5", "styled-components": "^6.1.14", "tabulator-tables": "^6.3.0", "unplugin-auto-import": "^19.3.0", "updateLocale": "link:dayjs/plugin/updateLocale", "uuid": "^11.0.5", "vconsole": "^3.15.1", "weekOfYear": "link:dayjs/plugin/weekOfYear"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwindcss-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@turbo/gen": "^2.3.3", "@types/crypto-js": "^4.2.2", "@types/cytoscape": "^3.21.8", "@types/cytoscape-dagre": "^2.3.3", "@types/cytoscape-klay": "^3.1.4", "@types/cytoscape-popper": "^2.0.4", "@types/leaflet": "^1.9.16", "@types/lodash": "^4.17.14", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.5", "@types/tabulator-tables": "^6.2.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "@voerkai18n/plugins": "^3.0.10", "autoprefixer": "^10.4.20", "brower@latest": "link:msw/brower@latest", "commitlint": "^17.8.1", "cross-env": "^7.0.3", "history": "^5.3.0", "husky": "^8.0.3", "jsdom": "^22.1.0", "less": "^4.2.1", "lint-staged": "^15.3.0", "msw": "^2.7.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "terser": "^5.37.0", "typescript": "^5.7.2", "vite": "^5.4.11", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compress": "^2.1.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^0.34.6", "vitest-canvas-mock": "^0.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "msw": {"workerDirectory": ["public"]}}