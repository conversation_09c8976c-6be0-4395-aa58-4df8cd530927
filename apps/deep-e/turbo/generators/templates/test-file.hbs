# {{ title }}

### Created with Turborepo Generators

Read the docs at [turbo.build](https://turbo.build/repo/docs/core-concepts/monorepos/code-generation).

import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import {{ componentName }} from '.';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';

import store from '@/store/index.ts';

describe('{{ componentName }}', () => {
  it('should render page', () => {
    render(
      <Provider store={store}>
        <Router>
          <{{ componentName }} />
        </Router>
      </Provider>,
    );
    expect(screen.getByText('{{ pageText }}')).toBeInTheDocument();
  });

  it('should render with filter', () => {
    render(
      <Provider store={store}>
        <Router>
          <{{ componentName }} />
        </Router>
      </Provider>,
    );
    expect(
      screen.getByText(/{{ filterText }}/i, {
        selector: 'div',
      }),
    ).toBeInTheDocument();
  });

  it('should render table', () => {
    render(
      <Provider store={store}>
        <Router>
          <{{ componentName }} />
        </Router>
      </Provider>,
    );
    expect(screen.getAllByRole('table')).toHaveLength(1);
  });
});
