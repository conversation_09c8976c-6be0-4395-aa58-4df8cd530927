import { createStoreWithMiddlewares } from '../zustand_index';
import { create } from 'zustand';
import { BubbleProps } from '@ant-design/x';

interface PromptsState {
  charts: Record<string, BubbleProps[]>;
}

interface PropmtsActions {
  setCharts: (charts: Record<string, BubbleProps[]>) => void;
  addMessageToChart: (key: string, message: BubbleProps) => void;
  clearAllCharts: () => void;
}

export const usePromptsStore = create(
  createStoreWithMiddlewares('prompts')<PromptsState & PropmtsActions>(
    (set, get) => ({
      charts: {},
      setCharts: (charts) => set({ charts }),
      addMessageToChart: (key: string, message: BubbleProps) => {
        const charts = get().charts;
        set({
          charts: {
            ...charts,
            [key]: [...(charts[key] || []), message],
          },
        });
      },
      clearAllCharts: () => set({ charts: {} }),
    }),
  ),
);
