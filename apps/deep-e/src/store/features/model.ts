import { createStoreWithMiddlewares } from '../zustand_index';
import { create } from 'zustand';
interface ModelConfig {
  modelConfigId: number;
  modelPath: string;
  taskUuid: string;
  modelName: string;
}

interface ModelState {
  modelList: ModelConfig[];
}
interface ModelActions {
  setModelList: (modelList: ModelConfig[]) => void;
}
export const useModelStore = create(
  createStoreWithMiddlewares('model')<ModelState & ModelActions>((set) => ({
    modelList: [],
    setModelList: (modelList) => {
      set((state) => ({
        ...state,
        modelList: modelList,
      }));
    },
  })),
);
