import { createStoreWithMiddlewares } from '../zustand_index';
import { create } from 'zustand';
import { z } from 'zod';

export const InternationalSchema = z.object({
  language: z.enum(['zh', 'en', 'de']),
  dayFormatMap: z.object({
    zh: z.string(),
    en: z.string(),
    de: z.string(),
  }),
  dayFormat: z.string(),
});

type InternationalState = z.infer<typeof InternationalSchema>;
interface InternationalActions {
  setLanguage: (language: InternationalState['language']) => void;
  getDayFormat: (
    language: InternationalState['language'],
  ) => InternationalState['dayFormat'];
}
export const useInternationalStore = create(
  createStoreWithMiddlewares('InterNationalName')<
    InternationalState & InternationalActions
  >((set, get) => ({
    language: 'zh',
    dayFormat: 'YYYY-MM-DD HH:mm:ss',
    dayFormatMap: {
      zh: 'YYYY-MM-DD HH:mm:ss',
      en: 'MM-DD-YYYY HH:mm:ss',
      de: 'DD-MM-YYYY HH:mm:ss',
    },

    setLanguage: (language) => set({ language }),
    getDayFormat(language: InternationalState['language']) {
      const dayFormat = get().dayFormatMap[language];
      set({ dayFormat });
      return dayFormat;
    },
  })),
);
