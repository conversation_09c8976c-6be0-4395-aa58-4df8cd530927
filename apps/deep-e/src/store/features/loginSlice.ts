import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
  token: null,
  showLoginConfirm: false,
};

const loginSlice = createSlice({
  name: 'login',
  initialState,
  reducers: {
    setUser: (state, { payload }) => {
      state.user = payload;
    },
    setToken: (state, { payload }) => {
      state.token = payload;
    },
    setShowLoginConfirm: (state, { payload }) => {
      state.showLoginConfirm = payload;
    },
  },
});

export const { setUser, setToken, setShowLoginConfirm } = loginSlice.actions;
export default loginSlice.reducer;
