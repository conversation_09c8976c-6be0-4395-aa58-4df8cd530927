import { create } from 'zustand';
import { createStoreWithMiddlewares } from '../zustand_index';

interface LoginState {
  user: any | null;
  token: string | null;
  showLoginConfirm: boolean;
  language: string;
}

interface LoginActions {
  setUser: (user: any) => void;
  setToken: (token: string | null) => void;
  setShowLoginConfirm: (show: boolean) => void;
  setLanguage: (language: string) => void;
  logout: () => void;
}

export const useAuthStore = create(
  createStoreWithMiddlewares('auth-store')<LoginState & LoginActions>(
    (set) => ({
      // 初始状态
      user: null,
      token: null,
      showLoginConfirm: false,
      language: 'en',

      // actions
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token }),
      setShowLoginConfirm: (show) => set({ showLoginConfirm: show }),
      logout: () => {
        set({ token: null, user: null });
        localStorage.clear();
      },
      setLanguage(language) {
        set({ language });
      },
    }),
  ),
);

export const getToken = () => useAuthStore.getState().token;
export const setToken = (token: string | null) =>
  useAuthStore.getState().setToken(token);

export const getLanguage = () => useAuthStore.getState().language;

export const logout = () => useAuthStore.getState().logout();
