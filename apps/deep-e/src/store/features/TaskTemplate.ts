import { createStoreWithMiddlewares } from '../zustand_index';
import { JobTemplatePageProps } from '@repo/ui';
import { create } from 'zustand';
import { authFetch } from '@/services/fetch';
export type PageDataType = {
  total: number;
  pageSize: number;
  current: number;
};

interface TaskTemplateState {
  tableData: JobTemplatePageProps['mainTableData']['dataSource'];
  pageData: PageDataType;
}
interface TaskTemplateActions {
  getTableData: (templateName?: string, steps?: number) => void;
  setPageData: (current: number) => void;
}
export const useTaskTemplateStore = create(
  createStoreWithMiddlewares('task-template')<
    TaskTemplateState & TaskTemplateActions
  >((set, get) => ({
    tableData: [],
    pageData: {
      current: 1,
      total: 0,
      pageSize: 10,
    },
    getTableData: async (templateName, steps) => {
      const params = new URLSearchParams({
        page: String(get().pageData.current),
        pageSize: String(get().pageData.pageSize),
        templateName: templateName || '',
        steps: steps ? String(steps) : '',
      });
      const res = await authFetch(
        `/api/plan/getJobTemplateByUserOnlyRead?${params}`,
      ).then((res) => res.json());
      set({
        tableData: res.data.list,
        pageData: {
          total: res.data.total || 0,
          current: res.data.page,
          pageSize: res.data.pageSize,
        },
      });
    },
    setPageData: (current) => {
      set({
        pageData: {
          ...get().pageData,
          current,
        },
      });
    },
  })),
);
