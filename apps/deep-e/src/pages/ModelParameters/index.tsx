import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Card,
  App,
  Space,
  Select,
  Popconfirm,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import { DeleteButton } from '@/components/ui/delete-button';
import usePageData from '@/hooks/usePageData';
import { t } from '@/languages';
import {
  getParameterFileList,
  createParameterFile,
  deleteParameterFile,
  getParameterFileDetail,
  addParameterToFile,
  deleteParameterFromFile,
  getDataTypes,
  type ParameterFile,
  type ParameterItem,
  type CreateParameterFileParams,
  type AddParameterParams,
} from './api';

// 简单的删除按钮组件，专用于参数详情弹窗
const SimpleDeleteButton: React.FC<{
  onConfirm: () => void;
  title: string;
  size?: 'small' | 'middle' | 'large';
}> = ({ onConfirm, title, size = 'small' }) => {
  return (
    <Popconfirm
      title={title}
      onConfirm={onConfirm}
      okText={t('确定')}
      cancelText={t('取消')}
      placement='topRight'>
      <Button type='link' danger size={size}>
        {t('删除')}
      </Button>
    </Popconfirm>
  );
};

// 参数值类型校验函数
const validateParameterValue = (fieldType: string, value: string): boolean => {
  if (!value || !fieldType) return false;

  switch (fieldType) {
    case 'int':
      // 整数校验：只能是正整数、负整数或0
      return /^-?\d+$/.test(value) && !isNaN(parseInt(value));

    case 'float':
      // 浮点数校验：支持小数、科学计数法
      return (
        /^-?\d*\.?\d+([eE][+-]?\d+)?$/.test(value) && !isNaN(parseFloat(value))
      );

    case 'bool':
      // 布尔值校验：只能是true或false（不区分大小写）
      return /^(true|false)$/i.test(value);

    case 'string':
      // 字符串校验：任何非空字符串都有效
      return value.trim().length > 0;

    default:
      return true;
  }
};

// 获取参数类型的错误提示信息
const getParameterTypeErrorMessage = (fieldType: string): string => {
  switch (fieldType) {
    case 'int':
      return t('请输入有效的整数，例如：123、-456、0');
    case 'float':
      return t('请输入有效的浮点数，例如：3.14、-2.5、1e-3');
    case 'bool':
      return t('请输入布尔值：true 或 false');
    case 'string':
      return t('请输入非空字符串');
    default:
      return t('请输入有效的参数值');
  }
};

// 获取参数类型的placeholder提示
const getParameterTypePlaceholder = (fieldType: string): string => {
  switch (fieldType) {
    case 'int':
      return t('例如：123、-456、0');
    case 'float':
      return t('例如：3.14、-2.5、1e-3');
    case 'bool':
      return t('输入 true 或 false');
    case 'string':
      return t('输入任意文本');
    default:
      return t('请输入参数值');
  }
};

export default function ModelParameters() {
  const { message } = App.useApp();
  const [parameterFiles, setParameterFiles] = useState<ParameterFile[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [isAddParameterModalVisible, setIsAddParameterModalVisible] =
    useState(false);
  const [selectedFile, setSelectedFile] = useState<ParameterFile | null>(null);
  const [parameterItems, setParameterItems] = useState<ParameterItem[]>([]);
  const [dataTypes, setDataTypes] = useState<string[]>([]);
  const [selectedFieldType, setSelectedFieldType] = useState<string>('');
  const [createForm] = Form.useForm();
  const [addParameterForm] = Form.useForm();
  const { pageData, setPageData, pagination } = usePageData();

  // 获取参数文件列表
  const fetchParameterFiles = async () => {
    try {
      setLoading(true);
      const res = await getParameterFileList(pageData);
      if (res.code === 0) {
        setParameterFiles(res.data.list);
        setPageData({
          ...pageData,
          total: res.data.total,
        });
      } else {
        message.error(res.msg || '获取参数文件列表失败');
        setParameterFiles([]);
        setPageData({
          ...pageData,
          total: 0,
        });
      }
    } catch (error) {
      console.error('获取参数文件列表失败:', error);
      message.error('获取参数文件列表失败，请检查网络连接或联系管理员');
      setParameterFiles([]);
      setPageData({
        ...pageData,
        total: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  // 创建参数文件
  const handleCreateFile = async (values: CreateParameterFileParams) => {
    try {
      const res = await createParameterFile(values);
      if (res.code === 0) {
        message.success('创建成功');
        setIsCreateModalVisible(false);
        createForm.resetFields();
        fetchParameterFiles();
      } else {
        message.error(res.msg || '创建参数文件失败');
      }
    } catch (error) {
      console.error('创建参数文件失败:', error);
      message.error('创建参数文件失败，请检查网络连接或联系管理员');
    }
  };

  // 删除参数文件
  const handleDeleteFile = async (profileUuid: string) => {
    try {
      const res = await deleteParameterFile({ profileUuid });
      if (res.code === 0) {
        message.success('删除成功');
        fetchParameterFiles();
      } else {
        message.error(res.msg || '删除参数文件失败');
      }
    } catch (error) {
      console.error('删除参数文件失败:', error);
      message.error('删除参数文件失败，请检查网络连接或联系管理员');
    }
  };

  // 查看参数文件详情
  const handleViewFile = async (file: ParameterFile) => {
    try {
      setSelectedFile(file);
      const res = await getParameterFileDetail({
        profileUuid: file.profileUuid,
        page: 1,
        pageSize: 100, // 获取所有参数
      });
      if (res.code === 0) {
        setParameterItems(res.data.list);
        setIsViewModalVisible(true);
      } else {
        message.error(res.msg || '获取参数文件详情失败');
        setParameterItems([]);
      }
    } catch (error) {
      console.error('获取参数文件详情失败:', error);
      message.error('获取参数文件详情失败，请检查网络连接或联系管理员');
      setParameterItems([]);
    }
  };

  // 表格列定义
  const columns: ColumnsType<ParameterFile> = [
    {
      title: t('参数文件名称'),
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <div className='flex items-center space-x-2'>
          <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
          <span className='font-medium text-gray-900'>{text}</span>
        </div>
      ),
    },
    {
      title: t('公司ID'),
      dataIndex: 'coId',
      key: 'coId',
      render: (text) => (
        <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800'>
          {text}
        </span>
      ),
    },
    {
      title: t('操作'),
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size='small'>
          <Button
            type='link'
            className='text-blue-600 hover:text-blue-800 font-medium'
            onClick={() => handleViewFile(record)}>
            {t('查看')}
          </Button>
          <DeleteButton
            onDelete={() => handleDeleteFile(record.profileUuid)}
            confirmDescription={t(
              '确定要删除参数文件"{name}"吗？',
              record.name,
            )}
          />
        </Space>
      ),
    },
  ];

  // 参数项表格列定义
  const parameterColumns: ColumnsType<ParameterItem> = [
    {
      title: t('参数标签'),
      dataIndex: 'fieldLabel',
      key: 'fieldLabel',
      render: (text) => (
        <span className='font-medium text-gray-900'>{text}</span>
      ),
    },
    {
      title: t('参数名'),
      dataIndex: 'fieldName',
      key: 'fieldName',
      render: (text) => (
        <code className='px-2 py-1 bg-gray-100 text-gray-800 rounded text-sm font-mono'>
          {text}
        </code>
      ),
    },
    {
      title: t('参数值'),
      dataIndex: 'fieldValue',
      key: 'fieldValue',
      render: (text) => <span className='text-gray-700 break-all'>{text}</span>,
    },
    {
      title: t('类型'),
      dataIndex: 'fieldType',
      key: 'fieldType',
      render: (text: string) => {
        const colorMap: Record<string, string> = {
          string: 'bg-blue-100 text-blue-800',
          int: 'bg-green-100 text-green-800',
          float: 'bg-yellow-100 text-yellow-800',
          bool: 'bg-purple-100 text-purple-800',
        };
        return (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[text] || 'bg-gray-100 text-gray-800'}`}>
            {text}
          </span>
        );
      },
    },
    {
      title: t('操作'),
      key: 'action',
      width: 80,
      render: (_, record) => (
        <SimpleDeleteButton
          onConfirm={() =>
            handleDeleteParameter(record.profileUuid, record.fieldName)
          }
          title={t('确定要删除参数"{name}"吗？', record.fieldLabel)}
          size='small'
        />
      ),
    },
  ];

  // 获取数据类型列表
  const fetchDataTypes = async () => {
    try {
      const res = await getDataTypes();
      if (res.code === 0) {
        setDataTypes(res.data.list);
      } else {
        message.error(res.msg || '获取数据类型失败');
        setDataTypes([]);
      }
    } catch (error) {
      console.error('获取数据类型失败:', error);
      message.error('获取数据类型失败，请检查网络连接或联系管理员');
      setDataTypes([]);
    }
  };

  // 新增参数
  const handleAddParameter = async (values: AddParameterParams) => {
    try {
      if (!selectedFile) return;

      const params = {
        profileUuid: selectedFile.profileUuid,
        fieldLabel: values.fieldLabel,
        fieldName: values.fieldName,
        fieldType: values.fieldType,
        fieldValue: values.fieldValue,
      };

      const res = await addParameterToFile(params);
      if (res.code === 0) {
        message.success('新增参数成功');
        setIsAddParameterModalVisible(false);
        addParameterForm.resetFields();
        // 刷新参数列表
        await handleViewFile(selectedFile);
      } else {
        message.error(res.msg || '新增参数失败');
      }
    } catch (error) {
      console.error('新增参数失败:', error);
      message.error('新增参数失败，请检查网络连接或联系管理员');
    }
  };

  // 删除参数
  const handleDeleteParameter = async (
    profileUuid: string,
    fieldName: string,
  ) => {
    try {
      const res = await deleteParameterFromFile({ profileUuid, fieldName });
      if (res.code === 0) {
        message.success('删除参数成功');
        // 刷新参数列表
        if (selectedFile) {
          await handleViewFile(selectedFile);
        }
      } else {
        message.error(res.msg || '删除参数失败');
      }
    } catch (error) {
      console.error('删除参数失败:', error);
      message.error('删除参数失败，请检查网络连接或联系管理员');
    }
  };

  // 打开新增参数Modal
  const handleOpenAddParameter = () => {
    setIsAddParameterModalVisible(true);
    setSelectedFieldType(''); // 重置选择的参数类型
    if (dataTypes.length === 0) {
      fetchDataTypes();
    }
  };

  useEffect(() => {
    fetchParameterFiles();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 w-full min-h-full'>
      {/* 主要内容卡片 */}
      <Card
        title={
          <div className='flex justify-between items-center'>
            <div>
              <span className='text-xl font-semibold'>{t('模型参数管理')}</span>
              {/* <div className='text-sm text-gray-500 mt-1 font-normal'>
                {t('管理和配置模型训练参数文件')}
              </div> */}
            </div>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => setIsCreateModalVisible(true)}>
              {t('新建参数文件')}
            </Button>
          </div>
        }
        className='w-full h-full'>
        <Table
          columns={columns}
          dataSource={parameterFiles}
          loading={loading}
          rowKey='profileUuid'
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: true }}
          size='middle'
        />
      </Card>

      {/* 新建参数文件Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-2'>
            <PlusOutlined className='text-blue-500' />
            <span className='text-lg font-semibold'>{t('新建参数文件')}</span>
          </div>
        }
        open={isCreateModalVisible}
        onOk={() => createForm.submit()}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
        }}
        okText={t('创建')}
        cancelText={t('取消')}
        className='rounded-lg'
        destroyOnHidden>
        <div className='py-4'>
          <Form form={createForm} layout='vertical' onFinish={handleCreateFile}>
            <Form.Item
              name='name'
              label={
                <span className='text-sm font-medium text-gray-700'>
                  {t('参数文件名称')}
                </span>
              }
              rules={[
                { required: true, message: t('请输入参数文件名称') },
                { max: 50, message: t('名称不能超过50个字符') },
              ]}>
              <Input
                placeholder={t('请输入参数文件名称')}
                className='rounded-md'
                size='large'
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>

      {/* 查看参数文件详情Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-2'>
            <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
            <span className='text-lg font-semibold'>
              {t('参数文件详情')} - {selectedFile?.name}
            </span>
          </div>
        }
        open={isViewModalVisible}
        onCancel={() => setIsViewModalVisible(false)}
        footer={[
          <Button
            key='close'
            size='large'
            onClick={() => setIsViewModalVisible(false)}
            className='rounded-md'>
            {t('关闭')}
          </Button>,
        ]}
        width={900}
        className='rounded-lg'
        destroyOnHidden>
        <div className='space-y-6 py-2'>
          {/* 基本信息卡片 */}
          <div className='bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100'>
            <h4 className='text-base font-semibold text-gray-800 mb-3 flex items-center'>
              <div className='w-1 h-4 bg-blue-500 rounded-full mr-2'></div>
              {t('基本信息')}
            </h4>
            <div className='grid grid-cols-1 gap-4'>
              <div className='bg-white rounded-md p-3 shadow-sm'>
                <div className='text-xs text-gray-500 mb-1'>{t('名称')}</div>
                <div className='font-medium text-gray-900'>
                  {selectedFile?.name}
                </div>
              </div>
            </div>
          </div>

          {/* 参数列表区域 */}
          <div className='bg-white rounded-lg border border-gray-200'>
            <div className='flex justify-between items-center p-4 border-b border-gray-200'>
              <h4 className='text-base font-semibold text-gray-800 flex items-center'>
                <div className='w-1 h-4 bg-green-500 rounded-full mr-2'></div>
                {t('参数列表')}
                <span className='ml-2 text-sm text-gray-500'>
                  ({parameterItems.length} {t('个参数')})
                </span>
              </h4>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                className='rounded-md shadow-sm hover:shadow-md transition-shadow'
                onClick={handleOpenAddParameter}>
                {t('新增参数')}
              </Button>
            </div>
            <div className='p-4'>
              <Table
                columns={parameterColumns}
                dataSource={parameterItems}
                rowKey='fieldName'
                pagination={false}
                size='middle'
                className='rounded-md border border-gray-100'
              />
            </div>
          </div>
        </div>
      </Modal>

      {/* 新增参数Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-2'>
            <PlusOutlined className='text-green-500' />
            <span className='text-lg font-semibold'>{t('新增参数')}</span>
          </div>
        }
        open={isAddParameterModalVisible}
        onOk={() => addParameterForm.submit()}
        onCancel={() => {
          setIsAddParameterModalVisible(false);
          addParameterForm.resetFields();
        }}
        okText={t('添加')}
        cancelText={t('取消')}
        width={600}
        className='rounded-lg'
        destroyOnHidden>
        <div className='py-4'>
          <Form
            form={addParameterForm}
            layout='vertical'
            onFinish={handleAddParameter}
            className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Form.Item
                name='fieldLabel'
                label={
                  <span className='text-sm font-medium text-gray-700'>
                    {t('参数标签')}
                  </span>
                }
                rules={[
                  { required: true, message: t('请输入参数标签') },
                  { max: 50, message: t('标签不能超过50个字符') },
                ]}>
                <Input
                  placeholder={t('请输入参数标签')}
                  className='rounded-md'
                  size='large'
                />
              </Form.Item>
              <Form.Item
                name='fieldName'
                label={
                  <span className='text-sm font-medium text-gray-700'>
                    {t('参数名')}
                  </span>
                }
                rules={[
                  { required: true, message: t('请输入参数名') },
                  { max: 50, message: t('参数名不能超过50个字符') },
                  {
                    pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                    message: t(
                      '参数名只能包含字母、数字和下划线，且不能以数字开头',
                    ),
                  },
                ]}>
                <Input
                  placeholder={t('请输入参数名')}
                  className='rounded-md'
                  size='large'
                />
              </Form.Item>
            </div>

            <Form.Item
              name='fieldType'
              label={
                <span className='text-sm font-medium text-gray-700'>
                  {t('参数类型')}
                </span>
              }
              rules={[{ required: true, message: t('请选择参数类型') }]}>
              <Select
                placeholder={t('请选择参数类型')}
                className='rounded-md'
                size='large'
                onChange={(value) => {
                  setSelectedFieldType(value);
                  // 当参数类型改变时，重新验证参数值
                  addParameterForm.validateFields(['fieldValue']);
                }}>
                {dataTypes.map((type) => (
                  <Select.Option key={type} value={type}>
                    <div className='flex items-center space-x-2'>
                      <span className='font-mono text-sm bg-gray-100 px-2 py-1 rounded'>
                        {type}
                      </span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name='fieldValue'
              label={
                <span className='text-sm font-medium text-gray-700'>
                  {t('参数值')}
                </span>
              }
              rules={[
                { required: true, message: t('请输入参数值') },
                { max: 200, message: t('参数值不能超过200个字符') },
                {
                  validator: (_, value) => {
                    const fieldType =
                      addParameterForm.getFieldValue('fieldType');
                    if (!fieldType) {
                      return Promise.reject(new Error(t('请先选择参数类型')));
                    }
                    if (value && !validateParameterValue(fieldType, value)) {
                      return Promise.reject(
                        new Error(getParameterTypeErrorMessage(fieldType)),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}>
              <Input
                placeholder={
                  selectedFieldType
                    ? getParameterTypePlaceholder(selectedFieldType)
                    : t('请先选择参数类型')
                }
                className='rounded-md'
                size='large'
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </div>
  );
}
