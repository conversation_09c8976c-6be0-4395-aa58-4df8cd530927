import request, { returnData } from '@/services';
import { PageData } from '@/types';

// 参数文件数据类型
export interface ParameterFile {
  profileUuid: string;
  name: string;
  coId: number;
}

// 参数项数据类型
export interface ParameterItem {
  profileUuid: string;
  fieldLabel: string;
  fieldName: string;
  fieldType: string;
  fieldValue: string;
  coId: number;
}

// 创建参数文件请求参数
export interface CreateParameterFileParams {
  name: string;
}

// 添加参数请求参数
export interface AddParameterParams {
  profileUuid: string;
  fieldLabel: string;
  fieldName: string;
  fieldType: string;
  fieldValue: string;
}

// 删除参数请求参数
export interface DeleteParameterParams {
  profileUuid: string;
  fieldName: string;
}

// 删除参数文件请求参数
export interface DeleteParameterFileParams {
  profileUuid: string;
}

// 获取参数详情请求参数
export interface GetParameterDetailParams extends PageData {
  profileUuid: string;
}

// 获取参数文件列表
export const getParameterFileList = (
  params: PageData,
): Promise<returnData<ParameterFile[]>> => {
  return request.get('/api/profile/getProfilesByCoId', { params });
};

// 创建参数文件
export const createParameterFile = (
  data: CreateParameterFileParams,
): Promise<returnData<any>> => {
  return request.post('/api/profile/createPlanProfile', data);
};

// 删除参数文件
export const deleteParameterFile = (
  data: DeleteParameterFileParams,
): Promise<returnData<any>> => {
  return request.post('/api/profile/deletePlanProfile', data);
};

// 获取参数文件详情
export const getParameterFileDetail = (
  params: GetParameterDetailParams,
): Promise<returnData<ParameterItem[]>> => {
  return request.get('/api/profile/getParamsByProfile', { params });
};

// 添加参数到参数文件
export const addParameterToFile = (
  data: AddParameterParams,
): Promise<returnData<any>> => {
  return request.post('/api/profile/addParamsToPlanProfile', data);
};

// 删除参数文件中的某个参数
export const deleteParameterFromFile = (
  data: DeleteParameterParams,
): Promise<returnData<any>> => {
  return request.post('/api/profile/deleteParamsToPlanProfile', data);
};

// 获取数据类型列表
export const getDataTypes = (): Promise<returnData<string[]>> => {
  return request.get('/api/profile/getDataType');
};
