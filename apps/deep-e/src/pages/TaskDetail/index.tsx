import {
  Flex,
  Typography,
  Input,
  Form,
  Col,
  Row,
  Checkbox,
  Button,
} from 'antd';
import LeftArrow from '@/assets/LeftArrow.svg';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

const TaskDetail = () => {
  const navigate = useNavigate();

  const [form] = Form.useForm();

  const labels = [
    {
      label: t('训练名称'),
      name: 'trainingName',
    },
    {
      label: t('连接器'),
      name: 'connector',
    },
    {
      label: t('数据'),
      name: 'data',
    },
    {
      label: t('基础模型'),
      name: 'baseModel',
    },
    {
      label: t('训练资源'),
      name: 'resource',
    },
    {
      label: t('训练参数'),
      name: 'parameters',
    },
  ];

  return (
    <div className='min-h-full h-full overflow-auto px-4'>
      <Flex
        vertical={false}
        gap={'30px'}
        align='center'
        style={{
          marginBottom: '22px',
          paddingTop: '10px',
        }}>
        <Flex vertical={false}>
          <img src={LeftArrow}></img>
          <span
            onClick={() => navigate(-1)}
            className='cursor-pointer text-s1'
            style={{
              color: '#2F6BFF',
            }}>
            {t('返回')}
          </span>
        </Flex>
        <Title
          level={4}
          className='mb-6 text-s1'
          style={{
            color: '#0D152D',
            margin: '0',
          }}>
          {t('任务详情')}
        </Title>
      </Flex>
      <Flex
        vertical={true}
        style={{
          backgroundColor: '#fff',
          borderRadius: '10px',
          padding: '23px 30px',
          marginBottom: '22px',
        }}>
        <Flex vertical={true} gap={10}>
          <h2
            className='text-s1'
            style={{
              color: '#0D152D',
            }}>
            {t('任务详情') + ':'}
          </h2>
          <Input disabled={true}></Input>
        </Flex>
        <Form
          form={form}
          style={{
            marginTop: '20px',
          }}>
          <Form.Item>
            <Row gutter={10}>
              {labels.map((item: any, index: number) => {
                return (
                  <Col span={6} key={item.name + index}>
                    <Flex vertical={true}>
                      <span>{item.label}</span>
                      <Form.Item name={item.name} noStyle>
                        <Input disabled={true}></Input>
                      </Form.Item>
                    </Flex>
                  </Col>
                );
              })}
            </Row>
            <Flex
              gap={10}
              align='center'
              style={{
                marginTop: '20px',
              }}>
              <Form.Item name={'saveNewModel'} noStyle>
                <Checkbox></Checkbox>
              </Form.Item>
              <span>{t('保存新模型')}</span>
              <Form.Item name={'newModelName'} noStyle>
                <Input
                  style={{
                    maxWidth: '200px',
                  }}></Input>
              </Form.Item>
            </Flex>
          </Form.Item>
        </Form>
      </Flex>
      <Flex gap={10}>
        <Button type={'primary'}>{t('复制任务')}</Button>
        <Button type={'primary'}>{t('训练新版本')}</Button>
      </Flex>
    </div>
  );
};

export default TaskDetail;
