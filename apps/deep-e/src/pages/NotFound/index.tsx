import { Link, useLocation } from 'react-router-dom';

interface NotFoundProps {
  findBestMatch: (path: string) => { path: string; component: string } | null;
}
const NotFound: React.FC<NotFoundProps> = ({ findBestMatch }) => {
  const location = useLocation();
  const bestMatch = findBestMatch(location.pathname);

  return (
    <div className='w-screen h-screen flex flex-col items-center justify-center min-h-screen bg-gray-100'>
      <h1 className='text-4xl font-bold mb-4'>404 - Page Not Found</h1>
      <p className='text-xl mb-8'>
        {`The page you are looking for doesn't exist or you don't have permission to view it.`}
      </p>
      {bestMatch && (
        <p className='text-lg mb-4'>
          Did you mean to visit
          <Link to={bestMatch.path} className='text-blue-500 hover:underline'>
            {bestMatch.component}
          </Link>
          ?
        </p>
      )}
      <Link
        to={'/'}
        className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors'>
        Go Home
      </Link>
    </div>
  );
};

export default NotFound;
