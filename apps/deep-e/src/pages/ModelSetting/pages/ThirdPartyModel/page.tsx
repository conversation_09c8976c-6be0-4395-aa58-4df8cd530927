import React, { useEffect, useState } from 'react';
import { Card, Button, Pagination, Form, App } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import CardItem from './components/CardItem';
import ConfigModel from './components/ConfigModel';
import { InnerIconProps } from '@/components/InnerIcon';
import { removeUndefinedValues } from '@/utils';
import FilterComponent from './components/filter-component';
import {
  getModelList,
  getModelTypeList,
  addModelConfig,
  getModelConfigList,
  updateModelConfig,
  deleteModelConfig,
  testModelPing,
  getModelConfigStateList,
  getModelFactoryList,
} from '../../api/ThirdPartyModel';

export interface ModelData {
  factory: InnerIconProps['vendor'];
  factoryTechnicalName: InnerIconProps['vendor'];
  modelConfigId: number;
  factoryName: string;
  factoryId: number;
  modelName: string;
  requestUrl: string;
  apiKey?: string;
  desc?: string;
  status: number;
  statusName: string;
  isPrivate: boolean;
  modelType: string;
}

const TrainedModel: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { modalOpen, modelName, serverURL } = location.state || {
    modelOpen: false,
    modelName: '',
    serverURL: '',
  };
  const [stateList, setStateList] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(modalOpen);
  const [providers, setProviders] = useState([]);
  const [isEditModel, setIsEditModel] = useState(!modalOpen);
  const [formData, setFormData] = useState<any>({});
  const [currentModel, setCurrentModel] = useState<ModelData | null>(() => {
    if (modalOpen) {
      return {
        modelConfigId: 0,
        factoryId: 3,
        factoryTechnicalName: 'ollama',
        factory: 'ollama',
        factoryName: '',
        modelName: modelName,
        requestUrl: serverURL,
        describe: '',
        status: 0,
        statusName: '',
        isPrivate: false,
        modelType: 'chat',
      };
    }
    return null;
  });
  const [modelTypes, setModelTypes] = useState([]);
  const [modelData, setModelData] = useState<ModelData[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [modelFactoryList, setModelFactoryList] = useState<any[]>([]);

  const filterData = {
    modelType: {
      title: t('类型'),
      options: modelTypes,
    },
    modelConfigState: {
      title: t('状态'),
      options: stateList.map((item) => ({
        label: item.stateName,
        value: item.state,
      })),
    },
    modelFactory: {
      title: t('提供商'),
      options: modelFactoryList.map((item) => ({
        label: item.factoryName,
        value: item.id,
      })),
    },
  };

  const getModelConfigStateListFetch = async () => {
    try {
      const res = await getModelConfigStateList();
      if (!res.code) {
        setStateList(res.data.list);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getModelFactoriesFetch = async () => {
    try {
      const res = await getModelFactoryList();
      if (!res.code) {
        setModelFactoryList(res.data.list);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleEdit = (id: number) => {
    const modelToEdit = modelData.find((model) => model.modelConfigId === id);
    setIsEditModel(true);
    if (modelToEdit) {
      setCurrentModel(modelToEdit);
      form.setFieldsValue({ ...modelToEdit, type: modelToEdit.modelType });
      setIsModalVisible(true);
    }
  };

  const getModelListFetch = async () => {
    try {
      const res = await getModelList();
      if (!res.code) {
        setProviders(
          res.data.list.map((item: any) => {
            return {
              label: item.factoryName,
              value: item.id,
            };
          }),
        );
      }
    } catch (error) {
      console.error('Error fetching model list:', error);
    }
  };

  const getModelTypeListFetch = async () => {
    try {
      const res = await getModelTypeList();
      if (!res.code) {
        setModelTypes(
          res.data.list.map((item: any) => ({
            label: item,
            value: item,
          })),
        );
      }
    } catch (error) {
      console.error('Error fetching model types:', error);
    }
  };
  const clearState = () => {
    // 保留当前路径，但清空 state
    navigate(location.pathname, { replace: true, state: {} });
  };

  const getModelConfigListFetch = async (
    page = pagination.current,
    pageSize = pagination.pageSize,
  ) => {
    try {
      const values = removeUndefinedValues(formData);

      const res = await getModelConfigList({ page, pageSize, ...values });
      if (!res.code) {
        setModelData(res.data.list as any);
        setPagination({
          current: page,
          pageSize,
          total: res.data.total || 0,
        });
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  const handlePageChange = (page: number, pageSize: number) => {
    getModelConfigListFetch(page, pageSize);
  };

  const handleDelete = async (id: number) => {
    try {
      const res = await deleteModelConfig({ modelConfigId: id });
      if (res.code === 0) {
        message.success(t('模型删除成功'));
        getModelConfigListFetch(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      message.error(t('模型删除失败'));
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (isEditModel && currentModel) {
        // 如果apiKey为-1，说明用户没有修改密钥
        if (values.apiKey === '-1') {
          delete values.apiKey; // 删除apiKey字段，不更新密钥
        }
        console.log('Editing model:', values);
        const res = await updateModelConfig({
          ...values,
          modelConfigId: currentModel.modelConfigId,
        });
        if (!res.code) {
          message.success(t('模型编辑成功'));
        } else {
          message.error(t('模型编辑失败:') + res?.msg);
        }
      } else {
        const res = await addModelConfig(values);
        if (!res.code) {
          message.success(t('模型添加成功'));
        } else {
          message.error(t('模型添加失败:') + res?.msg);
        }
      }
      setIsModalVisible(false);
      setCurrentModel(null);
      form.resetFields();
      getModelConfigListFetch(1, pagination.pageSize);
    } catch (error) {
      message.error(t('操作失败'));
    }
  };

  const handleTest = async (values: any) => {
    try {
      const res = await testModelPing(values);
      if (!res.code) {
        return res.data.result;
      }
      return {
        pingRes: 0,
      }; // 如果接口调用失败，返回测试失败状态
    } catch (error) {
      message.error(t('测试失败'));
      return {
        pingRes: 0,
      };
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setCurrentModel(null);
    form.resetFields();
    clearState();
  };

  const onChange = (values: any) => {
    const { modelFactory, modelConfigState, modelType } =
      values.selectedFilters;
    const modelName = values.searchQuery;
    const params = {
      modelFactory,
      modelName,
      modelType,
      modelConfigState,
    };
    setFormData(params);
    setPagination({ ...pagination, current: 1 });
  };

  useEffect(() => {
    getModelListFetch();
    getModelTypeListFetch();
    getModelConfigStateListFetch();
    getModelFactoriesFetch();
  }, []);

  useEffect(() => {
    getModelConfigListFetch();
  }, [formData, pagination.current, pagination.pageSize]);

  return (
    <div className='p-4 space-y-2'>
      <header>
        <FilterComponent filterData={filterData} onChange={onChange} />
      </header>
      <Card
        title={t('模型设置')}
        variant={'borderless'}
        extra={
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => {
              setCurrentModel(null);
              setIsModalVisible(true);
              setIsEditModel(false);
            }}>
            {t('添加模型')}
          </Button>
        }>
        <div className='grid gap-6'>
          {modelData.map((model) => (
            <CardItem
              key={model.modelConfigId}
              {...model}
              factoryTechnicalName={model.factory}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onRefresh={() => getModelConfigListFetch()}
            />
          ))}
        </div>
        <div className='flex justify-end mt-4'>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            showSizeChanger
            showTotal={(total) => t('共{total}条', total)}
          />
        </div>
      </Card>

      <ConfigModel
        form={form}
        isModalVisible={isModalVisible}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
        onTest={handleTest}
        currentModel={currentModel}
        providers={providers}
        modelTypes={modelTypes}
        isEditModel={isEditModel}
      />
    </div>
  );
};

export default TrainedModel;
