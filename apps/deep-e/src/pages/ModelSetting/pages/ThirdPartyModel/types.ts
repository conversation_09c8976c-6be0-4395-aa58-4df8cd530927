export interface ModelData {
  modelConfigId: number;
  factoryName: string;
  modelName: string;
  requestUrl: string;
  apiKey?: string;
  describe?: string;
  status: number;
  statusName: string;
  isPrivate: boolean;
  modelType: string;
}

/**
 * Request
 */
export interface ModelFormProps {
  /**
   * 密钥
   */
  apiKey?: string;
  /**
   * 描述
   */
  desc?: string;
  /**
   * 模型提供商id
   */
  factoryId: number;
  /**
   * 是否私人可见
   */
  isPrivate: boolean;
  /**
   * 大模型id
   */
  llmId: number;
  /**
   * 请求地址
   */
  requestUrl: string;
  [property: string]: any;
}

export interface TestResponse {
  code: number;
  data: {
    status: number; // 0: 未测试, 1: 测试通过, 2: 测试失败
  };
  message?: string;
}
