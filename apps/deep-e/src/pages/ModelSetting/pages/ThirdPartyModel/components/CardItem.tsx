import { Card, Button, message, Tag } from 'antd';
import { EditOutlined, LinkOutlined } from '@ant-design/icons';
import { testModelPing } from '../../../api/ThirdPartyModel';
import { useState } from 'react';
import InnerIcon from '@/components/InnerIcon';
import { ModelData } from '../page';
import { DeleteButton } from '@/components/ui/delete-button';

interface CardItemProps extends ModelData {
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onRefresh: () => void;
}

const CardItem: React.FC<CardItemProps> = ({
  modelConfigId,
  factoryTechnicalName,
  factoryName,
  modelName,
  requestUrl,
  desc,
  status,
  statusName,
  isPrivate,
  onEdit,
  onDelete,
  onRefresh,
}) => {
  const [testLoading, setTestLoading] = useState(false);
  const [testStatus, setTestStatus] = useState<number>(0);
  const handleTestConnection = async () => {
    setTestLoading(true);
    setTestStatus(3);
    try {
      const res = await testModelPing({ modelConfigId: modelConfigId });
      if (res.code === 0) {
        if (res.data.result.pingRes === 1) {
          setTestStatus(1);
          message.success(t('连接测试成功'));
        } else {
          setTestStatus(2);
          message.error(t('连接测试失败'));
        }
      }
    } catch (error) {
      setTestStatus(2);
      message.error(t('连接测试失败'));
    } finally {
      setTestLoading(false);
      onRefresh();
    }
  };

  // 渲染测试状态
  const renderTestStatus = () => {
    const colorMap: { [key: number]: string } = {
      0: '',
      1: 'tag-success',
      2: 'tag-danger',
      3: 'tag-info',
    };
    return <div className={colorMap[status]}>{statusName}</div>;
  };

  return (
    <Card className='hover:shadow-[0_8px_16px_-6px_rgba(0,0,0,0.1)] transition-shadow duration-300 border-gray-100 py-1'>
      <div className='grid grid-cols-3 items-center gap-4 min-h-[64px]'>
        <div className='flex items-center gap-4'>
          <div className='w-16 h-16 rounded-lg flex items-center justify-center bg-gray-50'>
            {
              <InnerIcon
                vendor={factoryTechnicalName}
                className='w-full h-full flex items-center justify-center '
                width={64}
                height={64}
              />
            }
          </div>
          <div>
            <div className='flex items-center gap-2'>
              <h3 className='text-lg font-semibold text-gray-800 leading-tight'>
                {modelName}
              </h3>
              {isPrivate && <Tag color='blue'>{t('私有化')}</Tag>}
            </div>
            <span className='text-sm text-gray-600'>
              {t('提供商')}: {factoryName}
            </span>
            {desc && <p className='text-sm text-gray-500 mt-1'>{desc}</p>}
          </div>
        </div>

        <div className='flex flex-col justify-center items-center'>
          <span className='text-sm text-gray-600'>
            {t('请求地址')}: {requestUrl}
          </span>
          {renderTestStatus()}
        </div>

        <div className='flex items-center justify-end gap-4'>
          <div className='flex gap-2'>
            <div className='flex items-center gap-2'>
              <Button
                onClick={handleTestConnection}
                icon={<LinkOutlined />}
                loading={testLoading}
                disabled={testStatus === 3}
                className='hover:bg-green-50'>
                {t('测试连接')}
              </Button>
            </div>
            <Button
              onClick={() => onEdit(modelConfigId)}
              icon={<EditOutlined />}
              className='hover:bg-blue-50'>
              {t('编辑')}
            </Button>
            <DeleteButton
              className='text-white'
              onDelete={() => onDelete(modelConfigId)}
              type='destructive'
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CardItem;
