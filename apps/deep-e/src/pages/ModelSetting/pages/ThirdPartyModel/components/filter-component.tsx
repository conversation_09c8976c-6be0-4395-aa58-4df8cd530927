'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';

interface FilterOption {
  value: any;
  label: string;
}

interface HorizontalFilterProps {
  filterData: Record<string, { title: string; options: FilterOption[] }>;
  onChange?: (filters: {
    searchQuery: string;
    selectedFilters: Record<string, any[]>;
  }) => void;
}

export default function Component({
  onChange,
  filterData,
}: HorizontalFilterProps) {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any[]>>(
    {},
  );
  const [isExpanded, setIsExpanded] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [appliedSearchQuery, setAppliedSearchQuery] = useState('');

  const toggleFilter = (category: string, option: FilterOption) => {
    setSelectedFilters((prev) => {
      const current = prev[category] || [];
      const isSelected = current.includes(option.value);

      let newFilters;
      if (isSelected) {
        newFilters = {
          ...prev,
          [category]: current.filter((item) => item !== option.value),
        };
      } else {
        newFilters = {
          ...prev,
          [category]: [...current, option.value],
        };
      }

      // 调用 onChange 回调
      onChange?.({
        searchQuery: appliedSearchQuery,
        selectedFilters: newFilters,
      });

      return newFilters;
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    setSearchQuery('');
    setAppliedSearchQuery('');

    // 调用 onChange 回调
    onChange?.({
      searchQuery: '',
      selectedFilters: {},
    });
  };

  const handleSearch = () => {
    setAppliedSearchQuery(searchQuery);

    // 调用 onChange 回调
    onChange?.({
      searchQuery,
      selectedFilters,
    });
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const getSelectedCount = () => {
    return Object.values(selectedFilters).reduce(
      (total, filters) => total + filters.length,
      0,
    );
  };

  const getTotalFilterCount = () => {
    const filterCount = getSelectedCount();
    const searchCount = appliedSearchQuery.trim() ? 1 : 0;
    return filterCount + searchCount;
  };

  const leftColumnFilters = Object.keys(filterData);

  return (
    <div>
      <Card className='shadow-lg'>
        <Collapsible open={isExpanded} onOpenChange={toggleExpand}>
          <CardHeader className='pb-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CardTitle className='text-xl font-semibold'>
                  {t('筛选条件')}
                </CardTitle>
                <CollapsibleTrigger asChild>
                  <Button variant='ghost' size='sm' className='p-0 h-8 w-8'>
                    {isExpanded ? (
                      <ChevronUp className='h-5 w-5 text-gray-500' />
                    ) : (
                      <ChevronDown className='h-5 w-5 text-gray-500' />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </div>
              <div className='flex items-center gap-3'>
                {getTotalFilterCount() > 0 && (
                  <Badge variant='secondary' className='text-sm'>
                    {t('已选择 {count} 项', { count: getTotalFilterCount() })}
                  </Badge>
                )}
                <Button
                  variant='outline'
                  size='sm'
                  onClick={clearAllFilters}
                  disabled={getTotalFilterCount() === 0}>
                  {t('清空筛选')}
                </Button>
              </div>
            </div>
          </CardHeader>

          <CollapsibleContent>
            <CardContent className='space-y-6'>
              {/* 名称搜索区域 */}
              <div className='flex items-center gap-4'>
                <div className='flex items-center gap-2 min-w-fit'>
                  <h3 className='font-medium text-gray-900 text-sm whitespace-nowrap'>
                    {t('名称')}
                  </h3>
                </div>
                <div className='flex-1 max-w-md'>
                  <div className='relative flex items-center'>
                    <div className='relative flex-1'>
                      <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                      <Input
                        type='text'
                        placeholder='搜索模型名称...'
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSearch();
                          }
                        }}
                        className='pl-10 h-8 text-sm pr-4'
                      />
                    </div>
                    <Button
                      size='sm'
                      onClick={handleSearch}
                      className='ml-2 h-8'>
                      {t('搜索')}
                    </Button>
                  </div>
                </div>
              </div>

              {/* 筛选选项区域 - 左右布局 */}
              <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
                {/* 左列 */}
                <div className='space-y-4'>
                  {leftColumnFilters.map((key) => {
                    const data = filterData[key as keyof typeof filterData];
                    return (
                      <div key={key} className='flex items-center gap-4'>
                        <div className='flex items-center gap-2 min-w-fit'>
                          <h3 className='font-medium text-gray-900 text-sm whitespace-nowrap'>
                            {data.title}
                          </h3>
                        </div>
                        <div className='flex flex-wrap gap-2'>
                          {data.options.map((option) => {
                            const isSelected =
                              selectedFilters[key]?.includes(option.value) ||
                              false;
                            return (
                              <Button
                                key={option.value}
                                variant={isSelected ? 'default' : 'outline'}
                                size='sm'
                                className={`h-8 px-3 text-sm transition-all ${
                                  isSelected
                                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                                    : 'hover:bg-gray-50 text-gray-700 border-gray-200'
                                }`}
                                onClick={() => toggleFilter(key, option)}>
                                {option.label}
                              </Button>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}
