import { useState, useEffect } from 'react';
import { Table, Card, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import usePageData from '@/hooks/usePageData';
import {
  DeployedModelType,
  getDeployedModelList,
} from '../../api/DeployedModel';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import ExpandContent from './components/ExpandContent';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import useDayFormat from '@/hooks/useDayFormat';

const getColumns = (
  jumpThirdPage: (record: DeployedModelType) => void,
  format: string,
): ColumnsType<DeployedModelType> => {
  const formatTimestamp = (timestamp: number) => {
    if (timestamp === undefined) return '---';
    return dayjs(timestamp * 1000).format(format);
  };
  return [
    {
      title: t('模型名称'),
      dataIndex: 'deployModelName',
      key: 'deployModelName',
    },

    {
      title: t('量化方式'),
      dataIndex: 'quantize',
      key: 'quantize',
    },
    {
      title: t('部署框架'),
      dataIndex: 'deploymentFramework',
      key: 'deploymentFramework',
    },
    {
      title: t('服务地址'),
      dataIndex: 'serverURL',
      key: 'serverURL',
    },
    {
      title: t('开始时间'),
      dataIndex: 'taskStartTime',
      key: 'taskStartTime',
      render: formatTimestamp,
    },
    {
      title: t('结束时间'),
      dataIndex: 'taskEndTime',
      key: 'taskEndTime',
      render: formatTimestamp,
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      key: 'action',
      render: (_: any, record: DeployedModelType) => {
        return (
          <div>
            <Button
              type='link'
              size='small'
              onClick={() => {
                jumpThirdPage(record);
              }}>
              {t('加载到第三方')}
            </Button>
          </div>
        );
      },
    },
  ];
};

const DeployedModel = () => {
  const [modelList, setModelList] = useState<DeployedModelType[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const format = useDayFormat();

  const fetchDeployedList = async () => {
    try {
      setLoading(true);
      const { total, ...pageParams } = pageData;
      const res = await getDeployedModelList(pageParams);
      if (res.code === 0) {
        setPageData({
          ...pageData,
          total: res.data.total,
        });
        setModelList(res.data.list);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const jumpThirdPage = (record: DeployedModelType) => {
    navigate('/modelSetting/thirdPartyModel', {
      state: {
        modalOpen: true,
        modelName: record.deployModelName,
        serverURL: record.serverURL,
      },
    });
  };

  useEffect(() => {
    fetchDeployedList();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 w-full min-h-full rounded-2xl'>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <div>{t('已部署模型')}</div>
          </div>
        }>
        <Table
          rowKey='taskUuid'
          columns={getColumns(jumpThirdPage, format)}
          dataSource={modelList}
          pagination={pagination}
          scroll={{ x: true }}
          loading={loading}
          expandable={{
            expandedRowRender: (record) => (
              <ExpandContent
                taskUuid={record.taskUuid}
                saveModelPath={record.saveModelPath}
              />
            ),
            expandIcon: ({ expanded, onExpand, record }) => {
              return expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              );
            },
          }}
        />
      </Card>
    </div>
  );
};

export default DeployedModel;
