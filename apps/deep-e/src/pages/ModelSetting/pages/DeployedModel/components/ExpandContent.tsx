import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
import { useEffect, useState } from 'react';
import { getDeployedModelDetail } from '../../../api/DeployedModel';
const ExpandContent: React.FC<{ taskUuid: string; saveModelPath: string }> = ({
  taskUuid,
  saveModelPath,
}) => {
  const [items, setItems] = useState<DescriptionsProps['items']>([]);

  const getDeployedModelDetailFetch = async () => {
    const res = await getDeployedModelDetail(taskUuid);
    if (res.code === 0) {
      const record = res.data.result;
      setItems([
        {
          label: t('模型路径'),
          children: saveModelPath,
        },
        {
          label: t('版本'),
          children: record.version,
        },
        {
          label: t('模型类型'),
          children: record.modelType,
        },
        {
          label: t('架构'),
          children: record.architectures,
        },
        {
          label: t('语言'),
          children: record.language,
        },
        {
          label: t('任务'),
          children: record.tasks,
        },
        {
          label: t('标签'),
          children: record.tags,
        },
        {
          label: t('许可证'),
          children: record.license,
        },
        {
          label: t('衡量标准'),
          children: record.metrics,
        },
        {
          label: t('训练框架'),
          children: record.trainingFramework,
        },
        {
          label: t('保存的格式'),
          children: record.exportFormat,
        },
        {
          label: t('文件路径'),
          children: record.filePath,
        },
        {
          label: t('部署框架'),
          children: record.deploymentFramework,
        },
        {
          label: t('接口地址'),
          children: record.endpointUrl,
        },
        {
          label: t('延迟'),
          children: record.latency,
        },
        {
          label: t('限制'),
          children: record.limitations,
        },
        {
          label: t('作者'),
          children: record.author,
        },
        {
          label: t('创建时间'),
          children: record.createdAt,
        },
        {
          label: t('基础模型'),
          children: record.references,
        },
      ]);
    }
  };
  useEffect(() => {
    getDeployedModelDetailFetch();
  }, []);

  return (
    <div>
      <Descriptions items={items} />
    </div>
  );
};

export default ExpandContent;
