import { ModelData } from '../page';
import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
const ExpandContent: React.FC<{ record: ModelData }> = ({ record }) => {
  const items: DescriptionsProps['items'] = [
    {
      label: t('版本'),
      children: record.version,
    },
    {
      label: t('模型类型'),
      children: record.modelType,
    },
    {
      label: t('预训练数据集'),
      children: record.pretrainingDatasets,
    },
    {
      label: t('训练目标'),
      children: record.trainingObjectives,
    },
    {
      label: t('语言'),
      children: record.language,
    },
    {
      label: t('任务'),
      children: record.tasks,
    },
    {
      label: t('许可证'),
      children: record.license,
    },
    {
      label: t('限制'),
      children: record.limitations,
    },
    {
      label: t('作者'),
      children: record.authors,
    },
    {
      label: t('基础模型'),
      children: record.references,
    },
  ];
  return (
    <div>
      <Descriptions items={items} />
    </div>
  );
};

export default ExpandContent;
