import React, { useEffect, useState } from 'react';
import { Card, Table, TableColumnsType } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import {
  getDeployModelListByParent,
  getDeployedModelTaskLog,
} from '../../../../api/SavedModel';
import LogViewer from '@/components/ui/log-viewer';
import usePageData from '@/hooks/usePageData';
import useDayFormat from '@/hooks/useDayFormat';

const columns: TableColumnsType<any> = [
  {
    title: t('保存模型路径'),
    dataIndex: 'saveModelPath',
    key: 'saveModelPath',
  },
  {
    title: t('部署模型名称'),
    dataIndex: 'deployModelName',
    key: 'deployModelName',
  },
  {
    title: t('状态'),
    dataIndex: 'status',
    key: 'status',
    render: (status: string, { statusCode }) => {
      const tagMap: Record<number, string> = {
        2: 'tag-info',
        1: 'tag-success',
        3: 'tag-danger',
      };
      return <div className={tagMap[statusCode as number]}>{status}</div>;
    },
  },
  {
    title: t('服务器地址'),
    dataIndex: 'serverURL',
    key: 'serverURL',
  },
  {
    title: t('量化'),
    dataIndex: 'quantize',
    key: 'quantize',
  },
];

const TrainedModelLog = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [saveModelLogList, setSaveModelLogList] = useState<any[]>([]);

  const { taskUuid } = location.state;

  const { pageData, setPageData, pagination } = usePageData();
  const getSavedModelLogFetch = async () => {
    try {
      const res = await getDeployModelListByParent(taskUuid);
      if (!res.code) {
        setSaveModelLogList(res.data.list);
        setPageData((prev) => ({
          ...prev,
          total: res.data.total,
        }));
      }
    } catch (error) {
      console.error(error);
    }
  };
  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    getSavedModelLogFetch();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4'>
      <a
        onClick={handleBack}
        className='flex items-center text-gray-600 hover:text-gray-900 cursor-pointer mb-2'>
        <ArrowLeftOutlined className='mr-1' />
        {t('返回')}
      </a>
      <Card title={t('部署模型日志')}>
        <Table
          rowKey={'taskUuid'}
          pagination={pagination}
          expandable={{
            expandedRowRender: (record) => (
              <ExpendLogDetailContent logId={record.taskUuid} />
            ),
            expandIcon: ({ expanded, onExpand, record }) => {
              return expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              );
            },
          }}
          columns={columns}
          dataSource={saveModelLogList}
        />
      </Card>
    </div>
  );
};

type ExpendLogDetailContentProps = {
  logId: string;
};
const ExpendLogDetailContent: React.FC<ExpendLogDetailContentProps> = ({
  logId,
}) => {
  const [logList, setLogList] = useState<any[]>([]);
  const format = useDayFormat();
  const getSaveModelTaskLogFetch = async () => {
    try {
      const res = await getDeployedModelTaskLog(logId);
      if (!res.code) {
        setLogList(res.data.list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getSaveModelTaskLogFetch();
  }, []);
  return (
    <div>
      <div className='flex-1 overflow-auto font-mono text-sm'>
        {logList.length === 0 ? (
          <div className='flex items-center justify-center h-full text-muted-foreground'>
            {t('没有匹配的日志记录')}
          </div>
        ) : (
          <div className='space-y-2'>
            <LogViewer logList={logList} format={format} />
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainedModelLog;
