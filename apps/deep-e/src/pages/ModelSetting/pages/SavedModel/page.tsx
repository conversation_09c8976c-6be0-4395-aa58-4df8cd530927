import { useState, useEffect } from 'react';
import { Table, Card, Button, App } from 'antd';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import usePageData from '@/hooks/usePageData';
import { getSavedModelList, deleteSavedModel } from '../../api/SavedModel';
import ExpandContent from './components/ExpandContent';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { DeleteButton } from '@/components/ui/delete-button';
import useDayFormat from '@/hooks/useDayFormat';
import dayjs from 'dayjs';

const SavedModel = () => {
  const { message } = App.useApp();
  const [savedModelList, setSavedModelList] = useState<ModelData[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const format = useDayFormat();
  const formatTimestamp = (timestamp: number) => {
    if (timestamp === undefined) return '---';
    return dayjs(timestamp * 1000).format(format);
  };
  const getSavedModelListFetch = async () => {
    try {
      setLoading(true);
      const { total, ...pageParams } = pageData;
      const res = await getSavedModelList(pageParams);
      if (res.code === 0) {
        setPageData({
          ...pageData,
          total: res.data.total,
        });
        setSavedModelList(res.data.list);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const jumpDeployPage = (taskUuid: string) => {
    localStorage.setItem('taskUuid', taskUuid);
    navigate('/runningPage', {
      state: {
        type: '4',
      },
    });
  };

  const jumpLogPage = (taskUuid: string) => {
    localStorage.setItem('taskUuid', taskUuid);
    navigate('/modelSetting/savedModel/log', {
      state: {
        taskUuid,
      },
    });
  };

  const deleteSavedModelFetch = async (taskUuid: string) => {
    try {
      const res = await deleteSavedModel(taskUuid);
      if (!res.code) {
        message.success(res.msg);
        getSavedModelListFetch();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getSavedModelListFetch();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 w-full min-h-full rounded-2xl'>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <div>{t('已保存模型')}</div>
          </div>
        }>
        <Table
          rowKey='taskUuid'
          columns={getColumns(
            jumpDeployPage,
            jumpLogPage,
            deleteSavedModelFetch,
            formatTimestamp,
          )}
          scroll={{ x: true }}
          dataSource={savedModelList}
          pagination={pagination}
          loading={loading}
          expandable={{
            expandedRowRender: (record) => (
              <ExpandContent
                taskUuid={record.taskUuid}
                savePath={record.savePath}
                loraAdapterPath={record.loraAdapterPath}
                baseModelPath={record.baseModelPath}
              />
            ),
            expandIcon: ({ expanded, onExpand, record }) => {
              return expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              );
            },
          }}
        />
      </Card>
    </div>
  );
};

export default SavedModel;

interface ModelData {
  savePath: string;
  baseModelPath: string;
  loraAdapterPath: string;
  saveModelName: string;
  status: string;
  statusCode: 3 | 1 | 2;
  taskUuid: string;
  isDeleted: boolean;
}

const getColumns = (
  jumpDeployPage: (taskUuid: string) => void,
  jumpLogPage: (taskUuid: string) => void,
  deleteSavedModel: (taskUuid: string) => void,
  formatTimestamp: (timestamp: number) => string,
): ColumnsType<ModelData> => {
  return [
    {
      title: t('模型名称'),
      dataIndex: 'saveModelName',
      key: 'saveModelName',
    },
    /*   {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: { statusCode: 3 | 1 | 2 }) => {
        const tagMap = {
          2: 'tag-info',
          1: 'tag-success',
          3: 'tag-danger',
        } as const;
        return <div className={tagMap[record.statusCode]}>{status}</div>;
      },
    }, */

    {
      title: t('磁盘占用空间'),
      dataIndex: 'diskSizeGB',
      key: 'diskSizeGB',
      render: (diskSizeGB: string) => {
        return diskSizeGB;
      },
    },
    {
      title: t('基础模型'),
      dataIndex: 'baseModelPath',
      key: 'baseModelPath',
    },
    {
      title: t('开始时间'),
      dataIndex: 'taskStartTime',
      key: 'taskStartTime',
      render: formatTimestamp,
    },
    {
      title: t('结束时间'),
      dataIndex: 'taskEndTime',
      key: 'taskEndTime',
      render: formatTimestamp,
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <div className='flex flex-col items-center'>
          <Button
            type='link'
            onClick={() => jumpDeployPage(record.taskUuid)}
            disabled={record.isDeleted}>
            {t('部署')}
          </Button>
          <Button type='link' onClick={() => jumpLogPage(record.taskUuid)}>
            {t('日志')}
          </Button>
          <DeleteButton
            onDelete={() => deleteSavedModel(record.taskUuid)}
            disabled={record.isDeleted}
          />
        </div>
      ),
    },
  ];
};
