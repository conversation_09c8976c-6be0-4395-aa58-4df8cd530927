import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
import { getSavedModelDetail } from '../../../api/SavedModel';
import { useEffect, useState } from 'react';
const ExpandContent: React.FC<{
  taskUuid: string;
  savePath: string;
  loraAdapterPath: string;
  baseModelPath: string;
}> = ({ taskUuid, savePath, loraAdapterPath, baseModelPath }) => {
  const [items, setItems] = useState<DescriptionsProps['items']>([]);
  const getSaveModelDetailFetch = async () => {
    try {
      const res = await getSavedModelDetail(taskUuid);
      if (res.code === 0) {
        const record = res.data.result;
        setItems([
          {
            label: t('保存路径'),
            children: savePath,
          },
          {
            label: t('LoRA适配器路径'),
            children: loraAdapterPath,
          },
          {
            label: t('基础模型路径'),
            children: baseModelPath,
          },
          {
            label: t('版本'),
            children: record.version,
          },
          {
            label: t('模型类型'),
            children: record.modelType,
          },
          {
            label: t('架构'),
            children: record.architectures,
          },
          {
            label: t('语言'),
            children: record.language,
          },
          {
            label: t('任务'),
            children: record.tasks,
          },
          {
            label: t('标签'),
            children: record.tags,
          },
          {
            label: t('许可证'),
            children: record.license,
          },
          {
            label: t('衡量标准'),
            children: record.metrics,
          },

          {
            label: t('训练框架'),
            children: record.trainingFramework,
          },
          {
            label: t('保存格式'),
            children: record.exportFormat,
          },
          {
            label: t('限制'),
            children: record.limitations,
          },
          {
            label: t('作者'),
            children: record.authors,
          },
          {
            label: t('创建时间'),
            children: record.createdAt,
          },
          {
            label: t('基础模型'),
            children: record.references,
          },
        ]);
      }
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    getSaveModelDetailFetch();
  }, []);

  return (
    <div>
      <Descriptions items={items} />
    </div>
  );
};

export default ExpandContent;
