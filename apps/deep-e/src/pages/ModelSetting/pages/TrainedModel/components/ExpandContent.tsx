import { TableData } from '../page';
import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
const ExpandContent: React.FC<{ record: TableData }> = ({ record }) => {
  const items: DescriptionsProps['items'] = [
    {
      label: t('版本'),
      children: record.version,
    },
    {
      label: t('模型类型'),
      children: record.modelType,
    },
    {
      label: t('架构'),
      children: record.architectures,
    },
    {
      label: t('语言'),
      children: record.language,
    },
    {
      label: t('任务'),
      children: record.tasks,
    },
    {
      label: t('标签'),
      children: record.tags,
    },
    {
      label: t('许可证'),
      children: record.license,
    },
    {
      label: t('衡量标准'),
      children: record.metrics,
    },
    {
      label: t('微调目标'),
      children: record.fineTuneObjectives,
    },
    {
      label: t('训练框架'),
      children: record.trainingFramework,
    },

    {
      label: t('训练框架'),
      children: record.trainingFramework,
    },
    {
      label: t('限制'),
      children: record.limitations,
    },
    {
      label: t('作者'),
      children: record.authors,
    },
    {
      label: t('基础模型'),
      children: record.references,
    },
    {
      label: t('训练超参'),
      // children: <a>{t('查看详情')}</a>,
      children: '',
    },
  ];
  return (
    <div>
      <Descriptions items={items} />
    </div>
  );
};

export default ExpandContent;
