import { useEffect, useState } from 'react';
import { Table, Card, App, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { t } from '@/languages';
import {
  TrainedModelConfig,
  getModelConfig,
  deleteTrainedModel,
} from '../../api/TrainedModel';
import { ExpandContent } from './components';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import usePageData from '@/hooks/usePageData';
import { DeleteButton } from '@/components/ui/delete-button';
import dayjs from 'dayjs';
import useDayFormat from '@/hooks/useDayFormat';

export type TableData = Omit<TrainedModelConfig, 'baseInfo'> &
  TrainedModelConfig['baseInfo'];
const ModelManagement: React.FC = () => {
  const navigate = useNavigate();
  const format = useDayFormat();

  const [tableData, setTableData] = useState<TableData[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [loading, setLoading] = useState<boolean>(false);
  const { message } = App.useApp();

  const deleteTrainedModelFetch = async (taskUuid: string) => {
    try {
      const res = await deleteTrainedModel({ taskUuid });
      if (!res.code) {
        message.success(t('删除成功'));
      } else {
        message.error(res.msg || t('删除失败'));
      }
    } catch (error) {
      console.error(error);
    } finally {
      getModelConfigFetch();
    }
  };
  const formatTimestamp = (timestamp: number) => {
    if (timestamp === undefined) return '---';
    return dayjs(timestamp * 1000).format(format);
  };

  const columns: ColumnsType<TableData> = [
    {
      title: t('模型名称'),
      dataIndex: 'modelName',
      key: 'modelName',
    },
    {
      title: t('模型路径'),
      dataIndex: 'modelPath',
      key: 'modelPath',
    },
    {
      title: t('基础模型'),
      dataIndex: 'baseModel',
      key: 'baseModel',
    },
    {
      title: t('微调数据集'),
      width: 120,
      dataIndex: 'fineTuneDataset',
      key: 'fineTuneDataset',
    },
    {
      title: t('磁盘占用空间'),
      width: 120,
      dataIndex: 'diskSizeGB',
      key: 'diskSizeGB',
      render: (text) => {
        return <span>{text}</span>;
      },
    },
    {
      title: t('开始时间'),
      dataIndex: 'taskStartTime',
      width: 200,
      key: 'taskStartTime',
      render: formatTimestamp,
    },
    {
      title: t('结束时间'),
      dataIndex: 'taskEndTime',
      width: 200,
      key: 'taskEndTime',
      render: formatTimestamp,
    },
    {
      title: t('操作'),
      key: 'action',
      fixed: 'right',
      render: (_, record) => (
        <div className='flex justify-center items-center flex-col'>
          <Button
            type='link'
            disabled={record.isDeleted}
            onClick={async () => {
              await localStorage.setItem('taskUuid', record.taskUuid);
              navigate('/runningPage', {
                state: {
                  type: '3',
                },
              });
            }}>
            {t('保存')}
          </Button>
          <Button
            type='link'
            onClick={() =>
              navigate('/modelSetting/trainedModel/log', {
                state: {
                  trainTaskUuid: record.taskUuid,
                },
              })
            }>
            {t('日志')}
          </Button>
          <DeleteButton
            disabled={record.isDeleted}
            onDelete={() => deleteTrainedModelFetch(record.taskUuid)}
          />
        </div>
      ),
    },
  ];

  const getModelConfigFetch = async () => {
    try {
      setLoading(true);
      const res = await getModelConfig(pageData);
      console.log(t('获取模型配置：'), res);
      if (!res.code) {
        const list = res.data.list.map((item) => {
          return {
            ...item,
            ...item.baseInfo,
          };
        });
        setTableData(list);
        setPageData({
          ...pageData,
          total: res.data.total,
        });
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(t('获取模型配置失败：'), error);
      message.error(error as string);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getModelConfigFetch();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 w-full min-h-full rounded-2xl'>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <div> {t('已训练模型')}</div>
          </div>
        }
        className=' mx-auto p-8  w-full h-full bg-white rounded-2xl  '>
        <Table
          columns={columns}
          loading={loading}
          dataSource={tableData}
          scroll={{ x: true }}
          rowKey='taskUuid'
          pagination={pagination}
          expandable={{
            expandedRowRender: (record) => <ExpandContent record={record} />,
            expandIcon: ({ expanded, onExpand, record }) => {
              return expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              );
            },
          }}
        />
      </Card>
    </div>
  );
};
export default ModelManagement;
