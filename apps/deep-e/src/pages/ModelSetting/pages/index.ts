import { lazy } from 'react';

const ThirdPartyModel = lazy(() => import('./ThirdPartyModel/page'));
const TrainedModel = lazy(() => import('./TrainedModel/page'));
const TrainedModelLog = lazy(() => import('./TrainedModel/pages/log/page'));
const ModelBaseList = lazy(() => import('./ModelBaseList/page'));
const SavedModel = lazy(() => import('./SavedModel/page'));
const SavedModelLog = lazy(() => import('./SavedModel/pages/log/page'));
const DeployedModel = lazy(() => import('./DeployedModel/page'));
export default {
  ThirdPartyModel,
  TrainedModel,
  ModelBaseList,
  SavedModel,
  TrainedModelLog,
  DeployedModel,
  SavedModelLog,
};
