import request, { returnData } from '@/services';
import { ModelFormProps } from '../pages/ThirdPartyModel/types';

export const getModelList = () => {
  return request.get('/api/modelConfig/getModelFactories');
};

export const getModelTypeList = () => {
  return request.get('/api/modelConfig/getModelTypeList');
};

export const getModelInfoList = (data: {
  modelType: string;
  factoryId: number;
}) => {
  return request.get('/api/modelConfig/getModelInfoList', { params: data });
};

export const testModelPing = (data: { modelConfigId: number }) =>
  request.post('/api/modelConfig/testModelPing', data);

export const addModelConfig = (data: {
  factoryId: number;
  llmId: number;
  requestUrl: string;
  isPrivate: boolean;
  apiKey: string;
  desc: string;
}) => {
  return request.post('/api/modelConfig/addModelConfig', data);
};

interface ModelConfigResponse {
  code: number;
  message: string;
  data: {
    list: Array<{
      id: number;
      factoryName: string;
      llmName: string;
      requestUrl: string;
      apiKey: string;
      imported: boolean;
      isPrivate: boolean;
    }>;
    total: number;
  };
}

export const getModelConfigList = (data: {
  page: number;
  pageSize: number;
  modelName?: string;
  modelFactory?: string[];
  modelConfigState?: string[];
}): Promise<returnData<ModelConfigResponse[]>> => {
  return request.get('/api/modelConfig/getModelConfigList', { params: data });
};

export const updateModelConfig = (data: ModelFormProps) => {
  return request.post('/api/modelConfig/updateModelConfig', data);
};

export const deleteModelConfig = (data: { modelConfigId: number }) => {
  return request.post('/api/modelConfig/deleteModelConfig', data);
};

export const getModelConfigStateList = () => {
  return request.get('/api/modelConfig/getModelConfigStateList');
};

export const getModelFactoryList = () => {
  return request.get('/api/modelConfig/getModelFactories');
};
