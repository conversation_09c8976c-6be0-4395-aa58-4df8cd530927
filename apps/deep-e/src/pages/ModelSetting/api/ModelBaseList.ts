import request, { returnData } from '@/services';
import { BaseModel } from '../types';
import { PageData } from '@/types';
import { z } from 'zod';
export const getBaseModel = (): Promise<returnData<BaseModel[]>> => {
  return request.get('/api/model/getBaseModel');
};

export const addBaseModel = (data: any) => {
  return request.post('/api/model/addBaseModel', data);
};

export const BaseModelSchema = z.object({
  baseInfo: z.object({
    technicName: z.string(),
    modelName: z.string(),
    modelPath: z.string(),
    modelId: z.number(),
    template: z.string(),
    coId: z.number(),
    createdAt: z.number(),
    updatedAt: z.number(),
  }),
  version: z.string(),
  modelType: z.string(),
  architectures: z.array(z.string()),
  pretrainingDatasets: z.null(),
  trainingObjectives: z.null(),
  language: z.string(),
  tasks: z.null(),
  tags: z.array(z.string()),
  license: z.string(),
  trainingFramework: z.string(),
  limitations: z.string(),
  authors: z.string(),
  createdAt: z.string(),
  references: z.string(),
});
export const getBaseModelConfig = (
  data: PageData,
): Promise<returnData<z.infer<typeof BaseModelSchema>[]>> => {
  return request.get('/api/model/getBaseModelConfig', {
    params: data,
  });
};
