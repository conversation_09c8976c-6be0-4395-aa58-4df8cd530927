import request, { returnData } from '@/services';
import { PageData } from '@/types';

export const getSavedModelList = (
  data: PageData,
): Promise<returnData<any[]>> => {
  return request.get('/api/model/getSaveModelList', {
    params: data,
  });
};

export const getDeployModelListByParent = (saveTaskUuid: string) => {
  return request.get('/api/model/getDeployModelListByParent', {
    params: {
      saveTaskUuid,
    },
  });
};

export const getDeployedModelTaskLog = (taskUuid: string) => {
  return request.get('/api/scpLog/getLogsScpLogger', {
    params: {
      taskUuid,
      seq: 1,
    },
  });
};

export const getSavedModelDetail = (taskUuid: string) => {
  return request.get('/api/model/getSaveModelConfigInfoList', {
    params: {
      taskUuid,
    },
  });
};

export const deleteSavedModel = (taskUuid: string) => {
  return request.get('/api/model/deleteSaveModel', {
    params: {
      taskUuid,
    },
  });
};
