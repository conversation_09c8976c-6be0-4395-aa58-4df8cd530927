import request, { returnData } from '@/services';
import { PageData } from '@/types';
import { z } from 'zod';

export const DeployedModelSchema = z.object({
  saveModelPath: z.string(),
  deployModelName: z.string(),
  taskUuid: z.string(),
  statusCode: z.enum(['3', '1', '2']),
  status: z.string(),
  serverURL: z.string(),
  quantize: z.string(),
  deploymentFramework: z.string(),
});

export type DeployedModelType = z.infer<typeof DeployedModelSchema>;
export const getDeployedModelList = (
  data: PageData,
): Promise<returnData<DeployedModelType[]>> => {
  return request.get('/api/model/getDeployModelList', {
    params: data,
  });
};

export const getDeployedModelDetail = (taskUuid: string) => {
  return request.get('/api/model/getDeployModelConfigInfoList', {
    params: { taskUuid },
  });
};
