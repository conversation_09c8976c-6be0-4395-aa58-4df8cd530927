import request, { returnData } from '@/services';
import { PageData } from '@/types';
import { z } from 'zod';

export const TrainedModelConfigSchema = z.object({
  baseInfo: z.object({
    taskUuid: z.string(),
    modelName: z.string(),
    modelPath: z.string(),
    isDeleted: z.boolean(),
    baseModel: z.string(),
  }),
  version: z.string(),
  modelType: z.string(),
  architectures: z.array(z.string()).nullable(),
  language: z.string(),
  tasks: z.array(z.string()).nullable(),
  tags: z.array(z.string()).nullable(),
  license: z.string(),
  metrics: z.string().nullable(),
  fineTuneDataset: z.string(),
  fineTuneObjectives: z.string(),
  trainingFramework: z.string(),
  hyperParameters: z.object({
    batchSize: z.number(),
    datasetUuid: z.string(),
    gradAccumSteps: z.number(),
    inputTrainName: z.string(),
    learningRate: z.number(),
    loadInFourBit: z.boolean(),
    loraRank: z.number(),
    maxContentLength: z.number(),
    maxGradNorm: z.number(),
    maxInputLength: z.number(),
    maxSamples: z.number(),
    maxSeqLength: z.number(),
    maxSteps: z.number(),
    modelPath: z.string(),
    numGenerations: z.number(),
    outputTrainName: z.string(),
    trainingName: z.string(),
    warmupSteps: z.number(),
  }),
  limitations: z.string(),
  authors: z.string(),
  createdAt: z.string(),
  references: z.string(),
});

export type TrainedModelConfig = z.infer<typeof TrainedModelConfigSchema>;
export const getModelConfig = (
  data: PageData,
): Promise<returnData<TrainedModelConfig[]>> => {
  return request.get('/api//model/getTrainedModelConfig', {
    params: data,
  });
};

export const getSavedModelLog = (
  data: PageData & {
    trainTaskUuid: string;
  },
): Promise<returnData<any[]>> => {
  return request.get('/api/model/getSaveModelListByParent', {
    params: data,
  });
};

export const getSaveModelTaskLog = (data: { taskUuid: string }) => {
  return request.get('/api/scpLog/getLogsScpLogger', {
    params: {
      ...data,
      seq: 1,
    },
  });
};

export const deleteTrainedModel = (data: { taskUuid: string }) => {
  return request.get('/api/model/deleteTrainedModel', {
    params: data,
  });
};
