<mxfile host="65bd71144e">
    <diagram id="8MvI6gDVoIhRwZxXzDOK" name="第 1 页">
        <mxGraphModel dx="1881" dy="926" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="用户登陆" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="81" y="145" width="80" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="6" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="6" target="11">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="登陆是否成功" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="236" y="135" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="保存token、user到localStorage、redux。&lt;br&gt;修改rudex中的登陆状态，保存公司代码。" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="110" width="185" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="登陆成功" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="380" y="155" width="70" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="提示失败信息" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="239" y="310" width="124" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="登陆失败" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="291" y="240" width="70" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="14" target="15">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="15分钟未登陆" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="30" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="15" target="21">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="24">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="弹出登陆框" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="241" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="登陆" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="384" y="450" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="修改isLogin为true，关闭弹窗。" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="241" y="585" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="关闭" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="310" y="540" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="24" target="27">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="24" target="30">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="登陆是否成功" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="440" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="关闭弹窗、修改isLogin为true" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="659" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="成功" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="595" y="450" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="提示失败信息" style="whiteSpace=wrap;html=1;rotation=0;" vertex="1" parent="1">
                    <mxGeometry x="465" y="585" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="失败" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="479" y="540" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="携带公司id" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="160" y="450" width="80" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>