import React from 'react';
import {
  Table,
  Card,
  Space,
  Button,
  message,
  Form,
  DatePicker,
  Select,
  Input,
} from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/store/features/';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import { getUserList } from '@/pages/Setting/api/user';
import useDayFormat from '@/hooks/useDayFormat';
import usePageData from '@/hooks/usePageData';
import { useActivate } from 'react-activation';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

// 修改时间格式常量
const DATE_TIME_FORMAT = 'YYYY-MM-DD';

interface FineTuningLogItem {
  createdByName: string;
  jobId: string;
  jobType: number;
  taskEndTime: number;
  taskStartTime: number;
  taskStateCode: number;
  taskStateName: string;
  taskUuid: string;
  templateTypeCode: number;
  templateTypeName: string;
  trainingName: string;
}

interface FilterFormValues {
  timeRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  taskState: string;
  jobName: string;
  templateType: string;
  userId: string;
}

export default function FineTuningLog() {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<FineTuningLogItem[]>([]);
  const { token, language } = useAuthStore();
  const [userList, setUserList] = useState<any[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [selectedRows, setSelectedRows] = useState<FineTuningLogItem[]>([]);
  const format = useDayFormat();

  const taskStateOptions = [
    { label: t('已完成'), value: '1' },
    { label: t('失败'), value: '3' },
  ];

  const templateTypeOptions = [
    { label: t('原始模版'), value: '1' },
    { label: t('系统模版'), value: '2' },
    { label: t('他建模版'), value: '3' },
    { label: t('自建模版'), value: '4' },
  ];

  const formatTimestamp = (timestamp: number) => {
    return dayjs(timestamp * 1000).format(format);
  };

  const getUserListFetch = async () => {
    try {
      const response = await getUserList();
      if (response.code === 0) {
        setUserList(
          response.data.list.map((user: any) => ({
            label: user.nickName,
            value: user.id,
          })),
        );
      } else {
        throw new Error(t('获取用户列表失败'));
      }
    } catch (error) {
      console.error(t('获取用户列表失败:'), error);
      message.error(t('获取用户列表失败'));
      return [];
    }
  };

  const columns: ColumnsType<FineTuningLogItem> = [
    {
      title: t('任务ID'),
      dataIndex: 'Id',
      key: 'Id',
      render: (_, __, index: number) => index + 1,
    },
    {
      title: t('训练类型'),
      dataIndex: 'jobName',
      key: 'jobName',
    },
    {
      title: t('训练名称'),
      dataIndex: 'trainingName',
      key: 'trainingName',
    },
    {
      title: t('创建人'),
      dataIndex: 'createdByName',
      key: 'createdByName',
    },
    {
      title: t('模板类型'),
      dataIndex: 'templateTypeName',
      key: 'templateTypeName',
    },
    {
      title: t('状态'),
      dataIndex: 'taskStateName',
      key: 'taskStateName',
      render: (status: string, record: FineTuningLogItem) => {
        const statusMap: Record<string, { color: string; text: string }> = {
          '1': { color: 'tag-success', text: t('已完成') },
          '2': { color: 'tag-info', text: t('进行中') },
          '3': { color: 'tag-danger', text: t('失败') },
          '4': { color: 'tag-default', text: t('警告') },
          '5': { color: 'tag-warning', text: t('未知') },
        };
        const statusCode = record?.taskStateCode?.toString() ?? '';
        const statusInfo = statusMap[statusCode] || {
          color: 'text-gray-600',
          text: status,
        };
        return (
          <span className={`font-medium ${statusInfo.color}`}>
            {statusInfo.text}
          </span>
        );
      },
    },
    {
      title: t('开始时间'),
      dataIndex: 'taskStartTime',
      key: 'taskStartTime',
      render: (time: number) => formatTimestamp(time),
    },
    {
      title: t('结束时间'),
      dataIndex: 'taskEndTime',
      key: 'taskEndTime',
      render: (time: number, record) =>
        time === undefined
          ? '---'
          : record.taskStateCode === 2
            ? '---'
            : formatTimestamp(time),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size='middle'>
          <Button
            type='link'
            className='text-blue-600 hover:text-blue-700'
            onClick={() => handleViewDetails(record)}>
            {t('查看详情')}
          </Button>
        </Space>
      ),
    },
  ];

  const fetchData = async (page: number, pageSize: number, params?: any) => {
    setLoading(true);
    try {
      const sanitizedParams = Object.entries(params || {}).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            if (Array.isArray(value)) {
              value.forEach((v) => acc.append(key, v));
            } else {
              acc.append(key, value as any);
            }
          }
          return acc;
        },
        new URLSearchParams(),
      );

      sanitizedParams.append('page', page.toString());
      sanitizedParams.append('pageSize', pageSize.toString());

      const response = await fetch(
        `/api/cron/getCronJobTask?${sanitizedParams.toString()}&aiTemplateType=1`,
        {
          headers: {
            'X-Token': token || '',
            'accept-language': language,
          },
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result || typeof result.code === 'undefined') {
        throw new Error(t('Invalid response format'));
      }

      if (result.code === 0) {
        setPageData((prev) => {
          return {
            ...prev,
            total: result.data?.total || 0,
          };
        });
        const data = result.data?.list || [];
        setData(data);
      } else {
        throw new Error(result.msg || t('请求失败'));
      }
    } catch (error) {
      console.error(t('获取日志数据失败:'), error);
      message.error(
        error instanceof Error ? error.message : t('获取日志数据失败'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (record: FineTuningLogItem) => {
    navigate(
      `/fineTuning/logs/details?taskUuid=${record.taskUuid}&jobId=${record.jobId}`,
      {
        state: {
          id: record.jobId,
          taskUuid: record.taskUuid,
        },
      },
    );
  };

  const handleCompare = () => {
    if (selectedRows.length < 2) {
      message.warning(t('请至少选择两条记录进行对比'));
      return;
    }
    if (selectedRows.length > 2) {
      message.warning(t('最多只能对比2条记录'));
      return;
    }

    const queryParams = selectedRows
      .map((row) => `taskUuid=${row.taskUuid}&jobId=${row.jobId}`)
      .join('&');

    navigate(`/fineTuningLog/compareLogs?${queryParams}`);
  };

  useEffect(() => {
    const initialValues = form.getFieldsValue();
    handleSearch(initialValues);
    getUserListFetch();
  }, []);

  const handleTableChange = (newPagination: any) => {
    const formValues = form.getFieldsValue();
    const { timeRange, taskState, jobName, templateType, userId } = formValues;
    const [startTime, endTime] = timeRange || [null, null];

    const params = {
      startTime: startTime?.format(DATE_TIME_FORMAT + 'THH:mm:ssZ'),
      endTime: endTime?.format(DATE_TIME_FORMAT + 'THH:mm:ssZ'),
      taskState,
      jobName,
      templateType,
      userId,
    };

    setPageData({
      ...pagination,
      page: newPagination.current,
      pageSize: newPagination.pageSize,
    });

    fetchData(newPagination.current, newPagination.pageSize, params);
  };

  const handleSearch = async (values: FilterFormValues) => {
    const { timeRange, taskState, jobName, templateType, userId } = values;
    console.log(timeRange);
    const [startTime, endTime] = timeRange || [
      dayjs().subtract(3, 'days').startOf('day'),
      dayjs().endOf('day'),
    ];

    try {
      setLoading(true);
      setPageData({
        ...pageData,
        page: 1,
      });

      const params = {
        startTime: startTime
          ?.startOf('day')
          ?.format(DATE_TIME_FORMAT + 'THH:mm:ssZ'),
        endTime: endTime?.endOf('day')?.format(DATE_TIME_FORMAT + 'THH:mm:ssZ'),
        taskState,
        jobName,
        templateType,
        userId,
      };

      await fetchData(1, pageData.pageSize, params);
    } catch (error) {
      console.error(t('获取日志数据失败:'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    setPageData({
      ...pageData,
      page: 1,
    });
    handleSearch(form.getFieldsValue());
  };

  useActivate(() => {
    if (pageData.page === 1) {
      const values = form.getFieldsValue();
      handleSearch(values);
    }
  });

  return (
    <div className='p-4 min-h-full bg-gray-50 flex flex-col gap-2'>
      <Card>
        <Form
          form={form}
          layout='inline'
          onFinish={handleSearch}
          className='gap-4'
          initialValues={{
            timeRange: [
              dayjs().subtract(3, 'days').startOf('day'),
              dayjs().endOf('day'),
            ],
          }}>
          <Form.Item name='timeRange' label={t('时间范围')}>
            <RangePicker
              format={DATE_TIME_FORMAT}
              placeholder={[t('开始时间'), t('结束时间')]}
            />
          </Form.Item>
          <Form.Item name='taskState' label={t('执行任务状态')}>
            <Select
              options={taskStateOptions}
              style={{ width: 150 }}
              allowClear
              placeholder={t('请选择状态')}
            />
          </Form.Item>
          <Form.Item name='jobName' label={t('训练名称')}>
            <Input placeholder={t('请输入任务名称')} />
          </Form.Item>
          <Form.Item name='templateType' label={t('模版类型')}>
            <Select
              options={templateTypeOptions}
              style={{ width: 150 }}
              allowClear
              placeholder={t('请选择类型')}
            />
          </Form.Item>
          <Form.Item name='userId' label={t('用户')}>
            <Select
              style={{ width: 250 }}
              placeholder={t('请选择用户')}
              options={userList}
              mode='multiple'
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                {t('查询')}
              </Button>
              <Button onClick={handleReset}>{t('重置')}</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <span className='text-lg font-medium'>{t('微调任务历史')}</span>
            <div className='flex gap-2'>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={() =>
                  navigate('/runningPage', {
                    state: {
                      type: '1',
                    },
                  })
                }>
                {t('新建任务')}
              </Button>
              <Button
                type='primary'
                disabled={selectedRows.length < 2}
                onClick={handleCompare}>
                {t('对比日志')}
              </Button>
            </div>
          </div>
        }
        className='shadow-md rounded-lg'>
        <Table
          columns={columns}
          dataSource={data}
          rowKey='taskUuid'
          scroll={{ x: true }}
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
          className='bg-white'
          rowSelection={{
            type: 'checkbox',
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
            hideSelectAll: true,
            getCheckboxProps: (record) => ({
              disabled:
                selectedRows.length >= 2 &&
                !selectedRows.find((row) => row.taskUuid === record.taskUuid),
            }),
          }}
        />
      </Card>
    </div>
  );
}
