import { Card, Tabs } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { TaskOverview, TaskEvaluation, TaskLog } from './components';

export default function LogDetails() {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  const items = [
    {
      label: t('任务概览'),
      key: 'overview',
      children: <TaskOverview />,
    },
    {
      label: t('任务评估'),
      key: 'evaluation',
      children: <TaskEvaluation />,
    },
    {
      label: t('任务日志'),
      key: 'log',
      children: <TaskLog />,
    },
  ];

  return (
    <div className='p-6 min-h-full bg-gray-50 flex flex-col'>
      <div className='w-fit'>
        <a
          onClick={handleBack}
          className='flex items-center text-gray-600 hover:text-blue-500 transition-colors duration-200 cursor-pointer mb-4 text-lg'>
          <ArrowLeftOutlined className='mr-2' />
          {t('返回日志')}
        </a>
      </div>
      <Card
        title={
          <div className='flex items-center justify-between w-full'>
            <div className='flex items-center'>
              <span className='text-lg font-medium'>{t('微调日志详情')}</span>
            </div>
          </div>
        }
        className='shadow-md rounded-lg flex-1 h-full'>
        <Tabs items={items} />
      </Card>
    </div>
  );
}
