export interface LogDetailData {
  id: string;
  modelName: string;
  status: string;
  creator: string;
  createTime: string;
  duration: string;
  startTime: string;
  endTime: string;
  tuningMethod: 'SFT' | 'RLHF';
  baseModel: string;
  trainingMethod: 'LoRA' | 'Full';
  trainingFiles: {
    trainSet: string;
    validSet: string;
    splitRatio: string;
  };
  hyperParameters: {
    epochs: number;
    batchSize: number;
    learningRate: number;
    loraRank?: number;
    loraAlpha?: number;
    // 其他 LoRA 特定参数
  };
  progress: number;
  errorMessage?: string;
}
