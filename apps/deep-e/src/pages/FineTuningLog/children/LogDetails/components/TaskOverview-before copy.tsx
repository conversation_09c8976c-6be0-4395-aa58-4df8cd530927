import { Descriptions, Tag, Card, Space, Button, Collapse } from 'antd';
import { LogDetailData } from '../types';

interface Props {
  data: LogDetailData | null;
}

export default function TaskOverview({ data }: Props) {
  if (!data) return null;

  const getStatusTag = (status: string) => {
    const statusMap = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
    };
    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      color: 'default',
      text: status,
    };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  return (
    <Space direction='vertical' size='large' className='w-full'>
      {/* 基本信息 */}
      <Card title='任务信息' className='w-full'>
        <Descriptions column={2}>
          <Descriptions.Item label='任务名称'>
            {data.modelName}
          </Descriptions.Item>
          <Descriptions.Item label='任务ID'>{data.id}</Descriptions.Item>
          <Descriptions.Item label='创建人'>{data.creator}</Descriptions.Item>
          <Descriptions.Item label='创建时间'>
            {data.createTime}
          </Descriptions.Item>
          <Descriptions.Item label='训练时长'>
            {data.duration}
          </Descriptions.Item>
          <Descriptions.Item label='微调方式'>
            {data.tuningMethod}
          </Descriptions.Item>
          <Descriptions.Item label='状态'>
            {getStatusTag(data.status)}
          </Descriptions.Item>
          <Descriptions.Item label='进度'>{`${data.progress}%`}</Descriptions.Item>
          {data.errorMessage && (
            <Descriptions.Item span={2}>
              <span className='text-red-500'>{data.errorMessage}</span>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      {/* 训练配置 */}
      <Card title='训练配置' className='w-full'>
        <Descriptions column={2}>
          <Descriptions.Item label='基础模型'>
            {data.baseModel}
          </Descriptions.Item>
          <Descriptions.Item label='训练方法'>
            {data.trainingMethod}
          </Descriptions.Item>
        </Descriptions>
        <Collapse
          ghost
          items={[
            {
              key: '1',
              label: '更多参数',
              children: (
                <Descriptions column={2}>
                  <Descriptions.Item label='训练轮次'>
                    {data.hyperParameters.epochs}
                  </Descriptions.Item>
                  <Descriptions.Item label='批次大小'>
                    {data.hyperParameters.batchSize}
                  </Descriptions.Item>
                  <Descriptions.Item label='学习率'>
                    {data.hyperParameters.learningRate}
                  </Descriptions.Item>
                  {data.trainingMethod === 'LoRA' && (
                    <>
                      <Descriptions.Item label='LoRA Rank'>
                        {data.hyperParameters.loraRank}
                      </Descriptions.Item>
                      <Descriptions.Item label='LoRA Alpha'>
                        {data.hyperParameters.loraAlpha}
                      </Descriptions.Item>
                    </>
                  )}
                </Descriptions>
              ),
            },
          ]}
        />
      </Card>

      {/* 数据配置 */}
      <Card title='数据配置' className='w-full'>
        <Descriptions column={2}>
          <Descriptions.Item label='训练集名称'>
            {data.trainingFiles.trainSet}
          </Descriptions.Item>
          <Descriptions.Item label='验证集'>
            {data.trainingFiles.validSet}
          </Descriptions.Item>
          <Descriptions.Item label='拆分比例'>
            {data.trainingFiles.splitRatio}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 发布模型 */}
      <Card
        title='发布模型'
        className='w-full'
        extra={
          <Button type='primary' disabled={data.status !== 'completed'}>
            发布到模型仓库
          </Button>
        }>
        <Descriptions column={2}>
          <Descriptions.Item label='模型大小'>2.3GB</Descriptions.Item>
          <Descriptions.Item label='参数量'>1.3B</Descriptions.Item>
          <Descriptions.Item label='发布状态'>
            {data.status === 'completed' ? (
              <Tag color='blue'>可发布</Tag>
            ) : (
              <Tag color='default'>不可发布</Tag>
            )}
          </Descriptions.Item>
          <Descriptions.Item label='模型版本'>v1.0.0</Descriptions.Item>
        </Descriptions>
      </Card>
    </Space>
  );
}
