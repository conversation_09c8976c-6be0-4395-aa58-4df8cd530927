import { Card, Empty } from 'antd';
import { Chart } from '@antv/g2';
import { useLocation } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { getGraphDataApi } from '@/pages/FineTuningLog/api';
import React from 'react';

interface GraphData {
  key: string;
  value: (number | null)[];
}

export default function TaskEvaluation() {
  const location = useLocation();
  const { taskUuid } = location.state;
  const [graphData, setGraphData] = useState<GraphData[]>([]);

  const getGraphData = async (taskUuid: string) => {
    try {
      const res = await getGraphDataApi(taskUuid);
      if (!res.code) {
        setGraphData(res.data.list);
      }
    } catch (error) {
      console.error('Failed to fetch graph data:', error);
    }
  };

  useEffect(() => {
    getGraphData(taskUuid);
  }, [taskUuid]);

  const steps = graphData.find((item) => item.key === 'Steps')?.value || [];

  return (
    <div className='w-full flex flex-wrap gap-4 h-full'>
      {graphData.length === 0 && (
        <div className='w-full h-full flex items-center justify-center'>
          <Empty></Empty>
        </div>
      )}
      {graphData
        .filter((item) => item.key !== 'Steps' && item.key !== 'KL')
        .map((item) => (
          <ChartItem
            key={item.key}
            title={item.key}
            xNum={steps as number[]}
            yNum={item.value as number[]}
          />
        ))}
    </div>
  );
}

interface ChartItemProps {
  title: string;
  xNum: number[];
  yNum: (number | null)[];
}

const ChartItem = ({ title, xNum, yNum }: ChartItemProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const chart = new Chart({
        container: chartRef.current,
        autoFit: true,
        height: 300,
        axis: {
          y: {
            label: true,
            title: t('数值'),
            line: true,
            tick: true,
          },
          x: {
            label: true,
            title: t('步数'),
            line: true,
          },
        },
        tooltip: {
          shared: true,
          crosshairs: {
            type: 'x',
          },
        },
      });

      const data = xNum.map((x, index) => ({
        step: x,
        value: yNum[index],
      }));

      chart
        .line()
        .data(data)
        .encode('x', 'step')
        .encode('y', 'value')
        .style('stroke', '#4ECDC4')
        .style('lineWidth', 2)
        .animate('enter', { type: 'fadeIn' });
      chart.render();
      return () => chart.destroy();
    }
  }, [xNum, yNum]);

  return (
    <Card title={title} className='w-[calc(33.33%-1rem)]'>
      <div ref={chartRef} className='min-h-[300px]' />
    </Card>
  );
};
