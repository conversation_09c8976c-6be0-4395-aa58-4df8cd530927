import { Timeline, Card, Typography } from 'antd';
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import {
  getTrainingProgressApi,
  getLogsScpLogger,
} from '@/pages/FineTuningLog/api';
import { useEffect, useState } from 'react';
import { t } from '@/languages';

const { Text } = Typography;

interface LogItem {
  api: string;
  details: string;
  detailsType: string;
  file: string;
  func: string;
  level: string;
  line: number;
  msg: string;
  taskId: string;
  time: string;
  traceId: string;
  msgDetail: string;
}

export default function TaskLog() {
  const location = useLocation();
  const { taskUuid } = location.state;
  const [trainingLog, setTrainingLog] = useState([]);
  const [overviewLogs, setOverviewLogs] = useState([]);

  const getOverviewLogs = async () => {
    try {
      const res = await getLogsScpLogger(taskUuid);
      setOverviewLogs(res.data.list || []);
    } catch (error) {
      console.error(t('获取概览日志失败：'), error);
    }
  };

  const getTrainingProgress = async () => {
    try {
      const res = await getTrainingProgressApi(taskUuid);
      if (!res.code) {
        setTrainingLog(res.data.list || []);
      }
    } catch (error) {
      console.error(t('获取训练进度失败：'), error);
    }
  };

  useEffect(() => {
    getTrainingProgress();
    getOverviewLogs();
  }, []);

  return (
    <div className='w-full h-full flex gap-4'>
      {/* 左侧概览日志 */}
      <Card className='max-w-[400px]' title={t('日志概览')}>
        <Timeline
          items={overviewLogs.map((log: LogItem) => ({
            color:
              log.level === 'error'
                ? 'red'
                : log.level === 'warning'
                  ? 'yellow'
                  : 'blue',
            children: (
              <div>
                <Text type='secondary' className='text-sm'>
                  {dayjs(log.time).format('YYYY-MM-DD HH:mm:ss')}
                </Text>
                <div>{log.msg}</div>
                {log.msgDetail && (
                  <div className='text-gray-500 text-sm mt-1'>
                    {log.msgDetail}
                  </div>
                )}
              </div>
            ),
          }))}
        />
      </Card>

      {/* 右侧完整日志 */}
      <Card className='flex-1 overflow-auto' title={t('完整日志')}>
        <div className='max-h-[calc(100vh-420px)] w-full overflow-auto font-mono text-sm whitespace-pre'>
          {trainingLog.map((log, index) => (
            <div key={index}>
              <Text>{JSON.stringify(log)}</Text>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
