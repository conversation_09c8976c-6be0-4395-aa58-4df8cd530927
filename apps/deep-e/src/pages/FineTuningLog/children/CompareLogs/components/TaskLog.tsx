import { Timeline, Card, Typography } from 'antd';
import dayjs from 'dayjs';
import {
  getTrainingProgressApi,
  getLogsScpLogger,
} from '@/pages/FineTuningLog/api';
import { useEffect, useState } from 'react';

const { Text } = Typography;

interface LogItem {
  api: string;
  details: string;
  detailsType: string;
  file: string;
  func: string;
  level: string;
  line: number;
  msg: string;
  taskId: string;
  time: string;
  traceId: string;
  msgDetail: string;
}

export default function TaskLog({ taskUuid }: { taskUuid: string }) {
  const [trainingLog, setTrainingLog] = useState([]);
  const [overviewLogs, setOverviewLogs] = useState([]);

  const getOverviewLogs = async () => {
    try {
      const res = await getLogsScpLogger(taskUuid);
      setOverviewLogs(res.data.list || []);
    } catch (error) {
      console.error('Error fetching overview logs:', error);
    }
  };

  const getTrainingProgress = async () => {
    try {
      const res = await getTrainingProgressApi(taskUuid);
      if (!res.code) {
        setTrainingLog(res.data.list || []);
      }
    } catch (error) {
      console.error('Failed to fetch graph data:', error);
    }
  };

  useEffect(() => {
    getTrainingProgress();
    getOverviewLogs();
  }, []);

  return (
    <div className='flex gap-6'>
      <Card
        className='w-1/3 shadow hover:shadow-md transition-shadow duration-300'
        title={t('日志概览')}
        headStyle={{
          borderBottom: '1px solid #f0f0f0',
          fontSize: '14px',
          fontWeight: 500,
        }}>
        <Timeline
          className='px-2'
          items={overviewLogs.map((log: LogItem) => ({
            color:
              log.level === 'error'
                ? 'red'
                : log.level === 'warning'
                  ? 'gold'
                  : 'blue',
            children: (
              <div className='hover:bg-gray-50 p-2 rounded-md transition-colors duration-200'>
                <Text type='secondary' className='text-xs'>
                  {dayjs(log.time).format('YYYY-MM-DD HH:mm:ss')}
                </Text>
                <div className='text-sm mt-1'>{log.msg}</div>
                {log.msgDetail && (
                  <div className='text-gray-500 text-xs mt-1 bg-gray-50 p-2 rounded'>
                    {log.msgDetail}
                  </div>
                )}
              </div>
            ),
          }))}
        />
      </Card>

      <Card
        className='flex-1 shadow hover:shadow-md transition-shadow duration-300'
        title={t('完整日志')}
        headStyle={{
          borderBottom: '1px solid #f0f0f0',
          fontSize: '14px',
          fontWeight: 500,
        }}>
        <div className='h-[600px] overflow-y-auto font-mono text-sm whitespace-pre p-4 bg-gray-50 rounded'>
          {trainingLog.map((log, index) => (
            <div
              key={index}
              className='mb-2 hover:bg-white p-2 rounded transition-colors duration-200'>
              <Text copyable>{JSON.stringify(log, null, 2)}</Text>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
