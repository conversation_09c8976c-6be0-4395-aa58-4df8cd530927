import { Card } from 'antd';
import { Chart } from '@antv/g2';
import { useEffect, useRef } from 'react';
import { getGraphDataApi } from '@/pages/FineTuningLog/api';
import React, { useState } from 'react';

interface GraphData {
  key: string;
  value: (number | null)[];
}

interface TaskDetailsProps {
  taskUuid?: string;
}

export default function TaskDetails({ taskUuid }: TaskDetailsProps) {
  const [graphData, setGraphData] = useState<GraphData[]>([]);

  const getGraphData = async (uuid: string) => {
    try {
      const res = await getGraphDataApi(uuid);
      if (!res.code) {
        setGraphData(res.data.list);
      }
    } catch (error) {
      console.error('Failed to fetch graph data:', error);
    }
  };

  useEffect(() => {
    if (taskUuid) {
      getGraphData(taskUuid);
    }
  }, [taskUuid]);

  const steps = graphData.find((item) => item.key === 'Steps')?.value || [];

  if (!taskUuid) {
    return (
      <div className='flex items-center justify-center h-full text-gray-400'>
        {t('请选择日志')}
      </div>
    );
  }

  return (
    <div className='flex flex-wrap gap-6 mb-6'>
      {graphData
        .filter((item) => item.key !== 'Steps' && item.key !== 'KL')
        .map((item) => (
          <ChartItem
            key={item.key}
            title={item.key}
            xNum={steps as number[]}
            yNum={item.value as number[]}
          />
        ))}
    </div>
  );
}

interface ChartItemProps {
  title: string;
  xNum: number[];
  yNum: (number | null)[];
}

const ChartItem = ({ title, xNum, yNum }: ChartItemProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const chart = new Chart({
        container: chartRef.current,
        autoFit: true,
        height: 240,
        theme: 'light',
        axis: {
          y: {
            title: t('数值'),
            grid: { line: { style: { stroke: '#E5E7EB' } } },
            label: { style: { fill: '#6B7280' } },
          },
          x: {
            title: t('步数'),
            grid: { line: { style: { stroke: '#E5E7EB' } } },
            label: { style: { fill: '#6B7280' } },
          },
        },
        tooltip: {
          shared: true,
          crosshairs: {
            type: 'x',
            line: { style: { stroke: '#6B7280', strokeOpacity: 0.5 } },
          },
          domStyles: {
            'g2-tooltip': {
              background: 'rgba(255,255,255,0.95)',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              borderRadius: '6px',
              padding: '8px 12px',
            },
          },
        },
      });

      const data = xNum.map((x, index) => ({
        step: x,
        value: yNum[index],
      }));

      chart
        .line()
        .data(data)
        .encode('x', 'step')
        .encode('y', 'value')
        .style('stroke', '#4ECDC4')
        .style('lineWidth', 2)
        .animate('enter', { type: 'fadeIn' });
      chart.render();
      return () => chart.destroy();
    }
  }, [xNum, yNum]);

  return (
    <Card
      title={title}
      className='w-[calc(50%-1.5rem)] shadow hover:shadow-md transition-shadow duration-300'
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        fontSize: '14px',
        fontWeight: 500,
        padding: '12px 16px',
      }}
      bodyStyle={{ padding: '16px' }}>
      <div ref={chartRef} className='min-h-[240px]' />
    </Card>
  );
};
