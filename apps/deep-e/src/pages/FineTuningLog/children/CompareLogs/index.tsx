import { ArrowLeftOutlined } from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import { useEffect, useState, useRef } from 'react';
import { getGraphDataApi, getJobTemplateFrameAndValueForCopy } from '../../api';
import { message, Card, Table } from 'antd';
import { Chart } from '@antv/g2';

interface GraphData {
  list: Array<{
    key: string;
    value: (number | null)[];
  }>;
}

interface JobParams {
  [key: string]: any;
}

// 定义一组对比用的颜色
const COMPARE_COLORS = ['#4ECDC4', '#FF6B6B', '#45B7D1', '#96CEB4', '#FFEEAD'];

interface ParamValue {
  label: string;
  defaultValue: any;
}

export default function CompareLogs() {
  const [searchParams] = useSearchParams();
  const [tasks, setTasks] = useState<
    Array<{ taskUuid: string; jobId: string }>
  >([]);
  const [graphData, setGraphData] = useState<Record<string, GraphData>>({});
  const [loading, setLoading] = useState(false);
  const [paramsData, setParamsData] = useState<Record<string, JobParams>>({});
  const [paramLabels, setParamLabels] = useState<Record<string, string>>({});

  useEffect(() => {
    const taskUuids = searchParams.getAll('taskUuid');
    const jobIds = searchParams.getAll('jobId');
    if (taskUuids.length > 0 && jobIds.length > 0) {
      const tasksData = taskUuids.map((taskUuid, index) => ({
        taskUuid,
        jobId: jobIds[index],
      }));
      setTasks(tasksData);
      fetchAllGraphData(tasksData);
      fetchAllParams(tasksData);
    }
  }, [searchParams]);

  const fetchAllGraphData = async (
    tasksData: Array<{ taskUuid: string; jobId: string }>,
  ) => {
    setLoading(true);
    try {
      const promises = tasksData.map(({ taskUuid }) =>
        getGraphDataApi(taskUuid)
          .then((response) => ({ taskUuid, data: response.data }))
          .catch((error) => {
            console.error(
              `${t('获取任务')} ${taskUuid} ${t('的图表数据失败')}:`,
              error,
            );
            message.error(
              `${t('获取任务')} ${taskUuid} ${t('的图表数据失败')}`,
            );
            return { taskUuid, data: null };
          }),
      );

      const results = await Promise.all(promises);

      const newGraphData: Record<string, GraphData> = {};
      results.forEach((result) => {
        if (result.data) {
          newGraphData[result.taskUuid] = result.data;
        }
      });
      setGraphData(newGraphData);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllParams = async (
    tasksData: Array<{ taskUuid: string; jobId: string }>,
  ) => {
    try {
      const promises = tasksData.map(({ taskUuid, jobId }) =>
        getJobTemplateFrameAndValueForCopy({
          taskUuid,
          jobId,
          isFillData: false,
        })
          .then((response) => {
            const params = response.data.templateFrameValue[0].value;
            // 转换参数格式
            const formattedParams: Record<string, any> = {};
            Object.entries(params).forEach(([key, value]) => {
              if (typeof value === 'object' && value !== null) {
                formattedParams[key] = (value as ParamValue).defaultValue;
              } else {
                formattedParams[key] = value;
              }
            });
            return { taskUuid, params: formattedParams, rawParams: params };
          })
          .catch((error) => {
            console.error(
              `${t('获取任务')} ${taskUuid} ${t('的参数失败')}:`,
              error,
            );
            return { taskUuid, params: null, rawParams: null };
          }),
      );

      const results = await Promise.all(promises);
      const newParamsData: Record<string, JobParams> = {};
      const labelMap: Record<string, string> = {};

      results.forEach((result) => {
        if (result.params && result.rawParams) {
          newParamsData[result.taskUuid] = result.params;
          // 保存参数标签
          Object.entries(result.rawParams).forEach(([key, value]) => {
            if (
              typeof value === 'object' &&
              value !== null &&
              'label' in value
            ) {
              labelMap[key] = (value as ParamValue).label;
            }
          });
        }
      });
      setParamsData(newParamsData);
      setParamLabels(labelMap);
    } catch (error) {
      console.error(t('获取参数数据失败:'), error);
    }
  };

  const handleBack = () => {
    history.back();
  };

  return (
    <div className='p-8 h-full flex flex-col bg-gray-50'>
      <a
        onClick={handleBack}
        className='flex items-center text-gray-600 hover:text-blue-500 transition-colors duration-200 cursor-pointer mb-6 text-lg'>
        <ArrowLeftOutlined className='mr-2' />
        {t('返回日志')}
      </a>
      <div className='grid grid-cols-2 gap-4'>
        {Object.keys(graphData).length > 0 && (
          <CompareCharts
            graphDataMap={graphData}
            taskIds={tasks.map((t) => t.taskUuid)}
          />
        )}
      </div>
      <Card title={t('超参数对比')} className='mt-6'>
        <ParamsCompareTable
          loading={loading}
          paramsData={paramsData}
          taskIds={tasks.map((t) => t.taskUuid)}
          paramLabels={paramLabels}
        />
      </Card>
    </div>
  );
}

interface CompareChartsProps {
  graphDataMap: Record<string, GraphData>;
  taskIds: string[];
}

const CompareCharts = ({ graphDataMap, taskIds }: CompareChartsProps) => {
  const firstTaskData = graphDataMap[taskIds[0]]?.list || [];
  const steps = firstTaskData.find((item) => item.key === 'Steps')?.value || [];

  return (
    <>
      {firstTaskData
        .filter((item) => item.key !== 'Steps' && item.key !== 'KL')
        .map((item) => (
          <ChartItem
            key={item.key}
            title={item.key}
            graphDataMap={graphDataMap}
            taskIds={taskIds}
            metricKey={item.key}
            steps={steps as number[]}
          />
        ))}
    </>
  );
};

interface ChartItemProps {
  title: string;
  graphDataMap: Record<string, GraphData>;
  taskIds: string[];
  metricKey: string;
  steps: number[];
}

const ChartItem = ({
  title,
  graphDataMap,
  taskIds,
  metricKey,
  steps,
}: ChartItemProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = new Chart({
      container: chartRef.current,
      autoFit: true,
      height: 300,
    });

    // 合并所有任务的数据
    const allData = taskIds.flatMap((taskId, taskIndex) => {
      const taskData = graphDataMap[taskId]?.list;
      const metricData =
        taskData?.find((item) => item.key === metricKey)?.value || [];

      return steps.map((step, index) => ({
        step,
        value: metricData[index],
        task: `${t('任务')}${taskIndex + 1}`,
      }));
    });

    chart
      .line()
      .data(allData)
      .encode('x', 'step')
      .encode('y', 'value')
      .encode('color', 'task')
      .scale('color', {
        range: COMPARE_COLORS.slice(0, taskIds.length),
      })
      .style('lineWidth', 2)
      .animate('enter', { type: 'fadeIn' });

    chart.axis('y', { title: t('数值') });
    chart.axis('x', { title: t('步数') });
    chart.interaction('tooltip', {
      shared: true,
      crosshairs: {
        type: 'x',
      },
    });

    chart.render();
    return () => chart.destroy();
  }, [graphDataMap, taskIds, metricKey, steps]);

  return (
    <Card title={title} className='col-span-1'>
      <div ref={chartRef} className='min-h-[300px]' />
    </Card>
  );
};

interface ParamsCompareTableProps {
  paramsData: Record<string, JobParams>;
  taskIds: string[];
  paramLabels: Record<string, string>;
  loading?: boolean;
}

const ParamsCompareTable = ({
  paramsData,
  taskIds,
  paramLabels,
  loading,
}: ParamsCompareTableProps) => {
  // 获取所有参数的键名
  const allParamKeys = Array.from(
    new Set(Object.values(paramsData).flatMap((params) => Object.keys(params))),
  ).sort();

  // 找出每个参数的不同值
  const getDifferentValues = (key: string) => {
    const values = taskIds.map((taskId) => paramsData[taskId]?.[key]);
    return values.some((v) => v !== values[0]);
  };

  // 构建表格列
  const columns = [
    {
      title: t('参数名称'),
      dataIndex: 'paramName',
      key: 'paramName',
      fixed: 'left' as const,
      width: 200,
      render: (key: string) => paramLabels[key] || key,
      className: 'font-medium text-gray-900',
    },
    ...taskIds.map((taskId, index) => ({
      title: `${t('任务')}${index + 1}`,
      dataIndex: taskId,
      key: taskId,
      width: 150,
      className: 'font-medium text-gray-900',
      render: (value: any, record: any) => {
        const isDifferent = getDifferentValues(record.paramName);
        return (
          <span className={isDifferent ? 'text-red-600 font-medium' : ''}>
            {value ?? '-'}
          </span>
        );
      },
    })),
  ];

  // 构建表格数据
  const data = allParamKeys.map((key) => ({
    key,
    paramName: key,
    ...taskIds.reduce(
      (acc, taskId) => ({
        ...acc,
        [taskId]: paramsData[taskId]?.[key] ?? '-',
      }),
      {},
    ),
  }));

  return (
    <Table
      loading={loading}
      columns={columns}
      dataSource={data}
      scroll={{ x: 'max-content' }}
      pagination={false}
      size='middle'
      bordered
      className='compare-table'
    />
  );
};
