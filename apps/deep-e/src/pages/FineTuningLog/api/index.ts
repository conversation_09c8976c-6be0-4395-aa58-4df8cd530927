import request from '@/services';
export type GetJobTemplateFrameAndValueForCopyReq = {
  taskUuid: string;
  jobId: string;
  isFillData: boolean;
  seq?: number;
};

export const getJobTemplateFrameAndValueForCopy = (
  data: GetJobTemplateFrameAndValueForCopyReq,
) =>
  request.get('/api/plan/getJobTemplateFrameAndValueForCopy', {
    params: data,
  });

export const getGraphDataApi = (taskUuid: string) => {
  return request.get('/api/scpLog/getGraphData', {
    params: {
      taskUuid,
      seq: 1,
    },
  });
};

export const getTrainingProgressApi = (taskUuid: string) => {
  return request.get('/api/scpLog/getTrainingProgress', {
    params: {
      taskUuid,
      seq: 1,
    },
  });
};

export const getLogsScpLogger = (taskUuid: string) => {
  return request.get('/api/scpLog/getLogsScpLogger', {
    params: {
      taskUuid,
      seq: 1,
    },
  });
};

export const getCronTaskDetailContainsParentApi = (
  taskUuid: string,
  jobId: string,
) =>
  request.get('/api/cron/getCronTaskDetailContainsParent', {
    params: { taskUuid, jobId },
  });
