import { useState } from 'react';
import { RunPlanCard } from '@repo/ui';
import { useLocation, useNavigate } from 'react-router-dom';
import { useUpdateEffect } from 'ahooks';
import { FormField } from '@repo/ui';
import { useAuthStore } from '@/store/features/auth';
import _ from 'lodash';
import ajax from '@/api';
import { getJobTemplateFrameAndValueForCopy } from './api';
import { ArrowLeftOutlined } from '@ant-design/icons';

interface TemplateFrameValue {
  key: string;
  title: string;
  remark?: string;
  sourceName: string;
  value: FormField[];
}

const RunningPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { type } = location.state || { type: '' };

  const [planTemplateList, setPlanTemplateList] = useState<any[]>([]);
  const [jobFrameValue, setJobFrameValue] = useState<FormField[]>([]);
  const [templateFrameValue, setTemplateFrameValue] = useState<
    TemplateFrameValue[]
  >([]);
  const { token } = useAuthStore();
  const getPlanTemplate = async () => {
    try {
      const res = await ajax.getJobTemplateByUserOnlyRead({
        templateType: type,
      });
      if (res.code !== 0) {
        throw new Error(res.msg || t('获取计划模板失败'));
      }
      const list = _.get(res, 'data.list', []) || [];
      setPlanTemplateList(
        list.map((item: any) => ({
          label: item.templateName,
          value: item.templateUuid,
        })),
      );
    } catch (error) {
      console.error(t('错误:'), error);
    }
  };

  const getJobTemplateFrameAndValue = async (templateUuid: string) => {
    try {
      const res = await ajax.getJobTemplateFrameAndValue(templateUuid);
      if (res.code !== 0) {
        throw new Error(res.msg || t('获取计划模板失败'));
      }
      setJobFrameValue(_.get(res, 'data.jobFrameValue', []));
      setTemplateFrameValue(_.get(res, 'data.templateFrameValue', []));
    } catch (error) {
      console.error(t('错误:'), error);
    }
  };

  const getJobTemplateFrameAndValueByCopy = async (
    templateUuid: string,
    jobId: string,
    isFillData: boolean,
    seq: number,
  ) => {
    try {
      const res = await getJobTemplateFrameAndValueForCopy({
        taskUuid: templateUuid,
        isFillData,
        jobId,
        seq,
      });
      if (res.code !== 0) {
        throw new Error(res.msg || t('获取计划模板失败'));
      }
      setJobFrameValue(_.get(res, 'data.jobFrameValue', []));
      setTemplateFrameValue(_.get(res, 'data.templateFrameValue', []));
    } catch (error) {
      console.error(t('错误:'), error);
    }
  };

  return (
    <div>
      <RunPlanCard
        startCronJob={ajax.startCronJob}
        getPlanTemplate={getPlanTemplate}
        getJobTemplateFrameAndValue={getJobTemplateFrameAndValue}
        getJobTemplateFrameAndValueByCopy={getJobTemplateFrameAndValueByCopy}
        useNavigate={useNavigate}
        useUpdateEffect={useUpdateEffect}
        token={token || ''}
        planTemplateList={planTemplateList}
        jobFrameValue={jobFrameValue}
        templateFrameValue={templateFrameValue}
        setPlanTemplateList={setPlanTemplateList}
        title={
          <div className='flex items-center gap-4'>
            <a
              className='flex items-center text-gray-600 hover:text-gray-900 cursor-pointer'
              onClick={() => {
                navigate(-1);
              }}>
              <ArrowLeftOutlined className='mr-1' />
              {t('返回')}
            </a>
            {t('运行AI任务')}
          </div>
        }
        showReturn={false}
      />
    </div>
  );
};

export default RunningPage;
