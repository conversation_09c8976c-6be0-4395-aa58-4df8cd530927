import React, { useState } from 'react';
import { Form, Card, Tabs } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { t } from '@/languages';
import SingleFileUpload from './components/SingleFileUpload';
import MultiModalUpload from './components/MultiModalUpload';

interface DatasetFormValues {
  datasetName: string;
  datasetDesc: string;
  file?: any; // Optional for single file upload
  folder?: any; // Optional for multi-modal folder upload
}

export default function UploadDatasets() {
  const [form] = Form.useForm<DatasetFormValues>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('singleFile');

  const handleBack = () => {
    navigate('/datasets');
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    form.resetFields(); // Reset form fields when switching tabs
  };

  return (
    <div className='h-full p-6'>
      <div className='mb-6 flex items-center'>
        <a
          onClick={handleBack}
          className='flex items-center text-blue-600 hover:text-blue-800 cursor-pointer text-lg font-semibold'>
          <ArrowLeftOutlined className='mr-2' />
          {t('返回数据集列表')}
        </a>
      </div>
      <Card className='w-full'>
        <h2 className='text-2xl font-bold mb-6'>{t('上传新数据集')}</h2>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <Tabs.TabPane tab={t('单文件上传')} key='singleFile'>
            <SingleFileUpload form={form} />
          </Tabs.TabPane>
          <Tabs.TabPane tab={t('多模态数据集上传')} key='multiModal'>
            <MultiModalUpload form={form} />
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </div>
  );
}
