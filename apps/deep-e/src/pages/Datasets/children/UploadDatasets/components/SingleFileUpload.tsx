import React, { useState } from 'react';
import { Form, Input, Button, Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { uploadDataset, uploadFileForDataSet } from '@/pages/Datasets/api';
import { t } from '@/languages';

interface DatasetFormValues {
  datasetName: string;
  datasetDesc: string;
  file?: string; // 现在存储的是UUID字符串
}

interface SingleFileUploadProps {
  form: any;
}

export default function SingleFileUpload({ form }: SingleFileUploadProps) {
  const navigate = useNavigate();
  const [fileUuid, setFileUuid] = useState<string>('');
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadedFileName, setUploadedFileName] = useState<string>('');

  // 处理文件上传到临时存储
  const handleFileUpload = async (file: File) => {
    setUploading(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const res = await uploadFileForDataSet(formData);
      if (res.code === 0 && res.data.result) {
        setFileUuid(res.data.result);
        setUploadedFileName(file.name);
        message.success(t('文件上传成功'));
        return false; // 阻止默认上传行为
      } else {
        message.error(t(`文件上传失败: ${res.msg || ''}`));
        return false;
      }
    } catch (error) {
      console.error(t('上传文件时发生错误:'), error);
      message.error(t('上传文件时发生错误'));
      return false;
    } finally {
      setUploading(false);
    }
  };

  const handleSubmit = async (values: DatasetFormValues) => {
    console.log('handleSubmit called with values:', values);

    if (!fileUuid) {
      message.error(t('请先上传文件'));
      return;
    }

    const formData = new FormData();
    formData.append('datasetName', values.datasetName);
    formData.append('datasetDesc', values.datasetDesc);
    formData.append('file', fileUuid); // 使用UUID而不是文件对象
    formData.append('datasetType', 'singleFile');

    try {
      const res = await uploadDataset(formData);
      if (res.code === 0) {
        message.success(t('数据集创建成功'));
        navigate('/datasets');
      } else {
        message.error(t(`数据集创建失败: ${res.msg || ''}`));
      }
    } catch (error) {
      console.error(t('创建数据集时发生错误:'), error);
      message.error(t('创建数据集时发生错误'));
    }
  };

  return (
    <div className='max-w-[600px]'>
      <Form form={form} onFinish={handleSubmit} layout='vertical'>
        <Form.Item
          label={t('数据集名称')}
          name='datasetName'
          rules={[{ required: true, message: t('请输入数据集名称') }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label={t('数据集描述')}
          name='datasetDesc'
          rules={[{ required: true, message: t('请输入数据集描述') }]}>
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item
          label={t('上传文件')}
          rules={[{ required: true, message: t('请先上传文件') }]}>
          <Upload.Dragger
            beforeUpload={handleFileUpload}
            multiple={false}
            showUploadList={false}
            disabled={uploading}>
            <p className='ant-upload-drag-icon'>
              <UploadOutlined />
            </p>
            <p className='ant-upload-text'>
              {uploading ? t('正在上传...') : t('点击或拖拽文件到此区域上传')}
            </p>
            <p className='ant-upload-hint'>
              {t('支持单文件上传，文件大小不超过 100MB，支持.jsonl等格式')}
            </p>
          </Upload.Dragger>
          {uploadedFileName && (
            <div className='mt-2 p-2 bg-green-50 border border-green-200 rounded'>
              <span className='text-green-600'>
                {t('已上传文件')}: {uploadedFileName}
              </span>
            </div>
          )}
        </Form.Item>
        <Form.Item>
          <div className='flex justify-center'>
            <Button
              type='primary'
              htmlType='submit'
              size='large'
              disabled={!fileUuid || uploading}>
              {t('提交')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
}
