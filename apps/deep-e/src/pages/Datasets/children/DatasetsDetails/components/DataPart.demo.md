# DataPart 组件 COCO 标注功能演示

## 功能说明

这个 demo 在 DataPart.tsx 文件中实现了以下功能：

### 1. 图片列识别和显示
- 自动识别包含 'image', 'img', 'pic', 'url' 关键字的列作为图片列
- 支持 HTTP/HTTPS 图片链接的显示
- 图片显示尺寸：最大宽度120px，最大高度80px
- 图片右上角显示"标注"标签，提示用户可以点击查看标注信息

### 2. 点击图片展开 Modal
- 点击图片时会阻止事件冒泡，避免触发表格行点击事件
- 弹出 Modal 显示 COCO 格式的标注信息
- Modal 宽度为 800px，支持滚动查看内容

### 3. COCO 格式标注信息展示

#### 图片信息卡片
- 显示图片预览（支持点击放大）
- 显示文件名、图片尺寸、图片ID等基本信息

#### 标注类别卡片
- 以标签形式展示所有标注类别
- 显示类别名称和ID

#### 标注详情卡片
- 每个标注对象单独显示在一个卡片中
- 包含以下信息：
  - 标注ID和类别ID
  - 边界框坐标 [x, y, width, height]
  - 标注面积
  - 是否为群体标注
  - 分割信息（如果存在）

### 4. 模拟数据生成
为了演示效果，实现了 `generateMockCOCOData` 函数，生成包含以下内容的模拟 COCO 数据：

#### 模拟类别
- person (人)
- car (汽车)
- dog (狗)
- cat (猫)
- bicycle (自行车)

#### 模拟标注
- 3个标注对象，分别对应 person、car、dog
- 包含完整的边界框、面积、分割信息

## 技术实现

### 接口定义
```typescript
interface COCOAnnotation {
  id: number;
  image_id: number;
  category_id: number;
  bbox: [number, number, number, number]; // [x, y, width, height]
  area: number;
  iscrowd: number;
  segmentation?: number[][];
}

interface COCOCategory {
  id: number;
  name: string;
  supercategory: string;
}

interface COCOImage {
  id: number;
  file_name: string;
  width: number;
  height: number;
  url?: string;
}
```

### 状态管理
- `modalVisible`: 控制 Modal 的显示/隐藏
- `selectedImageData`: 存储当前选中图片的标注数据

### 国际化支持
- 使用项目的 `t` 函数进行文本国际化
- 支持中文、英文等多语言显示

## 使用方式

### 🎯 虚拟数据测试（新增功能）
1. **点击"切换到虚拟数据"按钮** - 表格会加载包含图片的虚拟数据集
2. **查看虚拟数据** - 包含15条记录，每条都有图片、问题、答案等字段
3. **测试COCO功能** - 点击任意图片查看COCO格式标注信息
4. **切换回真实数据** - 点击"切换到真实数据"按钮恢复原始数据

### 📊 虚拟数据内容
- **图片来源**: 使用Unsplash高质量图片（狗、猫、车辆、建筑、场景）
- **数据字段**: image_url、question、answer、category、confidence、timestamp、messages
- **COCO标注**: 每张图片都有完整的模拟COCO标注数据
- **分页支持**: 支持表格分页，总共15条记录

### 🖼️ COCO标注测试
1. 在虚拟数据模式下，图片右上角有"标注"提示
2. 点击任意图片即可查看 COCO 格式的标注信息
3. Modal 中可以查看完整的标注数据结构
4. 包含图片信息、类别标签、详细标注数据

## 扩展建议

1. **真实数据集成**: 将模拟数据替换为从后端API获取的真实COCO标注数据
2. **可视化标注**: 在图片上绘制边界框和分割区域
3. **标注编辑**: 支持在Modal中编辑标注信息
4. **导出功能**: 支持导出COCO格式的标注文件
5. **批量操作**: 支持批量查看多张图片的标注信息
