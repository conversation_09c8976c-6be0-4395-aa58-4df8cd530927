import React from 'react';
import { t } from '@/languages';
import { COCOCategory, COCOAnnotation } from './AnnotatedImage';

interface AnnotationLegendProps {
  categories: COCOCategory[];
  annotations: COCOAnnotation[];
}

const AnnotationLegend: React.FC<AnnotationLegendProps> = ({
  categories,
  annotations,
}) => {
  // 颜色映射（与AnnotatedImage保持一致）
  const categoryColors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
  ];

  // 统计每个类别的标注数量
  const getCategoryCount = (categoryId: number) => {
    return annotations.filter(ann => ann.category_id === categoryId).length;
  };

  return (
    <div>
      <div style={{ marginBottom: 8 }}>
        <strong>{t('图例')}:</strong>
      </div>
      <div>
        {categories.map((category) => {
          const color = categoryColors[category.id % categoryColors.length];
          const count = getCategoryCount(category.id);
          
          return (
            <div
              key={category.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: 6,
                fontSize: '12px',
              }}
            >
              <div
                style={{
                  width: 16,
                  height: 16,
                  backgroundColor: color,
                  marginRight: 8,
                  border: '1px solid #ccc',
                  borderRadius: 2,
                }}
              />
              <span style={{ flex: 1 }}>
                {category.name}
              </span>
              {count > 0 && (
                <span style={{ 
                  color: '#666', 
                  marginLeft: 8,
                  fontSize: '11px'
                }}>
                  ({count})
                </span>
              )}
            </div>
          );
        })}
      </div>
      <div style={{ 
        marginTop: 12, 
        paddingTop: 8, 
        borderTop: '1px solid #f0f0f0',
        fontSize: '11px',
        color: '#999'
      }}>
        {t('总计')}: {annotations.length} {t('个标注')}
      </div>
    </div>
  );
};

export default AnnotationLegend;
