import React, { useEffect, useState, useRef } from 'react';
import { t } from '@/languages';

// COCO格式标注数据接口定义
export interface COCOAnnotation {
  id: number;
  image_id: number;
  category_id: number;
  bbox: [number, number, number, number]; // [x, y, width, height]
  area: number;
  iscrowd: number;
  segmentation?: number[][];
}

export interface COCOCategory {
  id: number;
  name: string;
  supercategory: string;
}

export interface COCOImage {
  id: number;
  file_name: string;
  width: number;
  height: number;
  url?: string;
}

// 图片标注可视化组件Props
export interface AnnotatedImageProps {
  imageUrl: string;
  annotations: COCOAnnotation[];
  categories: COCOCategory[];
  imageInfo?: COCOImage;
  maxWidth?: number;
  showControls?: boolean;
}

const AnnotatedImage: React.FC<AnnotatedImageProps> = ({
  imageUrl,
  annotations,
  categories,
  maxWidth = 600,
  showControls = false,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showAnnotations, setShowAnnotations] = useState(true);

  // 颜色映射
  const categoryColors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
  ];

  const drawAnnotations = () => {
    const canvas = canvasRef.current;
    const img = imgRef.current;
    if (!canvas || !img || !imageLoaded || !showAnnotations) {
      // 如果不显示标注，清空canvas
      if (canvas && imageLoaded) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
      }
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置canvas尺寸与图片一致
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;

    // 清空canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制标注框
    annotations.forEach((annotation) => {
      const category = categories.find(
        (cat) => cat.id === annotation.category_id,
      );
      const color =
        categoryColors[annotation.category_id % categoryColors.length];

      const [x, y, width, height] = annotation.bbox;

      // 绘制边界框
      ctx.strokeStyle = color;
      ctx.lineWidth = 3;
      ctx.strokeRect(x, y, width, height);

      // 绘制标签背景
      const label = `${category?.name || 'Unknown'} (${annotation.id})`;
      ctx.font = '14px Arial';
      const textMetrics = ctx.measureText(label);
      const textWidth = textMetrics.width;
      const textHeight = 20;

      ctx.fillStyle = color;
      ctx.fillRect(x, y - textHeight - 2, textWidth + 8, textHeight + 4);

      // 绘制标签文字
      ctx.fillStyle = 'white';
      ctx.fillText(label, x + 4, y - 6);

      // 如果有分割信息，绘制分割轮廓
      if (annotation.segmentation && annotation.segmentation.length > 0) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);

        annotation.segmentation.forEach((segment) => {
          if (segment.length >= 6) {
            // 至少需要3个点
            ctx.beginPath();
            ctx.moveTo(segment[0], segment[1]);
            for (let i = 2; i < segment.length; i += 2) {
              ctx.lineTo(segment[i], segment[i + 1]);
            }
            ctx.closePath();
            ctx.stroke();
          }
        });

        ctx.setLineDash([]); // 重置虚线
      }
    });
  };

  useEffect(() => {
    if (imageLoaded) {
      drawAnnotations();
    }
  }, [imageLoaded, annotations, showAnnotations]);

  return (
    <div>
      {/* 控制按钮 */}
      {showControls && (
        <div style={{ marginBottom: 12 }}>
          <button
            onClick={() => setShowAnnotations(!showAnnotations)}
            style={{
              padding: '4px 12px',
              backgroundColor: showAnnotations ? '#1890ff' : '#f0f0f0',
              color: showAnnotations ? 'white' : '#333',
              border: '1px solid #d9d9d9',
              borderRadius: 4,
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            {showAnnotations ? t('隐藏标注') : t('显示标注')}
          </button>
        </div>
      )}

      {/* 图片和标注 */}
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <img
          ref={imgRef}
          src={imageUrl}
          alt={t('标注图片')}
          style={{
            width: '100%',
            maxWidth: maxWidth,
            height: 'auto',
            display: 'block',
          }}
          onLoad={() => {
            setImageLoaded(true);
          }}
          onError={() => {
            console.error('Image failed to load:', imageUrl);
          }}
        />
        <canvas
          ref={canvasRef}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
          }}
        />
      </div>

      {/* 标注统计信息 */}
      {annotations.length > 0 && (
        <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
          {t('共')} {annotations.length} {t('个标注对象')}
        </div>
      )}
    </div>
  );
};

export default AnnotatedImage;
