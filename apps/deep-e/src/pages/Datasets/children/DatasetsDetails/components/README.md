# COCO标注可视化组件

这个目录包含了用于显示和可视化COCO格式标注数据的独立组件。

## 组件结构

### 1. AnnotatedImage.tsx
**主要的图片标注可视化组件**

#### 功能特性
- 在图片上绘制COCO格式的标注框
- 支持边界框、类别标签、分割轮廓的可视化
- 提供显示/隐藏标注的控制按钮
- 响应式设计，支持不同图片尺寸
- 自动颜色编码，每个类别使用不同颜色

#### Props接口
```typescript
interface AnnotatedImageProps {
  imageUrl: string;           // 图片URL
  annotations: COCOAnnotation[];  // COCO标注数据
  categories: COCOCategory[];     // 类别信息
  imageInfo?: COCOImage;         // 图片信息（可选）
  maxWidth?: number;             // 最大宽度（默认600px）
  showControls?: boolean;        // 是否显示控制按钮（默认false）
}
```

#### 使用示例
```tsx
<AnnotatedImage
  imageUrl="https://example.com/image.jpg"
  annotations={cocoAnnotations}
  categories={cocoCategories}
  maxWidth={800}
  showControls={true}
/>
```

### 2. AnnotationLegend.tsx
**标注图例组件**

#### 功能特性
- 显示所有类别的颜色图例
- 统计每个类别的标注数量
- 显示总标注数量
- 与AnnotatedImage使用相同的颜色映射

#### Props接口
```typescript
interface AnnotationLegendProps {
  categories: COCOCategory[];     // 类别信息
  annotations: COCOAnnotation[];  // 标注数据
}
```

#### 使用示例
```tsx
<AnnotationLegend
  categories={cocoCategories}
  annotations={cocoAnnotations}
/>
```

### 3. DataPart.tsx
**主要的数据表格组件**

#### 功能特性
- 显示数据集详情表格
- 自动识别图片列并显示缩略图
- 点击图片弹出Modal显示完整的COCO标注信息
- 支持虚拟数据和真实数据切换
- 集成AnnotatedImage和AnnotationLegend组件

## COCO数据格式

### COCOAnnotation
```typescript
interface COCOAnnotation {
  id: number;                    // 标注ID
  image_id: number;             // 图片ID
  category_id: number;          // 类别ID
  bbox: [number, number, number, number]; // 边界框 [x, y, width, height]
  area: number;                 // 标注面积
  iscrowd: number;             // 是否群体标注 (0/1)
  segmentation?: number[][];    // 分割信息（可选）
}
```

### COCOCategory
```typescript
interface COCOCategory {
  id: number;           // 类别ID
  name: string;         // 类别名称
  supercategory: string; // 父类别
}
```

### COCOImage
```typescript
interface COCOImage {
  id: number;           // 图片ID
  file_name: string;    // 文件名
  width: number;        // 图片宽度
  height: number;       // 图片高度
  url?: string;         // 图片URL（可选）
}
```

## 可视化效果

### 颜色方案
组件使用10种鲜明的颜色循环映射类别：
- #FF6B6B (红色)
- #4ECDC4 (青色)
- #45B7D1 (蓝色)
- #96CEB4 (绿色)
- #FFEAA7 (黄色)
- #DDA0DD (紫色)
- #98D8C8 (薄荷绿)
- #F7DC6F (金黄色)
- #BB8FCE (淡紫色)
- #85C1E9 (天蓝色)

### 绘制元素
- **边界框**: 3px粗细的彩色矩形框
- **类别标签**: 带背景的白色文字，显示类别名称和标注ID
- **分割轮廓**: 5px虚线样式，精确显示物体轮廓

## 技术实现

### Canvas绘制
- 使用HTML5 Canvas API在图片上绘制标注
- 动态设置Canvas尺寸与图片一致
- 支持高分辨率图片的精确绘制

### 响应式设计
- 图片自动适配容器宽度
- 支持maxWidth属性限制最大宽度
- Canvas叠加层自动跟随图片缩放

### 性能优化
- 只在图片加载完成后进行绘制
- 支持显示/隐藏标注的切换
- 颜色映射预计算，避免重复计算

## 使用建议

1. **组件复用**: AnnotatedImage和AnnotationLegend可以在其他页面独立使用
2. **数据验证**: 使用前确保COCO数据格式正确
3. **性能考虑**: 大量标注时考虑分页或虚拟化
4. **样式定制**: 可以通过修改categoryColors数组自定义颜色方案
5. **扩展功能**: 可以基于现有组件扩展编辑、导出等功能
