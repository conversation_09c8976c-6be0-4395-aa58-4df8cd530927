import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { getDatasetLog } from '../../../api/index';
import useDayFormat from '@/hooks/useDayFormat';
import dayjs from 'dayjs';

type LogPartProps = {
  datasetUuid: string;
};
const LogPart: React.FC<LogPartProps> = ({ datasetUuid }) => {
  const [logs, setLogs] = useState<any[]>([]);
  const format = useDayFormat();
  const getDatasetBaseInfoByUuid = async () => {
    try {
      const res = await getDatasetLog({
        datasetUuid,
      });
      if (res.code === 0) {
        console.log(t('获取数据集日志成功：'), res.data);
        setLogs(res.data.list || []);
      }
    } catch (error) {
      console.error(t('获取数据集日志失败：'), error);
    }
  };
  useEffect(() => {
    getDatasetBaseInfoByUuid();
  }, [datasetUuid]);
  return (
    <div className='flex flex-col h-full'>
      <div className='flex-1 overflow-auto font-mono text-sm'>
        {logs.length === 0 ? (
          <div className='flex items-center justify-center h-full text-muted-foreground'>
            {t('没有匹配的日志记录')}
          </div>
        ) : (
          <div className='space-y-2'>
            {logs.map((log) => (
              <div
                key={log.id}
                className={
                  'p-2 rounded-md  bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-500'
                }>
                <div className='flex items-center gap-2 cursor-pointer'>
                  <div className='text-xs text-muted-foreground whitespace-nowrap'>
                    {dayjs(log.time).format(format)}
                  </div>
                  <Badge variant={'secondary'} className='whitespace-nowrap'>
                    {'INFO'}
                  </Badge>
                  <div className='flex-1'>{log.msg}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
export default LogPart;
