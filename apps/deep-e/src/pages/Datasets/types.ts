// 标注对象类型
export interface Annotation {
  id: string;
  label: string;
  bboxes?: [number, number, number, number][]; // [x, y, width, height]
  segmentation?: number[][];
  attributes?: Record<string, string>;
}

// 标注组类型
export interface AnnotationGroup {
  label: string;
  annotations: Annotation[];
}

// QA 问答类型
export interface QAItem {
  question: string;
  answer: string;
  lang: string;
  type: string;
}

// 完整图片标注数据类型
export interface ImageAnnotationData {
  image_id: string;
  annotationGroups: AnnotationGroup[];
}

export interface ImageData {
  qa: QAItem[];
  images: ImageAnnotationData[];
}
