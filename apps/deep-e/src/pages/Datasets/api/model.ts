export interface Dataset {
  id: number;
  createdAt: number;
  createdBy: number;
  updatedAt: number;
  updatedBy: number;
  datasetName: string;
  datasetDesc: string;
  datasetType: number;
  datasetUuid: string;
  datasetState: number;
  datasetStateName: string;
  coId: number;
}

export interface GetDatasetListParams {
  page: number;
  pageSize: number;
}

export interface GetDatasetDetailParams {
  datasetUuid: string;
  page: number;
  pageSize: number;
}

export interface DeleteDatasetParams {
  datasetUuid: string;
}

interface Message {
  Role?: string; // 支持大写的Role
  role?: string; // 支持小写的role
  Content?: string; // 支持大写的Content
  content?: string; // 支持小写的content
}

interface DatasetDetail {
  messages: Message[];
  [key: string]: any;
}

export interface DatasetDetailItem {
  id: number;
  createdAt: number;
  createdBy: number;
  updatedAt: number;
  updatedBy: number;
  fileUuid: string;
  datasetDetail: DatasetDetail;
}

export interface UploadDatasetResponse {
  jsonlFiles?: string[];
  datasetUuid?: string;
  [key: string]: any;
}

export interface UploadFileResponse {
  uuid: string;
}
