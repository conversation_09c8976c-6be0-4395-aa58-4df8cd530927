import request, { returnData } from '@/services';
import {
  Dataset,
  GetDatasetListParams,
  GetDatasetDetailParams,
  DatasetDetailItem,
  DeleteDatasetParams,
  UploadDatasetResponse,
  UploadFileResponse,
} from './model';

export const getDatasetList = (
  params: GetDatasetListParams,
): Promise<returnData<Dataset[]>> => {
  return request.get('/api/dataset/getDatasetList', { params });
};

export const getDatasetDetail = (
  params: GetDatasetDetailParams,
): Promise<returnData<DatasetDetailItem[]>> => {
  return request.get('/api/dataset/getDatasetDetailList', { params });
};

export const deleteDataset = (
  params: DeleteDatasetParams,
): Promise<returnData<null>> => {
  return request.post('/api/dataset/deleteDataset', {
    datasetUuid: params.datasetUuid,
  });
};

export const uploadDataset = (
  params: FormData,
): Promise<returnData<UploadDatasetResponse>> => {
  return request.post('/api/dataset/uploadDataset', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getDatasetBaseInfoByUuid = (datasetUuid: string) => {
  return request.get('/api/dataset/getDatasetBaseInfoByUuid', {
    params: {
      datasetUuid,
    },
  });
};

export const getDatasetLog = (params: { datasetUuid: string }) => {
  return request.get('/api/scpLog/getDatasetLogs', { params });
};

export const getStatusForDatasets = () => {
  return request.get('/api/dataset/getDatasetStateList');
};

export const confirmJsonlSelection = (params: {
  datasetUuid: string;
  selectedJsonl: string;
}): Promise<returnData<null>> => {
  return request.post('/api/dataset/confirmJsonlSelection', params);
};

export const uploadFolderForDataSet = (
  params: FormData,
): Promise<returnData<UploadDatasetResponse>> => {
  return request.post('/api/file/uploadFolderForDataSet', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const uploadFileForDataSet = (
  params: FormData,
): Promise<returnData<UploadFileResponse>> => {
  return request.post('/api/file/uploadFileForDataSet', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
