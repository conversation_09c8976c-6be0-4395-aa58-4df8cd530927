import request, { returnData } from '@/services';
// type MainResourcesOverview struct {
// 	Name  string `json:"name"`
// 	Count int64  `json:"count"`
// }
import { BarChartProps } from '../components/bar-chart-section';
import { PieChartProps } from '../components/pie-chart-section';
import { ModelChartProps } from '../components/model-evaluation-chart';

interface MainResourcesOverview {
  name: string;
  count: number;
  path: string;
}
export const getOverviewData = (): Promise<
  returnData<MainResourcesOverview[]>
> => {
  return request.get('/api//dashboard/getOverviewOfTheMainResources');
};

export const getOverviewOfModelTrainingDetail = async (
  timeRange: string[],
): Promise<BarChartProps[]> => {
  const { list } = (
    await request.get('/api/dashboard/getOverviewOfModelTrainingDetail', {
      params: {
        startTime: timeRange[0],
        endTime: timeRange[1],
      },
    })
  ).data;
  const res: BarChartProps[] = [];

  list.map(
    (item: {
      trainingDetailList: { trainingName: string; count: number }[] | null;
      date: string;
    }) => {
      if (item.trainingDetailList !== null) {
        item.trainingDetailList.forEach((li) => {
          res.push({
            date: item.date,
            type: li.trainingName,
            value: li.count,
          });
        });
      }
    },
  );
  return res;
};

export const getBaseModeProportionOfTrainedModel = async (): Promise<
  PieChartProps[]
> => {
  const res = await request.get(
    '/api/dashboard/getBaseModeProportionOfTrainedModel',
  );
  return res.data.list.map(
    (item: { percentage: number; baseModel: string; count: number }) => ({
      name: item.baseModel,
      percent: item.percentage,
      value: item.count,
    }),
  );
};

export const getProportionOfModelEval = async (): Promise<
  ModelChartProps[]
> => {
  const res = await request.get('/api/dashboard/getProportionOfModelEval');
  return res.data.list.map(
    (item: { evalType: number; percentage: number; evalTypeName: string }) => ({
      name: item.evalTypeName,
      value: item.percentage,
    }),
  );
};

// 第三方模型分布接口
export interface ThirdPartyModelData {
  factoryName: string;
  factoryPercentage: number;
  modelTypePercentageInfoList: {
    typePercentage: number;
    modelType: string;
  }[];
}

export const getProportionOfThirdPartyModel = async (): Promise<
  ThirdPartyModelData[]
> => {
  const res = await request.get(
    '/api/dashboard/getProportionOfThirdPartyModel',
  );
  return res.data.list;
};
