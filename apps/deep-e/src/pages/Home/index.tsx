import DashboardCards from './components/dashboard-cards';
import BarChartSection from './components/bar-chart-section';
import PieChartSection from './components/pie-chart-section';
import ThirdModelChart from './components/third-modle-chart';
import ModelEvaluationChart from './components/model-evaluation-chart';
export default function Home() {
  return (
    <main className='min-h-screen bg-gray-50 p-4 md:p-8'>
      <div className='mx-auto max-w-full'>
        <header className='mb-8'>
          <h1 className='text-3xl font-bold text-[#0b43c3]'>
            {t('数据仪表盘')}
          </h1>
          <p className='text-gray-500'>{t('数据概览与分析')}</p>
        </header>

        <div className='mb-8'>
          <DashboardCards />
        </div>

        <div className='grid grid-cols-1 gap-8 lg:grid-cols-5'>
          <div className='lg:col-span-2'>
            <BarChartSection />
          </div>
          <div className='lg:col-span-3'>
            <PieChartSection />
          </div>
        </div>

        <div className=' max-h-20 mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2 '>
          <ModelEvaluationChart />
          <ThirdModelChart />
        </div>
      </div>
    </main>
  );
}
