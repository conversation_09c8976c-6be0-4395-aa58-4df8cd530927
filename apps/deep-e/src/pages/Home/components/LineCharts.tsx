import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';

export interface LineChartsProps {
  // 可根据需要扩展属性
}

const LineCharts: React.FC<LineChartsProps> = () => {
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const chart = new Chart({
      container: chartContainerRef.current || '',
      type: 'view',
      autoFit: true,
      padding: 40,
      paddingLeft: 50,
    });
    chart.options({
      style: {
        viewFill: '#fff',
      },
      axis: {
        x: {
          titlePosition: 'r', // 将 x 轴标题放在右下角
          // titleSpacing: -10, // x 轴标题距离坐标轴的距离
        },
        y: {
          titlePosition: 'lt',
          // titleSpacing: -15,
        },
      },
    });

    chart
      .line()
      .data({
        type: 'fetch',
        value:
          'https://gw.alipayobjects.com/os/bmw-prod/551d80c6-a6be-4f3c-a82a-abd739e12977.csv',
      })
      .encode('x', 'date')
      .encode('y', 'close');

    chart.interaction('brushXFilter', true);
    chart.render();
  }, []);

  return (
    <div className='w-full h-full p-2'>
      <div>123</div>
      <div
        className='w-[calc(100%-16px)] h-[calc(100%-32px)]'
        ref={chartContainerRef}></div>
    </div>
  );
};

export default LineCharts;
