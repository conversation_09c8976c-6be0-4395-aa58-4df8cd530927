'use client';

import { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts/core';
import { GridComponent, TooltipComponent } from 'echarts/components';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  getProportionOfThirdPartyModel,
  ThirdPartyModelData,
} from '../api/index';

// 注册必要的echarts组件
echarts.use([GridComponent, TooltipComponent]);

interface ModelData {
  name: string;
  value: number;
  color: string;
  children?: ModelData[];
}

export default function ThirdModelChart() {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.ECharts | null>(
    null,
  );
  const [thirdPartyData, setThirdPartyData] = useState<ThirdPartyModelData[]>(
    [],
  );
  const navigate = useNavigate();

  // 获取第三方模型数据
  useEffect(() => {
    getProportionOfThirdPartyModel().then((res) => {
      setThirdPartyData(res);
    });
  }, []);

  // 根据接口数据生成模型数据和颜色
  const modelData: ModelData[] = thirdPartyData.map((item, index) => {
    const colors = ['#4F7CFF', '#20D486', '#E879FF', '#FFB340', '#73c0de'];
    return {
      name: item.factoryName,
      value: item.factoryPercentage,
      color: colors[index % colors.length],
    };
  });

  const handleMouseEnter = (index: number) => {
    if (chartInstance && modelData[index]) {
      // 高亮对应的图表区域
      chartInstance.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index,
      });

      chartInstance.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        name: modelData[index].name,
      });

      // 在图表中心显示信息
      chartInstance.setOption({
        graphic: [
          {
            type: 'text',
            left: '32%',
            top: 'middle',
            style: {
              text: `${modelData[index].name}\n${modelData[index].value.toFixed(2)}%`,
              textAlign: 'center',
              textVerticalAlign: 'middle',
              fontSize: 16,
              fontWeight: 'bold',
              fill: '#333',
              lineHeight: 22,
            },
            z: 100,
          },
        ],
      });
    }
  };

  const handleMouseLeave = (index: number) => {
    if (chartInstance && modelData[index]) {
      // 取消高亮
      chartInstance.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index,
      });

      chartInstance.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        name: modelData[index].name,
      });

      // 清除中心文字
      chartInstance.setOption({
        graphic: [],
      });
    }
  };

  useEffect(() => {
    if (!chartRef.current || thirdPartyData.length === 0) return;

    const chart = echarts.init(chartRef.current);
    setChartInstance(chart);

    // 根据接口数据构建图表数据
    const colors = ['#4F7CFF', '#20D486', '#E879FF', '#FFB340', '#73c0de'];
    const chartData = thirdPartyData.map((item, index) => ({
      name: item.factoryName,
      value: item.factoryPercentage,
      itemStyle: { color: colors[index % colors.length] },
      emphasis: {
        itemStyle: {
          color: colors[index % colors.length],
          borderWidth: 3,
          borderColor: '#333',
          shadowBlur: 8,
          shadowColor: 'rgba(0,0,0,0.4)',
        },
      },
      children: item.modelTypePercentageInfoList.map((child, childIndex) => ({
        value: child.typePercentage,
        name: child.modelType,
        itemStyle: {
          color: colors[index % colors.length],
          opacity: 0.8 - childIndex * 0.2,
        },
      })),
    }));

    const option = {
      graphic: [], // 初始化时不显示任何文字
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `${params.name}: ${params.value}%`;
        },
      },
      series: [
        {
          type: 'sunburst',
          center: ['40%', '50%'], // 将图表稍微左移为右侧legend腾出空间
          radius: [30, '80%'], // 保持空心的中间部分
          nodeClick: false,
          emphasis: {
            focus: 'descendant',
            itemStyle: {
              borderWidth: 4,
              borderColor: '#333',
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.5)',
            },
          },
          data: chartData,
          label: {
            show: false, // 隐藏所有文字标签
          },
          levels: [
            {},
            {
              itemStyle: {
                borderRadius: 8,
                borderWidth: 2,
                borderColor: '#fff',
              },
              label: {
                show: false, // 隐藏第一层标签
              },
            },
            {
              itemStyle: {
                borderRadius: 4,
                borderWidth: 1,
                borderColor: '#fff',
              },
              label: {
                show: false, // 隐藏第二层标签
              },
            },
          ],
        },
      ],
    };

    // 添加图表事件监听器
    chart.on('mouseover', (params: any) => {
      // 在图表中心显示hover的信息
      chart.setOption({
        graphic: [
          {
            type: 'text',
            left: '32%',
            top: 'middle',
            style: {
              text: `${params.name}\n${params.value.toFixed(2)}%`,
              textAlign: 'center',
              textVerticalAlign: 'middle',
              fontSize: 16,
              fontWeight: 'bold',
              fill: '#333',
              lineHeight: 22,
            },
            z: 100,
          },
        ],
      });
    });

    chart.on('mouseout', () => {
      // 鼠标移出时清除中心文字
      chart.setOption({
        graphic: [],
      });
    });

    chart.setOption(option);
    return () => {
      chart.dispose();
      setChartInstance(null);
    };
  }, [thirdPartyData]);

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>{t('第三方模型分布')}</CardTitle>
        <Button
          variant='ghost'
          className='text-[#0b43c3]'
          onClick={() => navigate('/modelSetting/thirdPartyModel')}>
          {t('查看更多')} <ArrowRight className='ml-2 h-4 w-4' />
        </Button>
      </CardHeader>
      <CardContent>
        <div className='flex items-center'>
          <div className='flex-1'>
            <div ref={chartRef} className='w-full h-[280px]' />
          </div>
          <div className='flex-1 pl-4'>
            <div className='space-y-4'>
              {modelData.map((item, index) => (
                <div
                  key={item.name}
                  className='flex items-center justify-between cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors'
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={() => handleMouseLeave(index)}>
                  <div className='flex items-center space-x-2 flex-1 min-w-0'>
                    <div
                      className='w-3 h-3 rounded-full flex-shrink-0'
                      style={{
                        backgroundColor: item.color,
                      }}
                    />
                    <span
                      className='text-sm text-gray-600 truncate'
                      title={item.name}>
                      {item.name}
                    </span>
                  </div>
                  <div className='text-right flex-shrink-0 ml-2'>
                    <div className='text-xs text-gray-500'>
                      {item.value.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
