'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import ReactECharts from 'echarts-for-react';
import { getOverviewOfModelTrainingDetail } from '../api/index';
import dayjs from 'dayjs';

export interface BarChartProps {
  date: string;
  type: string;
  value: number;
}
export default function BarChartSection() {
  const [data, setData] = React.useState<BarChartProps[]>([]);
  const navigate = useNavigate();

  // 创建 ECharts 配置项
  const getOption = () => {
    // 数据转换：将原始数据转换为 ECharts 需要的格式
    const dates = [...new Set(data.map((item) => item.date))];
    const types = [...new Set(data.map((item) => item.type))];

    // 动态颜色分配 - 根据接口返回的数据类型分配颜色
    const colorPalette = [
      '#4F7CFF',
      '#E5E9FF',
      '#FF7B7B',
      '#7BE3FF',
      '#B8FF7B',
      '#FFB87B',
      '#E87BFF',
    ];
    const colorMap: { [key: string]: string } = {};
    types.forEach((type, index) => {
      colorMap[type] = colorPalette[index % colorPalette.length];
    });

    const seriesData = types.map((type, index) => ({
      name: type,
      type: 'bar',
      stack: t('总量'), // 堆叠效果
      data: dates.map((date) => {
        const item = data.find((d) => d.date === date && d.type === type);
        return item ? item.value : 0;
      }),
      itemStyle: {
        color: colorMap[type],
        // 根据堆叠位置设置圆角：第一个（底部）设置底部圆角，最后一个（顶部）设置顶部圆角
        borderRadius:
          index === 0
            ? [0, 0, 4, 4]
            : index === types.length - 1
              ? [4, 4, 0, 0]
              : 0,
      },
      barWidth: 32, // 设置柱子宽度
    }));

    return {
      grid: {
        left: '5%',
        right: '5%',
        bottom: '20%',
        top: '15%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: '#1A1A1A',
        borderColor: '#333',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        formatter: function (params: any) {
          let result = `<div style="font-weight: 500;">${params[0].axisValue}</div>`;
          params.forEach((param: any) => {
            result += `<div style="margin-top: 4px;">
              <span style="display: inline-block; width: 8px; height: 8px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${param.seriesName}: ${param.value}
            </div>`;
          });
          return result;
        },
      },
      legend: {
        data: types,
        bottom: 0,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#666',
          fontSize: 12,
        },
        itemStyle: {
          borderRadius: 50,
        },
      },
      xAxis: {
        type: 'category',
        data: dates.map((date) => dayjs(date).format('M/D')), // 格式化日期显示
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          margin: 12,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
        max: function (value: any) {
          return Math.ceil(value.max * 1.2); // 留出顶部空间
        },
      },
      series: seriesData,
    };
  };

  const getOverviewOfModelTrainingDetailFetch = async (timeRange: string[]) => {
    const res = await getOverviewOfModelTrainingDetail(timeRange);

    // 生成时间范围内的所有日期列表
    const start = dayjs(timeRange[0]);
    const end = dayjs(timeRange[1]);
    const dateList: string[] = [];
    let current = start;
    while (current.isBefore(end) || current.isSame(end, 'day')) {
      dateList.push(current.format('YYYY-MM-DD'));
      current = current.add(1, 'day');
    }

    // 获取所有存在的类型
    const types = Array.from(new Set(res.map((item) => item.type)));

    // 确保每个日期都有所有类型的数据
    const fullData: BarChartProps[] = [];
    dateList.forEach((date) => {
      types.forEach((type) => {
        const existing = res.find(
          (item) => item.date === date && item.type === type,
        );
        if (existing) {
          fullData.push(existing);
        } else {
          // 补充缺失的日期+类型组合，值设为9
          fullData.push({ date, type, value: 0 });
        }
      });
    });

    setData(fullData);
  };

  // 实现获取最近7天时间范围的函数
  const getTimeRange = () => {
    const endDate = dayjs(); // 当前日期
    const startDate = endDate.subtract(6, 'day'); // 7天范围（含今天）
    return [
      startDate.format('YYYY-MM-DDTHH:mm:ssZ'),
      endDate.format('YYYY-MM-DDTHH:mm:ssZ'),
    ];
  };

  // 修改useEffect，在组件挂载时获取最近7天数据
  React.useEffect(() => {
    getOverviewOfModelTrainingDetailFetch(getTimeRange());
  }, []);

  return (
    <Card className='col-span-1'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>{t('微调任务')}</CardTitle>
        <Button
          variant='ghost'
          className='text-[#0b43c3]'
          onClick={() => {
            navigate('/fineTuningLog');
          }}>
          {t('查看更多')} <ArrowRight className='ml-2 h-4 w-4' />
        </Button>
      </CardHeader>
      <CardContent className='px-4 pb-4'>
        {/* 使用 ECharts 组件替代 */}
        <ReactECharts
          option={getOption()}
          style={{ height: 280, width: '100%' }}
          notMerge={true}
          lazyUpdate={true}
        />
      </CardContent>
    </Card>
  );
}
