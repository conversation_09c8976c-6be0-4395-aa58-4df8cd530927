'use client';

import { Card, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { getOverviewData } from '../api/index';
import { useState, useEffect } from 'react';
import { route_info } from '@/router/index';

interface CardItem {
  name: string;
  count: number;
  path: string;
}

// 图标映射 - 根据卡片路径从route_info中获取对应的图标和颜色
const getCardIcon = (path: string, index: number) => {
  const iconColors = ['#20D486', '#4F7CFF', '#E879FF', '#FFB340'];
  // 低饱和度背景色 - 对应每个主题色的淡色版本
  const bgColors = ['#F0FBF4', '#F0F4FF', '#FDF2FF', '#FFF7E1'];

  // 从route_info中查找对应路径的图标
  const routeItem = route_info.find((item) => item.path === path);
  const routeIcon = routeItem?.icon;

  // 如果没有找到对应图标，使用模型管理的图标
  const modelSettingItem = route_info.find(
    (item) => item.path === '/modelSetting',
  );
  const fallbackIcon = modelSettingItem?.icon;

  return {
    iconColor: iconColors[index % iconColors.length],
    bgColor: bgColors[index % bgColors.length],
    icon: routeIcon || fallbackIcon || (
      // 如果连模型管理图标也没有，使用默认图标
      <svg
        width='20'
        height='20'
        viewBox='0 0 24 24'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'>
        <path
          d='M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z'
          fill='currentColor'
        />
      </svg>
    ),
  };
};

export default function DashboardCards() {
  const navigate = useNavigate();
  const [cards, setCards] = useState<CardItem[]>([]);

  const getOverviewDataFetch = async () => {
    const res: any = await getOverviewData();
    setCards(res.data.list);
  };

  useEffect(() => {
    getOverviewDataFetch();
  }, []);

  return (
    <div
      className='grid gap-6'
      style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))' }}>
      {cards.map((card, index) => {
        const { iconColor, bgColor, icon } = getCardIcon(card.path, index);

        return (
          <div
            key={index}
            className='cursor-pointer hover:shadow-md transition-shadow duration-200 relative'
            style={{ width: '240px', height: '116px' }}
            onClick={() => {
              navigate(card.path);
            }}>
            {/* 顶部突出的彩色矩形部分 - 在Card后面 */}
            <div
              className='absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
              style={{
                backgroundColor: bgColor,
                width: '221px',
                height: '32px',
                borderRadius: '14px 14px 0px 0px',
                zIndex: 1,
              }}></div>

            {/* 白色卡片主体 */}
            <Card className='bg-white border border-gray-200 h-full rounded-lg shadow-sm relative z-10'>
              <CardContent className='pt-6 pb-4 px-4 h-full'>
                <div className='flex items-center justify-between h-full'>
                  <div className='flex-1 min-w-0'>
                    <h3 className='text-base font-medium text-gray-600 mb-1'>
                      {card.name}
                    </h3>
                    <p className='text-2xl font-bold text-gray-900'>
                      {card.count}
                    </p>
                  </div>
                  <div
                    className='w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ml-2'
                    style={{ backgroundColor: `${iconColor}15` }}>
                    <div style={{ color: iconColor }}>{icon}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      })}
    </div>
  );
}
