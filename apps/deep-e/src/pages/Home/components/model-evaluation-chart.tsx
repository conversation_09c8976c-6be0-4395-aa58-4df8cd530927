'use client';

import { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts/core';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getProportionOfModelEval } from '../api';

export interface ModelChartProps {
  name: string;
  value: number;
}
export default function ModelEvaluationChart() {
  const chartRef = useRef<HTMLDivElement>(null);
  const [children, setChildren] = useState<ModelChartProps[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    getProportionOfModelEval().then((res) => {
      setChildren(res);
    });
  }, []);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    const option = {
      tooltip: {
        formatter: '{b}: {c}%',
      },
      series: [
        {
          type: 'treemap',
          roam: false,
          width: '90%',
          height: '85%',
          nodeClick: false,
          itemStyle: {
            borderRadius: 6,
          },
          data: [
            {
              name: t('模型评估'),
              children: children,
              itemStyle: {
                borderColor: 'transparent',
                color: 'transparent',
                opacity: 0,
              },
            },
          ],
          visualDimension: 0,
          levels: [
            {
              itemStyle: {
                borderWidth: 2,
                gapWidth: 2,
              },
            },
            {
              colorSaturation: [0.3, 0.6],
              itemStyle: {
                borderWidth: 3,
                gapWidth: 3,
              },
            },
          ],
          label: {
            show: true,
            position: 'insideTopLeft',
            formatter: '{b}\n{c}%',
            fontSize: 16,
          },
          color: ['#4F7CFF', '#20D486', '#E879FF', '#FFB340'],
        },
      ],
    };

    chart.setOption(option);
    return () => chart.dispose();
  }, [children]);

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>{t('模型评估分布')}</CardTitle>
        <Button
          variant='ghost'
          className='text-[#0b43c3]'
          onClick={() => navigate('/modelEvaluation')}>
          {t('查看更多')} <ArrowRight className='ml-2 h-4 w-4' />
        </Button>
      </CardHeader>
      <CardContent>
        <div ref={chartRef} className='w-full h-[300px]' />
      </CardContent>
    </Card>
  );
}
