'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import ReactECharts from 'echarts-for-react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getBaseModeProportionOfTrainedModel } from '../api/index';
import { useEffect, useState, useRef } from 'react';

export interface PieChartProps {
  name: string;
  percent: number;
  value: number;
}
export default function PieChartSection() {
  const [data, setData] = useState<PieChartProps[]>([]);
  const chartRef = useRef<any>(null);
  const navigate = useNavigate();

  useEffect(() => {
    getBaseModeProportionOfTrainedModel().then((res) => {
      setData(res);
    });
  }, []);

  const handleMouseEnter = (index: number) => {
    const chart = chartRef.current?.getEchartsInstance();
    if (chart) {
      chart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index,
      });
    }
  };

  const handleMouseLeave = (index: number) => {
    const chart = chartRef.current?.getEchartsInstance();
    if (chart) {
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index,
      });
    }
  };

  // 统一颜色配置
  const colors = [
    '#4F7CFF',
    '#20D486',
    '#E879FF',
    '#FFB340',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc',
  ];

  const options = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.name}:${params.percent}%`;
      },
    },
    legend: {
      show: false, // 隐藏默认legend，使用自定义legend
    },
    color: colors, // 为饼图设置颜色
    series: [
      {
        name: t('模型占比'),
        type: 'pie',
        center: ['30%', '50%'], // 添加此配置使饼图靠左
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 10,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        // 使用实际value值进行展示，并确保颜色对应
        data: data.map((item, index) => ({
          value: item.percent,
          name: item.name,
          itemStyle: {
            color: colors[index % colors.length],
          },
        })),
      },
    ],
  };

  return (
    <Card className='col-span-1'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>{t('基础模型占比')}</CardTitle>
        <Button
          variant='ghost'
          className='text-[#0b43c3]'
          onClick={() => navigate('/modelSetting/modelBase')}>
          {t('查看更多')} <ArrowRight className='ml-2 h-4 w-4' />
        </Button>
      </CardHeader>
      <CardContent>
        <div className='flex items-center'>
          <div className='flex-1 pl-8'>
            <ReactECharts
              ref={chartRef}
              option={options}
              style={{ height: 300 }}
              notMerge={true}
              lazyUpdate={true}
            />
          </div>
          <div className='flex-1 pl-2.5'>
            <div className='space-y-4'>
              {data.map((item, index) => {
                return (
                  <div
                    key={item.name}
                    className='flex items-center justify-between cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors'
                    onMouseEnter={() => handleMouseEnter(index)}
                    onMouseLeave={() => handleMouseLeave(index)}>
                    <div className='flex items-center space-x-2 flex-1 min-w-0'>
                      <div
                        className='w-3 h-3 rounded-full flex-shrink-0'
                        style={{
                          backgroundColor: colors[index % colors.length],
                        }}
                      />
                      <span
                        className='text-sm text-gray-600 truncate'
                        title={item.name}>
                        {item.name}
                      </span>
                    </div>
                    <div className='text-right flex-shrink-0 ml-2'>
                      <div className='text-sm font-medium'>{item.value}</div>
                      <div className='text-xs text-gray-500'>
                        {item.percent}%
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
