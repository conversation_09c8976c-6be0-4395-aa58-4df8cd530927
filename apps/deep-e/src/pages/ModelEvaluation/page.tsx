import {
  Table,
  Card,
  Button,
  Form,
  Input,
  Row,
  Col,
  Select,
  message,
} from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import {
  getEvalTaskList,
  getModelEvalTypeList,
  getModelEvalStateList,
} from './api';
import { getToken } from '@/store/features';
import { useState, useEffect } from 'react';
import type { TaskInfo } from './type';
import { PlusOutlined } from '@ant-design/icons';
import { useActivate } from 'react-activation';
import axios from 'axios';
import useDayFormat from '@/hooks/useDayFormat';
import dayjs from 'dayjs';
import usePageData from '@/hooks/usePageData';
import { removeUndefinedValues } from '@/utils';

async function downloadFile(taskUuid: string) {
  const token = getToken();
  try {
    const response = await axios.get(
      '/api/eval/pairEvalByLLM/results/download',
      {
        params: { taskUuid }, // 替换为实际的任务 UUID
        responseType: 'blob',
        headers: { 'X-Token': token },
      },
    );

    // 校验是否为文件类型响应
    if (response.headers['content-type'].includes('application/json')) {
      const errorText = await response.data.text();
      const errorData = JSON.parse(errorText);
      throw new Error(errorData.message || t('服务器返回错误'));
    }

    // 提取文件名
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'eval_report.csv';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(
        /filename\*?=(?:UTF-8''|")(.+?)(?:"|;|$)/i,
      );
      if (filenameMatch) filename = decodeURIComponent(filenameMatch[1]);
    }

    // 创建 Blob 并触发下载
    const blob = new Blob([response.data], {
      type: response.headers['content-type'],
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error(t('下载失败:'), error);
    alert(t('下载失败'));
  }
}

export default function ModelEvaluation() {
  const [form] = Form.useForm();
  const format = useDayFormat();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TaskInfo[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [modelEvalTypeList, setModelEvalTypeList] = useState<any[]>([]);
  const [modelEvalStateList, setModelEvalStateList] = useState<any[]>([]);
  const columns = [
    {
      title: t('创建人'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100,
    },
    {
      title: t('评估类型'),
      dataIndex: 'evalTypeName',
      key: 'evalTypeName',
      width: 150,
    },
    {
      title: t('模型A'),
      dataIndex: 'modelAName',
      key: 'modelAName',
      width: 160,
      render: (modelA: string) => {
        if (modelA === '') {
          return '---';
        }
        return <div className='w-[150px] text-ellipsis'>{modelA}</div>;
      },
    },
    {
      title: t('模型B'),
      dataIndex: 'modelBName',
      key: 'modelBName',
      render: (modelA: string) => {
        if (modelA === '') {
          return '---';
        }
        return modelA;
      },
    },
    {
      title: t('裁判模型'),
      dataIndex: 'judgeModelName',
      key: 'judgeModelName',
      render: (modelA: string) => {
        if (modelA === '') {
          return '---';
        }
        return modelA;
      },
    },
    {
      title: t('数据集'),
      dataIndex: 'datasetName',
      key: 'datasetName',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => {
        const colorMap: { [key: string]: string } = {
          success: 'tag-success',
          running: 'tag-info',
          failed: 'tag-danger',
        };

        return <div className={`${colorMap[status]}`}>{record.statusName}</div>;
      },
    },
    {
      title: t('开始时间'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: number) => dayjs(time * 1000).format(format),
    },
    {
      title: t('结束时间'),
      dataIndex: 'finishedAt',
      key: 'finishedAt',
      render: (time: number) =>
        time !== 0 ? dayjs(time * 1000).format(format) : '---',
    },
    {
      title: t('操作'),
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <div className='flex gap-2 flex-col items-center'>
          <Link to='/modelEvaluation/result' state={{ id: record.taskUuid }}>
            {t('查看')}
          </Link>
          <a
            onClick={async () => {
              await downloadFile(record.taskUuid);
            }}>
            {t('下载结果')}
          </a>
        </div>
      ),
    },
  ];

  const getModelEvalTypeListFetch = async () => {
    try {
      const res = await getModelEvalTypeList();
      if (!res.code) setModelEvalTypeList(res.data.list);
      else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getModelEvalStateListFetch = async () => {
    try {
      const res = await getModelEvalStateList();
      if (!res.code) setModelEvalStateList(res.data.list);
      else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    const values = await form.validateFields();
    try {
      const res = await getEvalTaskList({
        page: pageData.page,
        pageSize: pageData.pageSize,
        ...removeUndefinedValues(values),
      });
      if (res.code === 0) {
        setData(
          res.data.list.map((item) => ({
            ...item,
          })),
        );
        setPageData((prev) => ({ ...prev, total: res.data.total || 0 }));
      }
    } finally {
      setLoading(false);
    }
  };

  const onFinish = () => {
    setPageData({ ...pageData, page: 1 });
    fetchData();
  };

  useEffect(() => {
    fetchData();
    getModelEvalStateListFetch();
    getModelEvalTypeListFetch();
  }, [pageData.page, pageData.pageSize]);

  useActivate(() => {
    fetchData();
  });

  return (
    <div className='p-4 space-y-2'>
      <header>
        <Card>
          <Form layout='inline' form={form} onFinish={onFinish}>
            <Row gutter={[16, 8]}>
              <Col span={6}>
                <Form.Item label={t('模型A名称')} name='modelAName'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={t('模型B名称')} name='modelBName'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={t('裁判模型名称')} name='judgeModelName'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={t('数据集名称')} name='datasetName'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={t('评估类型')} name='evalType'>
                  <Select
                    mode='multiple'
                    maxTagCount={'responsive'}
                    options={modelEvalTypeList}
                    fieldNames={{ label: 'evalTypeName', value: 'evalType' }}
                    allowClear
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={t('评估状态')} name='evalState'>
                  <Select
                    mode='multiple'
                    fieldNames={{ label: 'evalStateName', value: 'evalState' }}
                    options={modelEvalStateList}
                    maxTagCount={'responsive'}
                    allowClear
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <div className='flex justify-start gap-2'>
                  <Button type='primary' htmlType='submit'>
                    {t('查询')}
                  </Button>
                  <Button htmlType='reset'>{t('重置')}</Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card>
      </header>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <span>{t('模型评估')}</span>
            <Button
              type='primary'
              onClick={() => navigate('/modelEvaluation/run')}>
              <PlusOutlined />
              {t('新增评估')}
            </Button>
          </div>
        }
        className='w-full h-full'>
        <Table
          scroll={{ x: true }}
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey='taskUuid'
          pagination={pagination}
        />
      </Card>
    </div>
  );
}
