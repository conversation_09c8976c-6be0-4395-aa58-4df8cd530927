import { Table, Checkbox, Dropdown, Button } from 'antd';
import type { TableProps } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { useState } from 'react';

interface ConfigTableProps<T> extends TableProps<T> {
  defaultVisibleColumns?: string[];
}

export const ConfigTable = <T extends object>({
  columns = [],
  defaultVisibleColumns,
  ...restProps
}: ConfigTableProps<T>) => {
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    defaultVisibleColumns || columns.map((col: any) => col.dataIndex),
  );

  const handleColumnVisibilityChange = (
    dataIndex: string,
    checked: boolean,
  ) => {
    if (checked) {
      setVisibleColumns([...visibleColumns, dataIndex]);
    } else {
      setVisibleColumns(visibleColumns.filter((col) => col !== dataIndex));
    }
  };

  const columnSelector = (
    <div
      style={{ padding: '8px' }}
      className='bg-white border rounded border-gray-300'>
      {columns.map((column: any) => (
        <div key={column.dataIndex} style={{ marginBottom: '8px' }}>
          <Checkbox
            checked={visibleColumns.includes(column.dataIndex)}
            onChange={(e) =>
              handleColumnVisibilityChange(column.dataIndex, e.target.checked)
            }>
            {column.title}
          </Checkbox>
        </div>
      ))}
    </div>
  );

  const filteredColumns = columns.filter((col: any) =>
    visibleColumns.includes(col.dataIndex),
  );

  return (
    <div>
      <div style={{ textAlign: 'right', marginBottom: '16px' }}>
        <Dropdown overlay={columnSelector} trigger={['click']}>
          <Button icon={<SettingOutlined />}>{t('列设置')}</Button>
        </Dropdown>
      </div>
      <Table {...restProps} columns={filteredColumns} />
    </div>
  );
};

export default ConfigTable;
