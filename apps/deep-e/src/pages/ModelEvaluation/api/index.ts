import request, { returnData } from '@/services';
import {
  ModelEvaluationConfig,
  TaskInfo,
  ModelResponse,
  EvalConfig,
} from '../type';

export const getDatasetListForSelect = () =>
  request.get('/api/dataset/getDatasetListForSelect');

export const getPromptVar = (datasetUuid: string, promptKeyword: string) =>
  request.get('/api/eval/pairEvalByLLM/getPromptVar', {
    params: { datasetUuid, promptKeyword },
  });

export const pairEvalByLLM = (data: ModelEvaluationConfig) => {
  return request.post('/api/eval/pairEvalByLLM', data);
};

export const getEvalTaskList = (data: {
  page: number;
  pageSize: number;
}): Promise<returnData<TaskInfo[]>> => {
  return request.get('/api/eval/getEvalTaskList', {
    params: data,
  });
};

export const getResult = (data: {
  taskUuid: string;
  page: number;
  pageSize: number;
}): Promise<returnData<ModelResponse[]>> => {
  return request.get('/api/eval/pairEvalByLLM/results/view', {
    params: data,
  });
};

export const downloadResult = (data: { taskUuid: string }) => {
  return request.get('/api/eval/pairEvalByLLM/results/download', {
    params: data,
    responseType: 'blob',
  });
};

//获取日志信息

export const getEvalTaskLog = (data: {
  taskUuid: string;
}): Promise<returnData<ModelResponse[]>> => {
  return request.get('/api/scpLog/getEvalTaskLog', {
    params: data,
  });
};

export const getEvalTaskConfig = (data: {
  taskUuid: string;
}): Promise<returnData<EvalConfig>> => {
  return request.get('/api/eval/pairEvalByLLM/results/getEvalTaskConfig', {
    params: data,
  });
};

export const getModelEvalTypeList = () => {
  return request.get('/api/eval/getModelEvalTypeList');
};

export const getModelEvalStateList = () => {
  return request.get('/api/eval/getModelEvalStateList');
};
