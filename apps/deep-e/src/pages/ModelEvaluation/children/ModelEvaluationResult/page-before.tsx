import { useEffect, useState } from 'react';
import { Card, TableColumnProps } from 'antd';
import { AIResponseDisplay } from '@/components';
import { useLocation } from 'react-router-dom';
import { getResult } from '../../api';
import { ConfigTable } from '../../components/ConfigTable';

export default function ModelEvaluationResult() {
  const location = useLocation();
  const { id } = location.state || {};
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchData = async (page: number, pageSize: number) => {
    setLoading(true);
    try {
      const res = await getResult({
        taskUuid: id,
        page,
        pageSize,
      });
      setData(res.data.list || []);
      setPagination((prev) => ({
        ...prev,
        total: res.data.total || 0,
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
    fetchData(pagination.current, pagination.pageSize);
  };

  const handleRowClick = (record: any) => {
    setExpandedRowKeys((prev) =>
      prev.includes(String(record.taskUuid))
        ? prev.filter((key) => key !== String(record.taskUuid))
        : [...prev, String(record.taskUuid)],
    );
  };

  const allColumns: TableColumnProps[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_: any, __: any, index: number) =>
        index + 1 + (pagination.current - 1) * pagination.pageSize,
    },
    {
      title: 'CandidateSystemPrompt',
      dataIndex: 'candidateSystemPrompt',
      key: 'candidateSystemPrompt',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'CandidateUserPrompt',
      dataIndex: 'candidateUserPrompt',
      key: 'candidateUserPrompt',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'ResponseA',
      dataIndex: 'responseA',
      key: 'responseA',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'ResponseB',
      dataIndex: 'responseB',
      key: 'responseB',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'JudgeSystemPrompt',
      dataIndex: 'judgeSystemPrompt',
      key: 'judgeSystemPrompt',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'JudgeUserPrompt',
      dataIndex: 'judgeUserPrompt',
      key: 'judgeUserPrompt',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
    {
      title: 'judgeResponse',
      dataIndex: 'judgeResponse',
      key: 'judgeResponse',
      width: 300,
      align: 'left',
      render: (value: any, record: any) => {
        const isExpanded = expandedRowKeys.includes(record.taskUuid);
        return (
          <div
            className={`${
              isExpanded ? 'max-h-none' : 'max-h-[300px]'
            } overflow-hidden transition-all duration-200 pt-4`}>
            <AIResponseDisplay response={value} />
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    fetchData(pagination.current, pagination.pageSize);
  }, []);

  return (
    <div className='p-4'>
      <Card title='模型评估结果' className='w-full h-full'>
        <div className='overflow-x-auto'>
          <ConfigTable
            columns={allColumns}
            dataSource={data}
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 2400 }}
            onRow={(record) => ({
              onClick: () => handleRowClick(record),
              style: { cursor: 'pointer' },
            })}
          />
        </div>
      </Card>
    </div>
  );
}
