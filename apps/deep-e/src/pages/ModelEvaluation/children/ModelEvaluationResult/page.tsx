import React from 'react';
import { Button, TabsProps, Tabs } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import ResultDetail from './components/ResultDetail';
import ResultLogs from './components/ResultLogs';
import { ModelComparisonDashboard } from './components/ResultContent/ResultContent';

const ModelEvaluationResult: React.FC = () => {
  const navigate = useNavigate();
  const items: TabsProps['items'] = [
    {
      label: t('参数详情'),
      key: '1',
      children: <ResultDetail />,
    },
    {
      label: t('日志内容'),
      key: '2',
      children: <ResultLogs />,
    },
    {
      label: t('结果'),
      key: '3',
      children: <ModelComparisonDashboard />,
    },
  ];
  return (
    <div className='p-4'>
      {/* 头部 */}
      <Button
        type='link'
        className='w-12'
        icon={<ArrowLeftOutlined />}
        onClick={() => {
          navigate('/modelEvaluation');
        }}>
        {t('返回')}
      </Button>
      <div className='w-full bg-white border border-gray-300 rounded-2xl'>
        <div className=' p-4'>
          <div className='text-2xl font-bold '>{t('模型评估日志')}</div>
          <header className=' mt-4 w-full'>
            <Tabs defaultActiveKey='1' items={items} />
          </header>
        </div>
      </div>
    </div>
  );
};

export default ModelEvaluationResult;
