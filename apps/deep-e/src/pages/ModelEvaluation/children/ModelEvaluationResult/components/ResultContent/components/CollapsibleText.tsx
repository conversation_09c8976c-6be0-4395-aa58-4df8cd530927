'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { AIResponseDisplay } from '@/components';

interface CollapsibleTextProps {
  text: string;
  maxLength: number;
}

export function CollapsibleText({ text, maxLength }: CollapsibleTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLong, setIsLong] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (text !== null && text !== undefined && text.length)
      setIsLong(text.length > maxLength);
  }, [text, maxLength]);

  if (text === null) {
    return <div>{t('无')}</div>;
  }

  const displayText = isExpanded
    ? text
    : text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;

  return (
    <div className='relative'>
      <div
        ref={contentRef}
        className={`text-sm ${isExpanded ? '' : 'max-h-[300px] overflow-hidden'}`}>
        {/* {displayText} */}
        <AIResponseDisplay response={displayText} />
      </div>

      {isLong && (
        <div
          className={`flex justify-center mt-2 ${isExpanded ? '' : 'bg-gradient-to-t from-background to-transparent pt-6 -mt-6 relative'}`}>
          <Button
            variant='outline'
            size='sm'
            onClick={() => setIsExpanded(!isExpanded)}
            className='flex items-center gap-1'>
            {isExpanded ? (
              <>
                <ChevronUp className='h-4 w-4' />
                <span>{t('收起')}</span>
              </>
            ) : (
              <>
                <ChevronDown className='h-4 w-4' />
                <span>
                  {t('展开全部')} ({text.length} {t('字符')})
                </span>
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
