import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ComparisonList } from './components/CompareList';
import { ComparisonDetail } from './components/CompareDetail';
import { Search, Filter, SortDesc } from 'lucide-react';
import { getResult } from '../../../../api';
import { ModelResponse } from '../../../../type';
import { useLocation } from 'react-router-dom';
import { Pagination } from 'antd';

export function ModelComparisonDashboard() {
  const location = useLocation();
  const { id } = location.state;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLength, setSelectedLength] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [selectedComparison, setSelectedComparison] = useState<ModelResponse>();
  // sampleComparisons[0],
  const [currentPageData, setCurrentPageData] = useState<ModelResponse[]>([]);

  const [pageData, setPageData] = useState({
    page: 1,
    pageSize: 10,
    total: 1,
  });

  const getResultFetch = async () => {
    try {
      const res = await getResult({
        taskUuid: id,
        page: pageData.page,
        pageSize: pageData.pageSize,
      });
      if (!res.code) {
        console.log(res.data.list);
        setCurrentPageData(res.data.list || []);
        setPageData((prev) => {
          return {
            ...prev,
            total: res.data.total || 0,
          };
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getResultFetch();
  }, [pageData.page]);

  return (
    <div className='flex h-full'>
      <div className='w-1/3 border-r flex flex-col h-full'>
        <div className='p-4 border-b'>
          <div className='flex gap-2 mb-4'>
            <div className='relative flex-1'>
              <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder={t('搜索比较...')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='pl-8'
              />
            </div>
            <Button variant='outline' size='icon'>
              <Filter className='h-4 w-4' />
            </Button>
            <Button variant='outline' size='icon'>
              <SortDesc className='h-4 w-4' />
            </Button>
          </div>

          <div className='flex gap-2'>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}>
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder={t('分类')} />
              </SelectTrigger>
              <SelectContent></SelectContent>
            </Select>

            <Select value={selectedLength} onValueChange={setSelectedLength}>
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder={t('长度')} />
              </SelectTrigger>
              <SelectContent>
                {/* <SelectItem value='all'>{t('所有长度')}</SelectItem>
                <SelectItem value='short'>{t('短文本')}</SelectItem>
                <SelectItem value='medium'>{t('中等文本')}</SelectItem>
                <SelectItem value='long'>{t('长文本')}</SelectItem> */}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder={t('排序')} />
              </SelectTrigger>
              <SelectContent>
                {/* <SelectItem value='newest'>{t('最新')}</SelectItem>
                <SelectItem value='oldest'>{t('最早')}</SelectItem>
                <SelectItem value='scoreDesc'>{t('评分降序')}</SelectItem> */}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className='flex-1 overflow-auto'>
          <ComparisonList
            comparisons={currentPageData}
            selectedId={selectedComparison?.itemUuid || ''}
            onSelect={setSelectedComparison}
          />
        </div>

        <div className='p-3 border-t flex justify-end w-full'>
          <Pagination
            responsive={true}
            showSizeChanger={true}
            current={pageData.page}
            pageSize={pageData.pageSize}
            total={pageData.total}
            onChange={(page, pageSize) => {
              setPageData({ ...pageData, page, pageSize });
            }}
            onShowSizeChange={(current, size) => {
              setPageData({ ...pageData, page: current, pageSize: size });
            }}
          />
        </div>
      </div>

      <div className='flex-1 overflow-auto'>
        {selectedComparison && (
          <ComparisonDetail comparison={selectedComparison} />
        )}
      </div>
    </div>
  );
}
