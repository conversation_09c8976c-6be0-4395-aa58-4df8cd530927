// import { Badge } from '@/components/ui/badge';
// import { formatDistanceToNow } from '../utils';
import { ModelResponse } from '../../../../../type';
import * as _ from 'lodash';

interface ComparisonListProps {
  comparisons: ModelResponse[];
  selectedId: string;
  onSelect: (comparison: any) => void;
}

export function ComparisonList({
  comparisons,
  selectedId,
  onSelect,
}: ComparisonListProps) {
  if (comparisons.length === 0) {
    return (
      <div className='flex items-center justify-center h-full p-4 text-center text-muted-foreground'>
        {t('没有找到匹配的比较结果')}
      </div>
    );
  }

  return (
    <div className='divide-y'>
      {comparisons.map((comparison: ModelResponse, index: number) => (
        <div
          key={comparison.taskUuid + index}
          className={`p-4 cursor-pointer hover:bg-muted/50 ${selectedId === comparison.itemUuid ? 'bg-muted' : ''}`}
          onClick={() => onSelect(comparison)}>
          <div className='flex justify-between items-start mb-1'>
            <h3 className='font-medium'>
              {t('回答')}
              {index + 1}
            </h3>
            {/* {!_.isNil(comparison.judgeResponse) && (
              <Badge
                variant={
                  comparison.judgeResponse.better === 'A'
                    ? 'default'
                    : 'secondary'
                }>
                {comparison.judgeResponse.better === 'A'
                  ? t('模型A胜出')
                  : comparison.judgeResponse.better === 'B'
                    ? t('模型B胜出')
                    : t('平局')}
              </Badge>
            )} */}
          </div>

          {/* <div className='text-sm text-muted-foreground mb-2 flex items-center gap-2'>
            <span>{formatDistanceToNow(new Date(comparison.timestamp))}</span>
            <span>•</span>
            <span>{comparison.category}</span>
          </div> */}

          <p className='text-sm line-clamp-2 text-muted-foreground'>
            {comparison.candidateUserPrompt}
          </p>

          <div className='flex gap-2 mt-2'>
            {/* <Badge variant='outline'>
              {comparison.inputLength === 'short'
                ? t('短文本')
                : comparison.inputLength === 'medium'
                  ? t('中等文本')
                  : t('长文本')}
            </Badge> */}
            {/* <div className='flex items-center gap-1'>
              <span className='text-xs font-medium'>{t('模型A')}:</span>
              <div className='flex'>
                {Array.from({ length: 5 }).map((_, i) => (
                  <span
                    key={i}
                    className={`text-xs ${i < comparison.modelA.score ? 'text-yellow-500' : 'text-gray-300'}`}>
                    ★
                  </span>
                ))}
              </div>
            </div>
            <div className='flex items-center gap-1'>
              <span className='text-xs font-medium'>{t('模型B')}:</span>
              <div className='flex'>
                {Array.from({ length: 5 }).map((_, i) => (
                  <span
                    key={i}
                    className={`text-xs ${i < comparison.modelB.score ? 'text-yellow-500' : 'text-gray-300'}`}>
                    ★
                  </span>
                ))}
              </div>
            </div> */}
          </div>
        </div>
      ))}
    </div>
  );
}
