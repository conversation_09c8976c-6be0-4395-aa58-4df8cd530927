import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import useDayFormat from '@/hooks/useDayFormat';
import { getEvalTaskLog } from '../../../api';
import LogViewer from '@/components/ui/log-viewer';

export default function ResultLogs() {
  const format = useDayFormat();
  const location = useLocation();
  const { id } = location.state;

  const [logs, setLogs] = useState<any[]>([]);

  const getEvalTaskLogFetch = async () => {
    try {
      const res = await getEvalTaskLog({
        taskUuid: id,
      });
      if (!res.code) {
        setLogs(res.data.list);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getEvalTaskLogFetch();
  }, []);

  return (
    <div className='flex flex-col h-full w-full'>
      <div className='flex-1 overflow-auto p-4 font-mono text-sm'>
        {logs.length === 0 ? (
          <div className='flex items-center justify-center h-full text-muted-foreground'>
            {t('没有匹配的日志记录')}
          </div>
        ) : (
          <div className='space-y-2'>
            <LogViewer logList={logs} format={format} />
          </div>
        )}
      </div>
    </div>
  );
}
