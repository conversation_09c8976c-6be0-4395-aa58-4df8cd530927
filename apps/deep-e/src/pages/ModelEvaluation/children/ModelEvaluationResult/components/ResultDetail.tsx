import { useEffect, useState } from 'react';
import { getEvalTaskConfig } from '../../../api';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EvalConfig } from '../../../type';
import { z } from 'zod';

const DescriptionsItmeScheme = z.object({
  label: z.string(),
  value: z.union([z.string(), z.number()]),
});

type DescriptionsItmeProps = z.infer<typeof DescriptionsItmeScheme>;
export default function ResultDetail() {
  const location = useLocation();
  const navigate = useNavigate();
  const { id } = location.state;

  const [detail, setDetail] = useState<EvalConfig>();
  const getEvalTaskConfigFetch = async () => {
    try {
      const res = await getEvalTaskConfig({
        taskUuid: id,
      });
      if (!res.code) {
        setDetail(res.data.result);
      }
    } catch (error) {
      console.error('Failed to fetch detail:', error);
    }
  };
  const getRenderData = (
    detail: EvalConfig,
    modelname: 'modelAConfig' | 'modelBConfig' | 'judgeModelConfig',
  ) => {
    return [
      {
        label: t('名字：'),
        value: detail[modelname].modelName || '',
      },
      {
        label: t('知识库：'),
        value: detail[modelname].knowledgeBaseName || t('无'),
      },
      {
        label: 'maxTokens：',
        value: detail[modelname].maxTokens || 0,
      },
      {
        label: 'temperature：',
        value: detail[modelname].temperature || 0,
      },
      {
        label: 'topK：',
        value: detail[modelname].topK || 0,
      },
      {
        label: 'topP：',
        value: detail[modelname].topP || 0,
      },
    ];
  };

  const renderModelCardPrams = [
    {
      title: t('模型A'),
      content:
        detail &&
        getRenderData(detail, 'modelAConfig').map((item) => {
          return <DescriptionsItem key={item.label} {...item} />;
        }),
    },
    {
      title: t('模型B'),
      isRender: detail?.evalType === 0 || detail?.evalType === 1,
      content:
        detail &&
        getRenderData(detail, 'modelBConfig').map((item) => {
          return <DescriptionsItem key={item.label} {...item} />;
        }),
    },
    {
      title: t('裁判模型'),
      isRender: detail?.evalType === 0 || detail?.evalType === 3,
      content:
        detail &&
        getRenderData(detail, 'judgeModelConfig').map((item) => {
          return <DescriptionsItem key={item.label} {...item} />;
        }),
    },
  ];

  useEffect(() => {
    getEvalTaskConfigFetch();
  }, []);

  const getEvalType = (status: number): string => {
    const EvalObj: { [key: number]: string } = {
      0: t('裁判模型'),
      1: t('对比模式'),
      3: t('评估模式'),
      2: t('单模型模式'),
    };
    return EvalObj[status];
  };

  return (
    <div className='space-y-2 px-2'>
      <div className='flex gap-4 my-4'>
        <div className='text-lg font-bold'>
          {t('评估模式：')}
          <span className='text-gray-500 text-base font-medium'>
            {getEvalType(detail?.evalType || 0)}
          </span>
        </div>

        {/* 数据集信息 */}
        <div className='text-lg font-bold'>
          {t('数据集信息：')}
          <span className='text-gray-500 text-base font-medium'>
            <Link to={`/datasets/details?datasetUuid=${detail?.datasetUuid}`}>
              {detail?.datasetName}
            </Link>
          </span>
        </div>

        <div className='text-lg font-bold'>
          {t('评估总条数：')}
          <span className='text-gray-500 text-base font-medium'>
            {detail?.datasetItemNum === 0 ? t('全部') : detail?.datasetItemNum}
          </span>
        </div>

        <Button
          onClick={() => {
            navigate('/modelEvaluation/run', {
              state: {
                id,
              },
            });
          }}>
          {t('复制参数')}
        </Button>
      </div>

      <div className='space-y-2 flex gap-2'>
        {renderModelCardPrams.map((item) => {
          return <RenderModelCard key={item.title} {...item} />;
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className='text-lg font-bold'>{t('模型提示词')}</CardTitle>
        </CardHeader>
        <CardContent className='flex gap-2 w-full'>
          <Card className='w-full'>
            <CardHeader>
              <CardTitle>{t('系统提示词')}</CardTitle>
            </CardHeader>
            <CardContent> {detail?.candidateSystemPrompt}</CardContent>
          </Card>
          <Card className='w-full'>
            <CardHeader>
              <CardTitle>{t('用户提示词')}</CardTitle>
            </CardHeader>
            <CardContent> {detail?.candidateUserPrompt}</CardContent>
          </Card>
        </CardContent>
      </Card>

      {detail?.evalType === 0 || detail?.evalType === 3 ? (
        <Card className='mt-4'>
          <CardHeader>
            <CardTitle className='text-lg font-bold'>
              {t('裁判模型提示词')}
            </CardTitle>
          </CardHeader>
          <CardContent className='flex gap-2 w-full'>
            <Card className='w-full'>
              <CardHeader>
                <CardTitle>{t('裁判系统提示词')}</CardTitle>
              </CardHeader>
              <CardContent> {detail?.judgeSystemPrompt}</CardContent>
            </Card>
            <Card className='w-full'>
              <CardHeader>
                <CardTitle>{t('裁判用户提示词')}</CardTitle>
              </CardHeader>
              <CardContent> {detail?.judgeUserPrompt}</CardContent>
            </Card>
          </CardContent>
        </Card>
      ) : null}
    </div>
  );
}

interface RenderModelCardProps {
  title: string;
  content: any;
  isRender?: boolean;
}

const RenderModelCard: React.FC<RenderModelCardProps> = ({
  title,
  content,
  isRender = true,
}) => {
  if (!isRender) return null;
  return (
    <Card className='rounded-2xl border border-gray-300 container max-w-[400px] '>
      <CardHeader className='text-lg font-bold text-black '>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className='space-y-2'>{content}</CardContent>
    </Card>
  );
};

const DescriptionsItem: React.FC<DescriptionsItmeProps> = ({
  label,
  value,
}) => {
  return (
    <div>
      <span className='text-base font-bold'>{label}</span>
      <span className='text-base font-medium text-gray-500'>{value}</span>
    </div>
  );
};
