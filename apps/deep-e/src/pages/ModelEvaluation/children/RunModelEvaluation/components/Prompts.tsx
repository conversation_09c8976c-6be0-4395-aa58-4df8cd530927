'use client';

import { Input, Select, Button, Form } from 'antd';
import { useState, useRef } from 'react';
import KeepAlive, { AliveScope } from 'react-activation';

const { TextArea } = Input;

interface PromptEditorProps {
  status: string;
  paramsOptions: {
    cup: { label: string; value: string }[];
    jsp: { label: string; value: string }[];
    jup: { label: string; value: string }[];
  };
}

export function PromptEditor({ status, paramsOptions }: PromptEditorProps) {
  const [systemPrompt, setSystemPrompt] = useState('');

  return (
    <AliveScope>
      <div className=' flex flex-col gap-4'>
        <div className='border border-gray-300 rounded-lg p-8 bg-white space-y-6 mt-4'>
          <div>
            <div className='text-2xl font-bold '>{t('模型提示词')}</div>
            <div className='text-gray-500 mt-2'>
              {t('配置模型A和模型B的提示词')}
            </div>
          </div>

          <div className='space-y-6'>
            <div className='space-y-2'>
              <div className='text-sm font-medium'>{t('系统提示词')}</div>
              <Form.Item
                name='candidateSystemPrompt'
                initialValue={
                  'You are a professional AI assistant. For each question provided by the user, you should give accurate and expert answers.'
                }>
                <TextArea
                  placeholder={t('输入系统提示词...')}
                  className='min-h-[100px]'
                  value={systemPrompt}
                  rows={5}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                />
              </Form.Item>
              <p className='text-xs text-gray-500 mt-2'>
                {t('系统提示词用于设置模型的行为和角色')}
              </p>
            </div>

            <div className='space-y-2'>
              <Form.Item name='candidateUserPrompt' initialValue={'{{input}}'}>
                <CustomTextArea
                  tip={t('用户提示词是您想要模型回答的实际问题或任务')}
                  title={t('用户提示词')}
                  options={paramsOptions.cup}
                />
              </Form.Item>
            </div>
          </div>
        </div>

        {(status === 'referee' || status === 'evaluate') && (
          <KeepAlive>
            <div className='border border-gray-300 rounded-lg p-8 bg-white'>
              <div className='space-y-6'>
                <div>
                  <div className='text-2xl font-bold'>{t('模型提示词')}</div>
                  <div className='text-gray-500 mt-2'>
                    {t('配置模型A和模型B的提示词')}
                  </div>
                </div>
                <div className='space-y-2'>
                  <Form.Item
                    name='judgeSystemPrompt'
                    initialValue={`You are a professional evaluator tasked with comparing the responses of two large language models and determining which one is better. Please assess based on accuracy, logic, clarity of expression, and completeness.Make your judgment solely based on the provided responses, without relying on any external knowledge.You must return the evaluation result in the following JSON format: {"better": "A",  // or "B","reason": "Briefly explain why you chose this model's response, in no more than 3 sentences."}`}>
                    <CustomTextArea
                      title={t('裁判系统提示词')}
                      tip={t('设置裁判模型的行为和评估标准')}
                      options={paramsOptions.jsp}
                    />
                  </Form.Item>
                </div>

                <div className='space-y-2'>
                  <Form.Item
                    name='judgeUserPrompt'
                    initialValue={`Here is the question along with the responses from two models. Please determine which model's response is better and briefly explain the reason.
Question: {{CandidateUserPrompt}}
Response from Model A:
{{ResponseA}}
Response from Model B:
{{ResponseB}}`}>
                    <CustomTextArea
                      title={t('裁判用户提示词')}
                      tip={t('指导裁判模型如何评估模型的回答')}
                      options={paramsOptions.jup}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
          </KeepAlive>
        )}
      </div>
    </AliveScope>
  );
}

type CustomTextAreaType = {
  onChange?: any;
  value?: string;
  tip?: string;
  title?: string;
  options: { value: any; label: string }[];
  [key: string]: any;
};

const CustomTextArea: React.FC<CustomTextAreaType> = ({
  tip,
  title,
  value,
  onChange,
  options,
}) => {
  const [selectedText, setSelectedText] = useState<string>(t('AI文本'));
  const textAreaRef = useRef<any>(null);

  const handleInsertText = () => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = value || '';
    const newValue = `${currentValue.slice(0, start)}${selectedText}${currentValue.slice(end)}`;
    onChange?.(newValue);

    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + selectedText.length,
        start + selectedText.length,
      );
    }, 0);
  };

  return (
    <div className='space-y-2'>
      <div className='text-sm font-medium'>{title}</div>
      <div className='flex gap-2 mb-2'>
        <Select
          placeholder={t('请选择')}
          className='w-[200px]'
          options={options}
          onChange={(value) => {
            setSelectedText(`{{${value}}}`);
          }}
        />
        <Button onClick={handleInsertText} style={{ height: '30px' }}>
          {t('添加')}
        </Button>
      </div>
      <TextArea
        ref={textAreaRef}
        value={value}
        rows={5}
        onChange={(value) => {
          onChange?.(value.target.value);
        }}
      />
      <p className='text-xs text-gray-500 mt-2'>{tip}</p>
    </div>
  );
};
