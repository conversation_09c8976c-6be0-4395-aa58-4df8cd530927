import { Form, Select } from 'antd';
import { useEffect, useState } from 'react';
import { ModelList } from '@/pages/PromptWords/type';
import { getKnowledgeBaseListForSelect } from '@/pages/PromptWords/api';

interface ModelInputProps {
  status: 'referee' | 'compare' | 'evaluate' | 'single';
  modelList: ModelList[];
}

export default function ModelInput({ status, modelList }: ModelInputProps) {
  const [knowledgeBaseList, setKnowledgeBaseList] = useState<any[]>([]);

  const getKnowledgeBaseListForSelectFetch = async () => {
    try {
      const res = await getKnowledgeBaseListForSelect();
      if (!res.code) {
        setKnowledgeBaseList(
          res.data.list.map((item: { baseName: string; baseUuid: string }) => ({
            label: item.baseName,
            value: item.baseUuid,
          })),
        );
      }
    } catch (error) {
      console.error('Error fetching knowledge base list:', error);
    }
  };

  useEffect(() => {
    getKnowledgeBaseListForSelectFetch();
  }, []);

  return (
    <div className='bg-white p-4 rounded-lg'>
      <div className='text-2xl font-bold mb-2'>
        <span className='text-red-500 mr-1 text-sm'>*</span>
        {t('模型选择')}
      </div>
      <div className='text-gray-500 mb-6'>{t('选择要评估的模型和知识库')}</div>

      <div className='grid grid-cols-3 gap-6'>
        {/* 模型 A - 始终显示，在 evaluate 模式下占据所有列 */}
        <div className={`space-y-4 `}>
          <Form.Item
            label={t('模型 A')}
            className='mb-4'
            name='modelA_base'
            rules={[{ required: true, message: t('请选择模型 A') }]}>
            <Select
              placeholder={t('选择模型 A')}
              options={modelList.map((item) => ({
                title: item.groupName,
                label: <span>{item.groupName}</span>,
                options: item.modelInfoList.map((li, index) => {
                  return {
                    label: li.modelName,
                    value: item.groupName + '$' + index,
                  };
                }),
              }))}
            />
          </Form.Item>
          <Form.Item
            label={t('模型 A 知识库')}
            className='mb-4'
            name='modelA_baseUuid'>
            <Select placeholder={t('无知识库')} options={knowledgeBaseList} />
          </Form.Item>
        </div>

        {/* 模型 B - 在 compare 和 referee 模式显示，在 compare 模式下占据剩余列 */}
        {(status === 'compare' || status === 'referee') && (
          <div className={`space-y-4 `}>
            <Form.Item
              label={t('模型 B')}
              className='mb-4'
              name='modelB_base'
              rules={[{ required: true, message: t('请选择模型 B') }]}>
              <Select
                placeholder={t('选择模型 B')}
                options={modelList.map((item) => ({
                  title: item.groupName,
                  label: <span>{item.groupName}</span>,
                  options: item.modelInfoList.map((li, index) => {
                    return {
                      label: li.modelName,
                      value: item.groupName + '$' + index,
                    };
                  }),
                }))}
              />
            </Form.Item>
            <Form.Item
              label={t('模型 B 知识库')}
              className='mb-4'
              name='modelB_baseUuid'>
              <Select placeholder={t('无知识库')} options={knowledgeBaseList} />
            </Form.Item>
          </div>
        )}

        {/* 裁判模型 - 仅在 referee 模式显示 */}
        {(status === 'referee' || status === 'evaluate') && (
          <div className='space-y-4'>
            <Form.Item
              label={t('裁判模型')}
              className='mb-4'
              name='judgeModel_base'
              rules={[
                {
                  required: true,
                  message: t('请选择裁判模型'),
                },
              ]}>
              <Select
                placeholder={t('选择裁判模型')}
                options={modelList.map((item) => ({
                  title: item.groupName,
                  label: <span>{item.groupName}</span>,
                  options: item.modelInfoList.map((li, index) => {
                    return {
                      label: li.modelName,
                      value: item.groupName + '$' + index,
                    };
                  }),
                }))}
              />
            </Form.Item>
            <Form.Item
              label={t('裁判模型知识库')}
              className='mb-4'
              name='judgeModel_baseUuid'>
              <Select placeholder={t('无知识库')} options={knowledgeBaseList} />
            </Form.Item>
          </div>
        )}
      </div>
    </div>
  );
}
