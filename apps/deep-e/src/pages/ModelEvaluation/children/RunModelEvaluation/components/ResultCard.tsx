import React from 'react';
import { Divider } from 'antd';
import { FormOutlined } from '@ant-design/icons';

export interface EvaluationResult {
  id: number;
  description: string;
  modelA: string;
  modelB: string;
  evaluation: {
    winner: string;
    reasoning: string;
    scores: {
      modelA: string;
      modelB: string;
    };
  } | null;
}

interface ResultCardProps {
  // result: EvaluationResult;
  evaluationMode: string;
  modelA: string;
  modelB: string;
  tableSourceMap: Map<number, Map<string, string>>;
}

export function ResultCard({
  // result,
  evaluationMode,
  modelA,
  modelB,
  tableSourceMap,
}: ResultCardProps) {
  console.log(
    'result',
    Array.from(tableSourceMap.entries()).map(([key, valueMap]) => ({
      key,
      ...Object.fromEntries(valueMap),
    })),
  );
  return (
    <div className='mb-4 flex flex-col gap-4'>
      <div className='mb-4 text-2xl font-bold '>{t('预览结果')}</div>
      {Array.from(tableSourceMap.entries())
        .map(([key, valueMap]) => ({
          key,
          ...Object.fromEntries(valueMap),
        }))
        .map((item: any, index) => {
          return (
            <div key={index} className='p-4 border border-gray-300 rounded-xl'>
              <div className='text-lg mb-2 font-bold'>
                {(index + 1).toString() + '、'}
                {item?.candidateUserPrompt || ''}
              </div>
              {evaluationMode === 'referee' || evaluationMode === 'compare' ? (
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '16px',
                  }}>
                  <div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        marginBottom: '8px',
                      }}>
                      <FormOutlined />
                      <span style={{ fontWeight: 500 }}>
                        {t('模型 A')} ({modelA})
                      </span>
                    </div>
                    <div
                      style={{
                        border: '1px solid #d9d9d9',
                        borderRadius: '8px',
                        padding: '16px',
                        whiteSpace: 'pre-wrap',
                      }}>
                      {item?.responseA || ''}
                    </div>
                  </div>
                  <div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        marginBottom: '8px',
                      }}>
                      <FormOutlined />
                      <span style={{ fontWeight: 500 }}>
                        {t('模型 B')} ({modelB})
                      </span>
                    </div>
                    <div
                      style={{
                        border: '1px solid #d9d9d9',
                        borderRadius: '8px',
                        padding: '16px',
                        whiteSpace: 'pre-wrap',
                      }}>
                      {item?.responseB || ''}
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      marginBottom: '8px',
                    }}>
                    <FormOutlined />
                    <span style={{ fontWeight: 500 }}>
                      {t('模型 A')} ({modelA})
                    </span>
                  </div>
                  <div
                    style={{
                      border: '1px solid #d9d9d9',
                      borderRadius: '8px',
                      padding: '16px',
                      whiteSpace: 'pre-wrap',
                    }}>
                    {/* {result.modelA} */}
                    {item?.responseA || ''}
                  </div>
                </div>
              )}

              {(evaluationMode === 'referee' ||
                evaluationMode === 'evaluate') && (
                <>
                  <Divider />
                  <h4 style={{ fontWeight: 500, marginBottom: '16px' }}>
                    {t('评估结果:')}
                  </h4>
                  <div
                    style={{
                      border: '1px solid #d9d9d9',
                      borderRadius: '8px',
                      padding: '16px',
                    }}>
                    <p style={{ margin: 0 }}>
                      {/* {result.evaluation.reasoning} */}
                      {item?.judgeResponse || ''}
                    </p>
                  </div>
                </>
              )}
            </div>
          );
        })}
    </div>
  );
}
