import { FormField } from '@repo/ui';

export const evaluateFields: FormField[] = [
  {
    field: 'modelA',
    label: t('模型1'),
    type: 'select',
  },
  {
    field: 'modelAName',
    label: t('模型1-模型名称'),
    type: 'text',
  },
  {
    field: 'modelB',
    label: t('模型2'),
    type: 'select',
  },
  {
    field: 'modelBName',
    label: t('模型2-模型名称'),
    type: 'text',
  },
];

export const judgeFields: FormField[] = [
  {
    field: 'judgeModel',
    label: t('裁判模型'),
    type: 'select',
  },
  {
    field: 'judgeModelName',
    label: t('模型名称'),
    type: 'text',
  },
];
