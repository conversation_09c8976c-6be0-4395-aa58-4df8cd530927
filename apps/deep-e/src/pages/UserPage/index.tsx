import React from 'react';
import { memo, useState, useEffect } from 'react';
import { useAuthStore } from '@/store/features/auth';
import { useNavigate } from 'react-router-dom';
import UserSettingModal from './UserSettingModal';
import ShowButtonModal from './ShowButtonModal';
import UploadAvatar from '@/components/UploadAvatar';
import { Button } from 'antd';
import LeftArrow from '@/assets/LeftArrow.svg';
import { useVoerkaI18n } from '@voerkai18n/react';

const UserPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const token = useSelector((state: any) => state.login.token);
  // const user = useSelector((state: any) => state.login.user);
  const { token, user } = useAuthStore();
  const [avatar, setAvatar] = useState<string | undefined>(user?.headerImg);
  const navigate = useNavigate();
  const { t } = useVoerkaI18n();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleUpload = async (imageUrl: string): Promise<boolean> => {
    let flag = false;
    if (imageUrl) {
      const fetchBlob = async () => {
        try {
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          const file = new File([blob], 'avatar.png', { type: 'image/png' });
          const formData = new FormData();
          formData.append('photo', file);

          const res = await fetch('/api/file/uploadUserProfilePhoto', {
            method: 'POST',
            body: formData,
            headers: {
              'X-Token': token,
            } as any,
          }).then((res) => res.json());

          if (res.code === 0) {
            flag = true;
          } else {
            console.error(t('上传失败'));
          }
        } catch (error) {
          console.error(t('错误：'), error);
        }
      };
      await fetchBlob();
    }
    return new Promise((resolve) => {
      resolve(flag);
    });
  };

  const [userSettingModalVisible, setUserSettingModalVisible] =
    useState<boolean>(false);
  const [showButtonModalVisible, setShowButtonModalVisible] =
    useState<boolean>(false);

  const openUserSettingModal = () => {
    setUserSettingModalVisible(true);
  };
  const openShowButtonModal = () => {
    setShowButtonModalVisible(true);
  };

  useEffect(() => {
    console.log('user:', user);
    if (user) {
      setAvatar(user.headerImg);
    }
  }, [user]);

  const pareaInfo = localStorage.getItem('pareaInfo');

  return (
    <div className='container'>
      <section className='mb-8'>
        <div className='flex mb-4 items-center gap-4'>
          <div className='flex h-4 items-center'>
            <img src={LeftArrow}></img>
            <span
              onClick={() => navigate(-1)}
              className='cursor-pointer text-s1'
              style={{
                color: '#2F6BFF',
              }}>
              {t('返回')}
            </span>
          </div>
          <h2 className='text-xl font-semibold '>{t('设置')}</h2>
        </div>

        <div className='bg-white p-4 rounded shadow'>
          <div className='flex items-center mb-[20px]'>
            <strong>{t('头像：')}</strong>
            <img
              src={avatar}
              alt='User Avatar'
              className='w-12 h-12 rounded-full mr-4'
            />
            <Button onClick={handleOpenModal}>{t('上传')}</Button>
            <UploadAvatar
              isOpen={isModalOpen}
              onClose={handleCloseModal}
              onUpload={handleUpload}
            />
          </div>
          <p>
            <strong>{t('姓名：')}</strong>
            {user?.nickName}
          </p>
          <p>
            <strong>{t('邮箱：')}</strong>
            {user?.email}
          </p>
          <p>
            <strong>{t('公司：')}</strong>
            {user?.coCode}
          </p>
          <p>
            <strong>{t('计划范围：')}</strong>
            {pareaInfo ? JSON.parse(pareaInfo).parea : ''}
          </p>
        </div>
      </section>

      <section className='mb-8'>
        <div className='bg-white p-4 rounded shadow'>
          <form></form>
          <span
            className='mr-4 text-blue-500 cursor-pointer'
            onClick={openShowButtonModal}>
            {t('AI助手设置')}
          </span>
        </div>
      </section>

      <section className='mb-8'>
        <div className='bg-white p-4 rounded shadow'>
          <form></form>
          <span
            className='text-blue-500 cursor-pointer'
            onClick={openUserSettingModal}>
            {t('修改密码')}
          </span>
        </div>
      </section>

      <UserSettingModal
        visible={userSettingModalVisible}
        close={() => setUserSettingModalVisible(false)}
      />
      <ShowButtonModal
        open={showButtonModalVisible}
        close={() => setShowButtonModalVisible(false)}
      />
    </div>
  );
};
const MemoUserPage = memo(UserPage);
export default MemoUserPage;
