import React from 'react';
import FineTuningLog from './FineTuningLog';
import Datasets from './Datasets';
import KnowledgeHub from './KnowledgeHub';
import ModelSetting from './ModelSetting';
import ModelEvaluation from './ModelEvaluation';
import Setting from './Setting';
const Home = React.lazy(() => import('./Home'));
const Login = React.lazy(() => import('./Login'));
// const SSOLogin = React.lazy(() => import('./Login/SSOLogin'));
const UserPage = React.lazy(() => import('./UserPage'));
const Error = React.lazy(() => import('./Error'));
const NotFound = React.lazy(() => import('./NotFound'));
const PromptWords = React.lazy(() => import('./PromptWords/page'));
const ModelParameters = React.lazy(() => import('./ModelParameters'));
const RunningPage = React.lazy(() => import('./RunningPage'));
const TaskDetail = React.lazy(() => import('./TaskDetail'));

export default {
  Home,
  Login,
  // SSOLogin,
  UserPage,
  Error,
  NotFound,
  PromptWords,
  ModelParameters,
  RunningPage,
  TaskDetail,
  ...FineTuningLog,
  ...Datasets,
  ...KnowledgeHub,
  ...ModelSetting,
  ...ModelEvaluation,
  ...Setting,
};
