import { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { DeleteButton } from '@/components/ui/delete-button';
import { getUserList, addNewUser, deleteUser } from '../../api/user';
import { Table, Button, Modal, Form, App, Tag, Card } from 'antd';
import { useAuthStore } from '@/store/features';
import { PlusOutlined } from '@ant-design/icons';
import Avatar from 'react-avatar';
import usePageData from '@/hooks/usePageData';

// 用户类型定义
interface User {
  id: number;
  nickName: string;
  userName: string;
  coCode: string;
  email: string;
  phone: string;
  headerImg?: string;
}

const UserManagement = () => {
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const { user } = useAuthStore();

  const [users, setUsers] = useState<User[]>([]);

  const { pageData, setPageData, pagination } = usePageData();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const getUserListFetch = async () => {
    try {
      const res = await getUserList();
      setUsers(res.data.list);
      setPageData((prev) => ({
        ...prev,
        total: res.data.list.length || 0,
      }));
    } catch (error) {
      console.log(error);
    }
  };

  const deleteUserFetch = async (id: number) => {
    try {
      const res = await deleteUser({
        userId: id,
      });
      if (!res.code) {
        message.success(t('删除成功'));
        setPageData((prev) => {
          return { ...prev, page: 1 };
        });
      } else {
        message.error(t('删除失败'));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const onFinish = async (values: any) => {
    const params = {
      ...values,
      enable: 1,
      headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
    };
    try {
      const res = await addNewUser(params);
      if (!res.code) {
        message.success(t('添加成功'));
      } else {
        message.error(t('添加失败：') + res?.msg || '');
      }
    } catch (error) {
      console.error(error);
    }
    setIsAddDialogOpen(false);
  };

  useEffect(() => {
    getUserListFetch();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 '>
      <Card
        className='space-y-6 bg-white rounded-2xl p-8'
        title={
          <div className='flex justify-between items-center'>
            <div>{t('用户管理')}</div>
            <Button
              type='primary'
              onClick={() => {
                setIsAddDialogOpen(true);
              }}
              icon={<PlusOutlined />}>
              {t('添加用户')}
            </Button>
          </div>
        }>
        <div className='rounded-md'>
          <Table
            bordered={false}
            rowKey={'id'}
            dataSource={users}
            columns={[
              {
                title: t('用户'),
                key: 'nickName',
                render: (_, record: User) => (
                  <div className='flex items-center gap-2'>
                    <Avatar
                      className='mr-2w'
                      size='50'
                      round={true}
                      src={record.headerImg}
                      name={record.nickName}
                    />
                    <div>
                      <div>{record.nickName}</div>
                      <div className='text-sm text-gray-500 flex gap-2'>
                        {record.email}
                        {user.id === record.id && <Tag>{t('自己')}</Tag>}
                      </div>
                    </div>
                  </div>
                ),
              },
              {
                title: t('项目代码'),
                dataIndex: 'coCode',
                key: 'coCode',
              },
              {
                title: t('手机号'),
                dataIndex: 'phone',
                key: 'phone',
              },
              {
                title: t('操作'),
                key: 'action',
                render: (_, record: User) => (
                  <DeleteButton
                    disabled={user.id === record.id}
                    confirmTitle={t('是否删除该用户？')}
                    confirmDescription={t(
                      '该操作将永久删除该用户，请谨慎操作。',
                    )}
                    onDelete={() => {
                      deleteUserFetch(record.id);
                    }}
                  />
                ),
              },
            ]}
            pagination={pagination}
          />
        </div>
      </Card>
      <Modal
        title={t('添加用户')}
        open={isAddDialogOpen}
        onCancel={() => setIsAddDialogOpen(false)}
        footer={false}
        style={{
          padding: 16,
        }}>
        <Form
          form={form}
          onFinish={onFinish}
          colon={false}
          labelCol={{ span: 4 }}>
          <Form.Item
            label={t('用户名')}
            name='username'
            rules={[{ required: true, message: t('请输入用户名') }]}>
            <Input />
          </Form.Item>
          <Form.Item
            label={t('昵称')}
            name='nickName'
            rules={[{ required: true, message: t('请输入昵称') }]}>
            <Input />
          </Form.Item>
          <Form.Item
            label={t('密码')}
            name='password'
            rules={[{ required: true, message: t('请输入密码') }]}>
            <Input type='password' />
          </Form.Item>
          <Form.Item label={t('电话号')} name='phone'>
            <Input />
          </Form.Item>
          <Form.Item label={t('邮箱')} name='email'>
            <Input />
          </Form.Item>
          <div className='flex justify-end gap-2'>
            <Button type='primary' htmlType='submit'>
              {t('添加')}
            </Button>
            <Button onClick={() => setIsAddDialogOpen(false)}>
              {t('取消')}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
