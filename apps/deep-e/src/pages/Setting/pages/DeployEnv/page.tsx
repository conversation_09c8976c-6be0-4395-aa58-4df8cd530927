import {
  App,
  Button,
  Card,
  Empty,
  Input,
  Form,
  Select,
  message,
  Modal,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DeployEnvCard from './components/DeployEnvCard';
import { DialogFooter } from '@/components/ui/dialog';
import { useEffect, useState } from 'react';
import {
  getDeployEnvList,
  EnvList,
  addDeployEnv,
  AddDeployEnv,
  getDeployFactoryList,
  updateDeployEnv,
} from '../../api/deployEnv';
import usePageData from '@/hooks/usePageData';
import { ZodError } from 'zod';
const DeployEnv = () => {
  const [open, setOpen] = useState<boolean>(false);
  const { pageData, setPageData } = usePageData();
  const [deployEnvList, setDeployEnvList] = useState<EnvList[]>([]);
  const { message } = App.useApp();
  const [currentEnv, setCurrentEnv] = useState<
    (AddDeployEnv & { id: number }) | null
  >(null);
  const getDeployEnvListFetch = async () => {
    try {
      const res = await getDeployEnvList(pageData);
      if (!res.code) {
        setDeployEnvList(res.data.list);
        setPageData((prev) => ({
          ...prev,
          total: res.data.total,
        }));
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const addDeployEnvFetch = async (values: AddDeployEnv) => {
    try {
      const res = await addDeployEnv(values);
      console.log(res);
      if (!res.code) {
        message.success(t('添加成功'));
        getDeployEnvListFetch();
        setOpen(false);
      } else {
        message.error(res.msg);
        setOpen(false);
      }
    } catch (error) {
      if (error instanceof ZodError) {
        const firstError = error.errors[0];
        message.error(firstError.message);
      }
    }
  };
  const updateDeployEnvFetch = async (data: any) => {
    try {
      const res = await updateDeployEnv(data);
      if (!res.code) {
        message.success(t('更新成功'));
        setOpen(false);
        getDeployEnvListFetch();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      if (error instanceof ZodError) {
        const firstError = error.errors[0];
        message.error(firstError.message);
      }
    }
  };

  useEffect(() => {
    getDeployEnvListFetch();
  }, []);
  return (
    <div className='p-4 h-full'>
      <Card
        className='h-full'
        title={
          <div className='flex items-center gap-2 justify-between'>
            <div>{t('部署环境')}</div>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => {
                setOpen(true);
              }}>
              {t('添加环境')}
            </Button>
          </div>
        }>
        <div className=' w-full  p-4 space-y-4'>
          {deployEnvList.length !== 0 ? (
            deployEnvList.map((env) => {
              return (
                <DeployEnvCard
                  key={env.id}
                  ip={env.envUrl || ''}
                  title={env.deployEnvName || ''}
                  vendor={env.deployEnvName || 'deepe'}
                  onEdit={(values) => {
                    setOpen(true);
                    setCurrentEnv({
                      id: env.id as number,
                      envUrl: values.ip,
                      deployFactory: values.title,
                    });
                  }}
                />
              );
            })
          ) : (
            <Empty />
          )}
        </div>
        <ContentModal
          open={open}
          onOpenChange={setOpen}
          onSubmit={(values) => {
            addDeployEnvFetch(values);
          }}
          onUpdate={(values) => {
            updateDeployEnvFetch({
              id: currentEnv?.id,
              ...values,
            });
          }}
          initialValues={currentEnv}
          isEdit={!!currentEnv}
        />
      </Card>
    </div>
  );
};

interface ContentModelProps {
  open: boolean;
  isEdit: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: AddDeployEnv) => void;
  onUpdate: (values: AddDeployEnv) => void;
  initialValues: AddDeployEnv | null;
}
const ContentModal: React.FC<ContentModelProps> = ({
  open,
  isEdit,
  onOpenChange,
  onSubmit,
  onUpdate,
  initialValues,
}) => {
  const [deployFactoryList, setDeployFactoryList] = useState<
    {
      value: string;
      label: string;
    }[]
  >([]);
  const getDeployFactoryListFetch = async () => {
    try {
      const res = await getDeployFactoryList();
      if (!res.code) {
        setDeployFactoryList(
          res.data.list.map((item: any) => {
            return {
              value: item.deployEnv,
              label: item.deployEnvName,
            };
          }),
        );
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    getDeployFactoryListFetch();
  }, []);

  return (
    <Modal
      title={t('添加环境')}
      open={open}
      destroyOnClose
      footer={null}
      onCancel={() => onOpenChange(false)}
      className='p-4'>
      <Form
        onFinish={(values: AddDeployEnv) => {
          isEdit ? onUpdate(values) : onSubmit(values);
        }}
        initialValues={initialValues || undefined}>
        <Form.Item
          rules={[{ required: true }]}
          label={t('环境名称')}
          name='deployFactory'>
          <Select options={deployFactoryList} />
        </Form.Item>
        <Form.Item
          rules={[{ required: true }]}
          label={t('环境地址')}
          name='envUrl'>
          <Input placeholder={t('请输入环境地址')} />
        </Form.Item>
        <DialogFooter>
          <Button htmlType='submit' type='primary'>
            {t('提交')}
          </Button>
          <Button onClick={() => onOpenChange(false)}>{t('取消')}</Button>
        </DialogFooter>
      </Form>
    </Modal>
  );
};

export default DeployEnv;
