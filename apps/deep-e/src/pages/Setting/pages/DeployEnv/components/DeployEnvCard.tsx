'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import InnerIcon, { InnerIconProps } from '@/components/InnerIcon';
import { cn } from '@/lib/utils';
import { useAlertDialog } from '@/hooks/useAlertDialog';

interface DeployEnvCardProps {
  title: string;
  ip: string;
  vendor: InnerIconProps['vendor'];
  onEdit?: ({ title, ip }: { title: string; ip: string }) => void;
  onDelete?: () => void;
  className?: string;
}

export default function DeployEnvCard({
  title,
  ip,
  vendor,
  onEdit,
  className,
}: DeployEnvCardProps) {
  const { show } = useAlertDialog();

  const handleEdit = () => {
    if (onEdit) {
      onEdit({ title, ip });
    } else {
      show({
        title: t('提示'),
        description: t(
          '目前您使用的是免费版本，无法删除环境，请前往官网购买专业版。',
        ),
        confirmText: t('确认'),
      });
    }
  };

  return (
    <Card className={cn('w-full transition-all hover:shadow-md', className)}>
      <CardContent className='flex items-center justify-between  gap-2'>
        <div className='flex items-center gap-4 overflow-hidden'>
          <div className='flex-shrink-0 w-16 h-16 flex items-center justify-center  rounded-lg'>
            {/* Assuming InnerIcon is a component that renders vendor icons */}
            <InnerIcon vendor={vendor} width={48} height={48} />
          </div>

          <div className='min-w-0'>
            <h3 className='text-xl font-bold truncate'>{title}</h3>
            <p className='text-sm text-muted-foreground truncate mt-1'>{ip}</p>
          </div>
        </div>

        <div className='flex gap-2 flex-shrink-0'>
          <Button
            variant='ghost'
            size='icon'
            className='rounded-full h-10 w-10 hover:bg-slate-100'
            onClick={handleEdit}
            aria-label='Edit'>
            <Pencil className='h-5 w-5' />
          </Button>

          {/*  <Button
            variant='ghost'
            size='icon'
            className='rounded-full h-10 w-10 hover:bg-red-100 hover:text-red-600'
            onClick={handleDelete}
            aria-label='Delete'>
            <Trash2 className='h-5 w-5' />
          </Button> */}
        </div>
      </CardContent>
    </Card>
  );
}
