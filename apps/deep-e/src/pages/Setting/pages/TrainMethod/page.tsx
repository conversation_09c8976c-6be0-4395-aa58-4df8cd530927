import { useEffect, useState } from 'react';
import { Card, Table, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useAlertDialog } from '@/hooks/useAlertDialog';
import ajax from '@/api';
import usePageData from '@/hooks/usePageData';

const TrainMethod = () => {
  const { /* pageData, setPageData, */ pagination } = usePageData();
  const { show } = useAlertDialog();
  const [trainModelList, setTrainMethodList] = useState([]);

  const getTrainMethodList = async () => {
    try {
      const res = await ajax.getJobTemplateByUserOnlyRead({
        templateType: 1,
      });
      if (!res.code) {
        setTrainMethodList(res.data.list);
      }
    } catch (error) {
      console.error(t('错误:'), error);
    }
  };
  const handleAddMethod = () => {
    show({
      title: t('提示'),
      description: t(
        '当前版本暂不支持添加方法功能。如需解锁此功能及更多专业服务，欢迎联系团队升级至专业版。',
      ),
      confirmText: t('确认'),
      onConfirm: () => {},
    });
  };

  useEffect(() => {
    getTrainMethodList();
  }, []);

  return (
    <div className='p-4'>
      <Card
        className='space-y-6 bg-white rounded-2xl p-8'
        title={
          <div className='flex justify-between items-center'>
            <div>{t('训练方法')}</div>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={handleAddMethod}>
              {t('添加方法')}
            </Button>
          </div>
        }>
        <div className='rounded-md'>
          <Table
            bordered={false}
            rowKey={'templateUuid'}
            dataSource={trainModelList}
            columns={[
              {
                title: t('训练名称'),
                dataIndex: 'templateName',
                key: 'templateName',
              },
              {
                title: t('训练文件'),
                dataIndex: 'trainFile',
                key: 'trainFile',
              },
            ]}
            pagination={pagination}
          />
        </div>
      </Card>
    </div>
  );
};
export default TrainMethod;
