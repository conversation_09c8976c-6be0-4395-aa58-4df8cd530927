import request, { returnData } from '@/services';
import { PageData } from '@/types';
import { InnerIconProps } from '@/components/InnerIcon';
import { z } from 'zod';
export type EnvList = {
  coCode?: number;
  deployEnv?: InnerIconProps['vendor'];
  deployEnvName?: InnerIconProps['vendor'];
  envUrl?: string;
  id?: number;
  [property: string]: any;
};
export const getDeployEnvList = (
  data: PageData,
): Promise<returnData<EnvList[]>> => {
  return request.get('/api/deployEnv/getDeployEnvList', { params: data });
};

const addDeployEnvSchema = z.object({
  envUrl: z.string().url({
    message: '请输入正确的URL格式，例如：http://**************:11434',
  }),
  // .regex(
  //   /^https?:\/\/((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.|$)){4}:\d+|^https?:\/\/host\.docker\.internal:\d+|^https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$/,
  //   {
  //     message:
  //       '请输入正确的URL格式，例如：http://**************:11434、https://**************:11434、http://host.docker.internal:12345 或 https://aliyun.com/v1',
  //   },
  // ),
  deployFactory: z.string(),
});
export type AddDeployEnv = z.infer<typeof addDeployEnvSchema>;
export const addDeployEnv = (data: AddDeployEnv) => {
  if (addDeployEnvSchema.safeParse(data).success) {
    return request.post('/api/deployEnv/addDeployEnv', data);
  } else {
    return Promise.reject(addDeployEnvSchema.safeParse(data).error);
  }
};

export const getDeployFactoryList = () => {
  return request.get('/api/deployEnv/getDeployFactoryList');
};

export const updateDeployEnv = (data: {
  envUrl: string;
  deployFactory: string;
  id: number;
}) => {
  if (addDeployEnvSchema.safeParse(data).success) {
    return request.post('/api/deployEnv/editDeployEnv', data);
  } else {
    return Promise.reject(addDeployEnvSchema.safeParse(data).error);
  }
};
