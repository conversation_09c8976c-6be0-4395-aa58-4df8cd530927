import React from 'react';
import { Card, Form, Upload, Button, Checkbox, Space, message } from 'antd';
import { UploadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { uploadFileForKnowledgeBase } from '../../api';

const UploadKnowledgeFile: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id, name } = location.state || {};
  const [form] = Form.useForm();

  const onFinish = async (values: any) => {
    try {
      const formData = new FormData();
      // 只取第一个文件，参数名为 file
      if (values.files && values.files.length > 0) {
        formData.append('file', values.files[0].originFileObj);
      }
      formData.append(
        'isParseOnCreation',
        values.isParseOnCreation ? 'true' : 'false',
      );
      formData.append('baseUuid', id);
      const res = await uploadFileForKnowledgeBase(formData);
      if (!res.code) {
        message.success(t('上传成功'));
        navigate('/knowledgeHub/details', { state: { id, name } });
      } else {
        message.error(t(`上传失败: ${res.msg}`));
      }
    } catch (error) {
      message.error(t('上传失败，请重试'));
    }
  };

  const handleCancel = () => {
    navigate('/knowledgeHub/details', { state: { id, name } });
  };

  return (
    <div className='p-6'>
      <div className='mb-4'>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          onClick={handleCancel}
          className='px-0'>
          {t('返回')}
        </Button>
      </div>
      <Card title={t('导入文件')} className='w-full'>
        <Form
          form={form}
          layout='vertical'
          onFinish={onFinish}
          className='w-full'>
          <Form.Item
            name='files'
            label={t('选择文件')}
            rules={[{ required: true, message: t('请选择要上传的文件') }]}
            className='max-w-lg'
            valuePropName='fileList'
            getValueFromEvent={(e) => (Array.isArray(e) ? e : e && e.fileList)}>
            <Upload.Dragger
              multiple={false}
              beforeUpload={() => false}
              accept='.pdf,.docx,.txt,.xlsx,.csv'
              maxCount={1}>
              <p className='ant-upload-drag-icon'>
                <UploadOutlined />
              </p>
              <p className='ant-upload-text'>
                {t('点击或拖拽文件到此区域上传')}
              </p>
              <p className='ant-upload-hint'>
                {t('仅支持单个文件上传，文件格式：PDF、DOCX、TXT、XLSX、CSV')}
              </p>
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            name='isParseOnCreation'
            valuePropName='checked'
            className='max-w-lg'>
            <Checkbox>{t('创建时解析')}</Checkbox>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={handleCancel}>{t('取消')}</Button>
              <Button type='primary' htmlType='submit'>
                {t('保存')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default UploadKnowledgeFile;
