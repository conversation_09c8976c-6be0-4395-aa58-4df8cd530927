import React, { useEffect } from 'react';
import { Card, Form, Input, Select, Button, Space, message } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { createKnowledge, getEmbedingModelList } from '../../api';
import { CreateKnowledgeParams } from '../../types';

const CreateKnowledge: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<CreateKnowledgeParams>();
  const [embedingModelList, setEmbedingModelList] = React.useState<any[]>([]);

  const onFinish = async (values: CreateKnowledgeParams) => {
    try {
      const res = await createKnowledge(values);
      if (res.code === 0) {
        message.success(t('创建成功'));
        navigate('/knowledgeHub');
      } else {
        message.error(res.msg || t('创建失败，请重试'));
      }
    } catch (error) {
      message.error(t('创建失败'));
    }
  };

  const handleCancel = () => {
    navigate('/knowledgeHub');
  };

  const getEmbedingModelListFetch = async () => {
    try {
      const res = await getEmbedingModelList();
      if (res.code === 0) {
        const list = res.data.list;
        const options = list.map((group: any) => {
          return {
            label: <span>{group.groupName}</span>,
            title: group.groupName,
            options: group.modelInfoList.map((item: any) => ({
              label: item.modelName,
              value: item.modelConfigId,
            })),
          };
        });
        setEmbedingModelList(options);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getEmbedingModelListFetch();
  }, []);

  return (
    <div className='p-6'>
      <div className='mb-4'>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          onClick={handleCancel}
          className='px-0'>
          {t('返回')}
        </Button>
      </div>
      <Card title={t('创建知识库')} className='w-full'>
        <Form
          form={form}
          layout='vertical'
          onFinish={onFinish}
          className='w-full'>
          <Form.Item
            name='baseName'
            label={t('知识库名称')}
            rules={[{ required: true, message: t('请输入知识库名称') }]}
            className='max-w-lg'>
            <Input placeholder={t('请输入知识库名称')} />
          </Form.Item>

          <Form.Item
            name='baseDesc'
            label={t('知识库描述')}
            rules={[{ required: true, message: t('请输入知识库描述') }]}
            className='max-w-lg'>
            <Input.TextArea placeholder={t('请输入知识库描述')} rows={4} />
          </Form.Item>

          <Form.Item
            name='modelConfigId'
            label={t('向量模型')}
            rules={[{ required: true, message: t('请选择向量模型') }]}
            className='max-w-lg'>
            <Select
              placeholder={t('请选择向量模型')}
              options={embedingModelList}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={handleCancel}>{t('取消')}</Button>
              <Button type='primary' htmlType='submit'>
                {t('保存')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default CreateKnowledge;
