export interface KnowledgeFile {
  id: string;
  fileName: string;
  fileSize: string;
  fileType: string;
  fileUuid: string;
  analyzeState: number;
  analyzeStateName: string;
  createdAt: string;
  sliceCount: number;
  baseUuid: string;
}

export type FileStatus = 'unanalyzed' | 'success' | 'failed';

export interface CreateKnowledgeParams {
  baseName: string;
  embeddingModel: string;
  embeddingPlatform: string;
  baseDesc: string;
}

export interface KnowledgeBase {
  baseUuid: string;
  baseName: string;
  baseDesc: string;
  embeddingPlatform: string;
  embeddingModel: string;
  updatedAt: number;
  fileNum: number;
}
