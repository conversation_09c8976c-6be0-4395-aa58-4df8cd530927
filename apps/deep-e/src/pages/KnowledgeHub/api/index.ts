import request, { returnData } from '@/services';
import { CreateKnowledgeParams, KnowledgeBase } from '../types';

export const createKnowledge = (data: CreateKnowledgeParams) => {
  return request.post('/api/knowledge/createKnowledgeBase', data);
};

export const getKnowledgeList = (data: {
  page: number;
  pageSize: number;
  baseName?: string;
}): Promise<returnData<KnowledgeBase[]>> => {
  return request.get('/api/knowledge/getKnowledgeBaseList', { params: data });
};

export const uploadFileForKnowledgeBase = (data: any) => {
  return request.post('/api/file/uploadFileForKnowledgeBase', data);
};

export const getKnowledgeDetailList = (data: {
  page: number;
  pageSize: number;
  baseUuid: string;
}) => {
  return request.get('/api/knowledge/getKnowledgeDetailList', { params: data });
};

export const deleteKnowledgeBase = (data: { baseUuid: string }) => {
  return request.post('/api/knowledge/deleteKnowledgeBase', data);
};

export const analyzeFileToKnowledgeBase = (data: {
  baseUuid: string;
  fileUuid: string;
}) => request.post('/api/knowledge/analyzeFileToKnowledgeBase', data);

export const getEmbedingModelList = () => {
  return request.get('/api/modelConfig/getModelInfoListForSelect', {
    params: {
      modelType: 'embedding',
    },
  });
};

export const downloadFileForKnowledge = (fileUuid: string) => {
  return request.get('/api/file/downloadFileForKnowledge', {
    responseType: 'blob',
    params: {
      fileUuid,
    },
  });
};
