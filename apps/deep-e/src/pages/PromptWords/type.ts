import { z } from 'zod';

const ModelListScheme = z.object({
  groupName: z.string(),
  modelInfoList: z.array(
    z.object({
      factoryName: z.string(),
      factoryTechnicalName: z.string(),
      isPrivate: z.boolean(),
      modelConfigId: z.number(),
      modelName: z.string(),
      modelPath: z.string(),
      taskUuid: z.string(),
    }),
  ),
});
export type ModelList = z.infer<typeof ModelListScheme>;
