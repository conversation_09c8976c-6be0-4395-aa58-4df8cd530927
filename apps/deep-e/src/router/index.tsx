import type { RouteObject } from 'react-router-dom';
import { matchPath, Outlet } from 'react-router-dom';
import { AllowedRoute } from './type';
import React, { Suspense } from 'react';
import PlanningViewIcon from '@/assets/PlanningView.svg?react';
import DashBoard from '@/assets/menus/dashboard.svg?react';
import Chat from '@/assets/menus/chat.svg?react';
import Datasets from '@/assets/menus/datasets.svg?react';
import { Loading } from '@/components';
import Evaluation from '@/assets/menus/evaluation.svg?react';
import Knowledge from '@/assets/menus/knowledge.svg?react';
import ModelSetting from '@/assets/menus/modelsetting.svg?react';
import Setting from '@/assets/menus/setting.svg?react';
import FineTuning from '@/assets/menus/finetuning.svg?react';
// import ErrorBoundary from '@/pages/Error';

import AllPage from '@/pages';
import { ErrorBoundary } from '@ant-design/pro-components';
// import { ErrorBoundary } from '@ant-design/pro-components';

export type RouteInfoParams = {
  name?: string;
  icon?: React.ReactNode;
  path?: string;
  children?: RouteInfoParams[];
};

export const route_info: RouteInfoParams[] = [
  {
    icon: <DashBoard />,
    path: '/home',
  },
  {
    path: '/modelParameters',
    icon: <PlanningViewIcon />,
  },
  {
    path: '/promptWords',
    icon: <Chat />,
  },
  {
    path: '/datasets',
    icon: <Datasets />,
  },
  {
    path: '/knowledgeHub',
    icon: <Knowledge />,
  },
  {
    path: '/fineTuning',
    icon: <FineTuning />,
  },
  {
    path: '/modelEvaluation',
    icon: <Evaluation />,
  },
  {
    path: '/modelSetting',
    icon: <ModelSetting />,
    children: [
      {
        path: '/modelBase',
      },
      {
        path: '/trainedModel',
      },
      {
        path: '/savedModel',
      },
      {
        path: '/deployedModel',
      },
      {
        path: '/thirdPartyModel',
      },
    ],
  },
  {
    path: '/setting',
    icon: <Setting />,
    children: [
      {
        path: '/deployEnverment',
      },
      {
        path: '/trainMethod',
      },
      {
        path: '/user',
      },
    ],
  },
];

const componentMap: {
  [key: string]: React.LazyExoticComponent<React.FC<any>>;
} = { ...AllPage };

function splitRoutePath(path: string) {
  if (!path.includes(':')) {
    return path.split('/').filter(Boolean);
  } else {
    const list = path.split('/').filter(Boolean);
    const lastPart = list.pop();
    list[list.length - 1] += '/' + lastPart;
    return list;
  }
}

function findBestMatch(path: string, allowedRoutes: Array<AllowedRoute>) {
  let bestMatch = null;
  let highestScore = 0;

  for (const route of allowedRoutes) {
    const match = matchPath(route.path, path);
    if (match) {
      const score = match.pathname.split('/').filter(Boolean).length;
      if (score > highestScore) {
        highestScore = score;
        bestMatch = route;
      }
    }
  }
  return bestMatch;
}

export function createNestedRoutes(routes: Array<AllowedRoute>): RouteObject[] {
  const routeMap: { [key: string]: RouteObject } = {};
  const nonMenuRoutes: RouteObject[] = []; // 存储 index === false 的路由
  routes
    .sort((a, b) => {
      return a.path.length - b.path.length;
    })
    .forEach((route) => {
      if (route.index === false) {
        const element = (
          <Suspense fallback={<Loading />}>
            <ErrorBoundary>
              {route.component === 'Outlet' ? (
                <Outlet />
              ) : route.component === '' ||
                route.component === undefined ||
                componentMap[route.component] === undefined ? (
                <AllPage.NotFound
                  findBestMatch={(path: any) => findBestMatch(path, [])}
                />
              ) : (
                React.createElement(componentMap[route.component])
              )}
            </ErrorBoundary>
          </Suspense>
        );

        nonMenuRoutes.push({
          path: route.path,
          element,
          // errorElement: <ErrorBoundary />,
        });
        return; // 跳过后续处理
      }

      const pathParts = splitRoutePath(route.path);
      let currentLevel = routeMap;

      pathParts.forEach((part, index) => {
        const isLast = index === pathParts.length - 1;
        const currentPath = part;

        if (!currentLevel[currentPath]) {
          currentLevel[currentPath] = {
            path: isLast ? part : undefined,
            children: [],
          };
        }
        if (!currentLevel[currentPath].children) {
          currentLevel[currentPath].children = [];
        }

        if (isLast) {
          const element = (
            <Suspense fallback={<Loading />}>
              <ErrorBoundary>
                <>
                  {route.component === 'Outlet' ? (
                    <Outlet />
                  ) : route.component === '' ||
                    route.component === undefined ||
                    componentMap[route.component] === undefined ? (
                    <AllPage.NotFound
                      findBestMatch={(path: any) => findBestMatch(path, [])}
                    />
                  ) : (
                    React.createElement(componentMap[route.component])
                  )}
                </>
              </ErrorBoundary>
            </Suspense>
          );
          if (route.index) {
            currentLevel[currentPath].children.push({
              index: true,
              element,
              // errorElement: <ErrorBoundary />,
            });
          } else {
            // 如果 index 为 false，直接渲染组件
            // 否则保持原来的父子结构
            if (route.index === false || route.index === undefined) {
              currentLevel[currentPath] = {
                ...currentLevel[currentPath],
                element,
                path: route.path, // 使用完整路径
                // errorElement: <ErrorBoundary />,
              };
            } else {
              currentLevel[currentPath].element = element;
            }
          }
        }

        if (route.index === true || !isLast) {
          currentLevel = currentLevel[currentPath].children as unknown as {
            [key: string]: RouteObject;
          };
        }
      });
    });

  const convertToArray = (obj: {
    [key: string]: RouteObject;
  }): RouteObject[] => {
    return Object.values(obj).map((route) => {
      // route.errorElement = <ErrorBoundary />;
      if (route.children && Object.keys(route.children).length > 0) {
        return {
          ...route,
          children: convertToArray(
            route.children as unknown as { [key: string]: RouteObject },
          ),
        };
      }
      return route;
    });
  };

  return [...convertToArray(routeMap), ...nonMenuRoutes];
}

export default createNestedRoutes;
