import { DefaultModelIcon, VENDOR_ICONS, VENDOR_SCHEMA } from './Icons';
import { z } from 'zod';
const InnerIconScheme = z
  .object({
    vendor: VENDOR_SCHEMA,
    className: z.string().optional(),
    width: z.number(),
    height: z.number(),
  })
  .passthrough();

export type InnerIconProps = z.infer<typeof InnerIconScheme>;

const InnerIcon: React.FC<InnerIconProps> = ({
  vendor,
  className,
  width,
  height,
  ...props
}) => {
  const IconComponent = VENDOR_ICONS[vendor] || DefaultModelIcon;
  return (
    <div className={className}>
      <IconComponent {...props} width={width} height={height} />
    </div>
  );
};

export default InnerIcon;
