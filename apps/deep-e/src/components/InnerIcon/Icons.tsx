// src/components/Icons/index.js
import tongyiIcon from '@/assets/llm/tongyi.svg?react';
import openAiIcon from '@/assets/llm/openai.svg?react';
import ollamaIcon from '@/assets/llm/ollama.svg?react';
import deepseekIcon from '@/assets/llm/deepseek.svg?react';
import siliconflowIcon from '@/assets/llm/siliconflow.svg?react';
import deepeIcon from '@/assets/llm/deepe.svg?react';
import { Brain } from 'lucide-react';
import { z } from 'zod';

export const VENDOR_SCHEMA = z.enum([
  'dashscope',
  'openai',
  'ollama',
  'deepseek',
  'siliconflow',
  'deepe',
]);

export type Vendor = z.infer<typeof VENDOR_SCHEMA>;

export const VENDOR_ICONS: Record<Vendor, any> = {
  dashscope: tongyiIcon,
  openai: openAiIcon,
  ollama: ollamaIcon,
  deepseek: deepseekIcon,
  siliconflow: siliconflowIcon,
  deepe: deepeIcon,
};

export const DefaultModelIcon = ({ width, height }: any) => (
  <Brain width={width} height={height} />
); // 默认图标
