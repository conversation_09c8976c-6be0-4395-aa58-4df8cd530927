import { memo, useCallback, useMemo } from 'react';
import Icon from '@ant-design/icons';
import { Button } from '../Button/Button';
import PrevPage from '/PrevPage.svg';
import NextPage from '/NextPage.svg';
import { useVoerkaI18n } from '@voerkai18n/react';

export interface PaginationProps {
  current: number;
  pageSize: number;
  maxItems?: number;
  showJump?: boolean;
  total: number;
  onPageChange: (page: number, pageSize: number) => void;
}

export const Pagination: React.FC<PaginationProps> = memo(
  ({
    current,
    pageSize,
    maxItems = 3,
    showJump = true,
    onPageChange,
    total,
  }) => {
    const { t } = useVoerkaI18n();

    const MAX_ITEMS = maxItems;
    const totalPage = useMemo(
      () => Math.ceil(total / pageSize),
      [total, pageSize],
    );
    const pages = useMemo(
      () => Array.from({ length: totalPage }, (_, i) => i + 1),
      [totalPage],
    );

    const handlePrevPage = useCallback(() => {
      if (current > 1) {
        onPageChange(current - 1, pageSize);
      }
    }, [current, pageSize, onPageChange]);

    const handleNextPage = useCallback(() => {
      if (current < totalPage) {
        onPageChange(current + 1, pageSize);
      }
    }, [current, pageSize, totalPage, onPageChange]);

    const handlePageClick = useCallback(
      (page: number) => {
        onPageChange(page, pageSize);
      },
      [onPageChange, pageSize],
    );

    const handleEllipsisClick = useCallback(
      (page: number) => {
        const newPage =
          page === 2
            ? current - 5 < 1
              ? 1
              : current - 5
            : current + 5 > totalPage
              ? totalPage
              : current + 5;
        onPageChange(newPage, pageSize);
      },
      [current, totalPage, onPageChange, pageSize],
    );

    const handleJumpSubmit = useCallback(
      (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault(); // 阻止表单的默认提交行为
        const inputElement = event.currentTarget.querySelector(
          'input[name="page"]',
        ) as HTMLInputElement;
        const newPage = +inputElement.value;
        if (isNaN(newPage) || newPage < 1 || newPage > totalPage) {
          console.warn('page is invalid');
        } else {
          onPageChange(newPage, pageSize);
        }
      },
      [onPageChange, pageSize, totalPage],
    );

    return (
      <div className='ui-flex ui-gap-3'>
        <div className='ui-h-14 ui-bg-white ui-flex ui-items-center ui-px-6 ui-rounded-3xl '>
          <button
            className="ui-text-sky-500 ui-text-lg ui-font-medium ui-font-['PingFang SC'] ui-tracking-wide ui-flex ui-items-center ui-gap-2"
            onClick={handlePrevPage}
            disabled={current === 1}>
            <Icon
              component={() => <img src={PrevPage}></img>}
              style={{ width: '9px' }}
            />
            {t('上一页')}
          </button>
          <div className='ui-mx-9 ui-flex ui-gap-4 ui-text-stone-300'>
            {pages.map((page) => {
              if (page === current) {
                return (
                  <button
                    key={page}
                    disabled
                    className='ui-bg-sky-500 ui-size-10 ui-rounded-full ui-text-white'>
                    {page}
                  </button>
                );
              }
              const isEllipsis =
                (page === 2 && current > MAX_ITEMS) ||
                (page === totalPage - 1 && current < totalPage - MAX_ITEMS + 1);
              const isInRange =
                page === 1 ||
                page === totalPage ||
                Math.abs(page - current) <= Math.floor(MAX_ITEMS / 2) ||
                (page <= MAX_ITEMS && current <= MAX_ITEMS) ||
                (page >= totalPage - MAX_ITEMS + 1 &&
                  current >= totalPage - MAX_ITEMS + 1);
              if (isEllipsis) {
                return (
                  <button
                    key={page}
                    className='ui-size-10'
                    onClick={() => handleEllipsisClick(page)}>
                    ...
                  </button>
                );
              }
              if (isInRange) {
                return (
                  <button
                    key={page}
                    className='ui-size-10'
                    onClick={() => handlePageClick(page)}>
                    {page}
                  </button>
                );
              }
              return null;
            })}
          </div>

          <button
            className="ui-text-sky-500 text-lg font-medium font-['PingFang SC'] tracking-wide ui-flex ui-items-center ui-gap-2"
            onClick={handleNextPage}
            disabled={current === totalPage}>
            {t('下一页')}
            <Icon
              component={() => <img src={NextPage}></img>}
              style={{ width: '9px' }}
            />
          </button>
        </div>
        {showJump && (
          <div className='ui-bg-white ui-flex ui-items-center  ui-rounded-3xl ui-h-14 ui-px-6 ui-gap-4'>
            <span className='ui-text-zinc-400'>{t('跳转至第')}</span>
            <form
              className='ui-gap-4 ui-flex ui-items-center'
              onSubmit={handleJumpSubmit}>
              <input
                name='page'
                type='text'
                className='ui-border ui-border-slate-400 ui-rounded-lg ui-size-10 ui-text-center'
              />
              <span className='ui-text-zinc-400'>{t('页')}</span>
              <Button
                intent='primary'
                size='medium'
                buttonType='normal'
                type='submit'>
                {t('跳转')}
              </Button>
            </form>
          </div>
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 自定义 props 比较函数，如果 props 没有变化则不重新渲染
    return (
      prevProps.current === nextProps.current &&
      prevProps.pageSize === nextProps.pageSize &&
      prevProps.maxItems === nextProps.maxItems &&
      prevProps.showJump === nextProps.showJump &&
      prevProps.total === nextProps.total &&
      prevProps.onPageChange === nextProps.onPageChange
    );
  },
);
