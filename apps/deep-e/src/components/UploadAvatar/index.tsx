import React, { useState } from 'react';
import { <PERSON>dal, Button, Typography, message } from 'antd';
import { setUser } from '@/store/features/loginSlice';
import { useDispatch } from 'react-redux';
import { CheckOutlined } from '@ant-design/icons';
import AvatarUploader from './AvatarUploader';
// import ajax from '@/api';
import _ from 'lodash';

interface AvatarUploaderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (imageUrl: string) => Promise<boolean>;
}

const AvatarUploaderModal: React.FC<AvatarUploaderModalProps> = ({
  isOpen,
  onClose,
  onUpload,
}) => {
  const dispatch = useDispatch();
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);

  //更新用户信息
  const getUserInfo = async () => {
    try {
      // const res = await ajax.getUserInfo();
      const res = {};
      const userInfo = _.get(res, 'data.userInfo', {}) || {};
      dispatch(setUser(userInfo));
    } catch (error) {
      console.error(error);
    }
  };

  const handleUpload = async () => {
    if (croppedImageUrl) {
      const res = await onUpload(croppedImageUrl);
      console.log(res);
      if (res) {
        message.success('头像上传成功！');
        await getUserInfo();
      }
    } else {
      message.error('头像上传失败！');
    }
    onClose();
  };

  return (
    <Modal
      title={
        <Typography.Title level={4} style={{ margin: 0 }}>
          上传头像
        </Typography.Title>
      }
      open={isOpen}
      onCancel={onClose}
      footer={null}
      destroyOnClose
      width={1200}
      style={{ padding: '24px' }}>
      <div className='flex flex-col'>
        <AvatarUploader onCrop={setCroppedImageUrl} />
        <div className='mt-6 flex justify-end w-full'>
          <Button onClick={onClose} className='mr-4'>
            {t('取消')}
          </Button>
          <Button
            type='primary'
            icon={<CheckOutlined />}
            onClick={handleUpload}
            disabled={!croppedImageUrl}>
            确认上传
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default AvatarUploaderModal;
