type NoSearchDataIconType = {
  isCollapse: boolean;
};
const noSearchDataIcon: React.FC<NoSearchDataIconType> = (props) => {
  return (
    <div
      className='overflow-y-hidden bg-white flex justify-center'
      style={{
        borderRadius: '1.78571rem',
        alignItems: 'center',
        height: props.isCollapse
          ? 'calc(100vh - 39rem)'
          : 'calc(100vh - 32rem)',
        paddingTop: props.isCollapse ? '4rem' : '0',
      }}>
      <svg
        width='115.14286rem'
        height='54.14286rem'
        viewBox='0 0 1612 758'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'>
        <rect width='1612' height='758' rx='20' fill='white' />
        <g clipPath='url(#clip0_403_950)'>
          <path
            d='M627.385 469.914L773.539 555.375L920.462 470.684L772.769 385.222L627.385 469.914Z'
            fill='#F1F1F1'
          />
          <path
            d='M631.23 396.771L777.384 482.233L924.307 397.541L776.615 312.08L631.23 396.771Z'
            fill='#F8F8F8'
          />
          <path
            d='M777.385 482.233V513.799L924.308 428.338V397.541L777.385 482.233Z'
            fill='#DCDEDE'
          />
          <path
            d='M778.154 481.463V513.029L631.23 427.568V396.771L778.154 481.463Z'
            fill='#EAEBEB'
          />
          <path
            d='M815.661 555.429C671.176 587.219 542.107 557.469 527.376 488.969C516.046 436.245 575.769 375.929 669.776 336.447C592.092 371.664 543.699 423.256 553.476 468.728C566.692 530.222 681.292 557.223 809.423 529.028C937.553 500.834 1030.71 428.13 1017.49 366.636C1007.46 319.963 955.476 293.671 867.069 295.78C972.553 290.352 1039.01 319.771 1050.63 373.85C1065.35 442.343 960.153 523.639 815.661 555.429Z'
            fill='url(#paint0_radial_403_950)'
          />
          <path
            d='M727.208 394.415L783.07 427.083C790.408 431.372 799.485 431.402 806.854 427.16L862.908 394.847C869.124 391.266 869.131 382.289 862.924 378.701L799.193 341.822C796.3 340.151 792.731 340.151 789.847 341.837L727.224 378.316C721.054 381.904 721.047 390.82 727.208 394.415Z'
            fill='#DCDEDE'
          />
          <path
            d='M847.845 296.843L740.922 234.433L669.537 356.943L776.591 416.443C782.845 419.923 790.699 418.306 795.083 412.647L853.868 336.702C860.56 328.063 862.014 316.23 856.945 306.544C854.868 302.579 851.852 299.184 847.845 296.843Z'
            fill='#C9C9C9'
          />
          <path
            d='M769.776 414.21L676.568 364.357C671.876 361.847 670.106 356.004 672.614 351.315L742.668 220.097C745.176 215.4 751.014 213.63 755.699 216.14L848.906 265.992C853.599 268.502 855.368 274.346 852.86 279.035L782.806 410.253C780.299 414.949 774.468 416.72 769.776 414.21Z'
            fill='#DDECFF'
          />
          <path
            d='M826.93 288.389L824.13 282.168C823.415 280.582 821.699 279.712 819.999 280.074L814.215 281.306C812.769 281.614 811.622 280.089 812.315 278.781L826.053 253.042L843.884 262.581L830.045 288.497C829.376 289.767 827.53 289.706 826.93 288.389Z'
            fill='url(#paint1_linear_403_950)'
          />
          <path
            d='M663.43 358.853L775.715 421.74C781.523 424.997 788.869 422.602 791.653 416.543L823.007 348.305C825.561 342.738 823.277 336.148 817.823 333.361L757.084 302.356C754.377 300.978 752.338 298.568 751.407 295.681L747.292 282.877C746.377 280.036 744.392 277.672 741.761 276.278L709.684 259.309C703.846 256.222 696.615 258.724 693.93 264.76L658.577 344.24C656.184 349.637 658.284 355.965 663.43 358.853Z'
            fill='#EFEFEF'
          />
          <path
            d='M556.684 292.824C597.461 324.537 632.314 306.174 618.507 290.276C601.999 271.274 579.691 329.018 638.153 326.708'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M903.1 332.991C946.877 337.664 963.907 308.7 946.769 303.379C926.284 297.02 936.007 348.897 977.346 320.964'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M900.707 301.924C925.384 304.742 953.176 276.879 949.992 254.466'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </g>
        <path
          d='M746.02 138.62V140.3C745.16 140.38 744.28 140.48 743.38 140.56V141.88H741.46V140.72C740.24 140.8 738.96 140.88 737.64 140.96L737.38 139.16C738.82 139.1 740.18 139.04 741.46 138.96V137.84H738.04L737.6 136.16C738.04 135.66 738.44 135.06 738.84 134.38H737.08V132.54H739.7C739.86 132.16 740 131.78 740.14 131.38L742.22 131.72C742.1 132 742 132.28 741.9 132.54H746.42V134.38H741.06C740.74 135.02 740.38 135.6 740.02 136.14H741.46V135H743.38V136.14H746.22V137.84H743.38V138.84C744.3 138.76 745.18 138.7 746.02 138.62ZM749.12 137.86C748.88 139.46 748.32 140.88 747.48 142.1L746.04 140.6C746.86 139.38 747.3 137.94 747.34 136.26V132.9C749.98 132.64 751.94 132.2 753.26 131.6L754.62 133.14C753.16 133.78 751.38 134.22 749.26 134.46V136.04H754.96V137.86H753.12V141.9H751.1V137.86H749.12ZM753 142.28V150.1H750.88V149.4H741.36V150.1H739.24V142.28H753ZM741.36 147.64H750.88V146.62H741.36V147.64ZM741.36 144.98H750.88V144H741.36V144.98ZM759.32 132.4H773.96V134.44H766.96V135.6C766.92 136.68 766.86 137.68 766.74 138.62H775.54V140.64H769.56V146.84C769.56 147.48 769.86 147.82 770.46 147.82H772.6C772.96 147.82 773.24 147.66 773.42 147.38C773.64 147.1 773.78 146.2 773.86 144.68L775.82 145.34C775.64 147.5 775.32 148.78 774.88 149.18C774.48 149.56 773.84 149.76 773 149.76H769.82C768.26 149.76 767.48 148.92 767.48 147.26V140.64H766.4C766.04 142.34 765.54 143.82 764.88 145.06C763.64 147.26 761.64 148.94 758.88 150.1L757.7 148.24C760.46 146.98 762.3 145.4 763.26 143.52C763.66 142.64 763.98 141.68 764.24 140.64H758.04V138.62H764.6C764.72 137.66 764.8 136.66 764.84 135.6V134.44H759.32V132.4ZM791 136.42C790.92 136.7 790.86 136.96 790.78 137.24C791.22 139.22 791.76 140.96 792.42 142.46C793.02 140.7 793.36 138.68 793.44 136.42H791ZM791.46 144.6C790.82 143.28 790.26 141.8 789.78 140.16C789.48 140.82 789.16 141.44 788.84 142L787.54 140.38C788.64 138.18 789.38 135.24 789.76 131.56L791.8 131.92C791.68 132.8 791.56 133.62 791.42 134.4H796.42V136.42H795.32C795.2 139.64 794.62 142.38 793.56 144.66C794.52 146.26 795.66 147.52 796.98 148.42L795.82 150.12C794.58 149.2 793.48 148 792.54 146.52C791.6 147.92 790.44 149.12 789.08 150.1L788 148.34C789.44 147.38 790.6 146.14 791.46 144.6ZM782 140.84L783.96 141.08C783.84 141.38 783.74 141.68 783.64 141.96H787.66V143.64C787.32 144.8 786.72 145.84 785.84 146.72C786.62 147.06 787.38 147.42 788.12 147.82L787 149.46C786.24 149 785.32 148.52 784.24 148.02C782.92 148.86 781.28 149.52 779.3 150.04L778.34 148.3C779.78 147.98 781.02 147.56 782.08 147.08C781.28 146.74 780.42 146.4 779.54 146.06C780.02 145.26 780.44 144.48 780.8 143.74H778.82V141.96H781.6C781.74 141.56 781.88 141.2 782 140.84ZM783.96 145.94C784.78 145.3 785.4 144.56 785.82 143.74H782.86C782.6 144.26 782.32 144.76 782.04 145.24C782.68 145.44 783.32 145.68 783.96 145.94ZM780.78 131.86C781.2 132.6 781.58 133.4 781.9 134.26L780.42 134.92C780.02 133.96 779.6 133.12 779.16 132.38L780.78 131.86ZM786.4 131.84L787.86 132.54C787.46 133.38 786.96 134.2 786.34 135L784.88 134.26C785.52 133.44 786.04 132.64 786.4 131.84ZM778.86 135.22H782.58V131.6H784.5V135.22H787.9V137.04H784.5V137.08C785.54 137.5 786.6 137.98 787.68 138.52L786.6 140.16C785.76 139.5 785.06 138.98 784.5 138.6V140.7H782.58V137.56C781.9 138.92 780.86 140.1 779.42 141.14L778.38 139.46C779.68 138.72 780.64 137.92 781.24 137.04H778.86V135.22ZM801.7 149.9H800L799.56 147.94C800.06 148.02 800.54 148.08 801 148.08C801.42 148.08 801.64 147.82 801.64 147.32V143.28C800.96 143.52 800.3 143.76 799.62 143.98L799.08 141.92C799.94 141.72 800.8 141.48 801.64 141.2V137.2H799.48V135.18H801.64V131.64H803.68V135.18H805.38V137.2H803.68V140.38C804.24 140.12 804.78 139.86 805.3 139.58V141.62C804.74 141.9 804.2 142.16 803.68 142.42V147.84C803.68 149.2 803.02 149.9 801.7 149.9ZM808.02 141.28C807.78 144.62 807.02 147.56 805.76 150.12L804.12 148.66C805.42 146.12 806.08 142.84 806.1 138.84V132.2H817.06V137.5H813.46V139.38H817.5V141.28H813.46V143.2H816.62V150.08H814.7V149.26H810.2V150.08H808.28V143.2H811.42V141.28H808.02ZM811.42 137.5H808.12V138.84C808.12 139.02 808.1 139.2 808.1 139.38H811.42V137.5ZM810.2 147.42H814.7V145.04H810.2V147.42ZM815.02 134.1H808.12V135.66H815.02V134.1Z'
          fill='black'
        />
        <defs>
          <radialGradient
            id='paint0_radial_403_950'
            cx='0'
            cy='0'
            r='1'
            gradientUnits='userSpaceOnUse'
            gradientTransform='translate(788.994 431.671) rotate(-13.1921) scale(275.55 136.612)'>
            <stop offset='0.4694' stopColor='#0C40DE' />
            <stop offset='0.9724' stopColor='white' />
          </radialGradient>
          <linearGradient
            id='paint1_linear_403_950'
            x1='816.938'
            y1='290.957'
            x2='840.482'
            y2='248.278'
            gradientUnits='userSpaceOnUse'>
            <stop offset='0.1783' stopColor='#F25445' />
            <stop offset='0.9724' stopColor='white' />
          </linearGradient>
          <clipPath id='clip0_403_950'>
            <rect
              width='526'
              height='353.171'
              fill='white'
              transform='translate(526 215)'
            />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
};
const noSearchDataIcon1 = () => {
  return (
    <div
      className='overflow-y-hidden bg-white flex justify-center'
      style={{ borderRadius: '1.78571rem', alignItems: 'center' }}>
      <svg
        width='115.14286rem'
        height='54.14286rem'
        viewBox='0 0 1612 758'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'>
        <rect width='1612' height='758' rx='20' fill='white' />
        <g clipPath='url(#clip0_403_950)'>
          <path
            d='M627.385 469.914L773.539 555.375L920.462 470.684L772.769 385.222L627.385 469.914Z'
            fill='#F1F1F1'
          />
          <path
            d='M631.23 396.771L777.384 482.233L924.307 397.541L776.615 312.08L631.23 396.771Z'
            fill='#F8F8F8'
          />
          <path
            d='M777.385 482.233V513.799L924.308 428.338V397.541L777.385 482.233Z'
            fill='#DCDEDE'
          />
          <path
            d='M778.154 481.463V513.029L631.23 427.568V396.771L778.154 481.463Z'
            fill='#EAEBEB'
          />
          <path
            d='M815.661 555.429C671.176 587.219 542.107 557.469 527.376 488.969C516.046 436.245 575.769 375.929 669.776 336.447C592.092 371.664 543.699 423.256 553.476 468.728C566.692 530.222 681.292 557.223 809.423 529.028C937.553 500.834 1030.71 428.13 1017.49 366.636C1007.46 319.963 955.476 293.671 867.069 295.78C972.553 290.352 1039.01 319.771 1050.63 373.85C1065.35 442.343 960.153 523.639 815.661 555.429Z'
            fill='url(#paint0_radial_403_950)'
          />
          <path
            d='M727.208 394.415L783.07 427.083C790.408 431.372 799.485 431.402 806.854 427.16L862.908 394.847C869.124 391.266 869.131 382.289 862.924 378.701L799.193 341.822C796.3 340.151 792.731 340.151 789.847 341.837L727.224 378.316C721.054 381.904 721.047 390.82 727.208 394.415Z'
            fill='#DCDEDE'
          />
          <path
            d='M847.845 296.843L740.922 234.433L669.537 356.943L776.591 416.443C782.845 419.923 790.699 418.306 795.083 412.647L853.868 336.702C860.56 328.063 862.014 316.23 856.945 306.544C854.868 302.579 851.852 299.184 847.845 296.843Z'
            fill='#C9C9C9'
          />
          <path
            d='M769.776 414.21L676.568 364.357C671.876 361.847 670.106 356.004 672.614 351.315L742.668 220.097C745.176 215.4 751.014 213.63 755.699 216.14L848.906 265.992C853.599 268.502 855.368 274.346 852.86 279.035L782.806 410.253C780.299 414.949 774.468 416.72 769.776 414.21Z'
            fill='#DDECFF'
          />
          <path
            d='M826.93 288.389L824.13 282.168C823.415 280.582 821.699 279.712 819.999 280.074L814.215 281.306C812.769 281.614 811.622 280.089 812.315 278.781L826.053 253.042L843.884 262.581L830.045 288.497C829.376 289.767 827.53 289.706 826.93 288.389Z'
            fill='url(#paint1_linear_403_950)'
          />
          <path
            d='M663.43 358.853L775.715 421.74C781.523 424.997 788.869 422.602 791.653 416.543L823.007 348.305C825.561 342.738 823.277 336.148 817.823 333.361L757.084 302.356C754.377 300.978 752.338 298.568 751.407 295.681L747.292 282.877C746.377 280.036 744.392 277.672 741.761 276.278L709.684 259.309C703.846 256.222 696.615 258.724 693.93 264.76L658.577 344.24C656.184 349.637 658.284 355.965 663.43 358.853Z'
            fill='#EFEFEF'
          />
          <path
            d='M556.684 292.824C597.461 324.537 632.314 306.174 618.507 290.276C601.999 271.274 579.691 329.018 638.153 326.708'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M903.1 332.991C946.877 337.664 963.907 308.7 946.769 303.379C926.284 297.02 936.007 348.897 977.346 320.964'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M900.707 301.924C925.384 304.742 953.176 276.879 949.992 254.466'
            stroke='#F25445'
            strokeWidth='3'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </g>
        <path
          d='M746.02 138.62V140.3C745.16 140.38 744.28 140.48 743.38 140.56V141.88H741.46V140.72C740.24 140.8 738.96 140.88 737.64 140.96L737.38 139.16C738.82 139.1 740.18 139.04 741.46 138.96V137.84H738.04L737.6 136.16C738.04 135.66 738.44 135.06 738.84 134.38H737.08V132.54H739.7C739.86 132.16 740 131.78 740.14 131.38L742.22 131.72C742.1 132 742 132.28 741.9 132.54H746.42V134.38H741.06C740.74 135.02 740.38 135.6 740.02 136.14H741.46V135H743.38V136.14H746.22V137.84H743.38V138.84C744.3 138.76 745.18 138.7 746.02 138.62ZM749.12 137.86C748.88 139.46 748.32 140.88 747.48 142.1L746.04 140.6C746.86 139.38 747.3 137.94 747.34 136.26V132.9C749.98 132.64 751.94 132.2 753.26 131.6L754.62 133.14C753.16 133.78 751.38 134.22 749.26 134.46V136.04H754.96V137.86H753.12V141.9H751.1V137.86H749.12ZM753 142.28V150.1H750.88V149.4H741.36V150.1H739.24V142.28H753ZM741.36 147.64H750.88V146.62H741.36V147.64ZM741.36 144.98H750.88V144H741.36V144.98ZM759.32 132.4H773.96V134.44H766.96V135.6C766.92 136.68 766.86 137.68 766.74 138.62H775.54V140.64H769.56V146.84C769.56 147.48 769.86 147.82 770.46 147.82H772.6C772.96 147.82 773.24 147.66 773.42 147.38C773.64 147.1 773.78 146.2 773.86 144.68L775.82 145.34C775.64 147.5 775.32 148.78 774.88 149.18C774.48 149.56 773.84 149.76 773 149.76H769.82C768.26 149.76 767.48 148.92 767.48 147.26V140.64H766.4C766.04 142.34 765.54 143.82 764.88 145.06C763.64 147.26 761.64 148.94 758.88 150.1L757.7 148.24C760.46 146.98 762.3 145.4 763.26 143.52C763.66 142.64 763.98 141.68 764.24 140.64H758.04V138.62H764.6C764.72 137.66 764.8 136.66 764.84 135.6V134.44H759.32V132.4ZM791 136.42C790.92 136.7 790.86 136.96 790.78 137.24C791.22 139.22 791.76 140.96 792.42 142.46C793.02 140.7 793.36 138.68 793.44 136.42H791ZM791.46 144.6C790.82 143.28 790.26 141.8 789.78 140.16C789.48 140.82 789.16 141.44 788.84 142L787.54 140.38C788.64 138.18 789.38 135.24 789.76 131.56L791.8 131.92C791.68 132.8 791.56 133.62 791.42 134.4H796.42V136.42H795.32C795.2 139.64 794.62 142.38 793.56 144.66C794.52 146.26 795.66 147.52 796.98 148.42L795.82 150.12C794.58 149.2 793.48 148 792.54 146.52C791.6 147.92 790.44 149.12 789.08 150.1L788 148.34C789.44 147.38 790.6 146.14 791.46 144.6ZM782 140.84L783.96 141.08C783.84 141.38 783.74 141.68 783.64 141.96H787.66V143.64C787.32 144.8 786.72 145.84 785.84 146.72C786.62 147.06 787.38 147.42 788.12 147.82L787 149.46C786.24 149 785.32 148.52 784.24 148.02C782.92 148.86 781.28 149.52 779.3 150.04L778.34 148.3C779.78 147.98 781.02 147.56 782.08 147.08C781.28 146.74 780.42 146.4 779.54 146.06C780.02 145.26 780.44 144.48 780.8 143.74H778.82V141.96H781.6C781.74 141.56 781.88 141.2 782 140.84ZM783.96 145.94C784.78 145.3 785.4 144.56 785.82 143.74H782.86C782.6 144.26 782.32 144.76 782.04 145.24C782.68 145.44 783.32 145.68 783.96 145.94ZM780.78 131.86C781.2 132.6 781.58 133.4 781.9 134.26L780.42 134.92C780.02 133.96 779.6 133.12 779.16 132.38L780.78 131.86ZM786.4 131.84L787.86 132.54C787.46 133.38 786.96 134.2 786.34 135L784.88 134.26C785.52 133.44 786.04 132.64 786.4 131.84ZM778.86 135.22H782.58V131.6H784.5V135.22H787.9V137.04H784.5V137.08C785.54 137.5 786.6 137.98 787.68 138.52L786.6 140.16C785.76 139.5 785.06 138.98 784.5 138.6V140.7H782.58V137.56C781.9 138.92 780.86 140.1 779.42 141.14L778.38 139.46C779.68 138.72 780.64 137.92 781.24 137.04H778.86V135.22ZM801.7 149.9H800L799.56 147.94C800.06 148.02 800.54 148.08 801 148.08C801.42 148.08 801.64 147.82 801.64 147.32V143.28C800.96 143.52 800.3 143.76 799.62 143.98L799.08 141.92C799.94 141.72 800.8 141.48 801.64 141.2V137.2H799.48V135.18H801.64V131.64H803.68V135.18H805.38V137.2H803.68V140.38C804.24 140.12 804.78 139.86 805.3 139.58V141.62C804.74 141.9 804.2 142.16 803.68 142.42V147.84C803.68 149.2 803.02 149.9 801.7 149.9ZM808.02 141.28C807.78 144.62 807.02 147.56 805.76 150.12L804.12 148.66C805.42 146.12 806.08 142.84 806.1 138.84V132.2H817.06V137.5H813.46V139.38H817.5V141.28H813.46V143.2H816.62V150.08H814.7V149.26H810.2V150.08H808.28V143.2H811.42V141.28H808.02ZM811.42 137.5H808.12V138.84C808.12 139.02 808.1 139.2 808.1 139.38H811.42V137.5ZM810.2 147.42H814.7V145.04H810.2V147.42ZM815.02 134.1H808.12V135.66H815.02V134.1Z'
          fill='black'
        />
        <defs>
          <radialGradient
            id='paint0_radial_403_950'
            cx='0'
            cy='0'
            r='1'
            gradientUnits='userSpaceOnUse'
            gradientTransform='translate(788.994 431.671) rotate(-13.1921) scale(275.55 136.612)'>
            <stop offset='0.4694' stopColor='#0C40DE' />
            <stop offset='0.9724' stopColor='white' />
          </radialGradient>
          <linearGradient
            id='paint1_linear_403_950'
            x1='816.938'
            y1='290.957'
            x2='840.482'
            y2='248.278'
            gradientUnits='userSpaceOnUse'>
            <stop offset='0.1783' stopColor='#F25445' />
            <stop offset='0.9724' stopColor='white' />
          </linearGradient>
          <clipPath id='clip0_403_950'>
            <rect
              width='526'
              height='353.171'
              fill='white'
              transform='translate(526 215)'
            />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
};
// 无数据

const noDataIcon = () => {
  return (
    <div
      className='mt-[1.29rem] overflow-y-hidden  bg-white h-full flex justify-center'
      style={{ borderRadius: '1.78571rem', alignItems: 'center' }}>
      <svg
        width='90%'
        height='90%'
        viewBox='0 0 1612 758'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
        xmlnsXlink='http://www.w3.org/1999/xlink'>
        <rect width='1612' height='758' rx='20' fill='white' />
        <g clipPath='url(#clip0_400_386)'>
          <path
            d='M644.385 485.795L790.539 571.211L937.462 486.564L789.769 401.148L644.385 485.795Z'
            fill='#F1F1F1'
          />
          <path
            d='M648.23 412.691L794.384 498.107L941.307 413.461L793.615 328.044L648.23 412.691Z'
            fill='#F8F8F8'
          />
          <path
            d='M794.385 498.107V529.657L941.308 444.241V413.46L794.385 498.107Z'
            fill='#DCDEDE'
          />
          <path
            d='M795.154 497.337V528.888L648.23 443.471V412.691L795.154 497.337Z'
            fill='#EAEBEB'
          />
          <path
            d='M840.538 226.468L757.461 274.948V426.542L840.538 378.063V226.468Z'
            fill='#DCDEDE'
          />
          <path
            d='M708.23 394.222L757.461 426.542V274.948L708.23 250.323V394.222Z'
            fill='#EAEBEB'
          />
          <path
            d='M710.539 391.121L754.385 418.078V277.256L710.539 255.972V391.121Z'
            fill='#DCDEDE'
          />
          <path
            d='M710.539 264.151L754.385 288.799V281.104L710.539 259.819V264.151Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 276.694L754.385 301.342V293.954L710.539 272.362V276.694Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 289.237L754.385 313.885V306.805L710.539 284.905V289.237Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 301.781L754.385 326.428V319.656L710.539 297.448V301.781Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 314.324L754.385 338.971V332.507L710.539 309.991V314.324Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 326.867L754.385 351.514V345.358L710.539 322.534V326.867Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 339.41L754.385 364.057V358.209L710.539 335.077V339.41Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 351.953L754.385 376.601V371.06L710.539 347.621V351.953Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 364.496L754.385 389.143V383.911L710.539 360.164V364.496Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 377.039L754.385 401.686V396.762L710.539 372.707V377.039Z'
            fill='#F8F8F8'
          />
          <path
            d='M710.539 389.582L754.385 414.229V409.612L710.539 385.25V389.582Z'
            fill='#F8F8F8'
          />
          <path
            d='M757.461 274.948L840.538 226.468L791.307 208L708.23 250.323L757.461 274.948Z'
            fill='#F8F8F8'
          />
          <path
            d='M906.692 250.323L823.615 298.803V450.397L906.692 401.918V250.323Z'
            fill='#DCDEDE'
          />
          <path
            d='M774.385 418.077L823.616 450.397V298.803L774.385 274.178V418.077Z'
            fill='#EAEBEB'
          />
          <path
            d='M776.693 414.976L820.54 441.933V301.111L776.693 279.827V414.976Z'
            fill='#DCDEDE'
          />
          <path
            d='M776.693 288.006L820.54 312.654V304.959L776.693 283.674V288.006Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 300.549L820.54 325.197V317.809L776.693 296.217V300.549Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 313.092L820.54 337.74V330.66L776.693 308.76V313.092Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 325.636L820.54 350.283V343.511L776.693 321.303V325.636Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 338.179L820.54 362.826V356.362L776.693 333.846V338.179Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 350.722L820.54 375.369V369.213L776.693 346.389V350.722Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 363.264L820.54 387.912V382.064L776.693 358.932V363.264Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 375.808L820.54 400.455V394.915L776.693 371.476V375.808Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 388.351L820.54 412.998V407.766L776.693 384.019V388.351Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 400.894L820.54 425.541V420.617L776.693 396.562V400.894Z'
            fill='#F8F8F8'
          />
          <path
            d='M776.693 413.437L820.54 438.084V433.467L776.693 409.104V413.437Z'
            fill='#F8F8F8'
          />
          <path
            d='M823.616 298.803L906.692 250.323L857.462 231.855L774.385 274.178L823.616 298.803Z'
            fill='#F8F8F8'
          />
          <path
            d='M823.576 399.901L705.707 297.648V276.102L823.576 378.355V399.901Z'
            fill='#F25445'
          />
          <path
            d='M732.569 320.949L723.184 312.808L705.707 276.102L715.099 284.244L732.569 320.949Z'
            fill='white'
          />
          <path
            d='M705.707 286.09L715.099 305.79L705.707 297.648V286.09Z'
            fill='white'
          />
          <path
            d='M750.608 336.593L741.215 328.452L723.746 291.746L733.131 299.895L750.608 336.593Z'
            fill='white'
          />
          <path
            d='M768.645 352.245L759.252 344.096L741.783 307.398L751.168 315.539L768.645 352.245Z'
            fill='white'
          />
          <path
            d='M786.676 367.89L777.291 359.748L759.822 323.042L769.207 331.184L786.676 367.89Z'
            fill='white'
          />
          <path
            d='M804.715 383.541L795.33 375.392L777.854 338.687L787.246 346.836L804.715 383.541Z'
            fill='white'
          />
          <path
            d='M822.754 399.186L813.37 391.044L795.893 354.338L805.285 362.48L822.754 399.186Z'
            fill='white'
          />
          <path
            d='M823.576 378.355L814.191 370.214L823.576 389.89V378.355Z'
            fill='white'
          />
          <path
            d='M824.615 349.106L706.477 319.964V298.418L824.615 327.559V349.106Z'
            fill='#F25445'
          />
          <path
            d='M733.4 326.605L723.992 324.281L706.477 298.418L715.884 300.734L733.4 326.605Z'
            fill='white'
          />
          <path
            d='M706.477 308.406L715.884 322.281L706.477 319.964V308.406Z'
            fill='white'
          />
          <path
            d='M751.476 331.061L742.068 328.745L724.553 302.874L733.968 305.197L751.476 331.061Z'
            fill='white'
          />
          <path
            d='M769.554 335.524L760.146 333.2L742.639 307.336L752.046 309.653L769.554 335.524Z'
            fill='white'
          />
          <path
            d='M787.638 339.979L778.223 337.663L760.715 311.792L770.123 314.116L787.638 339.979Z'
            fill='white'
          />
          <path
            d='M805.714 344.443L796.299 342.119L778.791 316.255L788.199 318.572L805.714 344.443Z'
            fill='white'
          />
          <path
            d='M823.792 348.898L814.385 346.574L796.869 320.711L806.277 323.035L823.792 348.898Z'
            fill='white'
          />
          <path
            d='M824.615 327.559L815.207 325.235L824.615 339.087V327.559Z'
            fill='white'
          />
          <path
            d='M906.568 399.094L823.398 349.206V327.66L906.568 377.547V399.094Z'
            fill='#F25445'
          />
          <path
            d='M842.352 360.572L835.729 356.601L823.398 327.66L830.022 331.63L842.352 360.572Z'
            fill='white'
          />
          <path
            d='M823.398 337.648L830.022 353.177L823.398 349.206V337.648Z'
            fill='white'
          />
          <path
            d='M855.075 368.206L848.452 364.235L836.129 335.293L842.752 339.264L855.075 368.206Z'
            fill='white'
          />
          <path
            d='M867.805 375.847L861.182 371.868L848.852 342.927L855.475 346.905L867.805 375.847Z'
            fill='white'
          />
          <path
            d='M880.53 383.48L873.907 379.502L861.584 350.561L868.207 354.539L880.53 383.48Z'
            fill='white'
          />
          <path
            d='M893.26 391.114L886.637 387.135L874.307 358.194L880.93 362.172L893.26 391.114Z'
            fill='white'
          />
          <path
            d='M905.983 398.747L899.36 394.777L887.037 365.828L893.66 369.806L905.983 398.747Z'
            fill='white'
          />
          <path
            d='M906.568 377.547L899.945 373.577L906.568 389.082V377.547Z'
            fill='white'
          />
          <path
            d='M906.322 329.96L823.398 399.224V377.678L906.322 308.414V329.96Z'
            fill='#F25445'
          />
          <path
            d='M842.298 383.442L835.691 388.959L823.398 377.678L830.006 372.161L842.298 383.442Z'
            fill='white'
          />
          <path
            d='M823.398 387.667L830.006 393.707L823.398 399.225V387.667Z'
            fill='white'
          />
          <path
            d='M854.982 372.846L848.382 378.355L836.09 367.082L842.69 361.564L854.982 372.846Z'
            fill='white'
          />
          <path
            d='M867.675 362.242L861.068 367.759L848.775 356.478L855.383 350.96L867.675 362.242Z'
            fill='white'
          />
          <path
            d='M880.367 351.645L873.759 357.163L861.467 345.882L868.074 340.364L880.367 351.645Z'
            fill='white'
          />
          <path
            d='M893.052 341.049L886.452 346.567L874.16 335.285L880.76 329.768L893.052 341.049Z'
            fill='white'
          />
          <path
            d='M905.744 330.445L899.136 335.963L886.844 324.682L893.451 319.172L905.744 330.445Z'
            fill='white'
          />
          <path
            d='M906.321 308.414L899.721 313.931L906.321 319.949V308.414Z'
            fill='white'
          />
          <path
            d='M832.661 571.264C688.176 603.038 559.107 573.304 544.376 504.84C533.046 452.144 592.769 391.86 686.776 352.399C609.092 387.597 560.699 439.162 570.476 484.609C583.692 546.07 698.292 573.057 826.423 544.878C954.553 516.698 1047.71 444.033 1034.49 382.572C1024.46 335.924 972.476 309.645 884.069 311.753C989.553 306.328 1056.01 335.731 1067.63 389.782C1082.35 458.23 977.153 539.491 832.661 571.264Z'
            fill='url(#paint0_radial_400_386)'
          />
          <path
            d='M804.537 487.542L799.952 484.979L790.883 493.105L782.375 488.35L791.444 480.224L775.137 471.112L783.867 463.287L827.275 444.518L836.314 449.566L796.075 467.357L802.337 470.851L810.391 463.633L818.637 468.234L810.583 475.453L815.167 478.015L804.537 487.542Z'
            fill='#DCDEDE'
          />
          <path
            d='M690.691 421.363L686.106 418.801L677.037 426.927L668.529 422.171L677.599 414.045L661.291 404.934L670.022 397.108L713.429 378.34L722.468 383.388L682.229 401.179L688.491 404.673L696.545 397.455L704.791 402.056L696.737 409.274L701.322 411.837L690.691 421.363Z'
            fill='#DCDEDE'
          />
          <path
            d='M716.683 451.721C715.906 449.697 716.375 447.388 718.106 444.795C719.837 442.202 722.799 439.501 727.014 436.692C731.222 433.883 735.529 431.721 739.945 430.205C744.36 428.689 748.56 427.881 752.545 427.766C756.529 427.65 760.045 428.212 763.106 429.436C766.129 430.651 768.037 432.275 768.814 434.299C769.591 436.323 769.122 438.631 767.391 441.225C765.66 443.818 762.691 446.519 758.483 449.328C754.275 452.136 749.96 454.299 745.552 455.815C741.137 457.33 736.937 458.138 732.952 458.254C728.968 458.369 725.468 457.815 722.437 456.599C719.383 455.376 717.468 453.752 716.683 451.721ZM738.26 450.382C741.068 449.581 744.429 447.873 748.337 445.257C752.245 442.648 754.599 440.532 755.406 438.924C756.206 437.315 755.76 436.169 754.06 435.484C752.322 434.791 750.052 434.838 747.245 435.646C744.437 436.446 741.075 438.154 737.168 440.771C733.26 443.379 730.899 445.495 730.099 447.104C729.299 448.712 729.76 449.866 731.499 450.559C733.199 451.244 735.452 451.182 738.26 450.382Z'
            fill='#DCDEDE'
          />
          <path
            d='M649.438 392.283L653.938 389.621L665.637 395.831L661.13 398.493L649.438 392.283Z'
            fill='#A1A1A1'
          />
          <path
            d='M677.445 419.994L681.945 417.331L682.722 428.704L678.215 431.367L677.445 419.994Z'
            fill='#9D9D9D'
          />
          <path
            d='M660.445 388.397L664.953 385.735L680.361 393.907L675.861 396.577L660.445 388.397Z'
            fill='#A1A1A1'
          />
          <path
            d='M675.861 396.577L680.361 393.907L681.046 404.003L676.546 406.673L675.861 396.577Z'
            fill='#9D9D9D'
          />
          <path
            d='M685.107 411.221L689.615 408.551L690.515 421.879L686.015 424.549L685.107 411.221Z'
            fill='#9D9D9D'
          />
          <path
            d='M676.545 406.673L681.045 404.003L689.614 408.551L685.106 411.221L676.545 406.673Z'
            fill='#A1A1A1'
          />
          <path
            d='M653.676 361.072L658.176 358.409L675.06 367.374L670.56 370.037L653.676 361.072Z'
            fill='#A1A1A1'
          />
          <path
            d='M670.561 370.037L675.061 367.374L653.938 389.621L649.438 392.283L670.561 370.037Z'
            fill='#818181'
          />
          <path
            d='M675.861 396.577L676.545 406.673L685.107 411.221L686.015 424.549L677.445 419.993L678.215 431.367L662.322 422.933L661.545 411.56L631.069 395.384L630.33 384.434L653.676 361.072L670.561 370.037L649.438 392.283L661.13 398.493L660.445 388.397L675.861 396.577Z'
            fill='#A3A3A3'
          />
          <mask
            id='mask0_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='630'
            y='361'
            width='57'
            height='71'>
            <path
              d='M675.861 396.577L676.545 406.673L685.107 411.221L686.015 424.549L677.445 419.993L678.215 431.367L662.322 422.933L661.545 411.56L631.069 395.384L630.33 384.434L653.676 361.072L670.561 370.037L649.438 392.283L661.13 398.493L660.445 388.397L675.861 396.577Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask0_400_386)'>
            <mask
              id='mask1_400_386'
              style={{ maskType: 'luminance' }}
              maskUnits='userSpaceOnUse'
              x='630'
              y='361'
              width='57'
              height='71'>
              <path
                d='M686.015 424.549L677.445 419.993L678.215 431.367L662.322 422.933L661.545 411.56L631.069 395.384L630.33 384.434L653.676 361.072L670.561 370.037L649.438 392.283L661.13 398.493L660.445 388.397L675.861 396.577L676.545 406.673L685.107 411.221L686.015 424.549Z'
                fill='white'
              />
            </mask>
            <g mask='url(#mask1_400_386)'>
              <rect
                x='630.328'
                y='361.076'
                width='56.1538'
                height='70.7953'
                fill='url(#pattern0_400_386)'
              />
            </g>
          </g>
          <mask
            id='mask2_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='719'
            y='437'
            width='7'
            height='4'>
            <path
              d='M719.43 440.386L723.93 437.723C724.222 437.715 724.284 437.708 724.337 437.708C724.422 437.7 724.476 437.692 724.537 437.685C724.622 437.669 724.676 437.662 724.73 437.646C724.799 437.631 724.86 437.615 724.914 437.6C724.976 437.585 725.03 437.569 725.091 437.546C725.16 437.523 725.214 437.5 725.268 437.485C725.345 437.454 725.391 437.431 725.445 437.415C725.522 437.377 725.568 437.354 725.622 437.331C725.691 437.292 725.745 437.261 725.791 437.238L721.291 439.901C721.245 439.932 721.191 439.955 721.122 439.993C721.076 440.016 721.022 440.039 720.945 440.078C720.891 440.101 720.845 440.124 720.768 440.147C720.714 440.17 720.66 440.186 720.591 440.209C720.537 440.224 720.476 440.247 720.414 440.262C720.36 440.278 720.299 440.293 720.23 440.309C720.176 440.324 720.114 440.332 720.037 440.347C719.984 440.355 719.922 440.363 719.837 440.37C719.784 440.37 719.73 440.378 719.43 440.386Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask2_400_386)'>
            <path
              d='M719.43 440.385L723.93 437.723C724.099 437.715 724.184 437.715 724.245 437.708L719.745 440.37C719.691 440.378 719.599 440.385 719.43 440.385Z'
              fill='#8C8C8C'
            />
            <path
              d='M719.744 440.378L724.244 437.715C724.29 437.715 724.313 437.708 724.336 437.708C724.406 437.7 724.46 437.692 724.506 437.685L720.006 440.347C719.96 440.355 719.906 440.362 719.836 440.37C719.813 440.37 719.79 440.37 719.744 440.378Z'
              fill='#8A8A8A'
            />
            <path
              d='M720.014 440.347L724.514 437.684C724.521 437.684 724.529 437.684 724.537 437.684C724.621 437.669 724.675 437.661 724.729 437.646C724.775 437.638 724.821 437.623 724.86 437.615L720.36 440.278C720.321 440.285 720.275 440.301 720.229 440.309C720.175 440.324 720.114 440.332 720.037 440.347C720.029 440.347 720.021 440.347 720.014 440.347Z'
              fill='#878787'
            />
            <path
              d='M720.367 440.278L724.867 437.615C724.883 437.608 724.906 437.607 724.921 437.6C724.983 437.584 725.036 437.569 725.098 437.546C725.167 437.523 725.221 437.5 725.275 437.484L720.775 440.147C720.721 440.17 720.667 440.185 720.598 440.208C720.544 440.224 720.483 440.247 720.421 440.262C720.406 440.27 720.383 440.27 720.367 440.278Z'
              fill='#858585'
            />
            <path
              d='M720.775 440.147L725.275 437.485C725.345 437.454 725.398 437.438 725.445 437.415C725.522 437.377 725.568 437.354 725.622 437.331C725.691 437.292 725.745 437.261 725.791 437.238L721.291 439.901C721.245 439.932 721.191 439.955 721.122 439.993C721.075 440.016 721.022 440.039 720.945 440.078C720.898 440.101 720.845 440.124 720.775 440.147Z'
              fill='#828282'
            />
          </g>
          <mask
            id='mask3_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='705'
            y='401'
            width='19'
            height='40'>
            <path
              d='M708.747 404.657L713.247 401.995C713.193 402.025 713.147 402.056 713.093 402.087C713.031 402.125 712.985 402.156 712.939 402.195C712.877 402.241 712.831 402.279 712.785 402.318C712.716 402.372 712.677 402.41 712.631 402.449C712.57 402.51 712.524 402.549 712.477 402.595C712.416 402.656 712.37 402.703 712.331 402.749C712.27 402.818 712.231 402.864 712.185 402.91C712.131 402.972 712.085 403.026 712.047 403.087C711.893 403.295 711.747 403.526 711.608 403.78C711.547 403.888 711.493 404.003 711.439 404.118C711.424 404.157 711.4 404.195 711.385 404.242C710.462 406.381 710.162 409.913 710.493 414.822C710.593 416.292 710.739 417.693 710.924 419.024C711.07 420.063 711.231 420.994 711.416 421.887C711.424 421.933 711.431 421.971 711.439 422.017C711.524 422.425 711.616 422.833 711.708 423.226C711.831 423.726 711.962 424.21 712.093 424.688C712.193 425.042 712.3 425.38 712.408 425.719C712.462 425.888 712.516 426.057 712.577 426.219C712.716 426.611 712.854 426.996 713.008 427.366C713.108 427.612 713.208 427.858 713.316 428.097C713.47 428.458 713.639 428.804 713.808 429.143C713.924 429.366 714.039 429.589 714.154 429.805C714.385 430.236 714.631 430.659 714.877 431.059C715 431.259 715.131 431.459 715.254 431.652C715.954 432.698 716.716 433.629 717.531 434.43C718.531 435.407 719.585 436.184 720.716 436.784C720.8 436.831 720.877 436.869 720.962 436.907C721.093 436.969 721.147 437 721.2 437.023C721.231 437.038 721.262 437.054 721.293 437.069C721.339 437.092 721.393 437.115 721.439 437.138C721.47 437.154 721.508 437.169 721.539 437.177C721.585 437.2 721.631 437.215 721.677 437.231C721.708 437.246 721.747 437.254 721.777 437.269C721.824 437.285 721.862 437.3 721.908 437.323C721.939 437.338 721.977 437.346 722.008 437.361C722.054 437.377 722.093 437.392 722.139 437.408C722.17 437.415 722.2 437.431 722.239 437.438C722.285 437.454 722.323 437.469 722.37 437.477C722.4 437.485 722.431 437.5 722.462 437.508C722.508 437.523 722.547 437.531 722.593 437.546C722.624 437.554 722.654 437.562 722.685 437.569C722.731 437.577 722.77 437.592 722.816 437.6C722.847 437.608 722.877 437.615 722.908 437.623C722.954 437.631 723 437.639 723.039 437.646C723.17 437.669 723.231 437.677 723.285 437.685C723.393 437.7 723.462 437.708 723.531 437.715C723.639 437.723 723.685 437.731 723.731 437.731C723.847 437.731 723.893 437.739 723.931 437.739L719.431 440.401C719.393 440.401 719.347 440.401 719.231 440.393C719.185 440.393 719.139 440.386 719.031 440.378C718.962 440.37 718.893 440.363 718.785 440.347C718.731 440.34 718.677 440.332 718.539 440.309C718.493 440.301 718.447 440.293 718.408 440.286C718.377 440.278 718.347 440.27 718.316 440.263C718.27 440.255 718.231 440.239 718.185 440.232C718.154 440.224 718.124 440.216 718.093 440.209C718.054 440.201 718.008 440.186 717.962 440.17C717.931 440.163 717.9 440.155 717.87 440.139C717.824 440.124 717.785 440.116 717.739 440.101C717.708 440.093 717.677 440.078 717.639 440.07C717.593 440.055 717.554 440.039 717.508 440.024C717.477 440.009 717.439 440.001 717.408 439.986C717.362 439.97 717.324 439.955 717.277 439.932C717.247 439.916 717.208 439.909 717.177 439.893C717.131 439.878 717.085 439.855 717.039 439.839C717.008 439.824 716.97 439.809 716.939 439.801C716.893 439.778 716.839 439.755 716.793 439.732C716.762 439.716 716.731 439.701 716.7 439.685C716.647 439.662 716.593 439.632 716.462 439.57C716.377 439.532 716.3 439.485 716.216 439.447C715.085 438.847 714.031 438.069 713.031 437.092C712.216 436.292 711.454 435.361 710.754 434.314C710.624 434.122 710.5 433.922 710.377 433.722C710.131 433.322 709.885 432.906 709.654 432.467C709.539 432.252 709.424 432.029 709.308 431.806C709.139 431.467 708.977 431.121 708.816 430.759C708.708 430.521 708.608 430.274 708.508 430.028C708.362 429.659 708.216 429.274 708.077 428.881C708.024 428.72 707.962 428.551 707.908 428.381C707.8 428.043 707.693 427.704 707.593 427.35C707.454 426.873 707.324 426.388 707.208 425.888C707.116 425.496 707.024 425.088 706.939 424.68C706.931 424.634 706.924 424.595 706.916 424.549C706.731 423.656 706.57 422.725 706.424 421.686C706.239 420.355 706.093 418.955 705.993 417.485C705.662 412.568 705.954 409.043 706.885 406.904C706.9 406.866 706.924 406.827 706.939 406.781C706.993 406.666 707.047 406.55 707.108 406.442C707.247 406.188 707.385 405.958 707.547 405.75C707.593 405.696 707.631 405.642 707.685 405.573C707.724 405.527 707.77 405.473 707.831 405.411C707.87 405.365 707.916 405.319 707.977 405.257C708.024 405.219 708.062 405.173 708.131 405.111C708.177 405.073 708.216 405.034 708.285 404.98C708.331 404.942 708.377 404.903 708.439 404.857C708.485 404.819 708.539 404.788 708.593 404.749C708.647 404.719 708.693 404.688 708.747 404.657Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask3_400_386)'>
            <path
              d='M708.746 404.657L713.246 401.995C713.192 402.025 713.146 402.056 713.092 402.087C713.031 402.125 712.984 402.156 712.938 402.195C712.877 402.241 712.831 402.279 712.784 402.318C712.715 402.372 712.677 402.41 712.631 402.449C712.569 402.51 712.523 402.549 712.477 402.595C712.415 402.656 712.369 402.703 712.331 402.749C712.269 402.818 712.231 402.864 712.184 402.91C712.131 402.972 712.084 403.026 712.046 403.087C712.023 403.118 712 403.149 711.984 403.172L707.484 405.834C707.507 405.804 707.531 405.773 707.546 405.75C707.592 405.696 707.631 405.642 707.684 405.573C707.723 405.527 707.769 405.473 707.831 405.411C707.869 405.365 707.915 405.319 707.977 405.257C708.023 405.219 708.061 405.173 708.131 405.111C708.177 405.073 708.215 405.034 708.284 404.98C708.331 404.942 708.377 404.903 708.438 404.857C708.484 404.819 708.538 404.788 708.592 404.749C708.646 404.719 708.692 404.688 708.746 404.657Z'
              fill='#828282'
            />
            <path
              d='M707.485 405.834L711.985 403.172C711.87 403.333 711.754 403.51 711.654 403.703L707.154 406.365C707.262 406.173 707.37 405.996 707.485 405.834Z'
              fill='#858585'
            />
            <path
              d='M707.154 406.358L711.654 403.695C711.638 403.718 711.623 403.741 711.615 403.772C711.554 403.88 711.5 403.995 711.446 404.111C711.431 404.149 711.408 404.188 711.392 404.234C711.385 404.249 711.385 404.257 711.377 404.272L706.877 406.935C706.885 406.92 706.885 406.912 706.892 406.896C706.908 406.858 706.931 406.82 706.946 406.773C707 406.658 707.054 406.543 707.115 406.435C707.123 406.412 707.138 406.389 707.154 406.358Z'
              fill='#878787'
            />
            <path
              d='M706.877 406.935L711.377 404.272C711.285 404.488 711.2 404.711 711.123 404.95L706.623 407.612C706.7 407.374 706.785 407.15 706.877 406.935Z'
              fill='#8A8A8A'
            />
            <path
              d='M706.623 407.612L711.123 404.95C711.039 405.219 710.954 405.496 710.885 405.796L706.385 408.459C706.454 408.159 706.539 407.882 706.623 407.612Z'
              fill='#8C8C8C'
            />
            <path
              d='M706.385 408.458L710.885 405.796C710.801 406.142 710.731 406.519 710.662 406.912L706.162 409.574C706.231 409.182 706.301 408.812 706.385 408.458Z'
              fill='#8F8F8F'
            />
            <path
              d='M706.161 409.574L710.661 406.912C710.584 407.396 710.523 407.92 710.477 408.474L705.977 411.136C706.023 410.582 706.084 410.059 706.161 409.574Z'
              fill='#919191'
            />
            <path
              d='M705.977 411.137L710.477 408.474C710.415 409.205 710.377 409.998 710.369 410.844L705.869 413.507C705.885 412.66 705.915 411.868 705.977 411.137Z'
              fill='#949494'
            />
            <path
              d='M705.876 413.507L710.376 410.844C710.368 412.052 710.407 413.376 710.507 414.815C710.507 414.83 710.507 414.853 710.507 414.869L706.007 417.531C706.007 417.516 706.007 417.493 706.007 417.477C705.907 416.038 705.861 414.715 705.876 413.507Z'
              fill='#969696'
            />
            <path
              d='M706.008 417.531L710.508 414.869C710.608 416.323 710.746 417.708 710.931 419.016C710.962 419.209 710.985 419.393 711.016 419.578L706.516 422.241C706.485 422.056 706.462 421.871 706.431 421.679C706.246 420.371 706.108 418.986 706.008 417.531Z'
              fill='#999999'
            />
            <path
              d='M706.516 422.248L711.016 419.586C711.139 420.402 711.277 421.156 711.423 421.887C711.431 421.933 711.439 421.971 711.446 422.018C711.531 422.425 711.623 422.833 711.716 423.226C711.731 423.28 711.739 423.326 711.754 423.38L707.254 426.042C707.239 425.988 707.231 425.942 707.216 425.888C707.123 425.496 707.031 425.088 706.946 424.68C706.939 424.634 706.931 424.595 706.923 424.549C706.777 423.818 706.639 423.064 706.516 422.248Z'
              fill='#9C9C9C'
            />
            <path
              d='M707.262 426.042L711.762 423.379C711.869 423.826 711.985 424.264 712.108 424.688C712.208 425.042 712.316 425.38 712.423 425.719C712.477 425.888 712.531 426.057 712.592 426.219C712.646 426.373 712.7 426.519 712.754 426.673L708.254 429.335C708.2 429.189 708.146 429.035 708.092 428.881C708.039 428.72 707.977 428.551 707.923 428.381C707.816 428.043 707.708 427.704 707.608 427.35C707.485 426.919 707.369 426.488 707.262 426.042Z'
              fill='#9E9E9E'
            />
            <path
              d='M708.254 429.328L712.754 426.665C712.839 426.904 712.931 427.134 713.023 427.358C713.123 427.604 713.223 427.85 713.331 428.089C713.485 428.45 713.654 428.797 713.823 429.135C713.939 429.358 714.054 429.582 714.169 429.797C714.292 430.02 714.415 430.243 714.539 430.459L710.039 433.121C709.915 432.906 709.792 432.69 709.669 432.459C709.554 432.244 709.439 432.021 709.323 431.798C709.154 431.459 708.992 431.113 708.831 430.751C708.723 430.513 708.623 430.266 708.523 430.02C708.431 429.797 708.339 429.566 708.254 429.328Z'
              fill='#A1A1A1'
            />
            <path
              d='M710.031 433.121L714.531 430.459C714.647 430.659 714.77 430.859 714.885 431.052C715.008 431.252 715.139 431.452 715.262 431.644C715.893 432.591 716.57 433.437 717.3 434.176L712.8 436.838C712.077 436.092 711.393 435.245 710.762 434.307C710.631 434.114 710.508 433.914 710.385 433.714C710.262 433.522 710.147 433.322 710.031 433.121Z'
              fill='#A3A3A3'
            />
            <path
              d='M712.799 436.838L717.299 434.176C717.376 434.26 717.46 434.337 717.545 434.422C718.083 434.953 718.645 435.422 719.222 435.838L714.722 438.5C714.145 438.085 713.591 437.616 713.045 437.085C712.96 437 712.883 436.923 712.799 436.838Z'
              fill='#A1A1A1'
            />
            <path
              d='M714.715 438.5L719.215 435.838C719.615 436.123 720.023 436.384 720.446 436.623L715.946 439.285C715.53 439.047 715.115 438.785 714.715 438.5Z'
              fill='#9E9E9E'
            />
            <path
              d='M715.945 439.285L720.445 436.623C720.538 436.676 720.63 436.723 720.73 436.776C720.815 436.823 720.891 436.861 720.976 436.9C721.107 436.961 721.161 436.992 721.215 437.015C721.245 437.03 721.276 437.046 721.307 437.061C721.345 437.077 721.384 437.092 721.422 437.115L716.922 439.778C716.884 439.762 716.845 439.747 716.807 439.724C716.776 439.708 716.745 439.693 716.715 439.678C716.661 439.654 716.607 439.624 716.476 439.562C716.391 439.524 716.315 439.477 716.23 439.439C716.138 439.385 716.038 439.331 715.945 439.285Z'
              fill='#9C9C9C'
            />
            <path
              d='M716.922 439.77L721.422 437.107C721.43 437.115 721.445 437.115 721.453 437.123C721.483 437.138 721.522 437.154 721.553 437.161C721.599 437.184 721.645 437.2 721.691 437.215C721.722 437.231 721.76 437.238 721.791 437.254C721.837 437.269 721.876 437.284 721.922 437.307C721.953 437.323 721.991 437.331 722.022 437.346C722.045 437.354 722.06 437.361 722.083 437.369L717.583 440.032C717.56 440.024 717.545 440.016 717.522 440.008C717.491 439.993 717.453 439.985 717.422 439.97C717.376 439.955 717.337 439.939 717.291 439.916C717.26 439.901 717.222 439.893 717.191 439.878C717.145 439.862 717.099 439.839 717.053 439.824C717.022 439.808 716.983 439.793 716.953 439.785C716.945 439.785 716.93 439.778 716.922 439.77Z'
              fill='#999999'
            />
            <path
              d='M717.584 440.039L722.084 437.377C722.107 437.385 722.13 437.392 722.153 437.4C722.184 437.408 722.215 437.423 722.253 437.431C722.299 437.446 722.338 437.462 722.384 437.469C722.415 437.477 722.446 437.492 722.476 437.5C722.522 437.515 722.561 437.523 722.607 437.539C722.63 437.546 722.646 437.546 722.669 437.554L718.169 440.216C718.146 440.209 718.13 440.209 718.107 440.201C718.069 440.193 718.022 440.178 717.976 440.163C717.946 440.155 717.915 440.147 717.884 440.132C717.838 440.116 717.799 440.109 717.753 440.093C717.722 440.086 717.692 440.07 717.653 440.063C717.63 440.055 717.607 440.047 717.584 440.039Z'
              fill='#969696'
            />
            <path
              d='M718.162 440.209L722.662 437.546C722.67 437.546 722.685 437.554 722.693 437.554C722.739 437.562 722.777 437.577 722.824 437.585C722.854 437.593 722.885 437.6 722.916 437.608C722.962 437.616 723.008 437.623 723.047 437.631C723.054 437.631 723.062 437.631 723.062 437.631L718.562 440.294C718.554 440.294 718.554 440.294 718.547 440.294C718.501 440.286 718.454 440.278 718.416 440.27C718.385 440.263 718.354 440.255 718.324 440.247C718.277 440.24 718.239 440.224 718.193 440.217C718.185 440.217 718.17 440.217 718.162 440.209Z'
              fill='#949494'
            />
            <path
              d='M718.561 440.301L723.061 437.639C723.184 437.662 723.237 437.669 723.284 437.677C723.368 437.685 723.422 437.693 723.476 437.7L718.976 440.363C718.922 440.355 718.868 440.347 718.784 440.34C718.737 440.332 718.684 440.324 718.561 440.301Z'
              fill='#919191'
            />
            <path
              d='M718.977 440.363L723.477 437.7C723.492 437.7 723.515 437.7 723.53 437.708C723.638 437.716 723.684 437.723 723.73 437.723C723.784 437.723 723.823 437.723 723.853 437.723L719.353 440.386C719.323 440.386 719.284 440.386 719.23 440.386C719.184 440.386 719.138 440.378 719.03 440.37C719.015 440.37 719 440.363 718.977 440.363Z'
              fill='#8F8F8F'
            />
            <path
              d='M719.361 440.386L723.861 437.723C723.892 437.723 723.915 437.723 723.938 437.723L719.438 440.386C719.415 440.386 719.392 440.386 719.361 440.386Z'
              fill='#8C8C8C'
            />
          </g>
          <mask
            id='mask4_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='734'
            y='426'
            width='12'
            height='30'>
            <path
              d='M738.699 452.475L734.199 455.137C734.33 455.061 734.453 454.984 734.584 454.899C735.645 454.199 736.568 453.283 737.376 452.152C737.445 452.059 737.507 451.959 737.576 451.867C737.707 451.675 737.83 451.467 737.953 451.259C738.076 451.051 738.192 450.836 738.307 450.62C738.368 450.513 738.422 450.397 738.476 450.29C738.645 449.951 738.799 449.597 738.945 449.235C738.992 449.112 739.045 448.989 739.092 448.866C739.368 448.119 739.615 447.327 739.815 446.48C739.845 446.342 739.884 446.196 739.915 446.057C740.038 445.48 740.145 444.888 740.238 444.264C740.261 444.11 740.284 443.956 740.299 443.795C740.361 443.325 740.407 442.833 740.445 442.34C740.468 442.01 740.492 441.671 740.515 441.325C740.599 439.609 740.576 437.746 740.445 435.753C740.307 433.753 740.076 431.783 739.738 429.844C739.668 429.459 739.599 429.066 739.522 428.682L744.022 426.019C744.099 426.404 744.168 426.789 744.238 427.181C744.576 429.12 744.807 431.09 744.945 433.091C745.084 435.092 745.107 436.946 745.015 438.662C744.999 439.008 744.976 439.347 744.945 439.678C744.907 440.178 744.853 440.663 744.799 441.132C744.776 441.286 744.761 441.448 744.738 441.602C744.645 442.217 744.545 442.818 744.415 443.395C744.384 443.541 744.353 443.679 744.315 443.818C744.115 444.664 743.876 445.457 743.592 446.203C743.545 446.327 743.499 446.45 743.445 446.573C743.299 446.934 743.138 447.288 742.976 447.627C742.922 447.742 742.861 447.85 742.807 447.958C742.692 448.181 742.568 448.389 742.453 448.597C742.33 448.804 742.207 449.004 742.076 449.205C742.015 449.305 741.945 449.397 741.876 449.489C741.076 450.628 740.145 451.544 739.084 452.236C738.961 452.321 738.83 452.398 738.699 452.475Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask4_400_386)'>
            <path
              d='M739.514 428.689L744.014 426.027C744.091 426.412 744.16 426.796 744.229 427.189C744.298 427.566 744.36 427.951 744.414 428.328L739.914 430.99C739.852 430.613 739.791 430.228 739.729 429.851C739.66 429.459 739.591 429.074 739.514 428.689Z'
              fill='#9C9C9C'
            />
            <path
              d='M739.914 430.99L744.414 428.328C744.653 429.882 744.822 431.46 744.929 433.052L740.429 435.715C740.322 434.122 740.145 432.545 739.914 430.99Z'
              fill='#999999'
            />
            <path
              d='M740.43 435.715L744.93 433.052C744.93 433.068 744.93 433.083 744.93 433.098C745.022 434.46 745.06 435.753 745.053 436.984L740.553 439.647C740.56 438.416 740.522 437.123 740.43 435.761C740.43 435.746 740.43 435.73 740.43 435.715Z'
              fill='#969696'
            />
            <path
              d='M740.553 439.64L745.053 436.977C745.045 437.554 745.03 438.116 745.006 438.67C744.991 439.016 744.968 439.355 744.937 439.686C744.93 439.74 744.93 439.793 744.922 439.847L740.422 442.51C740.43 442.456 740.43 442.402 740.437 442.348C740.46 442.017 740.483 441.679 740.507 441.333C740.53 440.786 740.553 440.217 740.553 439.64Z'
              fill='#949494'
            />
            <path
              d='M740.422 442.51L744.922 439.847C744.883 440.286 744.837 440.717 744.783 441.14C744.76 441.294 744.745 441.455 744.722 441.609C744.698 441.763 744.675 441.925 744.652 442.079L740.152 444.741C740.175 444.587 740.199 444.433 740.222 444.272C740.245 444.118 740.268 443.964 740.283 443.802C740.345 443.379 740.391 442.948 740.422 442.51Z'
              fill='#919191'
            />
            <path
              d='M740.153 444.734L744.653 442.071C744.583 442.525 744.499 442.964 744.406 443.395C744.376 443.541 744.345 443.68 744.306 443.818C744.299 443.841 744.291 443.864 744.291 443.887L739.791 446.55C739.799 446.527 739.806 446.504 739.806 446.481C739.837 446.342 739.876 446.196 739.906 446.057C739.999 445.634 740.083 445.188 740.153 444.734Z'
              fill='#8F8F8F'
            />
            <path
              d='M739.792 446.558L744.292 443.895C744.161 444.434 744.015 444.957 743.854 445.449L739.354 448.112C739.515 447.619 739.661 447.096 739.792 446.558Z'
              fill='#8C8C8C'
            />
            <path
              d='M739.353 448.12L743.853 445.457C743.769 445.711 743.684 445.965 743.592 446.211C743.545 446.334 743.499 446.457 743.445 446.581C743.407 446.673 743.369 446.765 743.33 446.85L738.83 449.512C738.869 449.42 738.907 449.335 738.945 449.243C738.992 449.12 739.045 448.997 739.092 448.874C739.184 448.627 739.269 448.374 739.353 448.12Z'
              fill='#8A8A8A'
            />
            <path
              d='M738.83 449.505L743.33 446.842C743.214 447.112 743.099 447.373 742.968 447.62C742.914 447.735 742.853 447.843 742.799 447.95C742.76 448.027 742.714 448.104 742.676 448.181L738.176 450.844C738.214 450.767 738.26 450.69 738.299 450.613C738.36 450.505 738.414 450.39 738.468 450.282C738.599 450.036 738.722 449.774 738.83 449.505Z'
              fill='#878787'
            />
            <path
              d='M738.176 450.851L742.676 448.188C742.599 448.327 742.522 448.466 742.445 448.596C742.322 448.804 742.199 449.004 742.068 449.204C742.006 449.304 741.937 449.397 741.868 449.489C741.845 449.527 741.814 449.566 741.791 449.597L737.291 452.259C737.314 452.221 737.345 452.19 737.368 452.151C737.437 452.059 737.499 451.959 737.568 451.867C737.699 451.674 737.822 451.467 737.945 451.259C738.022 451.128 738.106 450.99 738.176 450.851Z'
              fill='#858585'
            />
            <path
              d='M737.292 452.267L741.792 449.604C741.007 450.69 740.107 451.567 739.084 452.244C738.961 452.329 738.83 452.406 738.699 452.482L734.199 455.145C734.33 455.068 734.453 454.991 734.584 454.906C735.607 454.229 736.507 453.352 737.292 452.267Z'
              fill='#828282'
            />
          </g>
          <path
            d='M739.515 428.689C739.592 429.074 739.661 429.458 739.73 429.851C740.069 431.79 740.3 433.76 740.438 435.761C740.577 437.761 740.6 439.616 740.507 441.332C740.492 441.678 740.469 442.017 740.438 442.348C740.4 442.848 740.346 443.333 740.292 443.802C740.269 443.956 740.254 444.118 740.23 444.272C740.138 444.887 740.038 445.487 739.907 446.064C739.877 446.211 739.846 446.349 739.807 446.488C739.607 447.334 739.369 448.127 739.084 448.873C739.038 448.996 738.992 449.119 738.938 449.243C738.792 449.604 738.63 449.958 738.469 450.297C738.415 450.412 738.354 450.52 738.3 450.628C738.184 450.851 738.061 451.059 737.946 451.266C737.823 451.474 737.7 451.674 737.569 451.874C737.507 451.974 737.438 452.067 737.369 452.159C736.569 453.298 735.638 454.214 734.577 454.906C734.4 455.022 734.223 455.129 734.038 455.237C733.392 455.599 732.707 455.891 731.977 456.107C731.869 456.137 731.769 456.168 731.661 456.191C731.346 456.276 731.015 456.345 730.684 456.399C730.461 456.438 730.238 456.468 730.007 456.491C729.907 456.499 729.8 456.514 729.7 456.522C729.523 456.538 729.423 456.545 729.323 456.545C729.207 456.553 729.107 456.553 729 456.561C728.792 456.568 728.723 456.568 728.654 456.568C728.438 456.568 728.369 456.568 728.307 456.568C728.092 456.561 728.023 456.561 727.954 456.561C727.73 456.553 727.661 456.545 727.592 456.545C727.377 456.53 727.3 456.522 727.23 456.522C727.015 456.507 726.938 456.499 726.861 456.484C726.63 456.461 726.53 456.445 726.43 456.438C726.238 456.414 726.123 456.399 726 456.376C725.8 456.345 725.715 456.33 725.63 456.314C725.407 456.276 725.33 456.261 725.254 456.245C725.023 456.199 724.946 456.184 724.877 456.168C724.638 456.114 724.569 456.099 724.492 456.084C724.254 456.022 724.177 456.007 724.107 455.991C723.861 455.93 723.792 455.907 723.715 455.891C723.469 455.822 723.392 455.799 723.323 455.783C723.077 455.714 723 455.683 722.915 455.66C722.669 455.583 722.584 455.56 722.5 455.53C722.246 455.445 722.13 455.406 722.007 455.368C721.823 455.306 721.692 455.252 721.554 455.206C721.338 455.122 721.238 455.091 721.146 455.052C720.907 454.96 720.823 454.922 720.738 454.891C720.484 454.791 720.407 454.752 720.323 454.722C720.261 454.698 720.207 454.668 720.146 454.645C720.069 454.614 719.984 454.575 719.907 454.545C719.846 454.521 719.784 454.491 719.723 454.468C719.646 454.429 719.561 454.398 719.477 454.36C719.415 454.329 719.354 454.306 719.292 454.275C719.215 454.237 719.13 454.198 719.054 454.167C718.992 454.137 718.93 454.114 718.869 454.083C718.784 454.044 718.7 454.006 718.623 453.967C718.561 453.937 718.5 453.914 718.446 453.883C718.361 453.837 718.269 453.798 718.184 453.752C717.915 453.621 717.815 453.567 717.715 453.513C717.438 453.367 717.292 453.29 717.146 453.213C716.992 453.129 716.838 453.052 716.684 452.967C715.454 452.298 714.254 451.567 713.084 450.782C712.646 450.489 712.207 450.181 711.777 449.874C711.2 449.458 710.638 449.027 710.077 448.588C709.938 448.481 709.8 448.365 709.661 448.25C707.577 446.557 705.592 444.664 703.715 442.571C703 441.771 702.307 440.947 701.638 440.108C701.569 440.024 701.507 439.939 701.438 439.855C700.93 439.208 700.446 438.554 699.969 437.885C699.777 437.615 699.592 437.346 699.4 437.077C699.269 436.884 699.146 436.7 699.015 436.507C698.823 436.215 698.63 435.93 698.446 435.63C698.33 435.453 698.215 435.268 698.107 435.091C696.554 432.598 695.177 429.943 693.977 427.134C693.769 426.65 693.569 426.165 693.377 425.672C693.338 425.565 693.292 425.465 693.254 425.357C693.007 424.718 692.769 424.079 692.546 423.433C692.507 423.333 692.477 423.225 692.438 423.125C692.346 422.856 692.254 422.579 692.169 422.302C692.077 422.025 691.992 421.74 691.907 421.463C691.823 421.171 691.73 420.886 691.654 420.594C690.561 416.784 689.877 412.86 689.6 408.812C689.546 408.012 689.507 407.235 689.492 406.481C689.484 406.288 689.484 406.104 689.484 405.919C689.477 405.55 689.477 405.18 689.484 404.819C689.484 404.642 689.492 404.457 689.492 404.28C689.523 402.864 689.63 401.533 689.807 400.294C689.854 399.986 689.9 399.678 689.954 399.378C690.061 398.778 690.184 398.208 690.33 397.654C690.369 397.516 690.4 397.377 690.438 397.247C690.777 396.031 691.207 394.93 691.73 393.938C693 391.521 694.707 389.844 696.846 388.897C697.138 388.766 697.438 388.651 697.746 388.551C697.954 388.482 698.161 388.42 698.369 388.366C698.9 388.228 699.454 388.128 700.023 388.066C700.123 388.059 700.23 388.043 700.33 388.035C700.507 388.02 700.615 388.012 700.715 388.012C700.823 388.005 700.923 388.005 701.023 387.997C701.238 387.989 701.3 387.989 701.369 387.989C701.584 387.989 701.654 387.989 701.715 387.989C701.938 387.997 702 387.997 702.069 387.997C702.292 388.005 702.361 388.012 702.43 388.012C702.654 388.028 702.723 388.035 702.792 388.035C703.015 388.059 703.092 388.066 703.169 388.074C703.415 388.097 703.523 388.112 703.63 388.128C703.807 388.151 703.915 388.166 704.03 388.182C704.254 388.22 704.323 388.228 704.4 388.243C704.63 388.289 704.7 388.297 704.777 388.312C705.015 388.359 705.084 388.374 705.154 388.389C705.4 388.443 705.469 388.459 705.538 388.474C705.784 388.536 705.854 388.551 705.923 388.566C706.169 388.628 706.246 388.651 706.315 388.666C706.569 388.736 706.638 388.759 706.715 388.782C706.961 388.859 707.038 388.882 707.115 388.905C707.369 388.982 707.454 389.013 707.538 389.044C707.792 389.128 707.907 389.167 708.015 389.205C708.277 389.297 708.384 389.336 708.492 389.374C708.73 389.467 708.815 389.498 708.907 389.528C709.161 389.628 709.238 389.659 709.323 389.69C709.384 389.713 709.446 389.736 709.5 389.759C709.577 389.79 709.661 389.821 709.738 389.859C709.8 389.882 709.869 389.913 709.93 389.936C710.007 389.967 710.084 390.005 710.161 390.036C710.223 390.067 710.292 390.09 710.354 390.121C710.43 390.152 710.507 390.19 710.592 390.229C710.654 390.259 710.723 390.282 710.784 390.313C710.861 390.352 710.946 390.39 711.023 390.421C711.084 390.452 711.146 390.482 711.215 390.506C711.292 390.544 711.377 390.583 711.454 390.621C711.515 390.652 711.584 390.683 711.646 390.713C711.73 390.752 711.815 390.798 711.9 390.836C711.961 390.867 712.023 390.898 712.084 390.929C712.177 390.975 712.269 391.021 712.369 391.075C712.661 391.221 712.792 391.29 712.923 391.36C714.184 392.029 715.384 392.737 716.546 393.507C716.838 393.699 717.13 393.891 717.415 394.092C717.846 394.392 718.277 394.699 718.7 395.015C718.984 395.223 719.261 395.438 719.546 395.654C719.823 395.869 720.107 396.092 720.377 396.315C720.93 396.762 721.477 397.231 722.015 397.708C723.5 399.024 724.93 400.448 726.307 401.987C727.061 402.825 727.784 403.687 728.484 404.572C728.946 405.165 729.4 405.765 729.846 406.373C730.284 406.981 730.715 407.604 731.138 408.243C731.554 408.874 731.961 409.52 732.354 410.174C732.846 410.99 733.323 411.829 733.777 412.675C733.869 412.845 733.961 413.014 734.046 413.191C734.492 414.045 734.923 414.922 735.33 415.807C735.577 416.338 735.815 416.877 736.046 417.423C736.2 417.785 736.354 418.154 736.5 418.516C736.646 418.878 736.792 419.247 736.93 419.616C737.069 419.986 737.207 420.355 737.338 420.724C737.669 421.648 737.969 422.587 738.246 423.525C738.3 423.71 738.354 423.902 738.407 424.087C738.623 424.841 738.823 425.603 739 426.365C739.046 426.557 739.092 426.75 739.13 426.934C739.215 427.296 739.292 427.65 739.361 428.012C739.43 428.25 739.469 428.466 739.515 428.689ZM722.5 438.8C723.93 436.915 724.446 432.998 724.046 427.065C723.646 421.125 722.538 416.354 720.73 412.745C720.054 411.39 719.307 410.19 718.5 409.143C718.43 409.059 718.361 408.966 718.3 408.882C718.107 408.643 717.915 408.412 717.715 408.189C717.692 408.158 717.661 408.135 717.638 408.104C717.515 407.966 717.392 407.835 717.269 407.712C716.207 406.627 715.1 405.788 713.907 405.157C713.807 405.103 713.73 405.065 713.654 405.026C713.623 405.011 713.592 404.996 713.561 404.98C713.507 404.957 713.461 404.926 713.407 404.903C713.377 404.888 713.338 404.872 713.307 404.857C713.261 404.834 713.215 404.811 713.161 404.795C713.123 404.78 713.092 404.765 713.061 404.749C713.015 404.726 712.969 404.711 712.923 404.688C712.884 404.672 712.854 404.657 712.815 404.649C712.769 404.634 712.723 404.611 712.684 404.595C712.646 404.58 712.615 404.572 712.577 404.557C712.53 404.542 712.492 404.526 712.446 404.511C712.407 404.495 712.377 404.488 712.338 404.472C712.3 404.457 712.254 404.441 712.215 404.434C712.184 404.426 712.146 404.411 712.115 404.403C712.069 404.388 712.03 404.38 711.992 404.365C711.961 404.357 711.93 404.349 711.892 404.341C711.846 404.334 711.807 404.318 711.761 404.311C711.73 404.303 711.7 404.295 711.669 404.288C711.623 404.28 711.584 404.272 711.546 404.264C711.515 404.257 711.484 404.249 711.454 404.249C711.407 404.241 711.369 404.234 711.323 404.226C711.192 404.203 711.146 404.203 711.092 404.195C710.892 404.172 710.754 404.164 710.623 404.164C710.523 404.164 710.484 404.164 710.438 404.164C710.33 404.172 710.277 404.172 710.215 404.18C710.077 404.195 709.954 404.211 709.83 404.234C709.761 404.249 709.7 404.264 709.646 404.272C709.569 404.295 709.515 404.311 709.461 404.326C709.384 404.349 709.33 404.372 709.277 404.388C709.2 404.418 709.146 404.441 709.1 404.465C709.023 404.495 708.977 404.526 708.923 404.549C708.854 404.588 708.8 404.611 708.746 404.642C708.684 404.68 708.638 404.711 708.584 404.742C708.523 404.78 708.477 404.811 708.43 404.849C708.369 404.895 708.323 404.934 708.277 404.972C708.207 405.026 708.169 405.065 708.123 405.103C708.061 405.165 708.015 405.203 707.969 405.249C707.907 405.311 707.861 405.357 707.823 405.403C707.761 405.473 707.723 405.519 707.677 405.565C707.623 405.627 707.577 405.68 707.538 405.742C707.384 405.95 707.238 406.181 707.1 406.435C707.038 406.542 706.984 406.658 706.93 406.773C706.915 406.812 706.892 406.85 706.877 406.896C705.954 409.035 705.654 412.568 705.984 417.477C706.084 418.947 706.23 420.347 706.415 421.679C706.561 422.717 706.723 423.649 706.907 424.541C706.915 424.587 706.923 424.626 706.93 424.672C707.015 425.08 707.107 425.488 707.2 425.88C707.323 426.38 707.454 426.865 707.584 427.342C707.684 427.696 707.792 428.035 707.9 428.373C707.954 428.543 708.007 428.712 708.069 428.874C708.207 429.266 708.346 429.651 708.5 430.02C708.6 430.266 708.7 430.513 708.807 430.751C708.961 431.113 709.13 431.459 709.3 431.798C709.415 432.021 709.53 432.244 709.646 432.459C709.877 432.89 710.123 433.314 710.369 433.714C710.492 433.914 710.623 434.114 710.746 434.306C711.446 435.353 712.207 436.284 713.023 437.084C714.023 438.062 715.077 438.839 716.207 439.439C716.292 439.485 716.369 439.524 716.454 439.562C716.584 439.624 716.638 439.654 716.692 439.678C716.723 439.693 716.754 439.708 716.784 439.724C716.83 439.747 716.884 439.77 716.93 439.793C716.961 439.808 717 439.824 717.03 439.831C717.077 439.855 717.123 439.87 717.169 439.885C717.2 439.901 717.238 439.908 717.269 439.924C717.315 439.939 717.354 439.955 717.4 439.978C717.43 439.993 717.469 440.001 717.5 440.016C717.546 440.032 717.584 440.047 717.63 440.062C717.661 440.07 717.692 440.085 717.73 440.093C717.777 440.108 717.815 440.124 717.861 440.132C717.892 440.139 717.923 440.155 717.954 440.162C718 440.178 718.038 440.185 718.084 440.201C718.115 440.208 718.146 440.216 718.177 440.224C718.223 440.232 718.261 440.247 718.307 440.255C718.338 440.262 718.369 440.27 718.4 440.278C718.446 440.285 718.492 440.293 718.53 440.301C718.661 440.324 718.723 440.332 718.777 440.339C718.884 440.355 718.954 440.362 719.023 440.37C719.13 440.378 719.177 440.385 719.223 440.385C719.338 440.385 719.384 440.393 719.423 440.393C719.715 440.385 719.777 440.378 719.83 440.378C719.915 440.37 719.969 440.362 720.03 440.355C720.115 440.339 720.169 440.332 720.223 440.316C720.292 440.301 720.354 440.285 720.407 440.27C720.469 440.255 720.523 440.239 720.584 440.216C720.654 440.193 720.707 440.17 720.761 440.155C720.838 440.124 720.884 440.101 720.938 440.085C721.015 440.047 721.061 440.024 721.115 440.001C721.184 439.962 721.238 439.931 721.284 439.908C721.354 439.87 721.4 439.839 721.454 439.801C721.507 439.762 721.561 439.724 721.607 439.693C721.661 439.654 721.715 439.616 721.761 439.577C721.823 439.524 721.869 439.485 721.915 439.447C721.977 439.385 722.023 439.347 722.061 439.3C722.123 439.239 722.169 439.193 722.207 439.147C722.269 439.077 722.307 439.031 722.354 438.985C722.415 438.908 722.454 438.854 722.5 438.8Z'
            fill='#A3A3A3'
          />
          <mask
            id='mask5_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='689'
            y='387'
            width='52'
            height='70'>
            <path
              d='M739.515 428.689C739.592 429.074 739.661 429.458 739.73 429.851C740.069 431.79 740.3 433.76 740.438 435.761C740.577 437.761 740.6 439.616 740.507 441.332C740.492 441.678 740.469 442.017 740.438 442.348C740.4 442.848 740.346 443.333 740.292 443.802C740.269 443.956 740.254 444.118 740.23 444.272C740.138 444.887 740.038 445.487 739.907 446.064C739.877 446.211 739.846 446.349 739.807 446.488C739.607 447.334 739.369 448.127 739.084 448.873C739.038 448.996 738.992 449.119 738.938 449.243C738.792 449.604 738.63 449.958 738.469 450.297C738.415 450.412 738.354 450.52 738.3 450.628C738.184 450.851 738.061 451.059 737.946 451.266C737.823 451.474 737.7 451.674 737.569 451.874C737.507 451.974 737.438 452.067 737.369 452.159C736.569 453.298 735.638 454.214 734.577 454.906C734.4 455.022 734.223 455.129 734.038 455.237C733.392 455.599 732.707 455.891 731.977 456.107C731.869 456.137 731.769 456.168 731.661 456.191C731.346 456.276 731.015 456.345 730.684 456.399C730.461 456.438 730.238 456.468 730.007 456.491C729.907 456.499 729.8 456.514 729.7 456.522C729.523 456.538 729.423 456.545 729.323 456.545C729.207 456.553 729.107 456.553 729 456.561C728.792 456.568 728.723 456.568 728.654 456.568C728.438 456.568 728.369 456.568 728.307 456.568C728.092 456.561 728.023 456.561 727.954 456.561C727.73 456.553 727.661 456.545 727.592 456.545C727.377 456.53 727.3 456.522 727.23 456.522C727.015 456.507 726.938 456.499 726.861 456.484C726.63 456.461 726.53 456.445 726.43 456.438C726.238 456.414 726.123 456.399 726 456.376C725.8 456.345 725.715 456.33 725.63 456.314C725.407 456.276 725.33 456.261 725.254 456.245C725.023 456.199 724.946 456.184 724.877 456.168C724.638 456.114 724.569 456.099 724.492 456.084C724.254 456.022 724.177 456.007 724.107 455.991C723.861 455.93 723.792 455.907 723.715 455.891C723.469 455.822 723.392 455.799 723.323 455.783C723.077 455.714 723 455.683 722.915 455.66C722.669 455.583 722.584 455.56 722.5 455.53C722.246 455.445 722.13 455.406 722.007 455.368C721.823 455.306 721.692 455.252 721.554 455.206C721.338 455.122 721.238 455.091 721.146 455.052C720.907 454.96 720.823 454.922 720.738 454.891C720.484 454.791 720.407 454.752 720.323 454.722C720.261 454.698 720.207 454.668 720.146 454.645C720.069 454.614 719.984 454.575 719.907 454.545C719.846 454.521 719.784 454.491 719.723 454.468C719.646 454.429 719.561 454.398 719.477 454.36C719.415 454.329 719.354 454.306 719.292 454.275C719.215 454.237 719.13 454.198 719.054 454.167C718.992 454.137 718.93 454.114 718.869 454.083C718.784 454.044 718.7 454.006 718.623 453.967C718.561 453.937 718.5 453.914 718.446 453.883C718.361 453.837 718.269 453.798 718.184 453.752C717.915 453.621 717.815 453.567 717.715 453.513C717.438 453.367 717.292 453.29 717.146 453.213C716.992 453.129 716.838 453.052 716.684 452.967C715.454 452.298 714.254 451.567 713.084 450.782C712.646 450.489 712.207 450.181 711.777 449.874C711.2 449.458 710.638 449.027 710.077 448.588C709.938 448.481 709.8 448.365 709.661 448.25C707.577 446.557 705.592 444.664 703.715 442.571C703 441.771 702.307 440.947 701.638 440.108C701.569 440.024 701.507 439.939 701.438 439.855C700.93 439.208 700.446 438.554 699.969 437.885C699.777 437.615 699.592 437.346 699.4 437.077C699.269 436.884 699.146 436.7 699.015 436.507C698.823 436.215 698.63 435.93 698.446 435.63C698.33 435.453 698.215 435.268 698.107 435.091C696.554 432.598 695.177 429.943 693.977 427.134C693.769 426.65 693.569 426.165 693.377 425.672C693.338 425.565 693.292 425.465 693.254 425.357C693.007 424.718 692.769 424.079 692.546 423.433C692.507 423.333 692.477 423.225 692.438 423.125C692.346 422.856 692.254 422.579 692.169 422.302C692.077 422.025 691.992 421.74 691.907 421.463C691.823 421.171 691.73 420.886 691.654 420.594C690.561 416.784 689.877 412.86 689.6 408.812C689.546 408.012 689.507 407.235 689.492 406.481C689.484 406.288 689.484 406.104 689.484 405.919C689.477 405.55 689.477 405.18 689.484 404.819C689.484 404.642 689.492 404.457 689.492 404.28C689.523 402.864 689.63 401.533 689.807 400.294C689.854 399.986 689.9 399.678 689.954 399.378C690.061 398.778 690.184 398.208 690.33 397.654C690.369 397.516 690.4 397.377 690.438 397.247C690.777 396.031 691.207 394.93 691.73 393.938C693 391.521 694.707 389.844 696.846 388.897C697.138 388.766 697.438 388.651 697.746 388.551C697.954 388.482 698.161 388.42 698.369 388.366C698.9 388.228 699.454 388.128 700.023 388.066C700.123 388.059 700.23 388.043 700.33 388.035C700.507 388.02 700.615 388.012 700.715 388.012C700.823 388.005 700.923 388.005 701.023 387.997C701.238 387.989 701.3 387.989 701.369 387.989C701.584 387.989 701.654 387.989 701.715 387.989C701.938 387.997 702 387.997 702.069 387.997C702.292 388.005 702.361 388.012 702.43 388.012C702.654 388.028 702.723 388.035 702.792 388.035C703.015 388.059 703.092 388.066 703.169 388.074C703.415 388.097 703.523 388.112 703.63 388.128C703.807 388.151 703.915 388.166 704.03 388.182C704.254 388.22 704.323 388.228 704.4 388.243C704.63 388.289 704.7 388.297 704.777 388.312C705.015 388.359 705.084 388.374 705.154 388.389C705.4 388.443 705.469 388.459 705.538 388.474C705.784 388.536 705.854 388.551 705.923 388.566C706.169 388.628 706.246 388.651 706.315 388.666C706.569 388.736 706.638 388.759 706.715 388.782C706.961 388.859 707.038 388.882 707.115 388.905C707.369 388.982 707.454 389.013 707.538 389.044C707.792 389.128 707.907 389.167 708.015 389.205C708.277 389.297 708.384 389.336 708.492 389.374C708.73 389.467 708.815 389.498 708.907 389.528C709.161 389.628 709.238 389.659 709.323 389.69C709.384 389.713 709.446 389.736 709.5 389.759C709.577 389.79 709.661 389.821 709.738 389.859C709.8 389.882 709.869 389.913 709.93 389.936C710.007 389.967 710.084 390.005 710.161 390.036C710.223 390.067 710.292 390.09 710.354 390.121C710.43 390.152 710.507 390.19 710.592 390.229C710.654 390.259 710.723 390.282 710.784 390.313C710.861 390.352 710.946 390.39 711.023 390.421C711.084 390.452 711.146 390.482 711.215 390.506C711.292 390.544 711.377 390.583 711.454 390.621C711.515 390.652 711.584 390.683 711.646 390.713C711.73 390.752 711.815 390.798 711.9 390.836C711.961 390.867 712.023 390.898 712.084 390.929C712.177 390.975 712.269 391.021 712.369 391.075C712.661 391.221 712.792 391.29 712.923 391.36C714.184 392.029 715.384 392.737 716.546 393.507C716.838 393.699 717.13 393.891 717.415 394.092C717.846 394.392 718.277 394.699 718.7 395.015C718.984 395.223 719.261 395.438 719.546 395.654C719.823 395.869 720.107 396.092 720.377 396.315C720.93 396.762 721.477 397.231 722.015 397.708C723.5 399.024 724.93 400.448 726.307 401.987C727.061 402.825 727.784 403.687 728.484 404.572C728.946 405.165 729.4 405.765 729.846 406.373C730.284 406.981 730.715 407.604 731.138 408.243C731.554 408.874 731.961 409.52 732.354 410.174C732.846 410.99 733.323 411.829 733.777 412.675C733.869 412.845 733.961 413.014 734.046 413.191C734.492 414.045 734.923 414.922 735.33 415.807C735.577 416.338 735.815 416.877 736.046 417.423C736.2 417.785 736.354 418.154 736.5 418.516C736.646 418.878 736.792 419.247 736.93 419.616C737.069 419.986 737.207 420.355 737.338 420.724C737.669 421.648 737.969 422.587 738.246 423.525C738.3 423.71 738.354 423.902 738.407 424.087C738.623 424.841 738.823 425.603 739 426.365C739.046 426.557 739.092 426.75 739.13 426.934C739.215 427.296 739.292 427.65 739.361 428.012C739.43 428.25 739.469 428.466 739.515 428.689ZM722.5 438.8C723.93 436.915 724.446 432.998 724.046 427.065C723.646 421.125 722.538 416.354 720.73 412.745C720.054 411.39 719.307 410.19 718.5 409.143C718.43 409.059 718.361 408.966 718.3 408.882C718.107 408.643 717.915 408.412 717.715 408.189C717.692 408.158 717.661 408.135 717.638 408.104C717.515 407.966 717.392 407.835 717.269 407.712C716.207 406.627 715.1 405.788 713.907 405.157C713.807 405.103 713.73 405.065 713.654 405.026C713.623 405.011 713.592 404.996 713.561 404.98C713.507 404.957 713.461 404.926 713.407 404.903C713.377 404.888 713.338 404.872 713.307 404.857C713.261 404.834 713.215 404.811 713.161 404.795C713.123 404.78 713.092 404.765 713.061 404.749C713.015 404.726 712.969 404.711 712.923 404.688C712.884 404.672 712.854 404.657 712.815 404.649C712.769 404.634 712.723 404.611 712.684 404.595C712.646 404.58 712.615 404.572 712.577 404.557C712.53 404.542 712.492 404.526 712.446 404.511C712.407 404.495 712.377 404.488 712.338 404.472C712.3 404.457 712.254 404.441 712.215 404.434C712.184 404.426 712.146 404.411 712.115 404.403C712.069 404.388 712.03 404.38 711.992 404.365C711.961 404.357 711.93 404.349 711.892 404.341C711.846 404.334 711.807 404.318 711.761 404.311C711.73 404.303 711.7 404.295 711.669 404.288C711.623 404.28 711.584 404.272 711.546 404.264C711.515 404.257 711.484 404.249 711.454 404.249C711.407 404.241 711.369 404.234 711.323 404.226C711.192 404.203 711.146 404.203 711.092 404.195C710.892 404.172 710.754 404.164 710.623 404.164C710.523 404.164 710.484 404.164 710.438 404.164C710.33 404.172 710.277 404.172 710.215 404.18C710.077 404.195 709.954 404.211 709.83 404.234C709.761 404.249 709.7 404.264 709.646 404.272C709.569 404.295 709.515 404.311 709.461 404.326C709.384 404.349 709.33 404.372 709.277 404.388C709.2 404.418 709.146 404.441 709.1 404.465C709.023 404.495 708.977 404.526 708.923 404.549C708.854 404.588 708.8 404.611 708.746 404.642C708.684 404.68 708.638 404.711 708.584 404.742C708.523 404.78 708.477 404.811 708.43 404.849C708.369 404.895 708.323 404.934 708.277 404.972C708.207 405.026 708.169 405.065 708.123 405.103C708.061 405.165 708.015 405.203 707.969 405.249C707.907 405.311 707.861 405.357 707.823 405.403C707.761 405.473 707.723 405.519 707.677 405.565C707.623 405.627 707.577 405.68 707.538 405.742C707.384 405.95 707.238 406.181 707.1 406.435C707.038 406.542 706.984 406.658 706.93 406.773C706.915 406.812 706.892 406.85 706.877 406.896C705.954 409.035 705.654 412.568 705.984 417.477C706.084 418.947 706.23 420.347 706.415 421.679C706.561 422.717 706.723 423.649 706.907 424.541C706.915 424.587 706.923 424.626 706.93 424.672C707.015 425.08 707.107 425.488 707.2 425.88C707.323 426.38 707.454 426.865 707.584 427.342C707.684 427.696 707.792 428.035 707.9 428.373C707.954 428.543 708.007 428.712 708.069 428.874C708.207 429.266 708.346 429.651 708.5 430.02C708.6 430.266 708.7 430.513 708.807 430.751C708.961 431.113 709.13 431.459 709.3 431.798C709.415 432.021 709.53 432.244 709.646 432.459C709.877 432.89 710.123 433.314 710.369 433.714C710.492 433.914 710.623 434.114 710.746 434.306C711.446 435.353 712.207 436.284 713.023 437.084C714.023 438.062 715.077 438.839 716.207 439.439C716.292 439.485 716.369 439.524 716.454 439.562C716.584 439.624 716.638 439.654 716.692 439.678C716.723 439.693 716.754 439.708 716.784 439.724C716.83 439.747 716.884 439.77 716.93 439.793C716.961 439.808 717 439.824 717.03 439.831C717.077 439.855 717.123 439.87 717.169 439.885C717.2 439.901 717.238 439.908 717.269 439.924C717.315 439.939 717.354 439.955 717.4 439.978C717.43 439.993 717.469 440.001 717.5 440.016C717.546 440.032 717.584 440.047 717.63 440.062C717.661 440.07 717.692 440.085 717.73 440.093C717.777 440.108 717.815 440.124 717.861 440.132C717.892 440.139 717.923 440.155 717.954 440.162C718 440.178 718.038 440.185 718.084 440.201C718.115 440.208 718.146 440.216 718.177 440.224C718.223 440.232 718.261 440.247 718.307 440.255C718.338 440.262 718.369 440.27 718.4 440.278C718.446 440.285 718.492 440.293 718.53 440.301C718.661 440.324 718.723 440.332 718.777 440.339C718.884 440.355 718.954 440.362 719.023 440.37C719.13 440.378 719.177 440.385 719.223 440.385C719.338 440.385 719.384 440.393 719.423 440.393C719.715 440.385 719.777 440.378 719.83 440.378C719.915 440.37 719.969 440.362 720.03 440.355C720.115 440.339 720.169 440.332 720.223 440.316C720.292 440.301 720.354 440.285 720.407 440.27C720.469 440.255 720.523 440.239 720.584 440.216C720.654 440.193 720.707 440.17 720.761 440.155C720.838 440.124 720.884 440.101 720.938 440.085C721.015 440.047 721.061 440.024 721.115 440.001C721.184 439.962 721.238 439.931 721.284 439.908C721.354 439.87 721.4 439.839 721.454 439.801C721.507 439.762 721.561 439.724 721.607 439.693C721.661 439.654 721.715 439.616 721.761 439.577C721.823 439.524 721.869 439.485 721.915 439.447C721.977 439.385 722.023 439.347 722.061 439.3C722.123 439.239 722.169 439.193 722.207 439.147C722.269 439.077 722.307 439.031 722.354 438.985C722.415 438.908 722.454 438.854 722.5 438.8Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask5_400_386)'>
            <mask
              id='mask6_400_386'
              style={{ maskType: 'luminance' }}
              maskUnits='userSpaceOnUse'
              x='689'
              y='387'
              width='52'
              height='70'>
              <path
                d='M740.477 436.499L740.515 437.23L740.538 437.946L740.554 438.654V439.339V440.016L740.531 440.678L740.508 441.332L740.5 441.463L740.492 441.586L740.485 441.717L740.477 441.84L740.469 441.971L740.461 442.094L740.446 442.217L740.438 442.348L740.423 442.532L740.408 442.717L740.392 442.902L740.369 443.079L740.354 443.263L740.331 443.44L740.315 443.625L740.292 443.802L740.284 443.864L740.277 443.917L740.269 443.979L740.261 444.033L740.254 444.094L740.246 444.156L740.238 444.21L740.231 444.271L740.192 444.502L740.154 444.733L740.123 444.956L740.084 445.179L740.038 445.403L740 445.626L739.954 445.841L739.908 446.057L739.892 446.111L739.884 446.164L739.869 446.218L739.861 446.272L739.846 446.326L739.838 446.38L739.823 446.434L739.808 446.488L739.731 446.803L739.654 447.111L739.569 447.419L739.477 447.719L739.392 448.011L739.292 448.304L739.192 448.588L739.092 448.865L739.069 448.912L739.054 448.958L739.038 449.004L739.015 449.05L739 449.096L738.984 449.142L738.961 449.189L738.946 449.235L738.892 449.373L738.831 449.504L738.777 449.643L738.715 449.773L738.661 449.904L738.6 450.035L738.538 450.158L738.477 450.289L738.454 450.327L738.431 450.374L738.415 450.412L738.392 450.458L738.369 450.497L738.346 450.543L738.323 450.581L738.308 450.62L738.261 450.705L738.215 450.789L738.177 450.866L738.131 450.951L738.084 451.028L738.038 451.105L737.992 451.182L737.946 451.266L737.9 451.343L737.854 451.42L737.808 451.497L737.761 451.566L737.715 451.643L737.661 451.72L737.615 451.797L737.569 451.867L737.546 451.905L737.523 451.943L737.492 451.974L737.469 452.013L737.446 452.051L737.423 452.082L737.392 452.12L737.369 452.159L737.061 452.574L736.746 452.967L736.415 453.344L736.069 453.698L735.715 454.029L735.354 454.344L734.969 454.629L734.584 454.906L734.515 454.945L734.446 454.991L734.384 455.029L734.315 455.075L734.246 455.114L734.177 455.152L734.108 455.199L734.038 455.237L733.8 455.368L733.554 455.491L733.3 455.614L733.046 455.722L732.784 455.83L732.523 455.93L732.254 456.014L731.984 456.099L731.946 456.114L731.908 456.122L731.869 456.137L731.831 456.145L731.784 456.16L731.746 456.168L731.708 456.176L731.669 456.191L731.546 456.222L731.431 456.245L731.308 456.276L731.184 456.299L731.061 456.33L730.938 456.353L730.815 456.376L730.692 456.399L730.608 456.407L730.523 456.422L730.438 456.437L730.354 456.445L730.269 456.461L730.184 456.468L730.1 456.476L730.015 456.491H729.977L729.938 456.499H729.9H729.861L729.823 456.507H729.785L729.738 456.514H729.7L729.638 456.522H729.584L729.531 456.53H729.484L729.446 456.537H729.408H729.369L729.331 456.545H729.284H729.246H729.2L729.161 456.553H729.123H729.084H729.046H729.008L728.938 456.561H728.877H728.823H728.784H728.746H728.715H728.692H728.669H728.592H728.531H728.477H728.438H728.4H728.369H728.346H728.315H728.246H728.184L728.131 456.553H728.084H728.054H728.023H727.992H727.969L727.892 456.545H727.831H727.777L727.731 456.537H727.692H727.661H727.638H727.608L727.531 456.53L727.469 456.522H727.415L727.369 456.514H727.338H727.3L727.277 456.507H727.246L727.169 456.499H727.108L727.054 456.491L727.008 456.484H726.969L726.931 456.476H726.908H726.877L726.792 456.468L726.723 456.461L726.661 456.453L726.615 456.445L726.561 456.437L726.523 456.43H726.484L726.446 456.422L726.377 456.414L726.315 456.407L726.261 456.399L726.208 456.391L726.154 456.384L726.108 456.376L726.061 456.368L726.015 456.36L725.946 456.353L725.884 456.337L725.831 456.33L725.784 456.322H725.746L725.708 456.314L725.677 456.307L725.646 456.299L725.569 456.284L725.5 456.276L725.446 456.268L725.4 456.26L725.361 456.253L725.331 456.245L725.3 456.237L725.269 456.23L725.192 456.214L725.123 456.199L725.069 456.191L725.023 456.183L724.984 456.176L724.954 456.168L724.923 456.16L724.892 456.153L724.815 456.137L724.746 456.122L724.684 456.107L724.638 456.099L724.6 456.091L724.569 456.083L724.538 456.076L724.515 456.068L724.431 456.053L724.361 456.03L724.3 456.022L724.254 456.007L724.215 455.999L724.184 455.991L724.154 455.983L724.131 455.976L724.046 455.953L723.977 455.937L723.915 455.922L723.869 455.906L723.831 455.899L723.792 455.891L723.769 455.883L723.738 455.876L723.654 455.853L723.584 455.83L723.523 455.814L723.477 455.799L723.438 455.791L723.4 455.783L723.377 455.776L723.346 455.76L723.261 455.737L723.184 455.714L723.131 455.699L723.077 455.683L723.038 455.676L723 455.66L722.969 455.653L722.938 455.645L722.854 455.614L722.784 455.591L722.723 455.576L722.677 455.56L722.631 455.545L722.592 455.529L722.554 455.522L722.523 455.514L722.431 455.483L722.354 455.452L722.285 455.429L722.223 455.414L722.169 455.391L722.123 455.375L722.077 455.36L722.031 455.345L721.969 455.322L721.908 455.299L721.846 455.283L721.792 455.26L721.731 455.237L721.684 455.222L721.631 455.206L721.577 455.183L721.5 455.152L721.438 455.129L721.377 455.106L721.331 455.091L721.284 455.075L721.246 455.06L721.208 455.045L721.169 455.029L721.084 454.998L721.015 454.968L720.961 454.945L720.908 454.929L720.861 454.914L720.831 454.898L720.792 454.883L720.761 454.868L720.677 454.837L720.6 454.806L720.538 454.783L720.492 454.76L720.446 454.744L720.408 454.729L720.377 454.714L720.346 454.698L720.323 454.691L720.3 454.683L720.277 454.675L720.254 454.66L720.238 454.652L720.215 454.644L720.192 454.637L720.169 454.621L720.138 454.614L720.108 454.598L720.077 454.583L720.046 454.575L720.015 454.56L719.984 454.544L719.961 454.537L719.931 454.521L719.908 454.514L719.884 454.506L719.861 454.491L719.838 454.483L719.815 454.475L719.792 454.46L719.769 454.452L719.746 454.444L719.715 454.429L719.684 454.414L719.654 454.406L719.623 454.391L719.592 454.375L719.569 454.36L719.538 454.352L719.508 454.337L719.484 454.321L719.461 454.314L719.438 454.306L719.415 454.29L719.392 454.283L719.369 454.275L719.346 454.26L719.323 454.252L719.292 454.237L719.261 454.221L719.231 454.214L719.2 454.198L719.169 454.183L719.138 454.167L719.108 454.152L719.077 454.144L719.054 454.129L719.031 454.121L719.008 454.106L718.984 454.098L718.961 454.09L718.938 454.075L718.915 454.067L718.892 454.052L718.861 454.037L718.831 454.029L718.8 454.013L718.769 453.998L718.738 453.983L718.708 453.967L718.677 453.952L718.646 453.937L718.623 453.929L718.6 453.913L718.577 453.906L718.554 453.89L718.531 453.883L718.508 453.875L718.492 453.86L718.469 453.852L718.431 453.836L718.4 453.821L718.369 453.798L718.331 453.783L718.3 453.767L718.269 453.752L718.238 453.736L718.2 453.721L718.108 453.675L718.031 453.636L717.961 453.598L717.908 453.575L717.854 453.544L717.815 453.521L717.769 453.506L717.731 453.482L717.638 453.436L717.546 453.39L717.469 453.344L717.4 453.313L717.331 453.275L717.277 453.244L717.215 453.213L717.161 453.19L717.1 453.159L717.046 453.121L716.984 453.09L716.931 453.059L716.869 453.028L716.815 452.998L716.754 452.967L716.7 452.936L716.238 452.682L715.784 452.421L715.331 452.159L714.877 451.89L714.431 451.613L713.984 451.328L713.538 451.043L713.1 450.751L712.938 450.643L712.769 450.528L712.608 450.42L712.446 450.304L712.285 450.189L712.123 450.074L711.961 449.958L711.8 449.843L711.584 449.689L711.369 449.527L711.154 449.373L710.938 449.212L710.731 449.05L710.515 448.888L710.308 448.719L710.1 448.558L710.046 448.511L709.992 448.473L709.938 448.434L709.884 448.388L709.838 448.35L709.785 448.304L709.731 448.265L709.677 448.219L708.9 447.58L708.131 446.911L707.377 446.234L706.631 445.533L705.892 444.81L705.161 444.071L704.438 443.317L703.731 442.54L703.461 442.24L703.2 441.94L702.938 441.632L702.677 441.324L702.415 441.016L702.161 440.701L701.908 440.393L701.654 440.078L701.631 440.047L701.608 440.016L701.577 439.978L701.554 439.947L701.531 439.916L701.508 439.885L701.484 439.854L701.454 439.824L701.269 439.577L701.084 439.339L700.892 439.093L700.708 438.846L700.531 438.6L700.346 438.354L700.169 438.1L699.984 437.854L699.915 437.754L699.846 437.654L699.769 437.554L699.7 437.446L699.631 437.346L699.561 437.246L699.492 437.146L699.423 437.046L699.369 436.969L699.323 436.899L699.277 436.83L699.231 436.761L699.177 436.684L699.131 436.615L699.084 436.545L699.038 436.476L698.961 436.361L698.892 436.253L698.823 436.145L698.746 436.038L698.677 435.93L698.608 435.822L698.538 435.707L698.469 435.599L698.423 435.53L698.377 435.46L698.338 435.399L698.292 435.33L698.254 435.26L698.208 435.191L698.169 435.122L698.123 435.06L697.546 434.114L696.992 433.16L696.454 432.182L695.923 431.197L695.415 430.197L694.931 429.181L694.454 428.15L693.992 427.104L693.915 426.919L693.838 426.734L693.761 426.557L693.692 426.373L693.615 426.188L693.538 426.003L693.469 425.826L693.392 425.641L693.377 425.603L693.361 425.557L693.346 425.518L693.331 425.48L693.315 425.441L693.3 425.403L693.284 425.364L693.269 425.326L693.177 425.087L693.084 424.841L693 424.603L692.908 424.364L692.823 424.126L692.738 423.879L692.646 423.641L692.569 423.402L692.554 423.364L692.538 423.325L692.523 423.287L692.515 423.248L692.5 423.21L692.484 423.164L692.469 423.125L692.461 423.087L692.423 422.987L692.392 422.887L692.354 422.779L692.323 422.679L692.284 422.579L692.254 422.471L692.223 422.371L692.184 422.271L692.154 422.163L692.123 422.056L692.084 421.956L692.054 421.848L692.023 421.748L691.992 421.64L691.954 421.532L691.923 421.432L691.892 421.324L691.861 421.209L691.823 421.101L691.792 420.994L691.761 420.886L691.731 420.778L691.7 420.67L691.669 420.563L691.277 419.124L690.923 417.685L690.608 416.223L690.338 414.761L690.1 413.283L689.9 411.79L689.738 410.29L689.615 408.781L689.6 408.481L689.577 408.189L689.561 407.896L689.546 407.604L689.538 407.312L689.523 407.019L689.515 406.735L689.508 406.45V406.381V406.311L689.5 406.242V406.173V406.104V406.027V405.957V405.888L689.492 405.75V405.611V405.472V405.334V405.203V405.065V404.926L689.5 404.795V404.726V404.657V404.588V404.526V404.457V404.387L689.508 404.326V404.257L689.523 403.726L689.546 403.21L689.577 402.702L689.608 402.194L689.654 401.702L689.7 401.217L689.761 400.74L689.823 400.271L689.838 400.155L689.854 400.04L689.877 399.924L689.892 399.809L689.915 399.701L689.931 399.586L689.946 399.47L689.969 399.363L690.008 399.139L690.054 398.916L690.1 398.701L690.146 398.478L690.192 398.27L690.238 398.054L690.292 397.847L690.346 397.639L690.354 397.585L690.369 397.531L690.384 397.485L690.4 397.431L690.408 397.377L690.423 397.331L690.438 397.277L690.454 397.223L690.584 396.777L690.723 396.338L690.877 395.907L691.031 395.484L691.2 395.076L691.369 394.684L691.554 394.291L691.746 393.922L692.246 393.045L692.777 392.245L693.361 391.514L693.977 390.844L694.638 390.252L695.338 389.728L696.077 389.267L696.861 388.882L696.969 388.836L697.077 388.789L697.192 388.743L697.3 388.697L697.415 388.659L697.531 388.612L697.638 388.574L697.754 388.535L697.831 388.512L697.908 388.489L697.992 388.466L698.069 388.443L698.146 388.42L698.223 388.397L698.3 388.374L698.384 388.351L698.584 388.305L698.784 388.258L698.984 388.212L699.192 388.174L699.4 388.135L699.615 388.105L699.823 388.081L700.038 388.051H700.077L700.115 388.043H700.154L700.192 388.035H700.231H700.269L700.308 388.028H700.346L700.408 388.02L700.469 388.012H700.523H700.569L700.608 388.005H700.654H700.692L700.731 387.997H700.769H700.808H700.846L700.884 387.989H700.923H700.961H701H701.031L701.108 387.981H701.169H701.223H701.261H701.3H701.331H701.354H701.377H701.454H701.515H701.569H701.608H701.646H701.677H701.7H701.723H701.8H701.861L701.915 387.989H701.961H701.992H702.023H702.054H702.077L702.154 387.997H702.215H702.269L702.315 388.005H702.354H702.384H702.408L702.438 388.012H702.515L702.577 388.02H702.631L702.677 388.028H702.715H702.746L702.777 388.035H702.8L702.877 388.043L702.946 388.051H703L703.046 388.058H703.084L703.115 388.066H703.146L703.177 388.074L703.261 388.081L703.338 388.089L703.4 388.097L703.461 388.105L703.508 388.112H703.554L703.6 388.12L703.646 388.128L703.708 388.135L703.761 388.143L703.815 388.151L703.869 388.158L703.915 388.166L703.954 388.174L704 388.182H704.038L704.115 388.197L704.177 388.205L704.231 388.22H704.277L704.315 388.228L704.354 388.235L704.384 388.243H704.415L704.492 388.258L704.561 388.274L704.615 388.282L704.661 388.289L704.7 388.297L704.731 388.305L704.761 388.312L704.784 388.32L704.869 388.335L704.938 388.351L704.992 388.359L705.038 388.366L705.077 388.374L705.115 388.382L705.138 388.389L705.169 388.397L705.254 388.412L705.323 388.428L705.377 388.443L705.423 388.451L705.461 388.459L705.492 388.466L705.523 388.474L705.546 388.482L705.631 388.505L705.708 388.52L705.761 388.535L705.808 388.543L705.846 388.551L705.884 388.559L705.908 388.566L705.938 388.574L706.023 388.597L706.092 388.62L706.154 388.636L706.2 388.643L706.238 388.659L706.269 388.666L706.3 388.674L706.331 388.682L706.415 388.705L706.484 388.72L706.546 388.736L706.592 388.751L706.631 388.766L706.669 388.774L706.7 388.782L706.723 388.789L706.815 388.82L706.884 388.836L706.946 388.859L706.992 388.866L707.031 388.882L707.069 388.889L707.1 388.905L707.123 388.913L707.215 388.936L707.292 388.959L707.354 388.982L707.4 388.997L707.446 389.013L707.484 389.028L707.515 389.036L707.546 389.043L707.638 389.074L707.715 389.105L707.784 389.128L707.846 389.143L707.892 389.159L707.946 389.182L707.984 389.19L708.031 389.205L708.115 389.243L708.2 389.267L708.261 389.29L708.323 389.313L708.377 389.328L708.423 389.351L708.461 389.367L708.508 389.382L708.584 389.413L708.661 389.436L708.715 389.459L708.769 389.474L708.815 389.497L708.854 389.505L708.884 389.52L708.915 389.536L709.008 389.567L709.077 389.597L709.138 389.621L709.192 389.644L709.231 389.659L709.269 389.674L709.3 389.682L709.331 389.697L709.354 389.705L709.377 389.713L709.4 389.728L709.423 389.736L709.446 389.744L709.469 389.751L709.492 389.759L709.515 389.774L709.538 389.782L709.569 389.797L709.6 389.805L709.631 389.821L709.661 389.828L709.692 389.844L709.723 389.859L709.746 389.867L709.777 389.874L709.8 389.89L709.823 389.898L709.846 389.905L709.869 389.921L709.892 389.928L709.915 389.936L709.938 389.951L709.969 389.959L710 389.974L710.023 389.982L710.054 389.998L710.084 390.013L710.115 390.021L710.146 390.036L710.169 390.051L710.192 390.059L710.223 390.067L710.246 390.082L710.269 390.09L710.292 390.098L710.315 390.113L710.338 390.121L710.361 390.128L710.392 390.144L710.423 390.159L710.454 390.175L710.484 390.182L710.508 390.198L710.538 390.213L710.569 390.221L710.6 390.236L710.623 390.244L710.646 390.259L710.669 390.267L710.692 390.282L710.715 390.29L710.738 390.298L710.769 390.313L710.792 390.321L710.823 390.336L710.854 390.352L710.877 390.367L710.908 390.375L710.938 390.39L710.969 390.405L711 390.421L711.031 390.436L711.054 390.444L711.077 390.459L711.1 390.467L711.123 390.475L711.154 390.49L711.177 390.498L711.2 390.513L711.223 390.521L711.254 390.536L711.284 390.552L711.315 390.567L711.338 390.582L711.369 390.598L711.4 390.605L711.431 390.621L711.461 390.636L711.484 390.652L711.508 390.659L711.538 390.675L711.561 390.682L711.584 390.698L711.608 390.706L711.631 390.721L711.654 390.729L711.692 390.744L711.723 390.759L711.754 390.775L711.784 390.79L711.815 390.806L711.846 390.821L711.877 390.836L711.908 390.852L711.931 390.867L711.954 390.875L711.977 390.89L712 390.898L712.023 390.913L712.046 390.921L712.069 390.936L712.092 390.944L712.123 390.967L712.161 390.983L712.2 390.998L712.231 391.013L712.269 391.036L712.3 391.052L712.338 391.067L712.377 391.09L712.477 391.144L712.561 391.19L712.638 391.229L712.708 391.26L712.769 391.298L712.831 391.321L712.877 391.352L712.931 391.375L713.4 391.629L713.869 391.891L714.323 392.152L714.777 392.414L715.231 392.683L715.677 392.96L716.115 393.237L716.554 393.522L716.669 393.591L716.777 393.668L716.884 393.737L716.992 393.814L717.1 393.884L717.208 393.961L717.315 394.03L717.423 394.107L717.584 394.222L717.746 394.33L717.908 394.445L718.069 394.561L718.231 394.676L718.392 394.792L718.554 394.907L718.715 395.022L718.815 395.107L718.923 395.184L719.031 395.261L719.138 395.346L719.238 395.423L719.346 395.507L719.454 395.584L719.554 395.669L719.661 395.746L719.769 395.83L719.869 395.915L719.977 395.992L720.077 396.077L720.184 396.161L720.284 396.246L720.392 396.331L720.6 396.5L720.8 396.669L721.008 396.839L721.215 397.016L721.415 397.193L721.623 397.362L721.823 397.539L722.031 397.723L722.584 398.216L723.131 398.732L723.669 399.247L724.215 399.778L724.746 400.317L725.277 400.871L725.8 401.433L726.315 402.002L726.6 402.317L726.877 402.633L727.154 402.956L727.423 403.279L727.692 403.603L727.961 403.926L728.231 404.257L728.492 404.588L728.669 404.811L728.838 405.034L729.008 405.257L729.184 405.48L729.354 405.711L729.515 405.934L729.684 406.165L729.854 406.388L730.015 406.619L730.184 406.85L730.346 407.081L730.508 407.319L730.669 407.55L730.823 407.789L730.984 408.02L731.138 408.258L731.3 408.497L731.454 408.735L731.608 408.974L731.761 409.212L731.908 409.459L732.061 409.697L732.208 409.943L732.361 410.19L732.546 410.497L732.723 410.805L732.908 411.113L733.084 411.429L733.261 411.736L733.438 412.052L733.608 412.367L733.777 412.691L733.815 412.752L733.846 412.814L733.884 412.883L733.915 412.944L733.946 413.006L733.984 413.075L734.015 413.137L734.054 413.198L734.215 413.522L734.384 413.845L734.546 414.168L734.708 414.499L734.869 414.83L735.023 415.153L735.184 415.492L735.338 415.822L735.431 416.023L735.523 416.223L735.608 416.423L735.7 416.623L735.792 416.831L735.877 417.031L735.969 417.238L736.054 417.438L736.115 417.577L736.169 417.715L736.231 417.846L736.284 417.985L736.338 418.123L736.4 418.262L736.454 418.4L736.508 418.531L736.561 418.67L736.623 418.808L736.677 418.947L736.731 419.085L736.784 419.224L736.838 419.362L736.884 419.501L736.938 419.632L736.992 419.77L737.046 419.909L737.092 420.047L737.146 420.186L737.2 420.324L737.246 420.463L737.3 420.601L737.346 420.74L737.469 421.094L737.584 421.44L737.708 421.786L737.823 422.14L737.931 422.486L738.046 422.84L738.154 423.187L738.254 423.541L738.277 423.61L738.3 423.687L738.315 423.756L738.338 423.825L738.361 423.895L738.377 423.964L738.4 424.033L738.423 424.11L738.5 424.387L738.577 424.672L738.654 424.957L738.731 425.241L738.8 425.526L738.877 425.811L738.946 426.095L739.015 426.38L739.031 426.457L739.046 426.526L739.069 426.596L739.084 426.673L739.1 426.742L739.115 426.811L739.131 426.888L739.146 426.957L739.177 427.088L739.208 427.227L739.238 427.357L739.269 427.496L739.3 427.627L739.323 427.765L739.354 427.896L739.384 428.035L739.4 428.119L739.415 428.196L739.431 428.281L739.446 428.358L739.469 428.443L739.484 428.519L739.5 428.604L739.515 428.689L739.538 428.827L739.569 428.973L739.6 429.12L739.623 429.266L739.646 429.412L739.677 429.558L739.7 429.704L739.731 429.851L739.846 430.574L739.961 431.313L740.061 432.044L740.154 432.783L740.238 433.521L740.315 434.268L740.377 435.006L740.431 435.761L740.477 436.499Z'
                fill='white'
              />
            </mask>
            <g mask='url(#mask6_400_386)'>
              <rect
                x='689.492'
                y='387.98'
                width='51.5385'
                height='69.2563'
                fill='url(#pattern1_400_386)'
              />
            </g>
          </g>
          <mask
            id='mask7_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='695'
            y='385'
            width='49'
            height='44'>
            <path
              d='M695.846 389.398L700.346 386.735C700.669 386.543 701.007 386.373 701.353 386.22C701.646 386.089 701.946 385.973 702.253 385.873C702.461 385.804 702.669 385.742 702.876 385.689C703.407 385.55 703.961 385.45 704.53 385.388C704.63 385.381 704.738 385.365 704.838 385.358C705.015 385.342 705.123 385.335 705.223 385.335C705.33 385.327 705.43 385.327 705.53 385.319C705.746 385.312 705.807 385.312 705.876 385.312C706.092 385.312 706.161 385.312 706.223 385.312C706.446 385.319 706.507 385.319 706.576 385.319C706.8 385.327 706.869 385.335 706.938 385.335C707.161 385.35 707.23 385.358 707.3 385.358C707.523 385.381 707.6 385.388 707.676 385.396C707.923 385.419 708.03 385.435 708.138 385.45C708.315 385.473 708.423 385.489 708.538 385.504C708.761 385.542 708.83 385.55 708.907 385.565C709.138 385.612 709.207 385.619 709.284 385.635C709.523 385.681 709.592 385.696 709.661 385.712C709.907 385.766 709.976 385.781 710.046 385.796C710.292 385.858 710.361 385.873 710.43 385.889C710.676 385.95 710.753 385.973 710.823 385.989C711.076 386.058 711.146 386.081 711.223 386.104C711.469 386.181 711.546 386.204 711.623 386.227C711.876 386.304 711.961 386.335 712.046 386.366C712.3 386.45 712.415 386.489 712.523 386.527C712.784 386.62 712.892 386.658 713 386.697C713.238 386.789 713.323 386.82 713.415 386.851C713.669 386.951 713.746 386.981 713.83 387.012C713.892 387.035 713.953 387.058 714.007 387.081C714.084 387.112 714.169 387.143 714.246 387.181C714.307 387.205 714.376 387.235 714.438 387.258C714.515 387.289 714.592 387.328 714.669 387.358C714.73 387.389 714.8 387.412 714.861 387.443C714.938 387.474 715.015 387.512 715.1 387.551C715.161 387.582 715.23 387.605 715.292 387.635C715.369 387.674 715.453 387.712 715.53 387.743C715.592 387.774 715.653 387.805 715.723 387.828C715.8 387.866 715.884 387.905 715.961 387.943C716.023 387.974 716.092 388.005 716.153 388.036C716.238 388.074 716.323 388.12 716.407 388.159C716.469 388.19 716.53 388.22 716.592 388.251C716.684 388.297 716.776 388.343 716.876 388.397C717.169 388.543 717.3 388.613 717.43 388.682C718.692 389.351 719.892 390.059 721.053 390.829C721.346 391.021 721.638 391.214 721.923 391.414C722.353 391.714 722.784 392.022 723.207 392.337C723.492 392.545 723.769 392.76 724.053 392.976C724.33 393.191 724.615 393.415 724.884 393.638C725.438 394.084 725.984 394.553 726.523 395.03C728.007 396.346 729.438 397.77 730.815 399.309C731.569 400.148 732.292 401.01 732.992 401.895C733.453 402.487 733.907 403.087 734.353 403.695C734.792 404.303 735.223 404.926 735.646 405.565C736.061 406.196 736.469 406.843 736.861 407.497C737.353 408.312 737.83 409.151 738.284 409.998C738.376 410.167 738.469 410.336 738.553 410.513C739 411.367 739.43 412.245 739.838 413.129C740.084 413.66 740.323 414.199 740.553 414.745C740.707 415.107 740.861 415.476 741.007 415.838C741.153 416.2 741.3 416.569 741.438 416.939C741.576 417.308 741.715 417.677 741.846 418.047C742.176 418.97 742.476 419.909 742.753 420.848C742.807 421.032 742.861 421.225 742.915 421.409C743.13 422.164 743.33 422.925 743.507 423.687C743.553 423.88 743.6 424.072 743.638 424.257C743.723 424.618 743.8 424.972 743.869 425.334C743.915 425.549 743.961 425.765 744 425.988L739.5 428.651C739.453 428.435 739.415 428.22 739.369 427.996C739.292 427.635 739.215 427.273 739.138 426.919C739.092 426.727 739.053 426.534 739.007 426.35C738.823 425.588 738.63 424.826 738.415 424.072C738.361 423.88 738.307 423.695 738.253 423.51C737.976 422.571 737.669 421.64 737.346 420.709C737.215 420.34 737.076 419.97 736.938 419.601C736.8 419.232 736.653 418.862 736.507 418.501C736.361 418.139 736.207 417.77 736.053 417.408C735.823 416.862 735.584 416.323 735.338 415.792C734.93 414.899 734.5 414.03 734.053 413.176C733.961 413.006 733.876 412.837 733.784 412.66C733.33 411.814 732.853 410.975 732.361 410.159C731.969 409.505 731.561 408.859 731.146 408.228C730.73 407.597 730.3 406.973 729.853 406.358C729.415 405.75 728.961 405.142 728.492 404.557C727.792 403.672 727.069 402.81 726.315 401.972C724.938 400.432 723.507 399.009 722.023 397.693C721.484 397.216 720.938 396.754 720.384 396.3C720.107 396.077 719.83 395.854 719.553 395.638C719.276 395.423 718.992 395.207 718.707 395C718.284 394.684 717.853 394.376 717.423 394.076C717.138 393.876 716.846 393.684 716.553 393.491C715.384 392.722 714.192 392.022 712.93 391.345C712.8 391.275 712.661 391.206 712.376 391.06C712.284 391.014 712.184 390.96 712.092 390.914C712.03 390.883 711.969 390.852 711.907 390.821C711.823 390.783 711.738 390.737 711.653 390.698C711.592 390.667 711.523 390.637 711.461 390.606C711.384 390.567 711.3 390.529 711.223 390.49C711.161 390.46 711.1 390.429 711.03 390.406C710.946 390.367 710.869 390.329 710.792 390.298C710.73 390.267 710.661 390.236 710.6 390.213C710.523 390.175 710.446 390.144 710.361 390.106C710.3 390.075 710.23 390.052 710.169 390.021C710.092 389.99 710.015 389.952 709.938 389.921C709.876 389.89 709.807 389.867 709.746 389.844C709.669 389.813 709.584 389.775 709.507 389.744C709.446 389.721 709.384 389.698 709.33 389.675C709.246 389.644 709.169 389.613 708.915 389.513C708.83 389.482 708.746 389.452 708.5 389.359C708.392 389.321 708.284 389.282 708.023 389.19C707.915 389.151 707.8 389.113 707.546 389.028C707.461 388.997 707.376 388.974 707.123 388.89C707.046 388.867 706.969 388.844 706.723 388.767C706.646 388.744 706.576 388.72 706.323 388.651C706.253 388.628 706.184 388.613 705.93 388.551C705.861 388.536 705.792 388.513 705.546 388.459C705.476 388.443 705.407 388.428 705.161 388.374C705.092 388.359 705.023 388.343 704.784 388.297C704.715 388.282 704.638 388.266 704.407 388.228C704.33 388.213 704.253 388.205 704.038 388.166C703.93 388.151 703.815 388.136 703.638 388.113C703.53 388.097 703.415 388.082 703.176 388.059C703.1 388.051 703.023 388.043 702.8 388.02C702.73 388.013 702.661 388.005 702.438 387.997C702.369 387.989 702.307 387.989 702.076 387.982C702.015 387.982 701.946 387.974 701.723 387.974C701.661 387.974 701.592 387.974 701.376 387.974C701.315 387.974 701.246 387.974 701.03 387.982C700.93 387.982 700.838 387.989 700.723 387.997C700.623 388.005 700.515 388.013 700.338 388.02C700.238 388.028 700.13 388.036 700.03 388.051C699.453 388.113 698.907 388.213 698.376 388.351C698.161 388.405 697.953 388.467 697.753 388.536C697.446 388.636 697.146 388.751 696.853 388.882C696.507 389.036 696.176 389.205 695.846 389.398Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask7_400_386)'>
            <path
              d='M695.846 389.398L700.346 386.735C700.669 386.543 701.007 386.373 701.353 386.219C701.492 386.158 701.63 386.104 701.776 386.042L697.276 388.705C697.138 388.759 696.992 388.82 696.853 388.882C696.507 389.036 696.176 389.205 695.846 389.398Z'
              fill='#828282'
            />
            <path
              d='M697.277 388.705L701.777 386.042C701.931 385.981 702.093 385.927 702.254 385.873C702.462 385.804 702.67 385.742 702.877 385.689C702.916 385.681 702.954 385.665 702.993 385.658L698.493 388.32C698.454 388.328 698.416 388.343 698.377 388.351C698.162 388.405 697.954 388.466 697.754 388.536C697.593 388.59 697.439 388.643 697.277 388.705Z'
              fill='#858585'
            />
            <path
              d='M698.5 388.32L703 385.658C703.354 385.566 703.723 385.496 704.1 385.442L699.6 388.105C699.223 388.166 698.854 388.236 698.5 388.32Z'
              fill='#878787'
            />
            <path
              d='M699.6 388.113L704.1 385.45C704.246 385.427 704.392 385.412 704.538 385.396C704.638 385.388 704.746 385.373 704.846 385.365C705.015 385.35 705.115 385.342 705.215 385.342L700.715 388.005C700.615 388.012 700.515 388.02 700.346 388.028C700.246 388.036 700.138 388.043 700.038 388.059C699.892 388.066 699.746 388.089 699.6 388.113Z'
              fill='#8A8A8A'
            />
            <path
              d='M700.715 387.997L705.215 385.335C705.223 385.335 705.223 385.335 705.23 385.335C705.338 385.327 705.438 385.327 705.538 385.319C705.753 385.312 705.815 385.312 705.884 385.312C706.099 385.312 706.169 385.312 706.23 385.312C706.323 385.312 706.384 385.312 706.43 385.319L701.93 387.982C701.884 387.982 701.815 387.982 701.73 387.974C701.669 387.974 701.599 387.974 701.384 387.974C701.323 387.974 701.253 387.974 701.038 387.982C700.938 387.982 700.846 387.989 700.73 387.997C700.723 387.997 700.715 387.997 700.715 387.997Z'
              fill='#8C8C8C'
            />
            <path
              d='M701.93 387.99L706.43 385.327C706.499 385.327 706.537 385.327 706.576 385.335C706.799 385.343 706.868 385.35 706.937 385.35C707.16 385.366 707.23 385.373 707.299 385.373C707.484 385.389 707.568 385.396 707.637 385.404L703.137 388.067C703.068 388.059 702.984 388.051 702.799 388.036C702.73 388.028 702.66 388.02 702.437 388.013C702.368 388.005 702.307 388.005 702.076 387.997C702.037 387.99 701.999 387.99 701.93 387.99Z'
              fill='#8F8F8F'
            />
            <path
              d='M703.139 388.066L707.639 385.404C707.654 385.404 707.669 385.404 707.685 385.412C707.931 385.435 708.039 385.45 708.146 385.465C708.323 385.488 708.431 385.504 708.546 385.519C708.769 385.558 708.839 385.565 708.916 385.581C708.923 385.581 708.931 385.581 708.946 385.588L704.446 388.251C704.439 388.251 704.431 388.251 704.416 388.243C704.339 388.228 704.262 388.22 704.046 388.182C703.939 388.166 703.823 388.151 703.646 388.128C703.539 388.113 703.423 388.097 703.185 388.074C703.162 388.074 703.154 388.066 703.139 388.066Z'
              fill='#919191'
            />
            <path
              d='M704.438 388.251L708.938 385.588C709.145 385.627 709.214 385.642 709.284 385.65C709.522 385.696 709.591 385.712 709.661 385.727C709.907 385.781 709.976 385.796 710.045 385.812C710.291 385.873 710.361 385.888 710.43 385.904C710.538 385.935 710.614 385.95 710.668 385.965L706.168 388.628C706.114 388.613 706.038 388.597 705.93 388.566C705.861 388.551 705.791 388.528 705.545 388.474C705.476 388.459 705.407 388.443 705.161 388.389C705.091 388.374 705.022 388.359 704.784 388.312C704.714 388.305 704.653 388.289 704.438 388.251Z'
              fill='#949494'
            />
            <path
              d='M706.176 388.636L710.676 385.973C710.745 385.989 710.791 386.004 710.83 386.012C711.083 386.081 711.153 386.104 711.23 386.127C711.476 386.204 711.553 386.227 711.63 386.25C711.883 386.327 711.968 386.358 712.053 386.389C712.307 386.473 712.422 386.512 712.53 386.55C712.537 386.55 712.537 386.55 712.545 386.558L708.045 389.22C708.037 389.22 708.037 389.22 708.03 389.213C707.922 389.174 707.807 389.136 707.553 389.051C707.468 389.02 707.383 388.997 707.13 388.913C707.053 388.89 706.976 388.867 706.73 388.79C706.653 388.766 706.583 388.743 706.33 388.674C706.283 388.666 706.245 388.659 706.176 388.636Z'
              fill='#969696'
            />
            <path
              d='M708.037 389.213L712.537 386.55C712.791 386.643 712.891 386.673 712.999 386.72C713.237 386.812 713.322 386.843 713.414 386.873C713.668 386.974 713.745 387.004 713.829 387.035C713.891 387.058 713.952 387.081 714.006 387.104C714.083 387.135 714.168 387.166 714.245 387.204C714.306 387.227 714.376 387.258 714.437 387.281C714.514 387.312 714.591 387.351 714.668 387.381C714.729 387.412 714.799 387.435 714.86 387.466C714.922 387.489 714.976 387.52 715.037 387.543L710.537 390.205C710.476 390.182 710.422 390.152 710.36 390.129C710.299 390.098 710.229 390.075 710.168 390.044C710.091 390.013 710.014 389.975 709.937 389.944C709.876 389.913 709.806 389.89 709.745 389.867C709.668 389.836 709.583 389.798 709.506 389.767C709.445 389.744 709.383 389.721 709.329 389.698C709.245 389.667 709.168 389.636 708.914 389.536C708.829 389.505 708.745 389.474 708.499 389.382C708.399 389.336 708.291 389.297 708.037 389.213Z'
              fill='#999999'
            />
            <path
              d='M710.537 390.213L715.037 387.551C715.06 387.558 715.076 387.566 715.099 387.574C715.16 387.605 715.229 387.628 715.291 387.659C715.368 387.697 715.452 387.735 715.529 387.766C715.591 387.797 715.652 387.828 715.722 387.851C715.799 387.889 715.883 387.928 715.96 387.966C716.022 387.997 716.091 388.028 716.152 388.059C716.237 388.097 716.322 388.143 716.406 388.182C716.468 388.213 716.529 388.243 716.591 388.274C716.683 388.32 716.776 388.366 716.876 388.42C717.168 388.567 717.299 388.636 717.429 388.705C717.776 388.89 718.114 389.074 718.452 389.267L713.952 391.929C713.614 391.737 713.276 391.552 712.929 391.368C712.799 391.298 712.66 391.229 712.376 391.083C712.283 391.037 712.183 390.983 712.091 390.937C712.029 390.906 711.968 390.875 711.906 390.844C711.822 390.806 711.737 390.76 711.652 390.721C711.591 390.69 711.522 390.66 711.46 390.629C711.383 390.59 711.299 390.552 711.222 390.513C711.16 390.483 711.099 390.452 711.029 390.429C710.945 390.39 710.868 390.352 710.791 390.321C710.729 390.29 710.66 390.259 710.599 390.236C710.576 390.229 710.56 390.221 710.537 390.213Z'
              fill='#9C9C9C'
            />
            <path
              d='M713.953 391.937L718.453 389.274C719.345 389.775 720.207 390.306 721.053 390.86C721.345 391.052 721.638 391.244 721.922 391.444C722.176 391.621 722.438 391.798 722.684 391.983L718.184 394.646C717.93 394.461 717.676 394.284 717.422 394.107C717.138 393.907 716.845 393.715 716.553 393.522C715.707 392.968 714.845 392.437 713.953 391.937Z'
              fill='#9E9E9E'
            />
            <path
              d='M718.191 394.646L722.691 391.983C722.868 392.107 723.038 392.237 723.214 392.36C723.499 392.568 723.776 392.784 724.061 392.999C724.338 393.215 724.622 393.438 724.891 393.661C725.445 394.107 725.991 394.577 726.53 395.054C727.461 395.885 728.376 396.754 729.268 397.678L724.768 400.34C723.876 399.425 722.961 398.547 722.03 397.716C721.491 397.239 720.945 396.777 720.391 396.323C720.114 396.1 719.838 395.877 719.561 395.662C719.284 395.446 718.999 395.231 718.714 395.023C718.538 394.9 718.368 394.769 718.191 394.646Z'
              fill='#A1A1A1'
            />
            <path
              d='M724.77 400.34L729.27 397.678C729.793 398.216 730.308 398.77 730.823 399.34C731.577 400.179 732.3 401.041 733 401.925C733.462 402.518 733.916 403.118 734.362 403.726C734.8 404.334 735.231 404.957 735.654 405.596C736.07 406.227 736.477 406.873 736.87 407.528C737.031 407.805 737.2 408.074 737.354 408.351L732.854 411.013C732.693 410.736 732.531 410.459 732.37 410.19C731.977 409.536 731.57 408.89 731.154 408.259C730.739 407.628 730.308 407.004 729.862 406.389C729.423 405.781 728.97 405.173 728.5 404.588C727.8 403.703 727.077 402.841 726.323 402.002C725.808 401.433 725.293 400.879 724.77 400.34Z'
              fill='#A3A3A3'
            />
            <path
              d='M732.846 411.013L737.346 408.351C737.669 408.905 737.976 409.459 738.276 410.028C738.369 410.197 738.461 410.367 738.546 410.544C738.992 411.398 739.423 412.275 739.83 413.16C740.076 413.691 740.315 414.23 740.546 414.776C740.7 415.138 740.853 415.507 741 415.869C741.146 416.23 741.292 416.6 741.43 416.969C741.5 417.146 741.561 417.323 741.63 417.5L737.13 420.163C737.069 419.986 737 419.809 736.93 419.632C736.792 419.262 736.646 418.893 736.5 418.531C736.353 418.17 736.2 417.8 736.046 417.439C735.815 416.892 735.576 416.354 735.33 415.823C734.923 414.93 734.492 414.06 734.046 413.206C733.953 413.037 733.869 412.868 733.776 412.691C733.476 412.121 733.169 411.567 732.846 411.013Z'
              fill='#A1A1A1'
            />
            <path
              d='M737.139 420.163L741.639 417.5C741.708 417.693 741.777 417.885 741.846 418.078C742.177 419.001 742.477 419.94 742.754 420.879C742.808 421.063 742.862 421.256 742.916 421.44C743.093 422.064 743.262 422.687 743.416 423.318L738.916 425.981C738.762 425.35 738.593 424.726 738.416 424.103C738.362 423.911 738.308 423.726 738.254 423.541C737.977 422.602 737.669 421.671 737.346 420.74C737.277 420.548 737.208 420.355 737.139 420.163Z'
              fill='#9E9E9E'
            />
            <path
              d='M738.914 425.988L743.414 423.326C743.445 423.457 743.476 423.587 743.514 423.718C743.56 423.91 743.606 424.103 743.645 424.288C743.729 424.649 743.806 425.003 743.876 425.365C743.922 425.58 743.968 425.796 744.006 426.019L739.506 428.681C739.46 428.466 739.422 428.251 739.376 428.027C739.299 427.666 739.222 427.304 739.145 426.95C739.099 426.758 739.06 426.565 739.014 426.381C738.983 426.25 738.953 426.119 738.914 425.988Z'
              fill='#9C9C9C'
            />
          </g>
          <path
            d='M764.346 453.275L768.846 450.612L780.546 456.815L776.046 459.485L764.346 453.275Z'
            fill='#A1A1A1'
          />
          <path
            d='M792.354 480.977L796.854 478.315L797.623 489.681L793.123 492.351L792.354 480.977Z'
            fill='#9D9D9D'
          />
          <path
            d='M775.361 449.389L779.861 446.719L795.269 454.891L790.761 457.553L775.361 449.389Z'
            fill='#A1A1A1'
          />
          <path
            d='M790.762 457.553L795.269 454.891L795.954 464.994L791.446 467.657L790.762 457.553Z'
            fill='#9D9D9D'
          />
          <path
            d='M800.016 472.205L804.523 469.535L805.423 482.863L800.923 485.525L800.016 472.205Z'
            fill='#9D9D9D'
          />
          <path
            d='M791.445 467.657L795.953 464.995L804.522 469.535L800.015 472.205L791.445 467.657Z'
            fill='#A1A1A1'
          />
          <path
            d='M768.584 422.063L773.084 419.401L789.976 428.358L785.476 431.028L768.584 422.063Z'
            fill='#A1A1A1'
          />
          <path
            d='M785.476 431.028L789.976 428.358L768.846 450.612L764.346 453.275L785.476 431.028Z'
            fill='#818181'
          />
          <path
            d='M790.761 457.553L791.446 467.657L800.015 472.205L800.923 485.525L792.354 480.977L793.123 492.351L777.223 483.917L776.454 472.544L745.977 456.368L745.238 445.426L768.584 422.063L785.477 431.028L764.346 453.275L776.046 459.485L775.361 449.389L790.761 457.553Z'
            fill='#A3A3A3'
          />
          <mask
            id='mask8_400_386'
            style={{ maskType: 'luminance' }}
            maskUnits='userSpaceOnUse'
            x='745'
            y='422'
            width='56'
            height='71'>
            <path
              d='M790.761 457.553L791.446 467.657L800.015 472.205L800.923 485.525L792.354 480.977L793.123 492.351L777.223 483.917L776.454 472.544L745.977 456.368L745.238 445.426L768.584 422.063L785.477 431.028L764.346 453.275L776.046 459.485L775.361 449.389L790.761 457.553Z'
              fill='white'
            />
          </mask>
          <g mask='url(#mask8_400_386)'>
            <mask
              id='mask9_400_386'
              style={{ maskType: 'luminance' }}
              maskUnits='userSpaceOnUse'
              x='745'
              y='422'
              width='56'
              height='71'>
              <path
                d='M800.923 485.525L792.354 480.977L793.123 492.351L777.223 483.917L776.454 472.544L745.977 456.368L745.238 445.426L768.584 422.063L785.477 431.028L764.346 453.275L776.046 459.485L775.361 449.389L790.761 457.553L791.446 467.657L800.015 472.205L800.923 485.525Z'
                fill='white'
              />
            </mask>
            <g mask='url(#mask9_400_386)'>
              <rect
                x='745.234'
                y='422.064'
                width='56.1538'
                height='70.7953'
                fill='url(#pattern2_400_386)'
              />
            </g>
          </g>
        </g>
        <path
          d='M672.74 134.44C674.92 134.44 676.64 135.14 677.9 136.56C679.1 137.9 679.7 139.68 679.7 141.88C679.7 144.08 679.1 145.84 677.9 147.18C676.64 148.58 674.92 149.28 672.74 149.28C670.54 149.28 668.82 148.56 667.58 147.16C666.38 145.8 665.8 144.04 665.8 141.88C665.8 139.7 666.38 137.94 667.58 136.58C668.82 135.14 670.54 134.44 672.74 134.44ZM672.74 136.5C671.26 136.5 670.12 137 669.3 138C668.52 138.96 668.14 140.24 668.14 141.88C668.14 143.5 668.52 144.78 669.3 145.74C670.1 146.72 671.26 147.22 672.74 147.22C674.22 147.22 675.36 146.74 676.16 145.8C676.94 144.86 677.34 143.56 677.34 141.88C677.34 140.2 676.94 138.88 676.16 137.92C675.36 136.96 674.22 136.5 672.74 136.5ZM687.308 138.38C688.868 138.38 690.128 138.88 691.088 139.92C692.028 140.94 692.508 142.24 692.508 143.84C692.508 145.42 692.028 146.72 691.108 147.72C690.148 148.76 688.868 149.28 687.308 149.28C685.728 149.28 684.468 148.76 683.508 147.72C682.568 146.72 682.108 145.42 682.108 143.84C682.108 142.24 682.568 140.94 683.528 139.92C684.468 138.88 685.728 138.38 687.308 138.38ZM687.308 140.24C686.348 140.24 685.628 140.6 685.108 141.36C684.668 142 684.448 142.84 684.448 143.84C684.448 144.84 684.668 145.66 685.108 146.3C685.628 147.04 686.348 147.42 687.308 147.42C688.248 147.42 688.988 147.04 689.508 146.3C689.948 145.64 690.188 144.82 690.188 143.84C690.188 142.84 689.948 142 689.508 141.36C688.988 140.6 688.248 140.24 687.308 140.24ZM700.54 138.38C702.06 138.38 703.26 138.92 704.14 140C704.94 141 705.34 142.28 705.34 143.88C705.34 145.4 704.94 146.66 704.16 147.66C703.32 148.74 702.14 149.28 700.66 149.28C699.46 149.28 698.42 148.7 697.58 147.58V152.96H695.3V138.66H697.42V139.76C698.14 138.84 699.18 138.38 700.54 138.38ZM700.2 140.22C699.3 140.22 698.62 140.56 698.14 141.28C697.7 141.9 697.5 142.72 697.5 143.76V143.92C697.5 145 697.76 145.88 698.3 146.54C698.78 147.14 699.38 147.44 700.12 147.44C701.12 147.44 701.88 147.1 702.36 146.46C702.78 145.86 703 145 703 143.88C703 142.76 702.8 141.9 702.42 141.3C701.94 140.58 701.2 140.22 700.2 140.22ZM712.031 138.38C714.751 138.38 716.251 139.46 716.491 141.66H714.271C714.111 141.14 713.871 140.76 713.551 140.56C713.211 140.34 712.691 140.24 711.991 140.24C711.391 140.24 710.931 140.32 710.631 140.5C710.271 140.68 710.111 140.96 710.111 141.36C710.111 141.68 710.371 141.98 710.931 142.22C711.271 142.36 711.951 142.56 712.991 142.82C714.151 143.1 715.031 143.42 715.591 143.8C716.411 144.32 716.831 145.06 716.831 145.98C716.831 148.18 715.271 149.28 712.171 149.28C709.291 149.28 707.751 148.06 707.531 145.64H709.751C709.871 146.3 710.111 146.76 710.451 147.04C710.791 147.28 711.331 147.42 712.091 147.42C713.691 147.42 714.491 146.98 714.491 146.14C714.491 145.66 714.191 145.28 713.591 145C713.271 144.84 712.591 144.66 711.551 144.42C710.331 144.14 709.471 143.84 708.971 143.52C708.171 143.02 707.771 142.32 707.771 141.42C707.771 140.46 708.151 139.72 708.951 139.2C709.731 138.64 710.751 138.38 712.031 138.38ZM735.719 139.62V141.3C734.859 141.38 733.979 141.48 733.079 141.56V142.88H731.159V141.72C729.939 141.8 728.659 141.88 727.339 141.96L727.079 140.16C728.519 140.1 729.879 140.04 731.159 139.96V138.84H727.739L727.299 137.16C727.739 136.66 728.139 136.06 728.539 135.38H726.779V133.54H729.399C729.559 133.16 729.699 132.78 729.839 132.38L731.919 132.72C731.799 133 731.699 133.28 731.599 133.54H736.119V135.38H730.759C730.439 136.02 730.079 136.6 729.719 137.14H731.159V136H733.079V137.14H735.919V138.84H733.079V139.84C733.999 139.76 734.879 139.7 735.719 139.62ZM738.819 138.86C738.579 140.46 738.019 141.88 737.179 143.1L735.739 141.6C736.559 140.38 736.999 138.94 737.039 137.26V133.9C739.679 133.64 741.639 133.2 742.959 132.6L744.319 134.14C742.859 134.78 741.079 135.22 738.959 135.46V137.04H744.659V138.86H742.819V142.9H740.799V138.86H738.819ZM742.699 143.28V151.1H740.579V150.4H731.059V151.1H728.939V143.28H742.699ZM731.059 148.64H740.579V147.62H731.059V148.64ZM731.059 145.98H740.579V145H731.059V145.98ZM749.019 133.4H763.659V135.44H756.659V136.6C756.619 137.68 756.559 138.68 756.439 139.62H765.239V141.64H759.259V147.84C759.259 148.48 759.559 148.82 760.159 148.82H762.299C762.659 148.82 762.939 148.66 763.119 148.38C763.339 148.1 763.479 147.2 763.559 145.68L765.519 146.34C765.339 148.5 765.019 149.78 764.579 150.18C764.179 150.56 763.539 150.76 762.699 150.76H759.519C757.959 150.76 757.179 149.92 757.179 148.26V141.64H756.099C755.739 143.34 755.239 144.82 754.579 146.06C753.339 148.26 751.339 149.94 748.579 151.1L747.399 149.24C750.159 147.98 751.999 146.4 752.959 144.52C753.359 143.64 753.679 142.68 753.939 141.64H747.739V139.62H754.299C754.419 138.66 754.499 137.66 754.539 136.6V135.44H749.019V133.4ZM784.679 133.12L785.719 134.88C784.319 135.36 782.699 135.7 780.879 135.94V140.82H786.479V142.86H780.879V148.38H785.479V150.42H774.239V148.38H778.799V142.86H773.239V140.82H778.799V136.14C777.639 136.22 776.399 136.26 775.079 136.26L774.419 134.32C778.459 134.32 781.879 133.92 784.679 133.12ZM772.379 132.54L774.339 133.48C773.819 135 773.159 136.44 772.359 137.8V151.12H770.279V140.74C769.739 141.38 769.159 142.02 768.559 142.64L767.859 140.4C769.899 138.18 771.399 135.56 772.379 132.54ZM794.859 133.64H807.299V135.66H805.919V148.82C805.919 150.28 805.199 151.02 803.779 151.02H800.399L799.939 148.98C801.039 149.06 802.079 149.12 803.059 149.12C803.539 149.12 803.799 148.84 803.799 148.28V135.66H794.859V133.64ZM795.139 138.06H801.919V145.92H795.139V138.06ZM799.899 144.04V139.94H797.159V144.04H799.899ZM792.919 132.4L794.799 133.28C794.359 134.76 793.779 136.16 793.059 137.48V151.12H790.999V140.68C790.479 141.34 789.939 142 789.359 142.64L788.659 140.4C790.579 138.14 791.999 135.48 792.919 132.4ZM809.919 133.64H827.939V135.66H825.459V148.7C825.459 150.18 824.739 150.94 823.339 150.94H819.959L819.479 148.92C820.559 149 821.619 149.04 822.619 149.04C823.099 149.04 823.359 148.74 823.359 148.18V135.66H809.919V133.64ZM820.319 138.06V145.9H814.059V147.5H812.059V138.06H820.319ZM814.059 144.02H818.339V139.92H814.059V144.02ZM834.319 139.96V142.84H838.719V139.96H834.319ZM834.219 144.76C833.959 147.24 833.219 149.32 831.999 150.96L830.399 149.52C831.579 147.84 832.179 145.7 832.219 143.1V133.2H847.719V148.3C847.719 149.84 846.919 150.62 845.339 150.62H843.039L842.479 148.58C843.279 148.66 843.999 148.7 844.679 148.7C845.279 148.7 845.599 148.3 845.599 147.54V144.76H840.839V150.4H838.719V144.76H834.219ZM845.599 142.84V139.96H840.839V142.84H845.599ZM845.599 138.04V135.24H840.839V138.04H845.599ZM838.719 135.24H834.319V138.04H838.719V135.24ZM863.899 137.42C863.819 137.7 863.759 137.96 863.679 138.24C864.119 140.22 864.659 141.96 865.319 143.46C865.919 141.7 866.259 139.68 866.339 137.42H863.899ZM864.359 145.6C863.719 144.28 863.159 142.8 862.679 141.16C862.379 141.82 862.059 142.44 861.739 143L860.439 141.38C861.539 139.18 862.279 136.24 862.659 132.56L864.699 132.92C864.579 133.8 864.459 134.62 864.319 135.4H869.319V137.42H868.219C868.099 140.64 867.519 143.38 866.459 145.66C867.419 147.26 868.559 148.52 869.879 149.42L868.719 151.12C867.479 150.2 866.379 149 865.439 147.52C864.499 148.92 863.339 150.12 861.979 151.1L860.899 149.34C862.339 148.38 863.499 147.14 864.359 145.6ZM854.899 141.84L856.859 142.08C856.739 142.38 856.639 142.68 856.539 142.96H860.559V144.64C860.219 145.8 859.619 146.84 858.739 147.72C859.519 148.06 860.279 148.42 861.019 148.82L859.899 150.46C859.139 150 858.219 149.52 857.139 149.02C855.819 149.86 854.179 150.52 852.199 151.04L851.239 149.3C852.679 148.98 853.919 148.56 854.979 148.08C854.179 147.74 853.319 147.4 852.439 147.06C852.919 146.26 853.339 145.48 853.699 144.74H851.719V142.96H854.499C854.639 142.56 854.779 142.2 854.899 141.84ZM856.859 146.94C857.679 146.3 858.299 145.56 858.719 144.74H855.759C855.499 145.26 855.219 145.76 854.939 146.24C855.579 146.44 856.219 146.68 856.859 146.94ZM853.679 132.86C854.099 133.6 854.479 134.4 854.799 135.26L853.319 135.92C852.919 134.96 852.499 134.12 852.059 133.38L853.679 132.86ZM859.299 132.84L860.759 133.54C860.359 134.38 859.859 135.2 859.239 136L857.779 135.26C858.419 134.44 858.939 133.64 859.299 132.84ZM851.759 136.22H855.479V132.6H857.399V136.22H860.799V138.04H857.399V138.08C858.439 138.5 859.499 138.98 860.579 139.52L859.499 141.16C858.659 140.5 857.959 139.98 857.399 139.6V141.7H855.479V138.56C854.799 139.92 853.759 141.1 852.319 142.14L851.279 140.46C852.579 139.72 853.539 138.92 854.139 138.04H851.759V136.22ZM874.599 150.9H872.899L872.459 148.94C872.959 149.02 873.439 149.08 873.899 149.08C874.319 149.08 874.539 148.82 874.539 148.32V144.28C873.859 144.52 873.199 144.76 872.519 144.98L871.979 142.92C872.839 142.72 873.699 142.48 874.539 142.2V138.2H872.379V136.18H874.539V132.64H876.579V136.18H878.279V138.2H876.579V141.38C877.139 141.12 877.679 140.86 878.199 140.58V142.62C877.639 142.9 877.099 143.16 876.579 143.42V148.84C876.579 150.2 875.919 150.9 874.599 150.9ZM880.919 142.28C880.679 145.62 879.919 148.56 878.659 151.12L877.019 149.66C878.319 147.12 878.979 143.84 878.999 139.84V133.2H889.959V138.5H886.359V140.38H890.399V142.28H886.359V144.2H889.519V151.08H887.599V150.26H883.099V151.08H881.179V144.2H884.319V142.28H880.919ZM884.319 138.5H881.019V139.84C881.019 140.02 880.999 140.2 880.999 140.38H884.319V138.5ZM883.099 148.42H887.599V146.04H883.099V148.42ZM887.919 135.1H881.019V136.66H887.919V135.1Z'
          fill='black'
        />
        <defs>
          <pattern
            id='pattern0_400_386'
            patternContentUnits='objectBoundingBox'
            width='1'
            height='1'>
            <use
              xlinkHref='#image0_400_386'
              transform='scale(0.0135135 0.0107527)'
            />
          </pattern>
          <pattern
            id='pattern1_400_386'
            patternContentUnits='objectBoundingBox'
            width='1'
            height='1'>
            <use
              xlinkHref='#image1_400_386'
              transform='scale(0.0147059 0.010989)'
            />
          </pattern>
          <pattern
            id='pattern2_400_386'
            patternContentUnits='objectBoundingBox'
            width='1'
            height='1'>
            <use
              xlinkHref='#image2_400_386'
              transform='scale(0.0135135 0.0107527)'
            />
          </pattern>
          <radialGradient
            id='paint0_radial_400_386'
            cx='0'
            cy='0'
            r='1'
            gradientUnits='userSpaceOnUse'
            gradientTransform='translate(805.993 447.57) rotate(-13.1854) scale(275.543 136.543)'>
            <stop offset='0.4694' stopColor='#F25445' />
            <stop offset='0.9724' stopColor='white' />
          </radialGradient>
          <clipPath id='clip0_400_386'>
            <rect
              width='526'
              height='376'
              fill='white'
              transform='translate(543 208)'
            />
          </clipPath>
          <image
            id='image0_400_386'
            width='74'
            height='93'
            xlinkHref='data:image/png;base64,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'
          />
          <image
            id='image1_400_386'
            width='68'
            height='91'
            xlinkHref='data:image/png;base64,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'
          />
          <image
            id='image2_400_386'
            width='74'
            height='93'
            xlinkHref='data:image/png;base64,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'
          />
        </defs>
      </svg>
    </div>
  );
};
// 网络异常

const networkAbnormalityIcon = () => {
  return (
    <svg
      width='100%'
      height='100%'
      viewBox='0 0 526 297'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#clip0_430_710)'>
        <path
          d='M101.383 198.077L247.537 283.461L394.46 198.846L246.767 113.461L101.383 198.077Z'
          fill='#F1F1F1'
        />
        <path
          d='M105.23 125L251.384 210.385L398.307 125.769L250.615 40.3845L105.23 125Z'
          fill='#F8F8F8'
        />
        <path
          d='M251.383 210.385V241.923L398.306 156.539V125.769L251.383 210.385Z'
          fill='#DCDEDE'
        />
        <path
          d='M252.154 209.615V241.154L105.23 155.769V125L252.154 209.615Z'
          fill='#EAEBEB'
        />
        <path
          d='M289.661 283.515C145.176 315.277 16.1068 285.554 1.376 217.115C-9.96247 164.438 49.7683 104.177 143.768 64.7308C66.0914 99.9077 17.6991 151.462 27.476 196.885C40.6914 258.323 155.291 285.3 283.422 257.131C411.553 228.962 504.707 156.323 491.491 94.8846C481.461 48.2538 429.476 21.9846 341.068 24.0923C446.553 18.6692 513.007 48.0615 524.63 102.092C539.345 170.523 434.153 251.746 289.661 283.515Z'
          fill='url(#paint0_radial_430_710)'
        />
        <path
          d='M268.664 151.997C302.815 138.194 326.561 117.256 321.701 105.231C316.841 93.2068 285.216 94.6484 251.064 108.451C216.912 122.254 193.166 143.192 198.026 155.216C202.886 167.241 234.512 165.8 268.664 151.997Z'
          fill='#DCDEDE'
        />
        <mask
          id='mask0_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='229'
          y='138'
          width='19'
          height='11'>
          <path
            d='M247.392 148.846L230.238 139.515C229.984 139.146 229.73 138.785 229.469 138.423L246.623 147.754C246.876 148.115 247.138 148.477 247.392 148.846Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask0_430_710)'>
          <path
            d='M247.392 148.846L230.238 139.515C229.984 139.146 229.73 138.785 229.469 138.423L246.623 147.754C246.876 148.115 247.138 148.477 247.392 148.846Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask1_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='259'
          y='81'
          width='34'
          height='24'>
          <path
            d='M259.723 81.6538L276.876 90.9846C279.653 92.4923 282.3 94.323 284.776 96.4769C285.284 96.9077 285.776 97.3769 286.269 97.8384C286.753 98.2923 287.246 98.7692 287.715 99.2692C289.3 100.915 290.8 102.7 292.2 104.631L275.046 95.3C273.653 93.3692 272.153 91.5846 270.561 89.9384C270.092 89.4384 269.6 88.9615 269.115 88.5077C268.623 88.0461 268.123 87.5846 267.623 87.1461C265.146 85 262.5 83.1692 259.723 81.6538Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask1_430_710)'>
          <path
            d='M292.192 104.631L275.038 95.3C273.646 93.3692 272.146 91.5846 270.553 89.9384C270.084 89.4384 269.592 88.9615 269.107 88.5077C268.615 88.0461 268.115 87.5846 267.615 87.1461C265.138 85 262.492 83.1692 259.715 81.6538L276.869 90.9846C279.646 92.4923 282.292 94.323 284.769 96.4769C285.276 96.9077 285.769 97.3769 286.261 97.8384C286.746 98.2923 287.238 98.7692 287.707 99.2692C289.299 100.915 290.799 102.7 292.192 104.631Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask2_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='271'
          y='58'
          width='19'
          height='11'>
          <path
            d='M271.868 58.8462L289.022 68.177C288.999 68.1693 288.984 68.1539 288.96 68.1462L271.807 58.8154C271.83 58.8231 271.845 58.8308 271.868 58.8462Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask2_430_710)'>
          <path
            d='M288.96 68.1384L271.807 58.8076C271.83 58.8153 271.845 58.8307 271.868 58.8384L289.022 68.1692C288.999 68.1615 288.984 68.1538 288.96 68.1384Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask3_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='270'
          y='58'
          width='19'
          height='10'>
          <path
            d='M271.045 58.3769L288.199 67.7077C288.038 67.623 287.884 67.5384 287.722 67.4538L270.568 58.123C270.722 58.2077 270.884 58.2923 271.045 58.3769Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask3_430_710)'>
          <path
            d='M287.714 67.4616L270.561 58.1309C270.722 58.2155 270.884 58.2924 271.037 58.3847L288.191 67.7155C288.037 67.6232 287.876 67.5385 287.714 67.4616Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask4_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='245'
          y='103'
          width='20'
          height='12'>
          <path
            d='M247.254 104.838L264.408 114.169C263.792 113.838 263.169 113.531 262.539 113.254L245.385 103.923C246.016 104.2 246.639 104.508 247.254 104.838Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask4_430_710)'>
          <path
            d='M262.539 113.254L245.385 103.923C245.7 104.062 246.008 104.208 246.316 104.362L263.469 113.692C263.169 113.538 262.854 113.392 262.539 113.254Z'
            fill='#3D70A3'
          />
          <path
            d='M263.476 113.685L246.322 104.354C246.638 104.508 246.945 104.669 247.253 104.838L264.407 114.169C264.099 114 263.784 113.838 263.476 113.685Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask5_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='244'
          y='103'
          width='19'
          height='11'>
          <path
            d='M261.923 112.977L244.77 103.646C244.977 103.739 245.185 103.823 245.393 103.923L262.546 113.254C262.339 113.154 262.131 113.062 261.923 112.977Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask5_430_710)'>
          <path
            d='M261.923 112.977L244.77 103.646C244.977 103.739 245.185 103.823 245.393 103.923L262.546 113.254C262.339 113.154 262.131 113.062 261.923 112.977Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask6_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='282'
          y='34'
          width='46'
          height='36'>
          <path
            d='M282.139 34.7078L299.293 44.0385C310.139 49.9385 319.577 58.5847 327.662 69.7078L310.508 60.377C302.423 49.2462 292.985 40.6078 282.139 34.7078Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask6_430_710)'>
          <path
            d='M327.662 69.7078L310.508 60.377C302.416 49.2539 292.985 40.6078 282.139 34.7078L299.293 44.0385C310.139 49.9385 319.577 58.577 327.662 69.7078Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask7_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='244'
          y='103'
          width='18'
          height='10'>
          <path
            d='M261.677 112.877L244.523 103.546C244.6 103.585 244.685 103.615 244.762 103.646L261.916 112.977C261.839 112.946 261.762 112.915 261.677 112.877Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask7_430_710)'>
          <path
            d='M261.677 112.877L244.523 103.546C244.531 103.554 244.539 103.554 244.547 103.562L261.7 112.892C261.7 112.885 261.693 112.877 261.677 112.877Z'
            fill='#3B6D9E'
          />
          <path
            d='M261.709 112.884L244.555 103.554C244.624 103.592 244.701 103.615 244.77 103.638L261.924 112.969C261.847 112.946 261.778 112.923 261.709 112.884Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask8_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='269'
          y='57'
          width='19'
          height='11'>
          <path
            d='M270.268 57.9614L287.422 67.2922C287.145 67.1383 286.86 66.9922 286.568 66.8537L269.414 57.5229C269.706 57.6691 269.991 57.8076 270.268 57.9614Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask8_430_710)'>
          <path
            d='M286.568 66.8537L269.414 57.5229C269.46 57.546 269.499 57.5614 269.545 57.5845L286.699 66.923C286.653 66.8999 286.606 66.8768 286.568 66.8537Z'
            fill='#3D70A3'
          />
          <path
            d='M286.699 66.9231L269.545 57.5923C269.791 57.7154 270.03 57.8384 270.268 57.9692L287.422 67.3C287.183 67.1615 286.945 67.0384 286.699 66.9231Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask9_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='243'
          y='103'
          width='19'
          height='10'>
          <path
            d='M260.73 112.515L243.576 103.185C243.899 103.3 244.215 103.431 244.522 103.546L261.676 112.877C261.368 112.754 261.053 112.631 260.73 112.515Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask9_430_710)'>
          <path
            d='M260.73 112.515L243.576 103.185C243.899 103.3 244.215 103.431 244.522 103.546L261.676 112.877C261.368 112.754 261.053 112.631 260.73 112.515Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask10_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='241'
          y='102'
          width='20'
          height='11'>
          <path
            d='M258.615 111.838L241.461 102.508C241.784 102.592 242.107 102.692 242.43 102.792C242.815 102.908 243.207 103.046 243.584 103.192L260.738 112.523C260.361 112.377 259.976 112.246 259.584 112.123C259.261 112.015 258.938 111.915 258.615 111.838Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask10_430_710)'>
          <path
            d='M258.615 111.838L241.461 102.508C241.476 102.515 241.492 102.515 241.515 102.523L258.669 111.854C258.646 111.846 258.63 111.838 258.615 111.838Z'
            fill='#3F74A8'
          />
          <path
            d='M258.669 111.846L241.516 102.515C241.816 102.592 242.131 102.692 242.431 102.785C242.816 102.9 243.208 103.038 243.585 103.185L260.739 112.515C260.362 112.369 259.977 112.238 259.585 112.115C259.277 112.023 258.969 111.931 258.669 111.846Z'
            fill='#3D70A3'
          />
        </g>
        <path
          d='M258.446 111.785L258.615 111.831C258.938 111.915 259.261 112.015 259.584 112.115C259.969 112.231 260.361 112.369 260.738 112.515C261.061 112.631 261.377 112.762 261.684 112.877C261.761 112.915 261.846 112.946 261.923 112.977C262.131 113.069 262.338 113.154 262.546 113.254C263.731 113.777 264.9 114.408 266.015 115.123C266.277 115.3 266.546 115.477 266.8 115.654C267.123 115.877 267.454 116.108 267.777 116.346C268.069 116.562 268.354 116.792 268.646 117.023L268.784 117.139C269.077 117.369 269.377 117.623 269.661 117.877C269.8 118.008 269.931 118.115 270.069 118.254C271.131 119.208 272.138 120.285 273.1 121.439C273.392 121.8 273.684 122.162 273.969 122.546C265.092 131.323 256.423 139.915 247.384 148.854C247.131 148.485 246.877 148.123 246.615 147.762L258.446 111.785Z'
          fill='#5399DF'
        />
        <path
          d='M246.623 147.746L229.469 138.415L241.292 102.454L258.446 111.785L246.623 147.746Z'
          fill='#35618E'
        />
        <path
          d='M258.445 111.785L241.291 102.454L241.46 102.508L258.614 111.838L258.445 111.785Z'
          fill='#3E73A7'
        />
        <mask
          id='mask11_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='260'
          y='53'
          width='27'
          height='14'>
          <path
            d='M277.169 63.0307L260.016 53.7C260.554 53.8538 261.1 54.0384 261.631 54.2153C262.077 54.3615 262.523 54.5307 262.962 54.6923C263.385 54.8461 263.816 55.0076 264.239 55.1769C264.654 55.3384 265.077 55.5076 265.493 55.6923C266.823 56.2384 268.131 56.8461 269.416 57.523L286.569 66.8538C285.285 66.1769 283.969 65.5692 282.646 65.023C282.231 64.8461 281.808 64.6692 281.393 64.5076C280.977 64.3384 280.546 64.1769 280.116 64.023C279.677 63.8615 279.239 63.6923 278.785 63.5461C278.254 63.3692 277.708 63.1846 277.169 63.0307Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask11_430_710)'>
          <path
            d='M277.169 63.0307L260.016 53.7C260.554 53.8538 261.1 54.0384 261.631 54.2153C262.077 54.3615 262.523 54.5307 262.962 54.6923C263.385 54.8461 263.816 55.0076 264.239 55.1769C264.654 55.3384 265.077 55.5076 265.493 55.6923C266.462 56.0923 267.416 56.5153 268.362 56.9846L285.516 66.3153C284.569 65.8461 283.608 65.423 282.646 65.023C282.231 64.8461 281.808 64.6692 281.393 64.5076C280.977 64.3384 280.546 64.1769 280.116 64.023C279.677 63.8615 279.239 63.6923 278.785 63.5461C278.254 63.3692 277.708 63.1846 277.169 63.0307Z'
            fill='#3D70A3'
          />
          <path
            d='M285.515 66.323L268.361 56.9922C268.715 57.1691 269.069 57.346 269.415 57.5307L286.569 66.8614C286.215 66.6691 285.869 66.4922 285.515 66.323Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask12_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='233'
          y='102'
          width='19'
          height='11'>
          <path
            d='M250.337 112.123L233.184 102.792C233.507 102.815 233.837 102.823 234.161 102.861L251.314 112.192C250.984 112.154 250.661 112.138 250.337 112.123Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask12_430_710)'>
          <path
            d='M250.337 112.123L233.184 102.792C233.507 102.815 233.837 102.823 234.161 102.861L251.314 112.192C250.984 112.154 250.661 112.138 250.337 112.123Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask13_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='232'
          y='102'
          width='19'
          height='11'>
          <path
            d='M249.822 112.1L232.668 102.769C232.837 102.769 233.014 102.785 233.176 102.792L250.33 112.123C250.168 112.115 249.991 112.1 249.822 112.1Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask13_430_710)'>
          <path
            d='M249.822 112.1L232.668 102.769C232.837 102.769 233.014 102.785 233.176 102.792L250.33 112.123C250.168 112.115 249.991 112.1 249.822 112.1Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask14_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='231'
          y='102'
          width='19'
          height='11'>
          <path
            d='M248.599 112.085L231.445 102.754C231.861 102.754 232.268 102.754 232.668 102.769L249.822 112.1C249.422 112.085 249.007 112.085 248.599 112.085Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask14_430_710)'>
          <path
            d='M248.599 112.085L231.445 102.754C231.861 102.754 232.268 102.754 232.668 102.769L249.822 112.1C249.422 112.085 249.007 112.085 248.599 112.085Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask15_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='231'
          y='102'
          width='18'
          height='11'>
          <path
            d='M248.4 112.085L231.246 102.754C231.315 102.754 231.377 102.746 231.446 102.754L248.6 112.085C248.531 112.077 248.469 112.085 248.4 112.085Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask15_430_710)'>
          <path
            d='M248.4 112.085L231.246 102.754C231.315 102.754 231.377 102.746 231.446 102.754L248.6 112.085C248.531 112.077 248.469 112.085 248.4 112.085Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask16_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='258'
          y='53'
          width='20'
          height='11'>
          <path
            d='M275.353 62.4924L258.199 53.1616C258.299 53.1847 258.399 53.2155 258.492 53.2385C258.999 53.377 259.507 53.5308 260.015 53.7001L277.168 63.0309C276.661 62.8693 276.161 62.7155 275.645 62.5693C275.545 62.5462 275.445 62.5155 275.353 62.4924Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask16_430_710)'>
          <path
            d='M275.353 62.4924L258.199 53.1616C258.23 53.1693 258.261 53.177 258.292 53.1847L275.445 62.5155C275.407 62.5078 275.376 62.5001 275.353 62.4924Z'
            fill='#3F74A8'
          />
          <path
            d='M275.437 62.5156L258.283 53.1848C258.352 53.2002 258.422 53.2233 258.491 53.2387C258.999 53.3771 259.506 53.531 260.014 53.7002L277.168 63.031C276.66 62.8694 276.16 62.7156 275.645 62.5694C275.576 62.554 275.506 62.531 275.437 62.5156Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask17_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='230'
          y='102'
          width='19'
          height='11'>
          <path
            d='M247.337 112.115L230.184 102.785C230.537 102.769 230.899 102.754 231.253 102.754L248.407 112.085C248.045 112.092 247.684 112.1 247.337 112.115Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask17_430_710)'>
          <path
            d='M247.337 112.115L230.184 102.785C230.537 102.769 230.899 102.754 231.253 102.754L248.407 112.085C248.045 112.092 247.684 112.1 247.337 112.115Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask18_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='228'
          y='102'
          width='20'
          height='11'>
          <path
            d='M245.992 112.231L228.838 102.9C229.284 102.854 229.738 102.823 230.184 102.785L247.338 112.115C246.892 112.154 246.438 112.185 245.992 112.231Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask18_430_710)'>
          <path
            d='M245.992 112.231L228.838 102.9C229.284 102.854 229.738 102.823 230.184 102.785L247.338 112.115C246.892 112.154 246.438 112.185 245.992 112.231Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask19_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='227'
          y='102'
          width='19'
          height='11'>
          <path
            d='M244.892 112.361L227.738 103.031C228.146 102.977 228.484 102.931 228.838 102.9L245.992 112.231C245.631 112.261 245.292 112.308 244.892 112.361Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask19_430_710)'>
          <path
            d='M244.892 112.361L227.738 103.031C228.146 102.977 228.484 102.931 228.838 102.9L245.992 112.231C245.631 112.261 245.292 112.308 244.892 112.361Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask20_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='254'
          y='52'
          width='22'
          height='11'>
          <path
            d='M272.146 61.6923L254.992 52.3616C255.531 52.477 256.069 52.6077 256.608 52.7385C257.146 52.877 257.669 53.0077 258.2 53.1616L275.354 62.4923C274.823 62.3385 274.292 62.2077 273.761 62.0693C273.223 61.9462 272.677 61.8154 272.146 61.6923Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask20_430_710)'>
          <path
            d='M272.146 61.6923L254.992 52.3616C255.531 52.477 256.069 52.6077 256.608 52.7385C256.869 52.8077 257.123 52.8693 257.377 52.9385L274.531 62.2693C274.277 62.2 274.015 62.1385 273.761 62.0693C273.223 61.9462 272.677 61.8154 272.146 61.6923Z'
            fill='#3F74A8'
          />
          <path
            d='M274.531 62.2692L257.377 52.9385C257.654 53.0077 257.923 53.0846 258.2 53.1616L275.354 62.4923C275.077 62.4154 274.8 62.3462 274.531 62.2692Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask21_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='226'
          y='103'
          width='19'
          height='10'>
          <path
            d='M243.554 112.585L226.4 103.254C226.847 103.162 227.293 103.092 227.739 103.031L244.893 112.362C244.447 112.423 243.993 112.492 243.554 112.585Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask21_430_710)'>
          <path
            d='M243.554 112.585L226.4 103.254C226.847 103.162 227.293 103.092 227.739 103.031L244.893 112.362C244.447 112.423 243.993 112.492 243.554 112.585Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask22_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='226'
          y='103'
          width='18'
          height='10'>
          <path
            d='M243.314 112.623L226.16 103.292C226.245 103.277 226.329 103.262 226.391 103.254L243.545 112.585C243.483 112.592 243.399 112.608 243.314 112.623Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask22_430_710)'>
          <path
            d='M243.314 112.623L226.16 103.292C226.245 103.277 226.329 103.262 226.391 103.254L243.545 112.585C243.483 112.592 243.399 112.608 243.314 112.623Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask23_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='223'
          y='103'
          width='21'
          height='11'>
          <path
            d='M240.746 113.254L223.592 103.923C224.03 103.792 224.476 103.677 224.915 103.569C225.33 103.462 225.746 103.369 226.161 103.285L243.315 112.615C242.899 112.692 242.492 112.792 242.069 112.9C241.638 113.015 241.184 113.131 240.746 113.254Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask23_430_710)'>
          <path
            d='M240.746 113.254L223.592 103.923C224.03 103.792 224.476 103.677 224.915 103.569C225.33 103.462 225.746 103.369 226.161 103.285L243.315 112.615C242.899 112.692 242.492 112.792 242.069 112.9C241.638 113.015 241.184 113.131 240.746 113.254Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask24_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='223'
          y='103'
          width='18'
          height='11'>
          <path
            d='M240.56 113.308L223.406 103.977C223.475 103.962 223.537 103.938 223.591 103.923L240.745 113.254C240.691 113.269 240.629 113.292 240.56 113.308Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask24_430_710)'>
          <path
            d='M240.56 113.308L223.406 103.977C223.475 103.962 223.537 103.938 223.591 103.923L240.745 113.254C240.691 113.269 240.629 113.292 240.56 113.308Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask25_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='220'
          y='103'
          width='21'
          height='12'>
          <path
            d='M237.931 114.223L220.777 104.892C220.877 104.854 220.97 104.815 221.07 104.777C221.231 104.715 221.4 104.654 221.562 104.592C221.739 104.523 221.923 104.461 222.1 104.392C222.547 104.246 222.977 104.1 223.408 103.977L240.562 113.308C240.131 113.438 239.7 113.577 239.254 113.723C239.077 113.784 238.893 113.854 238.716 113.923C238.554 113.985 238.385 114.046 238.223 114.108C238.123 114.146 238.031 114.185 237.931 114.223Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask25_430_710)'>
          <path
            d='M237.931 114.223L220.777 104.892C220.877 104.854 220.97 104.815 221.07 104.777C221.231 104.715 221.4 104.654 221.562 104.592C221.739 104.523 221.923 104.461 222.1 104.392C222.547 104.246 222.977 104.1 223.408 103.977L240.562 113.308C240.131 113.438 239.7 113.577 239.254 113.723C239.077 113.784 238.893 113.854 238.716 113.923C238.554 113.985 238.385 114.046 238.223 114.108C238.123 114.146 238.031 114.185 237.931 114.223Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask26_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='219'
          y='104'
          width='19'
          height='11'>
          <path
            d='M236.615 114.785L219.461 105.454C219.784 105.308 220.122 105.162 220.453 105.023C220.561 104.977 220.669 104.931 220.776 104.892L237.93 114.223C237.822 114.269 237.715 114.315 237.607 114.354C237.276 114.5 236.946 114.638 236.615 114.785Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask26_430_710)'>
          <path
            d='M236.615 114.785L219.461 105.454C219.784 105.308 220.122 105.162 220.453 105.023C220.561 104.977 220.669 104.931 220.776 104.892L237.93 114.223C237.822 114.269 237.715 114.315 237.607 114.354C237.276 114.5 236.946 114.638 236.615 114.785Z'
            fill='#3F74A8'
          />
        </g>
        <path
          d='M237.608 114.354C237.277 114.492 236.938 114.638 236.615 114.785C236.946 114.638 237.277 114.492 237.608 114.354Z'
          fill='#5399DF'
        />
        <mask
          id='mask27_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='219'
          y='105'
          width='19'
          height='10'>
          <path
            d='M236.615 114.784L219.461 105.454C219.792 105.308 220.122 105.161 220.453 105.023L237.607 114.354C237.276 114.492 236.946 114.638 236.615 114.784Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask27_430_710)'>
          <path
            d='M236.615 114.784L219.461 105.454C219.792 105.308 220.122 105.161 220.453 105.023L237.607 114.354C237.276 114.492 236.946 114.638 236.615 114.784Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask28_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='219'
          y='105'
          width='18'
          height='10'>
          <path
            d='M236.416 114.885L219.262 105.554C219.331 105.523 219.4 105.5 219.462 105.461L236.616 114.792C236.546 114.831 236.485 114.854 236.416 114.885Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask28_430_710)'>
          <path
            d='M236.416 114.884L219.262 105.554C219.308 105.538 219.346 105.515 219.392 105.5L236.546 114.831C236.5 114.846 236.454 114.861 236.416 114.884Z'
            fill='#3F74A8'
          />
          <path
            d='M236.546 114.823L219.393 105.492C219.416 105.484 219.439 105.469 219.462 105.454L236.616 114.784C236.593 114.8 236.57 114.815 236.546 114.823Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask29_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='215'
          y='105'
          width='22'
          height='12'>
          <path
            d='M232.691 116.931L215.537 107.6C215.952 107.331 216.322 107.108 216.691 106.9C217.129 106.646 217.576 106.4 218.022 106.169C218.422 105.961 218.837 105.754 219.26 105.554L236.414 114.884C235.991 115.084 235.576 115.284 235.176 115.5C234.722 115.731 234.283 115.977 233.845 116.231C233.468 116.438 233.099 116.654 232.691 116.931Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask29_430_710)'>
          <path
            d='M232.691 116.931L215.537 107.6C215.952 107.331 216.322 107.108 216.691 106.9C217.129 106.646 217.576 106.4 218.022 106.169C218.399 105.969 218.783 105.777 219.183 105.592L236.337 114.923C235.937 115.108 235.552 115.308 235.176 115.5C234.722 115.731 234.283 115.977 233.845 116.231C233.468 116.438 233.099 116.654 232.691 116.931Z'
            fill='#3D70A3'
          />
          <path
            d='M236.337 114.915L219.184 105.585C219.207 105.569 219.23 105.562 219.261 105.546L236.414 114.877C236.391 114.892 236.361 114.908 236.337 114.915Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask30_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='215'
          y='107'
          width='18'
          height='11'>
          <path
            d='M232.285 117.185L215.131 107.854C215.269 107.762 215.392 107.685 215.531 107.6L232.685 116.931C232.554 117.015 232.423 117.092 232.285 117.185Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask30_430_710)'>
          <path
            d='M232.285 117.185L215.131 107.854C215.269 107.762 215.392 107.685 215.531 107.6L232.685 116.931C232.554 117.015 232.423 117.092 232.285 117.185Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask31_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='214'
          y='107'
          width='19'
          height='11'>
          <path
            d='M231.246 117.892L214.092 108.561C214.484 108.285 214.807 108.061 215.138 107.854L232.292 117.185C231.961 117.392 231.638 117.615 231.246 117.892Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask31_430_710)'>
          <path
            d='M231.246 117.892L214.092 108.561C214.215 108.477 214.33 108.4 214.438 108.323L231.592 117.654C231.476 117.723 231.369 117.808 231.246 117.892Z'
            fill='#3B6D9E'
          />
          <path
            d='M231.591 117.646L214.438 108.315C214.684 108.146 214.907 107.992 215.138 107.846L232.291 117.177C232.061 117.323 231.838 117.477 231.591 117.646Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask32_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='212'
          y='108'
          width='20'
          height='12'>
          <path
            d='M229.691 119.061L212.537 109.731C212.737 109.569 212.929 109.415 213.137 109.261C213.46 109.015 213.776 108.784 214.091 108.554L231.245 117.884C230.922 118.115 230.606 118.346 230.291 118.592C230.083 118.754 229.891 118.908 229.691 119.061Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask32_430_710)'>
          <path
            d='M229.691 119.061L212.537 109.731C212.737 109.569 212.929 109.415 213.137 109.261C213.46 109.015 213.776 108.784 214.091 108.554L231.245 117.884C230.922 118.115 230.606 118.346 230.291 118.592C230.083 118.754 229.891 118.908 229.691 119.061Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask33_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='210'
          y='109'
          width='20'
          height='12'>
          <path
            d='M227.923 120.569L210.77 111.238C211.039 110.985 211.323 110.738 211.6 110.5C211.908 110.231 212.216 109.977 212.539 109.731L229.693 119.061C229.37 119.308 229.062 119.561 228.754 119.831C228.477 120.077 228.193 120.323 227.923 120.569Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask33_430_710)'>
          <path
            d='M227.923 120.569L210.77 111.238C210.831 111.184 210.893 111.123 210.954 111.069L228.108 120.4C228.046 120.461 227.985 120.515 227.923 120.569Z'
            fill='#396999'
          />
          <path
            d='M228.109 120.4L210.955 111.069C211.17 110.877 211.386 110.685 211.601 110.5C211.909 110.231 212.217 109.977 212.54 109.731L229.694 119.061C229.37 119.308 229.063 119.561 228.755 119.831C228.54 120.023 228.324 120.208 228.109 120.4Z'
            fill='#3B6D9E'
          />
        </g>
        <path
          d='M249.953 79.9923L232.799 70.6616L254.991 52.3616L272.145 61.6923L249.953 79.9923Z'
          fill='#4075AB'
        />
        <path
          d='M272.146 61.6924C272.684 61.8078 273.222 61.9385 273.761 62.0693C274.299 62.2078 274.822 62.3385 275.353 62.4924C275.453 62.5155 275.553 62.5462 275.646 62.5693C276.153 62.7078 276.661 62.8616 277.169 63.0308C277.707 63.1847 278.253 63.3693 278.784 63.5462C279.23 63.6924 279.676 63.8616 280.115 64.0232C280.538 64.177 280.969 64.3385 281.392 64.5078C281.807 64.6693 282.23 64.8385 282.646 65.0232C283.976 65.5693 285.284 66.177 286.569 66.8539C286.961 67.0462 287.346 67.2462 287.722 67.4539C288.146 67.6693 288.561 67.9001 288.969 68.1385C289.399 68.3693 289.822 68.6232 290.246 68.877C290.761 69.2001 291.276 69.5155 291.799 69.8462C292.122 70.0462 292.461 70.2693 292.784 70.4924C293.115 70.7155 293.446 70.9385 293.769 71.177C294.992 72.0155 296.192 72.9308 297.361 73.9001C297.638 74.1232 297.899 74.3462 298.176 74.5693C299.607 75.7847 301.007 77.077 302.353 78.477C302.607 78.7155 302.846 78.9693 303.092 79.2308C304.192 80.3847 305.269 81.6078 306.322 82.9001C306.592 83.2232 306.861 83.5462 307.115 83.877C307.246 84.0385 307.376 84.2155 307.499 84.3847C307.776 84.7385 308.046 85.0924 308.307 85.4539C308.784 86.0924 309.253 86.7462 309.715 87.4078C303.892 93.1308 298.053 98.877 292.215 104.615C290.822 102.685 289.322 100.9 287.73 99.2539C287.261 98.7539 286.769 98.277 286.284 97.8232C285.792 97.3616 285.292 96.9001 284.792 96.4616C281.092 93.2462 276.999 90.7385 272.661 88.9539C272.384 88.8462 272.122 88.7385 271.846 88.6232C269.646 87.777 267.392 87.1001 265.092 86.6001C264.446 86.4616 263.807 86.3385 263.153 86.2385C260.084 85.7078 256.922 85.5001 253.73 85.6078L249.961 79.977L272.146 61.6924Z'
          fill='#5399DF'
        />
        <path
          d='M253.714 85.6309L236.56 76.3001L232.799 70.6616L249.953 79.9924L253.714 85.6309Z'
          fill='#305880'
        />
        <path
          d='M227.769 120.715L210.615 111.385L210.769 111.239L227.923 120.569L227.769 120.715Z'
          fill='#3F74A9'
        />
        <mask
          id='mask34_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='210'
          y='111'
          width='18'
          height='11'>
          <path
            d='M227.361 121.085L210.207 111.754C210.338 111.623 210.476 111.5 210.607 111.377L227.761 120.708C227.638 120.838 227.492 120.954 227.361 121.085Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask34_430_710)'>
          <path
            d='M227.361 121.085L210.207 111.754C210.338 111.623 210.476 111.5 210.607 111.377L227.761 120.708C227.638 120.838 227.492 120.954 227.361 121.085Z'
            fill='#396999'
          />
        </g>
        <path
          d='M227.269 121.185L210.115 111.846L210.208 111.754L227.361 121.085L227.269 121.185Z'
          fill='#3E73A8'
        />
        <mask
          id='mask35_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='209'
          y='111'
          width='19'
          height='11'>
          <path
            d='M226.431 122L209.277 112.669C209.547 112.392 209.824 112.115 210.108 111.846L227.262 121.177C226.985 121.446 226.708 121.723 226.431 122Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask35_430_710)'>
          <path
            d='M226.431 122L209.277 112.669C209.547 112.392 209.824 112.115 210.108 111.846L227.262 121.177C226.985 121.446 226.708 121.723 226.431 122Z'
            fill='#396999'
          />
        </g>
        <path
          d='M226.33 122.1L209.176 112.769L209.276 112.669L226.43 122L226.33 122.1Z'
          fill='#3E72A6'
        />
        <mask
          id='mask36_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='208'
          y='112'
          width='19'
          height='12'>
          <path
            d='M225.361 123.123L208.207 113.792C208.522 113.439 208.845 113.1 209.176 112.769L226.33 122.1C226.007 122.439 225.676 122.777 225.361 123.123Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask36_430_710)'>
          <path
            d='M225.361 123.123L208.207 113.792C208.522 113.439 208.845 113.1 209.176 112.769L226.33 122.1C226.007 122.439 225.676 122.777 225.361 123.123Z'
            fill='#396999'
          />
        </g>
        <path
          d='M225.253 123.254L208.1 113.923L208.215 113.792L225.361 123.123L225.253 123.254Z'
          fill='#3D70A3'
        />
        <mask
          id='mask37_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='206'
          y='113'
          width='20'
          height='13'>
          <path
            d='M223.56 125.262L206.406 115.931C206.606 115.685 206.806 115.438 207.006 115.2C207.368 114.754 207.729 114.338 208.099 113.923L225.252 123.254C224.883 123.669 224.522 124.092 224.16 124.531C223.96 124.769 223.768 125.008 223.56 125.262Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask37_430_710)'>
          <path
            d='M223.56 125.262L206.406 115.931C206.606 115.685 206.806 115.438 207.006 115.2C207.322 114.808 207.645 114.431 207.975 114.062L225.129 123.392C224.799 123.762 224.475 124.138 224.16 124.531C223.96 124.769 223.768 125.008 223.56 125.262Z'
            fill='#376694'
          />
          <path
            d='M225.128 123.392L207.975 114.062C208.013 114.015 208.059 113.969 208.098 113.923L225.252 123.254C225.213 123.3 225.167 123.346 225.128 123.392Z'
            fill='#396999'
          />
        </g>
        <mask
          id='mask38_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='205'
          y='115'
          width='19'
          height='12'>
          <path
            d='M222.423 126.785L205.27 117.454C205.639 116.931 206.023 116.423 206.408 115.931L223.562 125.261C223.177 125.754 222.8 126.254 222.423 126.785Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask38_430_710)'>
          <path
            d='M222.423 126.785L205.27 117.454C205.639 116.931 206.023 116.423 206.408 115.931L223.562 125.261C223.177 125.754 222.8 126.254 222.423 126.785Z'
            fill='#376694'
          />
        </g>
        <path
          d='M244.891 112.361C245.299 112.308 245.637 112.261 245.991 112.231C246.437 112.184 246.891 112.154 247.337 112.115C247.691 112.1 248.053 112.084 248.406 112.084C248.476 112.084 248.537 112.077 248.606 112.084C249.022 112.084 249.43 112.084 249.83 112.1C249.999 112.1 250.176 112.115 250.337 112.123C250.66 112.146 250.991 112.154 251.314 112.192L244.399 148.984C236.883 141.392 229.645 134.077 222.422 126.784C222.791 126.261 223.176 125.754 223.56 125.261C223.76 125.015 223.96 124.769 224.16 124.531C224.522 124.084 224.883 123.669 225.253 123.254L225.36 123.123C225.676 122.769 225.999 122.431 226.33 122.1L226.43 122C226.699 121.723 226.976 121.446 227.26 121.177L227.36 121.084C227.491 120.954 227.63 120.831 227.76 120.708L227.914 120.569C228.183 120.315 228.468 120.069 228.745 119.831C229.053 119.561 229.36 119.308 229.683 119.061C229.883 118.9 230.076 118.746 230.283 118.592C230.606 118.346 230.922 118.115 231.237 117.884C231.63 117.608 231.953 117.384 232.283 117.177C232.422 117.084 232.545 117.008 232.683 116.923C233.099 116.654 233.468 116.431 233.837 116.223C234.276 115.969 234.722 115.723 235.168 115.492C235.568 115.284 235.983 115.077 236.406 114.877C236.476 114.846 236.545 114.823 236.606 114.784C236.93 114.638 237.268 114.492 237.599 114.354C237.706 114.308 237.814 114.261 237.922 114.223C238.022 114.184 238.114 114.146 238.214 114.108C238.376 114.046 238.545 113.984 238.707 113.923C238.883 113.854 239.068 113.792 239.245 113.723C239.691 113.577 240.122 113.431 240.553 113.308C240.622 113.292 240.683 113.269 240.737 113.254C241.176 113.123 241.622 113.008 242.06 112.9C242.476 112.792 242.891 112.7 243.306 112.615C243.391 112.6 243.476 112.584 243.537 112.577C243.991 112.492 244.445 112.423 244.891 112.361Z'
          fill='#5399DF'
        />
        <mask
          id='mask39_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='205'
          y='117'
          width='40'
          height='32'>
          <path
            d='M244.4 148.977L227.246 139.646C219.731 132.054 212.493 124.738 205.27 117.446L222.423 126.777C229.646 134.069 236.885 141.385 244.4 148.977Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask39_430_710)'>
          <path
            d='M244.4 148.977L227.246 139.646C219.731 132.054 212.493 124.738 205.27 117.446L222.423 126.777C229.646 134.069 236.885 141.385 244.4 148.977Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask40_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='239'
          y='52'
          width='20'
          height='10'>
          <path
            d='M256.746 61.3615L239.592 52.0308C240.115 52.0385 240.653 52.0538 241.184 52.0769L258.338 61.4077C257.799 61.3846 257.269 61.3692 256.746 61.3615Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask40_430_710)'>
          <path
            d='M256.746 61.3615L239.592 52.0308C240.115 52.0385 240.653 52.0538 241.184 52.0769L258.338 61.4077C257.799 61.3846 257.269 61.3692 256.746 61.3615Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask41_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='238'
          y='52'
          width='19'
          height='10'>
          <path
            d='M255.445 61.3461L238.291 52.0154C238.722 52.0154 239.16 52.0231 239.583 52.0308L256.737 61.3615C256.314 61.3461 255.883 61.3385 255.445 61.3461Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask41_430_710)'>
          <path
            d='M255.445 61.3461L238.291 52.0154C238.722 52.0154 239.16 52.0231 239.583 52.0308L256.737 61.3615C256.314 61.3461 255.883 61.3385 255.445 61.3461Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask42_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='237'
          y='52'
          width='19'
          height='10'>
          <path
            d='M255.015 61.3462L237.861 52.0154C238.007 52.0001 238.154 52.0001 238.3 52.0154L255.454 61.3462C255.3 61.3308 255.161 61.3308 255.015 61.3462Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask42_430_710)'>
          <path
            d='M255.015 61.3462L237.861 52.0154C238.007 52.0001 238.154 52.0001 238.3 52.0154L255.454 61.3462C255.3 61.3308 255.161 61.3308 255.015 61.3462Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask43_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='235'
          y='52'
          width='21'
          height='10'>
          <path
            d='M252.369 61.4L235.215 52.0692C235.507 52.0539 235.815 52.0462 236.115 52.0385C236.361 52.0308 236.623 52.0308 236.876 52.0231C237.199 52.0154 237.53 52.0077 237.861 52.0154L255.015 61.3462C254.684 61.3308 254.353 61.3462 254.03 61.3539C253.776 61.3539 253.523 61.3615 253.269 61.3692C252.969 61.3692 252.661 61.3846 252.369 61.4Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask43_430_710)'>
          <path
            d='M252.369 61.4L235.215 52.0692C235.507 52.0539 235.815 52.0462 236.115 52.0385C236.361 52.0308 236.623 52.0308 236.876 52.0231C237.199 52.0154 237.53 52.0077 237.861 52.0154L255.015 61.3462C254.684 61.3308 254.353 61.3462 254.03 61.3539C253.776 61.3539 253.523 61.3615 253.269 61.3692C252.969 61.3692 252.661 61.3846 252.369 61.4Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask44_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='233'
          y='52'
          width='20'
          height='10'>
          <path
            d='M250.654 61.4922L233.5 52.1614C233.8 52.1383 234.1 52.1229 234.4 52.1075C234.677 52.0922 234.938 52.0691 235.223 52.0691L252.377 61.3999C252.1 61.4076 251.838 61.4229 251.554 61.4383C251.254 61.4537 250.954 61.4691 250.654 61.4922Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask44_430_710)'>
          <path
            d='M250.654 61.4922L233.5 52.1614C233.8 52.1383 234.1 52.1229 234.4 52.1075C234.677 52.0922 234.938 52.0691 235.223 52.0691L252.377 61.3999C252.1 61.4076 251.838 61.4229 251.554 61.4383C251.254 61.4537 250.954 61.4691 250.654 61.4922Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask45_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='232'
          y='52'
          width='19'
          height='10'>
          <path
            d='M249.976 61.5385L232.822 52.2078C233.045 52.1847 233.268 52.1693 233.491 52.1616L250.645 61.4924C250.43 61.5001 250.199 61.5155 249.976 61.5385Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask45_430_710)'>
          <path
            d='M249.976 61.5385L232.822 52.2078C233.045 52.1847 233.268 52.1693 233.491 52.1616L250.645 61.4924C250.43 61.5001 250.199 61.5155 249.976 61.5385Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask46_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='231'
          y='52'
          width='19'
          height='10'>
          <path
            d='M248.953 61.6308L231.799 52.3001C232.145 52.2616 232.483 52.2308 232.83 52.2078L249.976 61.5385C249.63 61.5616 249.291 61.5924 248.953 61.6308Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask46_430_710)'>
          <path
            d='M248.953 61.6308L231.799 52.3001C232.145 52.2616 232.483 52.2308 232.83 52.2078L249.976 61.5385C249.63 61.5616 249.291 61.5924 248.953 61.6308Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask47_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='228'
          y='52'
          width='21'
          height='11'>
          <path
            d='M245.615 62.0154L228.461 52.6847C229.115 52.5924 229.699 52.5154 230.292 52.4539C230.792 52.4001 231.292 52.3462 231.792 52.3L248.946 61.6308C248.438 61.677 247.938 61.7231 247.446 61.7847C246.853 61.8462 246.269 61.9231 245.615 62.0154Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask47_430_710)'>
          <path
            d='M245.615 62.0154L228.461 52.6847C229.115 52.5924 229.699 52.5154 230.292 52.4539C230.792 52.4001 231.292 52.3462 231.792 52.3L248.946 61.6308C248.438 61.677 247.938 61.7231 247.446 61.7847C246.853 61.8462 246.269 61.9231 245.615 62.0154Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask48_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='243'
          y='0'
          width='70'
          height='22'>
          <path
            d='M294.968 12.4077L312.122 21.7384C311.022 21.1384 309.906 20.5615 308.768 20.0077C293.16 12.3538 277.122 8.79998 260.691 9.39998L243.537 0.069206C259.968 -0.523102 275.999 3.02305 291.614 10.6769C292.745 11.2307 293.86 11.8077 294.968 12.4077Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask48_430_710)'>
          <path
            d='M260.691 9.39219L243.537 0.0614236C252.952 -0.277038 262.237 0.746039 271.391 3.11527L288.545 12.446C279.399 10.0768 270.114 9.05373 260.691 9.39219Z'
            fill='#3F74A8'
          />
          <path
            d='M288.544 12.4538L271.391 3.12305C278.206 4.89228 284.944 7.40766 291.614 10.6769C291.629 10.6846 291.644 10.6923 291.66 10.7L308.814 20.0307C308.798 20.023 308.783 20.0154 308.768 20.0077C302.106 16.7384 295.368 14.2154 288.544 12.4538Z'
            fill='#3D70A3'
          />
          <path
            d='M308.814 20.0307L291.66 10.7C292.776 11.2461 293.876 11.8153 294.96 12.4076L312.114 21.7384C311.037 21.1461 309.929 20.5769 308.814 20.0307Z'
            fill='#3B6D9E'
          />
        </g>
        <path
          d='M260.476 34.8L243.322 25.4692L232.291 12.5461L249.445 21.8769L260.476 34.8Z'
          fill='#305880'
        />
        <path
          d='M249.445 21.8769L232.291 12.5461L243.545 0.0615234L260.691 9.39229L249.445 21.8769Z'
          fill='#3D71A4'
        />
        <path
          d='M260.691 9.3922C277.122 8.7999 293.153 12.3461 308.768 19.9999C323.538 27.2614 335.707 38.1922 345.468 52.0691L345.338 53.0076C340.899 57.0999 336.461 61.1845 332.03 65.3076C330.668 66.5922 329.376 67.9845 327.668 69.6999C311.622 47.6307 290.276 35.3307 263.084 34.8076C262.215 34.7922 261.345 34.7922 260.476 34.7922L249.445 21.8691L260.691 9.3922Z'
          fill='#5399DF'
        />
        <mask
          id='mask49_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='206'
          y='52'
          width='40'
          height='17'>
          <path
            d='M224.068 68.5L206.914 59.1692C213.822 55.9307 221.099 53.7153 228.46 52.6846L245.614 62.0153C238.245 63.0461 230.968 65.2615 224.068 68.5Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask49_430_710)'>
          <path
            d='M224.068 68.5L206.914 59.1692C213.822 55.9307 221.099 53.7153 228.46 52.6846L245.614 62.0153C238.245 63.0461 230.968 65.2615 224.068 68.5Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask50_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='197'
          y='59'
          width='28'
          height='15'>
          <path
            d='M214.476 73.8L197.322 64.4692C198.861 63.4769 200.438 62.5307 202.038 61.6538C203.222 60.9923 204.422 60.3692 205.645 59.7769C206.068 59.5692 206.499 59.3615 206.922 59.1692L224.076 68.5C223.653 68.6846 223.222 68.9 222.799 69.1076C221.576 69.7 220.376 70.323 219.192 70.9846C217.584 71.8538 216.015 72.8 214.476 73.8Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask50_430_710)'>
          <path
            d='M214.476 73.8L197.322 64.4693C198.861 63.4769 200.438 62.5308 202.038 61.6539C203.222 60.9923 204.422 60.3693 205.645 59.777C205.922 59.6462 206.199 59.5077 206.476 59.377L223.63 68.7077C223.353 68.8385 223.076 68.9769 222.799 69.1077C221.576 69.7 220.376 70.3231 219.192 70.9846C217.584 71.8539 216.015 72.8 214.476 73.8Z'
            fill='#3D70A3'
          />
          <path
            d='M223.623 68.7L206.469 59.3692C206.615 59.3 206.769 59.2307 206.915 59.1692L224.069 68.5C223.923 68.5615 223.769 68.6307 223.623 68.7Z'
            fill='#3F74A8'
          />
        </g>
        <mask
          id='mask51_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='196'
          y='64'
          width='19'
          height='11'>
          <path
            d='M213.369 74.5153L196.215 65.1845C196.576 64.9383 196.946 64.6922 197.323 64.4614L214.476 73.7922C214.099 74.023 213.723 74.2691 213.369 74.5153Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask51_430_710)'>
          <path
            d='M213.369 74.5151L196.215 65.1843C196.246 65.1613 196.269 65.1459 196.299 65.1228L213.453 74.4536C213.423 74.4766 213.399 74.4997 213.369 74.5151Z'
            fill='#3B6D9E'
          />
          <path
            d='M213.453 74.4613L196.299 65.1305C196.63 64.8998 196.976 64.6767 197.322 64.469L214.476 73.7998C214.122 74.0075 213.783 74.2305 213.453 74.4613Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask52_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='188'
          y='65'
          width='26'
          height='16'>
          <path
            d='M205.662 80.2923L188.508 70.9615C190.085 69.623 191.723 68.3461 193.385 67.1384C194.316 66.4692 195.254 65.8153 196.208 65.1846L213.362 74.5153C212.408 75.1384 211.469 75.8 210.539 76.4692C208.877 77.6846 207.246 78.9538 205.662 80.2923Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask52_430_710)'>
          <path
            d='M205.662 80.2923L188.508 70.9615C190.085 69.6231 191.723 68.3461 193.385 67.1384C193.877 66.7846 194.369 66.4384 194.862 66.0923L212.016 75.4231C211.523 75.7692 211.031 76.1154 210.539 76.4692C208.877 77.6846 207.246 78.9538 205.662 80.2923Z'
            fill='#3B6D9E'
          />
          <path
            d='M212.023 75.4308L194.869 66.1001C195.315 65.7924 195.761 65.4847 196.215 65.1924L213.369 74.5232C212.915 74.8155 212.469 75.1155 212.023 75.4308Z'
            fill='#3D70A3'
          />
        </g>
        <mask
          id='mask53_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='185'
          y='70'
          width='21'
          height='14'>
          <path
            d='M202.56 83.0539L185.406 73.7231C186.422 72.7846 187.452 71.8615 188.506 70.9692L205.66 80.3C204.599 81.1846 203.568 82.1077 202.56 83.0539Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask53_430_710)'>
          <path
            d='M202.56 83.0537L185.406 73.7229C185.791 73.3691 186.175 73.0152 186.568 72.6614L203.722 81.9921C203.329 82.3383 202.945 82.6921 202.56 83.0537Z'
            fill='#396999'
          />
          <path
            d='M203.722 81.9922L186.568 72.6614C187.207 72.0845 187.853 71.523 188.515 70.9614L205.668 80.2922C205.007 80.846 204.361 81.4153 203.722 81.9922Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask54_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='185'
          y='70'
          width='21'
          height='14'>
          <path
            d='M202.56 83.0539L185.406 73.7231C186.422 72.7692 187.452 71.8615 188.506 70.9692L205.66 80.3C204.599 81.1846 203.568 82.1 202.56 83.0539Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask54_430_710)'>
          <path
            d='M202.56 83.0538L185.406 73.723C185.783 73.3692 186.152 73.023 186.537 72.6846L203.691 82.0153C203.306 82.3538 202.929 82.7 202.56 83.0538Z'
            fill='#396999'
          />
          <path
            d='M203.683 82.0154L186.529 72.6846C187.175 72.1 187.837 71.5308 188.506 70.9692L205.66 80.3C204.991 80.8615 204.337 81.4308 203.683 82.0154Z'
            fill='#3B6D9E'
          />
        </g>
        <mask
          id='mask55_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='180'
          y='73'
          width='23'
          height='15'>
          <path
            d='M198.146 87.4846L180.992 78.1538C182.415 76.6076 183.884 75.1384 185.408 73.7153L202.561 83.0461C201.038 84.4692 199.569 85.9461 198.146 87.4846Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask55_430_710)'>
          <path
            d='M198.146 87.4846L180.992 78.1538C182.415 76.6076 183.884 75.1384 185.408 73.7153L202.561 83.0461C201.038 84.4692 199.569 85.9461 198.146 87.4846Z'
            fill='#396999'
          />
        </g>
        <mask
          id='mask56_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='178'
          y='78'
          width='21'
          height='13'>
          <path
            d='M195.384 90.6385L178.23 81.3078C179.123 80.2385 180.046 79.1847 181 78.1616L198.154 87.4924C197.2 88.5155 196.277 89.5693 195.384 90.6385Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask56_430_710)'>
          <path
            d='M195.384 90.6385L178.23 81.3077C178.738 80.7077 179.246 80.1077 179.769 79.5154L196.923 88.8461C196.4 89.4308 195.884 90.0308 195.384 90.6385Z'
            fill='#376694'
          />
          <path
            d='M196.923 88.8384L179.77 79.5077C180.17 79.0538 180.577 78.6 180.993 78.1538L198.146 87.4846C197.731 87.9307 197.323 88.3846 196.923 88.8384Z'
            fill='#396999'
          />
        </g>
        <mask
          id='mask57_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='178'
          y='78'
          width='21'
          height='13'>
          <path
            d='M195.384 90.6385L178.23 81.3078C179.123 80.2308 180.046 79.1847 181 78.1616L198.154 87.4924C197.2 88.5155 196.277 89.5616 195.384 90.6385Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask57_430_710)'>
          <path
            d='M195.384 90.6383L178.23 81.3075C178.723 80.7152 179.215 80.1383 179.723 79.5691L196.877 88.8999C196.369 89.4691 195.869 90.046 195.384 90.6383Z'
            fill='#376694'
          />
          <path
            d='M196.877 88.8923L179.723 79.5615C180.138 79.0846 180.569 78.6153 181 78.1538L198.153 87.4846C197.715 87.9538 197.292 88.423 196.877 88.8923Z'
            fill='#396999'
          />
        </g>
        <mask
          id='mask58_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='174'
          y='86'
          width='33'
          height='25'>
          <path
            d='M206.477 110.831L189.323 101.5C184.254 96.3385 179.315 91.3154 174.377 86.3L191.531 95.6308C196.469 100.646 201.4 105.677 206.477 110.831Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask58_430_710)'>
          <path
            d='M206.477 110.831L189.323 101.5C184.254 96.3385 179.315 91.3154 174.377 86.3L191.531 95.6308C196.469 100.646 201.4 105.677 206.477 110.831Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask59_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='174'
          y='81'
          width='22'
          height='15'>
          <path
            d='M191.531 95.6308L174.377 86.3C175.6 84.5847 176.892 82.9077 178.231 81.3L195.385 90.6308C194.038 92.2385 192.754 93.9154 191.531 95.6308Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask59_430_710)'>
          <path
            d='M191.531 95.6308L174.377 86.3C175.6 84.5847 176.892 82.9077 178.231 81.3L195.385 90.6308C194.038 92.2385 192.754 93.9154 191.531 95.6308Z'
            fill='#376694'
          />
        </g>
        <path
          d='M245.616 62.0152C246.269 61.9229 246.854 61.846 247.446 61.7845C247.946 61.7306 248.446 61.6768 248.946 61.6306C249.293 61.5922 249.631 61.5614 249.977 61.5383C250.2 61.5152 250.423 61.4999 250.646 61.4922C250.946 61.4691 251.246 61.4537 251.546 61.4383C251.823 61.4229 252.085 61.3999 252.369 61.3999C252.662 61.3845 252.969 61.3768 253.269 61.3691C253.516 61.3614 253.777 61.3614 254.031 61.3537C254.354 61.346 254.685 61.3383 255.016 61.346C255.162 61.3306 255.308 61.3306 255.454 61.346C255.885 61.346 256.323 61.3537 256.746 61.3614C257.269 61.3691 257.808 61.3845 258.339 61.4076L238.462 82.7614L243.039 87.6075C242.623 87.6845 242.216 87.7691 241.8 87.8691C241.562 87.9152 241.323 87.9691 241.077 88.0306C239.831 88.3152 238.6 88.6614 237.354 89.046C237.1 89.1306 236.854 89.2076 236.593 89.2922C236.269 89.3922 235.962 89.4999 235.646 89.6075C235.254 89.7383 234.862 89.8845 234.477 90.0306C233.993 90.2076 233.508 90.3845 233.023 90.5845C232.731 90.7076 232.431 90.8306 232.139 90.9614C231.854 91.0768 231.562 91.1922 231.285 91.3306C230.854 91.5229 230.423 91.7075 229.993 91.9152C229.639 92.0768 229.285 92.246 228.946 92.4229C228.446 92.6691 227.939 92.9306 227.439 93.1999C227.016 93.4229 226.593 93.6537 226.177 93.8999C223.977 95.1306 221.808 96.5306 219.7 98.0768C219.393 98.3075 219.093 98.5306 218.793 98.7614C218.139 99.2614 217.493 99.7691 216.854 100.3C216.485 100.6 216.116 100.908 215.754 101.215C215.393 101.523 215.031 101.846 214.669 102.169C214.408 102.4 214.154 102.631 213.9 102.869C213.577 103.161 213.269 103.446 212.962 103.754C212.631 104.061 212.308 104.384 211.977 104.708C211.554 105.123 211.146 105.546 210.716 105.984C209.985 106.746 209.262 107.531 208.562 108.338C208.277 108.654 208 108.977 207.723 109.308C207.3 109.808 206.869 110.315 206.462 110.831C201.393 105.669 196.454 100.646 191.516 95.6306C192.739 93.9152 194.031 92.2383 195.369 90.6306C196.262 89.5614 197.185 88.5076 198.139 87.4845C199.562 85.9383 201.031 84.4691 202.554 83.046C203.569 82.1075 204.6 81.1845 205.654 80.2922C207.231 78.9537 208.869 77.6768 210.531 76.4691C211.462 75.7999 212.4 75.146 213.354 74.5152C213.716 74.2691 214.085 74.0229 214.462 73.7922C216 72.7999 217.577 71.8537 219.177 70.9768C220.362 70.3152 221.562 69.6922 222.785 69.0999C223.208 68.8922 223.639 68.6845 224.062 68.4922C230.969 65.2614 238.246 63.046 245.616 62.0152Z'
          fill='#5399DF'
        />
        <path
          d='M239.615 12.5691L229.977 26.4999L242.885 37.2306C216.639 41.6691 194.3 56.2691 175.654 79.7768C170.454 74.546 165.408 69.4922 160.254 64.3075C169.139 52.1537 179.562 42.4614 191.062 34.3229C207.031 23.0383 223.231 15.7922 239.615 12.5691Z'
          fill='#5399DF'
        />
        <mask
          id='mask60_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='143'
          y='54'
          width='33'
          height='26'>
          <path
            d='M175.653 79.7768L158.5 70.446C153.3 65.2153 148.253 60.1614 143.1 54.9768L160.253 64.3076C165.407 69.4922 170.453 74.546 175.653 79.7768Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask60_430_710)'>
          <path
            d='M175.653 79.7768L158.5 70.446C153.3 65.2153 148.253 60.1614 143.1 54.9768L160.253 64.3076C165.407 69.4922 170.453 74.546 175.653 79.7768Z'
            fill='#315B85'
          />
        </g>
        <mask
          id='mask61_430_710'
          style={{ maskType: 'luminance' }}
          maskUnits='userSpaceOnUse'
          x='143'
          y='3'
          width='97'
          height='62'>
          <path
            d='M160.246 64.3078L143.092 54.977C151.976 42.8231 162.399 33.1308 173.899 24.9924C189.876 13.7078 206.076 6.4616 222.461 3.23853L239.615 12.5693C223.23 15.7924 207.03 23.0385 191.053 34.3308C179.561 42.4693 169.13 52.1539 160.246 64.3078Z'
            fill='white'
          />
        </mask>
        <g mask='url(#mask61_430_710)'>
          <path
            d='M160.246 64.3077L143.092 54.9769C145.407 51.8154 147.823 48.8154 150.338 45.9692L167.492 55.3C164.976 58.1462 162.561 61.1462 160.246 64.3077Z'
            fill='#376694'
          />
          <path
            d='M167.492 55.3L150.338 45.9692C153.638 42.2308 157.107 38.7538 160.723 35.5L177.876 44.8308C174.261 48.0846 170.792 51.5615 167.492 55.3Z'
            fill='#396999'
          />
          <path
            d='M177.877 44.8386L160.723 35.5078C164.93 31.7232 169.33 28.2386 173.907 25.0001C174.353 24.6847 174.8 24.377 175.246 24.0693L192.4 33.4001C191.953 33.7078 191.507 34.0155 191.061 34.3309C186.484 37.5693 182.077 41.0463 177.877 44.8386Z'
            fill='#3B6D9E'
          />
          <path
            d='M192.392 33.3923L175.238 24.0615C182.046 19.3538 188.892 15.3692 195.777 12.123L212.931 21.4538C206.046 24.7077 199.2 28.6846 192.392 33.3923Z'
            fill='#3D70A3'
          />
          <path
            d='M212.931 21.4616L195.777 12.1308C204.616 7.9616 213.508 5.00006 222.462 3.23853L239.616 12.5693C230.662 14.3308 221.77 17.2924 212.931 21.4616Z'
            fill='#3F74A8'
          />
        </g>
      </g>
      <defs>
        <radialGradient
          id='paint0_radial_430_710'
          cx='0'
          cy='0'
          r='1'
          gradientUnits='userSpaceOnUse'
          gradientTransform='translate(262.995 159.862) rotate(-13.1807) scale(275.537 136.495)'>
          <stop offset='0.4694' stopColor='#0C40DE' />
          <stop offset='0.9724' stopColor='white' />
        </radialGradient>
        <clipPath id='clip0_430_710'>
          <rect width='526' height='296.246' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
// 没有权限

const noPermissionIcon = () => {
  return (
    <svg
      width='100%'
      height='100%'
      viewBox='0 0 526 388'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#clip0_439_1155)'>
        <path
          d='M101.385 289.792L247.539 375.177L394.462 290.562L246.769 205.177L101.385 289.792Z'
          fill='#F1F1F1'
        />
        <path
          d='M105.23 216.715L251.384 302.1L398.307 217.485L250.615 132.1L105.23 216.715Z'
          fill='#F8F8F8'
        />
        <path
          d='M251.385 302.1V333.638L398.308 248.254V217.485L251.385 302.1Z'
          fill='#DCDEDE'
        />
        <path
          d='M252.154 301.331V332.869L105.23 247.485V216.715L252.154 301.331Z'
          fill='#EAEBEB'
        />
        <path
          d='M289.661 375.231C145.176 407 16.0994 377.277 1.37636 308.839C-9.95441 256.162 49.7687 195.9 143.776 156.454C66.0918 191.631 17.6994 243.177 27.4764 288.608C40.6917 350.046 155.292 377.023 283.423 348.854C411.553 320.685 504.707 248.046 491.492 186.608C481.461 139.977 429.476 113.708 341.069 115.815C446.553 110.392 513.007 139.785 524.63 193.815C539.346 262.246 434.153 343.469 289.661 375.231Z'
          fill='url(#paint0_radial_439_1155)'
        />
        <path
          d='M224.423 222.285L280.285 254.923C287.623 259.208 296.7 259.239 304.069 255L360.123 222.715C366.338 219.139 366.346 210.169 360.138 206.585L296.408 169.739C293.515 168.069 289.946 168.069 287.061 169.754L224.438 206.2C218.269 209.785 218.261 218.685 224.423 222.285Z'
          fill='#DCDEDE'
        />
        <path
          d='M251.608 226.9L171.946 178.508C161.315 172.054 154.554 156.323 156.838 143.377L179.577 14.5154C181.861 1.5692 192.331 -3.69233 202.954 2.76151L282.623 51.1538C293.254 57.6077 300.015 73.3384 297.731 86.2846L274.992 215.146C272.708 228.092 262.238 233.354 251.608 226.9Z'
          fill='url(#paint1_linear_439_1155)'
        />
        <path
          d='M244.561 233.438L154.8 178.908C147.308 174.354 142.531 163.262 144.146 154.131L168.654 15.2385C170.261 6.10768 177.646 2.39999 185.146 6.94614L274.908 61.4769C282.4 66.0308 287.177 77.1231 285.561 86.2538L261.054 225.146C259.438 234.277 252.061 237.992 244.561 233.438Z'
          fill='#EFEFEF'
        />
        <path
          d='M249.792 130.931L177.661 87.1077C176.115 86.1692 174.592 86.9308 174.261 88.8154C173.93 90.7 174.915 92.9846 176.461 93.9231L248.592 137.746C250.138 138.685 251.661 137.923 251.992 136.038C252.323 134.162 251.338 131.869 249.792 130.931Z'
          fill='white'
        />
        <path
          d='M226.876 142.177L173.276 109.615C171.938 108.8 170.614 109.461 170.33 111.1L170.168 112.008C169.884 113.638 170.73 115.623 172.076 116.438L225.676 149C227.014 149.815 228.338 149.154 228.622 147.515L228.784 146.608C229.068 144.969 228.214 142.985 226.876 142.177Z'
          fill='white'
        />
        <path
          d='M209.092 159.046L168.569 134.431C167.4 133.715 166.238 134.3 165.992 135.731L165.7 137.377C165.446 138.808 166.192 140.538 167.369 141.254L207.892 165.869C209.061 166.585 210.223 166 210.469 164.569L210.761 162.923C211.008 161.492 210.261 159.754 209.092 159.046Z'
          fill='white'
        />
        <path
          d='M251.362 70.6615L201.208 40.1923C196.17 37.1307 192.962 29.6769 194.047 23.5384L196.231 11.1692L264.631 52.723L262.447 65.0923C261.362 71.223 256.401 73.7153 251.362 70.6615Z'
          fill='url(#paint2_linear_439_1155)'
        />
        <path
          d='M312.785 180.846C315.754 184.292 317.231 189.362 316.485 193.569L306.577 249.708C303.354 267.985 275.846 263.823 266.246 257.992C256.646 252.162 233.139 225.331 236.362 207.054L246.269 150.915C247.015 146.708 249.815 144.239 253.308 144.723C260.469 145.715 272.731 146.985 281.069 145.539C284.2 144.992 287.777 147.169 290.123 151.039C296.408 161.362 306.7 173.792 312.785 180.846Z'
          fill='#DCDEDE'
        />
        <path
          d='M302.638 181.754C305.607 185.2 307.084 190.269 306.338 194.477L296.43 250.615C293.207 268.892 265.7 264.731 256.1 258.9C246.5 253.069 222.992 226.238 226.215 207.961L236.123 151.823C236.869 147.615 239.669 145.146 243.161 145.631C250.323 146.623 262.584 147.892 270.923 146.446C274.054 145.9 277.631 148.077 279.977 151.946C286.254 162.269 296.546 174.7 302.638 181.754Z'
          fill='url(#paint3_linear_439_1155)'
        />
        <path
          d='M295.785 187.092C298.224 189.915 299.431 194.077 298.824 197.531L290.693 243.592C288.047 258.592 265.478 255.177 257.601 250.385C249.724 245.6 230.439 223.585 233.085 208.592L241.216 162.531C241.824 159.077 244.124 157.054 246.993 157.446C252.87 158.261 262.931 159.3 269.778 158.115C272.347 157.669 275.278 159.454 277.208 162.631C282.347 171.1 290.793 181.3 295.785 187.092Z'
          fill='url(#paint4_linear_439_1155)'
          stroke='#E3F2FF'
          strokeWidth='0.9651'
          strokeMiterlimit='10'
        />
        <path
          d='M262.207 205.831L264.392 187.292C264.822 183.684 265.176 181.123 265.445 179.6C265.815 177.531 266.545 176.184 267.638 175.561C268.73 174.938 270.007 175.077 271.468 175.961C273.23 177.031 274.276 178.492 274.607 180.338C274.938 182.184 274.861 184.508 274.368 187.3C274.076 188.946 273.707 190.577 273.268 192.184L268.692 209.846C268.161 211.954 267.553 213.454 266.876 214.338C266.199 215.223 265.315 215.338 264.23 214.677C263.122 214.008 262.453 212.969 262.222 211.569C261.984 210.177 261.984 208.261 262.207 205.831ZM261.268 232.538C260.015 231.777 259.015 230.623 258.253 229.069C257.492 227.515 257.268 225.854 257.584 224.084C257.861 222.538 258.538 221.492 259.615 220.946C260.699 220.4 261.884 220.523 263.176 221.308C264.468 222.092 265.476 223.308 266.192 224.946C266.915 226.584 267.138 228.177 266.861 229.731C266.553 231.477 265.845 232.569 264.753 233.008C263.661 233.446 262.499 233.292 261.268 232.538Z'
          fill='white'
        />
      </g>
      <defs>
        <radialGradient
          id='paint0_radial_439_1155'
          cx='0'
          cy='0'
          r='1'
          gradientUnits='userSpaceOnUse'
          gradientTransform='translate(262.993 251.584) rotate(-13.1807) scale(275.537 136.495)'>
          <stop offset='0.4694' stopColor='#F25445' />
          <stop offset='0.9724' stopColor='white' />
        </radialGradient>
        <linearGradient
          id='paint1_linear_439_1155'
          x1='230.013'
          y1='-49.8929'
          x2='137.67'
          y2='208.279'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#0C40DE' />
          <stop offset='0.9724' stopColor='white' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_439_1155'
          x1='226.167'
          y1='63.1624'
          x2='243.188'
          y2='31.1724'
          gradientUnits='userSpaceOnUse'>
          <stop offset='0.1783' stopColor='#F25445' />
          <stop offset='0.9724' stopColor='white' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_439_1155'
          x1='252.388'
          y1='145.926'
          x2='266.981'
          y2='277.056'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#0C40DE' />
          <stop offset='0.9724' stopColor='white' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_439_1155'
          x1='205.842'
          y1='309.724'
          x2='316.125'
          y2='211.424'
          gradientUnits='userSpaceOnUse'>
          <stop offset='0.3327' stopColor='#0C40DE' />
          <stop offset='1' stopColor='white' />
        </linearGradient>
        <clipPath id='clip0_439_1155'>
          <rect width='526' height='387.962' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
export default {
  noSearchDataIcon,
  noSearchDataIcon1,
  noDataIcon,
  networkAbnormalityIcon,
  noPermissionIcon,
};
