import errorReason from './ErrorReason';
import {
  setAgainLoginVisible,
  setShowbeLoginCovered,
} from '@/store/features/layoutSlice';
import { setShowLoginConfirm } from '@/store/features/loginSlice';
import store from '@/store';
import { logout } from '@/store/features/auth';

const errorReasonMap = new Map(Object.entries(errorReason));
function processError(status: number): string | null {
  switch (status) {
    case 20602:
      process_20602();
      break;
    case 20603:
    case 20604:
    case 20612:
      process_unauthorized();
      break;
    case 20616:
      store.dispatch(setShowbeLoginCovered(true));
      break;
    case 20618:
      store.dispatch(setShowLoginConfirm(true));
      break;
    default:
      return outputErrorReason(status.toString());
  }
  return null;
}

function process_20602() {
  store.dispatch(setAgainLoginVisible(true));
}

async function process_unauthorized() {
  logout();
}

function outputErrorReason(message: string): string {
  return errorReasonMap.get(message) || t('未知错误');
}

export { processError };
