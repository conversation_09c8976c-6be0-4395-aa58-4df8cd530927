import { useEffect, useState } from 'react';
import { Result } from 'antd';
const ErrorBoundaryFallback: React.FC = () => {
  const [time, setTime] = useState(1000000000000);
  const returnHome = () => {
    window.location.href = '/home';
  };
  useEffect(() => {
    let timer: any = null;
    timer = setInterval(() => {
      setTime((time) => {
        if (time === 0) {
          clearInterval(timer);
          returnHome();
        }
        return time - 1;
      });
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <Result
      status='500'
      title='500'
      subTitle='Sorry, something went wrong.'
      extra={<div>还有{time >= 0 ? time : 0}秒返回首页</div>}
    />
  );
};
export default ErrorBoundaryFallback;
