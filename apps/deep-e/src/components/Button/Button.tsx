import { cva, type VariantProps } from 'class-variance-authority';

const button = cva(
  [
    'button',
    "ui-font-['Roboto']",
    'ui-font-medium',
    'ui-rounded-[36px]',
    'ui-px-[21px]',
    'ui-flex',
    'ui-justify-center',
    'ui-items-center',
  ],
  {
    variants: {
      intent: {
        primary: [],
        disable: [
          'ui-bg-stone-300',
          'ui-text-slate-800',
          'ui-cursor-not-allowed',
        ],
      },
      size: {
        big: ['ui-text-lg', 'ui-min-w-[100px]', 'ui-h-[54px]'],
        small: ['ui-h-8', 'ui-min-w-[100px]'],
        medium: ['ui-min-w-[100px]', 'ui-h-[42px]'],
      },
      buttonType: {
        normal: [],
        secondary: [
          'ui-border',
          'text-slate-900',
          'ui-border-zinc-400',
          'ui-text-slate-900',
          'ui-bg-transparent',
          'hover:ui-text-sky-500',
          'hover:ui-border-sky-500',
          'active:ui-border-sky-700',
          'active:ui-text-sky-700',
        ],
        dotted: [
          'ui-border',
          'ui-bg-transparent',
          'ui-border-zinc-400',
          'ui-text-slate-900',
          'ui-border-dotted',
        ],
        link: [
          'ui-border',
          'ui-border-gray-300',
          'ui-text-sky-500',
          'active:ui-text-blue-700',
        ],
      },
    },
    compoundVariants: [
      {
        intent: ['disable'],
        buttonType: ['dotted', 'link', 'secondary'],
        class: [
          'ui-bg-stone-300',
          'ui-text-slate-800',
          'ui-cursor-not-allowed',
        ],
      },
      {
        intent: ['primary'],
        buttonType: ['normal', 'link', 'secondary', 'dotted'],
        class: [
          'hover:ui-text-primary-hover',
          'hover:ui-border-primary-hover',
          'active:ui-text-primary-active',
          'active:ui-border-primary-active',
        ],
      },
      {
        intent: ['primary'],
        buttonType: ['link', 'dotted', 'secondary'],
        class: ['ui-bg-transparent'],
      },
      {
        intent: ['primary'],
        buttonType: ['normal'],
        class: [
          'ui-bg-primary',
          'ui-text-white',
          'hover:ui-text-white',
          'hover:ui-bg-primary-hover',
          'active:ui-text-white',
          'active:ui-bg-primary-active',
        ],
      },
    ],
    defaultVariants: {
      intent: 'primary',
      size: 'medium',
      buttonType: 'normal',
    },
  },
);

interface InnerButtonProps {
  icon?: React.ReactNode;
}

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof button>,
    InnerButtonProps {}

export const Button: React.FC<ButtonProps> = ({
  className,
  intent,
  size,
  buttonType,
  icon,
  ...props
}) => {
  const iconNode = icon ? (
    <span className='ui-mr-2 ui-min-w-4 ui-min-h-4'>{icon}</span>
  ) : null;

  return (
    <button
      className={button({ intent, size, buttonType, className })}
      {...props}>
      {iconNode}
      {props.children}
    </button>
  );
};
