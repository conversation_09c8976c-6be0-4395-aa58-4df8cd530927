'use client';

import { useState, useEffect } from 'react';
import MarkdownIt from 'markdown-it';
//@ts-ignore
import markdownItKatex from 'markdown-it-katex';
import 'katex/dist/katex.min.css';

interface AIResponseProps {
  response: string;
}

export default function AIResponseDisplay({ response = '' }: AIResponseProps) {
  const [showThinking, setShowThinking] = useState(true);
  const [parsedThinking, setParsedThinking] = useState('');
  const [parsedResponse, setParsedResponse] = useState('');

  useEffect(() => {
    // Initialize markdown-it with plugins and options
    const md = new MarkdownIt({
      html: true,
      breaks: true,
    });

    // Configure KaTeX options
    md.use(markdownItKatex, {
      throwOnError: false,
      errorColor: '#cc0000',
      displayMode: true, // 使行间公式居中显示
    });

    // Extract thinking section and final response
    const thinkingMatch = response.match(/<think>([\s\S]*?)<\/think>/);
    const thinkingContent = thinkingMatch ? thinkingMatch[1].trim() : '';

    // Get the content after the thinking section
    const finalResponse = response.replace(/<think>[\s\S]*?<\/think>\n*/, '');

    // Parse markdown content
    if (thinkingContent) {
      setParsedThinking(md.render(thinkingContent));
    }

    setParsedResponse(md.render(finalResponse));
  }, [response]);

  return (
    <div className='ai-response-container'>
      {parsedThinking && (
        <div className='mb-4'>
          <div className='flex items-center mb-2'>
            <button
              onClick={() => setShowThinking(!showThinking)}
              className='text-xs flex items-center text-gray-500 hover:text-gray-700'>
              <span
                className={`mr-1 transition-transform ${showThinking ? 'rotate-90' : ''}`}>
                ▶
              </span>
              Thinking
            </button>
          </div>

          {showThinking && (
            <div className='thinking-section text-gray-500 bg-gray-50 p-4 rounded-md text-xs'>
              <div dangerouslySetInnerHTML={{ __html: parsedThinking }} />
            </div>
          )}
        </div>
      )}

      <div className='final-response'>
        <div dangerouslySetInnerHTML={{ __html: parsedResponse }} />
      </div>
    </div>
  );
}
