import React, { memo, useEffect, useState } from 'react';
import { setShowbeLoginCovered } from '@/store/features/layoutSlice';
import { Avatar, ConfigProvider, Dropdown, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import type { MenuProps } from 'antd/lib';
import ajax from '@/api';
import { t, i18nScope } from '@/languages';
import { useVoerkaI18n } from '@voerkai18n/react';
import { setShowLoginConfirm } from '@/store/features/loginSlice';
import { useAuthStore } from '@/store/features/auth';
import { PasswordChangeModal } from './components/ChangePasswordModal';
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
} from '@/components/ui/select';
import { LockOutlined } from '@ant-design/icons';
import store from '@/store';

type HeaderProps = {
  openDrawer: () => void;
};

const Header: React.FC<HeaderProps> = ({ openDrawer }) => {
  const { changeLanguage } = useVoerkaI18n();
  const [passwordModalOpen, setPasswordModalOpen] = useState<boolean>(false);
  const navigate = useNavigate();

  const { user, language, setLanguage } = useAuthStore();

  const changeLan = (language: 'zh-CN' | 'en-US' | 'de-DE') => {
    const languageMap = {
      'zh-CN': 'zh',
      'en-US': 'en',
      'de-DE': 'de',
    };
    setLanguage(languageMap[language]);
    // window.location.reload();
  };
  const items: MenuProps['items'] = [
    {
      label: (
        <div className='flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-gray-50 transition-colors'>
          <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
            <LockOutlined className='text-blue-600 text-sm' />
          </div>
          <span className='text-gray-700 font-medium'>{t('修改密码')}</span>
        </div>
      ),
      key: 'changePassword',
      onClick: () => {
        setPasswordModalOpen(true);
      },
    },
    {
      label: (
        <div className='flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-red-50 transition-colors'>
          <div className='w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='16'
              height='16'
              viewBox='-1 -1 18 18'
              fill='none'
              className='text-red-600'>
              <path
                d='M13.5001 4.50004C14.3901 5.39006 14.9962 6.52402 15.2417 7.75851C15.4873 8.993 15.3613 10.2726 14.8796 11.4355C14.3979 12.5983 13.5822 13.5922 12.5357 14.2915C11.4891 14.9908 10.2587 15.364 9.00003 15.364C7.74136 15.364 6.51095 14.9908 5.46439 14.2915C4.41784 13.5922 3.60215 12.5983 3.12048 11.4355C2.6388 10.2726 2.51278 8.993 2.75833 7.75851C3.00389 6.52402 3.61 5.39006 4.50002 4.50004'
                stroke='currentColor'
                strokeLinecap='round'
              />
              <path
                d='M9 8.00006V2.00006'
                stroke='currentColor'
                strokeLinecap='round'
              />
            </svg>
          </div>
          <span className='text-gray-700 font-medium'>{t('注销')}</span>
        </div>
      ),
      key: 'logout',
      onClick: () => {
        // 显示加载提示
        const hideLoading = message.loading(t('正在登出...'), 0);

        ajax
          .logout()
          .then((res) => {
            hideLoading();
            if (res.code === 0) {
              message.success(t('登出成功'));
              // 清理状态和存储
              store.dispatch(setShowLoginConfirm(false));
              store.dispatch(setShowbeLoginCovered(false));
              useAuthStore.getState().logout(); // 更新 Zustand 状态
              localStorage.removeItem('auth-store'); // 清除 localStorage，触发 storage 事件
              localStorage.clear();
              // 跳转到登录页
              navigate('/login');
            } else {
              message.error(res.msg || t('登出失败，请重试'));
            }
          })
          .catch((err: Error) => {
            hideLoading();
            console.error('登出失败:', err);
            message.error(t('登出失败，请重试'));
            // 即使接口失败，也清理本地状态（可选）
            // 这样可以确保用户能够重新登录
            store.dispatch(setShowLoginConfirm(false));
            store.dispatch(setShowbeLoginCovered(false));
            useAuthStore.getState().logout();
            localStorage.removeItem('auth-store');
            localStorage.clear();
            navigate('/login');
          });
      },
    },
  ];

  useEffect(() => {
    const _language =
      language === 'zh' ? 'zh-CN' : language === 'en' ? 'en-US' : 'de-DE';
    changeLan(_language);
    changeLanguage(_language);
  }, [language]);

  return (
    <div className='w-full h-full relative'>
      <div className='w-60 h-full flex justify-between items-center absolute right-0'>
        <ConfigProvider
          theme={{
            components: {},
          }}>
          <Select
            defaultValue={
              language === 'zh'
                ? 'zh-CN'
                : language === 'en'
                  ? 'en-US'
                  : 'de-DE'
            }
            onValueChange={async (value: 'zh-CN' | 'en-US' | 'de-DE') => {
              changeLanguage(value);
              changeLan(value);
              await i18nScope.change(value);
              window.location.reload();
            }}>
            <SelectTrigger>
              <SelectValue placeholder='Language' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='en-US'>English</SelectItem>
              <SelectItem value='zh-CN'>中文</SelectItem>
              <SelectItem value='de-DE'>Deutsch</SelectItem>
            </SelectContent>
          </Select>

          {/* 消息提示按钮 */}
          <div
            onClick={openDrawer}
            className='w-[38px] h-[38px] bg-neutral-5 rounded-full flex justify-center items-center'>
            {/* 无消息 */}
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='25'
              height='25'
              viewBox='0 0 25 25'
              fill='none'
              style={{ fontSize: '16px' }} // 统一图标大小
            >
              <path
                d='M11.442 25C9.289 25 7.53577 23.2479 7.53577 21.0938C7.53577 20.6625 7.88577 20.3125 8.31702 20.3125C8.74827 20.3125 9.09827 20.6625 9.09827 21.0938C9.09827 22.3866 10.1504 23.4375 11.442 23.4375C12.7337 23.4375 13.7858 22.3866 13.7858 21.0938C13.7858 20.6625 14.1358 20.3125 14.567 20.3125C14.9983 20.3125 15.3483 20.6625 15.3483 21.0938C15.3483 23.2479 13.5952 25 11.442 25Z'
                fill='#FE5C73'
              />
              <path
                d='M20.0357 21.875H2.84824C1.84307 21.875 1.02539 21.0573 1.02539 20.0521C1.02539 19.5187 1.25771 19.0136 1.66283 18.6666C1.68896 18.6438 1.717 18.623 1.74618 18.6041C3.2753 17.2697 4.15039 15.35 4.15039 13.3228V10.4166C4.15039 6.39591 7.42226 3.125 11.442 3.125C11.6087 3.125 11.7889 3.12805 11.9556 3.15628C12.3816 3.22704 12.6692 3.63026 12.5982 4.05521C12.5275 4.48017 12.117 4.7678 11.6993 4.69685C11.6159 4.6833 11.5244 4.6875 11.442 4.6875C8.28381 4.6875 5.71289 7.25727 5.71289 10.4166V13.3228C5.71289 15.8396 4.60968 18.2209 2.68898 19.8551C2.67334 19.8677 2.6598 19.8792 2.64301 19.8906C2.61497 19.9261 2.58789 19.9802 2.58789 20.0521C2.58789 20.1937 2.70672 20.3125 2.84824 20.3125H20.0357C20.1775 20.3125 20.2963 20.1937 20.2963 20.0521C20.2963 19.9791 20.2692 19.9261 20.24 19.8906C20.2244 19.8792 20.2108 19.8677 20.1952 19.8551C18.2734 18.2198 17.1713 15.8396 17.1713 13.3228V12.1876C17.1713 11.7563 17.5213 11.4063 17.9525 11.4063C18.3838 11.4063 18.7338 11.7563 18.7338 12.1876V13.3228C18.7338 15.3511 19.6098 17.2718 21.1411 18.6073C21.1691 18.626 21.1962 18.6459 21.2212 18.6678C21.6265 19.0136 21.8588 19.5187 21.8588 20.0521C21.8588 21.0573 21.0411 21.875 20.0357 21.875Z'
                fill='#FE5C73'
              />
              <path
                d='M18.7338 10.4166C15.8619 10.4166 13.5254 8.08029 13.5254 5.2084C13.5254 2.3365 15.8619 0 18.7338 0C21.6057 0 23.942 2.3365 23.942 5.2084C23.942 8.08029 21.6057 10.4166 18.7338 10.4166ZM18.7338 1.5625C16.7233 1.5625 15.0879 3.19786 15.0879 5.2084C15.0879 7.21874 16.7233 8.8541 18.7338 8.8541C20.7441 8.8541 22.3795 7.21874 22.3795 5.2084C22.3795 3.19786 20.7441 1.5625 18.7338 1.5625Z'
                fill='#FE5C73'
              />
            </svg>
          </div>

          <Dropdown
            trigger={['click']}
            menu={{ items }}
            popupRender={(menu) => (
              <div className='bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden min-w-[280px]'>
                {/* 用户信息头部 */}
                <div className='bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4'>
                  <div className='flex items-center space-x-3'>
                    <Avatar
                      src={
                        user?.headerImg ? (
                          <img src={user?.headerImg} />
                        ) : (
                          user?.nickName
                        )
                      }
                      className='w-12 h-12 shadow-sm'
                      style={{
                        fontSize: '16px',
                        background: user?.headerImg ? 'transparent' : '#1890ff',
                      }}>
                      {user?.nickName?.[0]?.toUpperCase()}
                    </Avatar>
                    <div className='flex-1 ml-4'>
                      <div className='font-semibold text-gray-900 text-base'>
                        {user.nickName}
                      </div>
                      <div className='text-sm text-gray-600'>{user.coCode}</div>
                    </div>
                  </div>
                </div>

                {/* 分割线 */}
                <div className='h-px bg-gray-100'></div>

                {/* 菜单项 */}
                <div className='px-3 py-3'>
                  {React.cloneElement(
                    menu as React.ReactElement<{
                      style: React.CSSProperties;
                    }>,
                    {
                      style: {
                        boxShadow: 'none',
                        border: 'none',
                        fontSize: '14px',
                        padding: '0',
                        background: 'transparent',
                      },
                    },
                  )}
                </div>
              </div>
            )}
            overlayStyle={{
              padding: '0',
            }}>
            <Avatar
              src={
                user?.headerImg ? <img src={user?.headerImg} /> : user?.nickName
              }
              className='w-10 h-10 cursor-pointer hover:opacity-80 transition-opacity'
              style={{
                fontSize: '14px',
                background: user?.headerImg ? 'transparent' : '#1890ff',
              }}>
              {user?.nickName?.[0]?.toUpperCase()}
            </Avatar>
          </Dropdown>
        </ConfigProvider>
      </div>
      <PasswordChangeModal
        open={passwordModalOpen}
        onOpenChange={setPasswordModalOpen}
      />
    </div>
  );
};
const MemoHeader = memo(Header);
export default MemoHeader;
