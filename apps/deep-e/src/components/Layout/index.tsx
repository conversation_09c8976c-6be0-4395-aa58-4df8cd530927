import React, { useRef, useState } from 'react';
import { Layout, Menu, Modal } from 'antd';
import Sider from 'antd/es/layout/Sider';
import { Content, Header as LayoutHeader } from 'antd/es/layout/layout';
import { useAuth } from '@/hooks';
import { useNavigate, useLocation, useOutlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { CollapsedIcon } from './layout/index.tsx';
import Header from './Header.tsx';
import { t } from '@/languages';
import { setShowbeLoginCovered } from '@/store/features/layoutSlice.ts';
import { setShowLoginConfirm } from '@/store/features/loginSlice.ts';
import AntdConfigProvider from '@/components/Layout/AntdConfigProvider.tsx';
import ConfirmLoginModal from '@/components/Layout/ConfirmLoginModal.tsx';
import store from '@/store';
import { useAuthStore } from '@/store/features/auth.ts';
import _ from 'lodash';
// import KeepAlive from 'react-activation';
import { KeepAliveContext } from '../KeepAlive/KeepAlliveContext.ts';
import KeepAliveOutlet from '../KeepAlive/KeepAliveOutlet.tsx';
import { useCurrentPath } from './hooks/useCurrentPath.ts';

const JWLayout: React.FC = () => {
  const { authMenus, menuList } = useAuth([]);
  const { language, logout } = useAuthStore();

  const unKeepAliveRouteList = ['/home', '/runningPage']
    .concat(
      authMenus
        .filter((item: any) => item.path.split('/').length !== 2)
        .map((item) => item.path),
    )
    .map((item: any) => item);
  const navigate = useNavigate();

  const location = useLocation();
  const user = useSelector((state: any) => state.login.user);
  const coCode = user?.coCode || '';
  //当前选中的nav

  const { currentPath, setCurrentPath } = useCurrentPath(
    authMenus,
    location.pathname,
  );

  const contentHeightRef = useRef<number>(0);

  // const [drawerVisible, setDrawerVisible] = useState(false);
  const [collapsed, setCollapsed] = useState<boolean>(false);

  const outlet = useOutlet();

  const handleLoginCovered = () => {
    localStorage.clear();
    navigate('/login');
  };

  return (
    <>
      <KeepAliveContext.Provider
        value={{
          outlet,
          key: location.pathname,
          height: contentHeightRef.current,
        }}>
        <div className='pchome'>
          <AntdConfigProvider>
            <Layout className='h-screen'>
              <Sider
                width={language === 'zh' ? 240 : 300}
                className='border border-solid border-r-neutral-5 border-gray-300'
                collapsed={collapsed}
                collapsedWidth={70}>
                <div className='flex flex-col justify-between h-full'>
                  <div className='overflow-y-auto'>
                    <div
                      className=' flex justify-center items-center cursor-pointer text-brand font-bold text-[32px]'
                      onClick={() => {
                        navigate('/home');
                      }}>
                      <img
                        className='h-[40px] mt-[20px]'
                        src={
                          coCode.startsWith('509')
                            ? '/BlackLake.svg'
                            : !collapsed
                              ? '/DE/SVG/LOGO-Color.svg'
                              : '/DE/favicon.svg'
                        }
                      />
                    </div>
                    <Menu
                      mode='inline'
                      style={{
                        height: 'calc(100vh - 140px)',
                        marginTop: '20px',
                        fontSize: '16px',
                        // overflow: 'auto',
                      }}
                      defaultSelectedKeys={['/home']}
                      items={menuList.map((item: any, index: number) => {
                        const processChildren = (children: any) => {
                          if (children === undefined) return null;
                          return children.map((child: any) => {
                            return {
                              key: child.path,
                              label: child.name,
                              children: processChildren(child.children),
                            };
                          });
                        };
                        return {
                          key: item.path || index,
                          label: <div className='max-w-xl'>{item.name}</div>,
                          icon: item.icon,
                          children: processChildren(item.children),
                        };
                      })}
                      selectedKeys={[currentPath]}
                      onClick={({ key }) => {
                        navigate(key);
                        setCurrentPath(key);
                      }}
                    />
                  </div>
                  <div className='w-full flex justify-center pb-4'>
                    <CollapsedIcon
                      onClick={() => setCollapsed(!collapsed)}
                      style={{ transform: !collapsed ? 'rotate(180deg)' : '' }}
                    />
                  </div>
                </div>
              </Sider>
              <Layout>
                <LayoutHeader className=' border-b-1 border-gray-300'>
                  <Header openDrawer={() => {}} />
                </LayoutHeader>
                <Content
                  className='h-full overflow-auto'
                  ref={(el) => {
                    if (el) {
                      contentHeightRef.current = el.clientHeight;
                    }
                  }}>
                  {unKeepAliveRouteList.includes(location.pathname) ? (
                    outlet
                  ) : (
                    <KeepAliveOutlet />
                  )}
                </Content>
              </Layout>
            </Layout>
          </AntdConfigProvider>
          <ConfirmLoginModal />
        </div>

        <Modal
          open={useSelector((state: any) => state.layout.showBeLoginCovered)}
          title={t('下线提示')}
          cancelText={<></>}
          okText={t('确认')}
          onOk={() => {
            store.dispatch(setShowLoginConfirm(false));
            store.dispatch(setShowbeLoginCovered(false));
            logout();
            handleLoginCovered();
          }}
          cancelButtonProps={{
            style: {
              display: 'none',
            },
          }}
          closable={false}>
          <span>{t('该账号已被其他用户登录, 您已下线, 请重新登录。')}</span>
        </Modal>
      </KeepAliveContext.Provider>
    </>
  );
};
export default JWLayout;
