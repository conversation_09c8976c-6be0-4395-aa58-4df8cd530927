import React, { useEffect } from 'react';

export const useCurrentPath = (authMenu: any[], path: string) => {
  const [currentPath, setCurrentPath] = React.useState('/');

  const isIndex = (path: string) => {
    return authMenu.find((item: any) => item.path === path)?.index || false;
  };

  useEffect(() => {
    if (!isIndex(path)) {
      const paths = path.split('/');
      paths.pop();
      setCurrentPath(paths.join('/'));
    } else {
      setCurrentPath(path);
    }
  }, [path, authMenu]);

  return {
    currentPath,
    setCurrentPath,
  };
};
