'use client';

import React from 'react';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import api from '@/api';

interface PasswordChangeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PasswordChangeModal({
  open,
  onOpenChange,
}: PasswordChangeModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear errors when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...errors };

    if (!formData.currentPassword) {
      newErrors.currentPassword = t('当前密码不能为空');
      valid = false;
    }

    if (!formData.newPassword) {
      newErrors.newPassword = t('新密码不能为空');
      valid = false;
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = t('新密码长度至少为8位');
      valid = false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t('两次输入的密码不一致');
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const res = await api.changePassword({
        password: formData.currentPassword,
        newPassword: formData.newPassword,
      });
      if (res.code !== 0) {
        toast({
          title: t('密码修改失败'),
          description: res.msg,
          variant: 'destructive',
        });
        return;
      }
      toast({
        title: t('密码修改成功'),
        description: t('您的密码已成功更新'),
      });

      onOpenChange(false);
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      toast({
        title: t('密码修改失败'),
        description: t('请检查您的当前密码是否正确'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='text-xl font-semibold'>
            {t('修改密码')}
          </DialogTitle>
          <DialogDescription>
            {t('请输入您的当前密码和新密码')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className='space-y-4 py-2'>
          <div className='space-y-2'>
            <Label htmlFor='currentPassword'>{t('当前密码')}</Label>
            <Input
              id='currentPassword'
              name='currentPassword'
              type='password'
              value={formData.currentPassword}
              onChange={handleChange}
              className={errors.currentPassword ? 'border-red-500' : ''}
            />
            {errors.currentPassword && (
              <p className='text-sm text-red-500'>{errors.currentPassword}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='newPassword'>{t('新密码')}</Label>
            <Input
              id='newPassword'
              name='newPassword'
              type='password'
              value={formData.newPassword}
              onChange={handleChange}
              className={errors.newPassword ? 'border-red-500' : ''}
            />
            {errors.newPassword && (
              <p className='text-sm text-red-500'>{errors.newPassword}</p>
            )}
            <p className='text-xs text-muted-foreground'>
              {t('密码长度至少为8位')}
            </p>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='confirmPassword'>{t('确认新密码')}</Label>
            <Input
              id='confirmPassword'
              name='confirmPassword'
              type='password'
              value={formData.confirmPassword}
              onChange={handleChange}
              className={errors.confirmPassword ? 'border-red-500' : ''}
            />
            {errors.confirmPassword && (
              <p className='text-sm text-red-500'>{errors.confirmPassword}</p>
            )}
          </div>

          <DialogFooter className='pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={() => onOpenChange(false)}
              disabled={isLoading}>
              {t('取消')}
            </Button>
            <Button type='submit' disabled={isLoading}>
              {isLoading ? t('提交中...') : t('确认修改')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
