import { Badge } from '@/components/ui/badge';
import { AlertCircle, AlertTriangle, Info } from 'lucide-react';
import dayjs from 'dayjs';
import { LogType } from '@/types/common';

interface LogViewerProps {
  logList: LogType[];
  format?: string;
}

const getLogStyles = (level: LogType['level']) => {
  switch (level) {
    case 'error':
      return {
        containerClass:
          'bg-rose-50 dark:bg-rose-950/20 border-l-4 border-rose-400',
        badgeVariant: 'secondary' as const,
        icon: AlertCircle,
        iconColor: 'text-rose-600 dark:text-rose-400',
      };
    case 'warning':
      return {
        containerClass:
          'bg-amber-50 dark:bg-amber-950/20 border-l-4 border-amber-400',
        badgeVariant: 'secondary' as const,
        icon: AlertTriangle,
        iconColor: 'text-amber-600 dark:text-amber-400',
      };
    case 'info':
      return {
        containerClass:
          'bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-400',
        badgeVariant: 'secondary' as const,
        icon: Info,
        iconColor: 'text-blue-600 dark:text-blue-400',
      };
    default:
      return {
        containerClass:
          'bg-gray-50 dark:bg-gray-950/20 border-l-4 border-gray-300',
        badgeVariant: 'secondary' as const,
        icon: Info,
        iconColor: 'text-gray-500',
      };
  }
};

export default function LogViewer({
  logList,
  format = 'YYYY-MM-DD HH:mm:ss',
}: LogViewerProps) {
  return (
    <div className='space-y-2'>
      {logList.map((log, index: number) => {
        const styles = getLogStyles(log.level);
        const IconComponent = styles.icon;

        return (
          <div
            key={log.taskUuid + index}
            className={`p-3 rounded-md ${styles.containerClass}`}>
            <div className='flex items-center gap-3 cursor-pointer'>
              <IconComponent
                className={`h-4 w-4 ${styles.iconColor} flex-shrink-0`}
              />
              <div className='text-xs text-muted-foreground whitespace-nowrap'>
                {dayjs(log.time).format(format)}
              </div>
              <Badge
                variant={styles.badgeVariant}
                className='whitespace-nowrap'>
                {log.level}
              </Badge>
              <div className='flex-1 text-sm'>{log.msg}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
