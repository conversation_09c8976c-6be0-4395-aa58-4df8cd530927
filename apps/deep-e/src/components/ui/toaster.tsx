import { useToast } from '@/hooks/use-toast';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

export function Toaster() {
  const { toasts, dismiss } = useToast();

  return (
    <div className='fixed bottom-0 right-0 z-50 w-full max-w-sm p-4 md:bottom-4 md:right-4 md:top-auto'>
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={cn(
            'mb-2 flex w-full items-center justify-between rounded-md border p-4 shadow-lg',
            'bg-white dark:bg-gray-800',
            toast.variant === 'destructive' &&
              'border-red-600 bg-red-50 text-red-900 dark:bg-red-900 dark:text-red-50',
          )}>
          <div className='grid gap-1'>
            {toast.title && <div className='font-medium'>{toast.title}</div>}
            {toast.description && (
              <div className='text-sm opacity-90'>{toast.description}</div>
            )}
          </div>
          <button
            onClick={() => dismiss(toast.id)}
            className='rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700'>
            <X className='h-4 w-4' />
            <span className='sr-only'>关闭</span>
          </button>
        </div>
      ))}
    </div>
  );
}
