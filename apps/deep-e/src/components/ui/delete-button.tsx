'use client';

import { useState, forwardRef } from 'react';
import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { t } from '@/languages';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DeleteButtonProps {
  onDelete: () => void;
  buttonText?: string;
  confirmTitle?: string;
  confirmDescription?: string;
  className?: string;
  type?:
    | 'default'
    | 'link'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | null
    | undefined;
  [key: string]: any;
}

const DeleteButton = forwardRef<HTMLButtonElement, DeleteButtonProps>(
  (
    {
      onDelete,
      buttonText = t('删除'),
      confirmTitle = t('确认删除'),
      confirmDescription = t('您确定要删除吗？此操作无法撤销。'),
      className,
      type = 'link',
      ...rest
    },
    ref,
  ) => {
    const [open, setOpen] = useState(false);

    const handleConfirm = () => {
      onDelete();
      setOpen(false);
    };

    return (
      <>
        <Button
          {...rest}
          ref={ref}
          variant={type}
          className={` h-[32px] text-[14px] text-red-500
          ${className}`}
          onClick={() => setOpen(true)}>
          <Trash2 className='mr-2 h-4 w-4' />
          {buttonText}
        </Button>

        <AlertDialog open={open} onOpenChange={setOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{confirmTitle}</AlertDialogTitle>
              <AlertDialogDescription>
                {confirmDescription}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('取消')}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirm}
                className='bg-blue-500 hover:bg-blue-500/80'>
                {t('确认')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  },
);

DeleteButton.displayName = 'DeleteButton';
export { DeleteButton };
