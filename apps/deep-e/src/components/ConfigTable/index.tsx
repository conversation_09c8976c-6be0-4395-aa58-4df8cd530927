import { Button, Space, Select, Input, Modal, Divider } from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import { PlusOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import {
  ProTable,
  TableDropdown,
  ColumnsState,
} from '@ant-design/pro-components';
import { useState, useRef } from 'react';
import HeaderSearch from './HeaderSearch';
import { HeaderResultValue } from './configTableType';

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  creator: string;
  status: string;
  createdAt: number;
  memo: string;
};
const valueEnum = {
  0: 'close',
  1: 'running',
  2: 'online',
  3: 'error',
};
const tabListData: TableListItem[] = [];
const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];
for (let i = 0; i < 5; i += 1) {
  tabListData.push({
    key: i,
    name: 'AppName',
    containers: Math.floor(Math.random() * 20),
    creator: creators[Math.floor(Math.random() * creators.length)],
    status: valueEnum[((Math.floor(Math.random() * 10) % 4) + '') as '0'],
    createdAt: Date.now() - Math.floor(Math.random() * 100000),
    memo:
      i % 2 === 1
        ? '很长很长很长很长很长很长很长的文字要展示但是要留下尾巴'
        : '简短备注文案',
  });
}
const columns: ProColumns<TableListItem>[] = [
  {
    title: '应用名称',
    dataIndex: 'name',
    render: (_) => <a>{_}</a>,
  },
  {
    title: '容器数量',
    dataIndex: 'containers',
    sorter: (a, b) => a.containers - b.containers,
  },
  {
    title: '状态',
    dataIndex: 'status',
    initialValue: 'all',
    filters: true,
    onFilter: true,
    valueEnum: {
      all: { text: '全部', status: 'Default' },
      close: { text: '关闭', status: 'Default' },
      running: { text: '运行中', status: 'Processing' },
      online: { text: '已上线', status: 'Success' },
      error: { text: '异常', status: 'Error' },
    },
  },
  // {
  //   title: '创建者',
  //   dataIndex: 'creator',
  //   valueEnum: {
  //     all: { text: '全部' },
  //     付小小: { text: '付小小' },
  //     曲丽丽: { text: '曲丽丽' },
  //     林东东: { text: '林东东' },
  //     陈帅帅: { text: '陈帅帅' },
  //     兼某某: { text: '兼某某' },
  //   },
  // },
  {
    title: '操作',
    width: 180,
    order: 0,
    key: 'option',
    valueType: 'option',
    render: () => [
      <a key='link'>链路</a>,
      <a key='link2'>报警</a>,
      <a key='link3'>监控</a>,
      <TableDropdown
        key='actionGroup'
        menus={[
          { key: 'copy', name: '复制' },
          { key: 'delete', name: '删除' },
        ]}
      />,
    ],
  },
];
export type PersonConfigItem = {
  label: string;
  condition: string;
  value: string;
};
const TestPage: React.FC = () => {
  const [columnsStateMap, setColumnsStateMap] = useState<
    Record<string, ColumnsState>
  >({
    name: {
      show: false,
      order: 0,
    },
  });
  let index = 0;
  const [visible, setVisible] = useState(false);
  const [items, setItems] = useState(['jack', 'lucy']);
  const [name, setName] = useState('');
  const inputRef = useRef<InputRef>(null);

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const addItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    setItems([...items, name || `New item ${index++}`]);
    setName('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const search = (value: HeaderResultValue) => {
    console.log(value);
  };
  return (
    <div>
      <header className='w-full'>
        <HeaderSearch onChange={() => {}} search={search} />
      </header>
      <main className='w-full bg-white'>
        <ProTable<TableListItem>
          dataSource={tabListData}
          rowKey='key'
          columns={columns}
          columnsState={{
            value: columnsStateMap,
            onChange: setColumnsStateMap,
          }}
          search={false}
          dateFormatter='string'
        />
      </main>
      <Modal
        title='选择保存到的配置'
        open={visible}
        onCancel={() => setVisible(false)}>
        <Select
          style={{ width: '250px' }}
          dropdownRender={(menu) => {
            return (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    placeholder='Please enter item'
                    ref={inputRef}
                    value={name}
                    onChange={onNameChange}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <Button type='text' icon={<PlusOutlined />} onClick={addItem}>
                    Add item
                  </Button>
                </Space>
              </>
            );
          }}
          options={items.map((item) => ({ label: item, value: item }))}
        />
      </Modal>
    </div>
  );
};

export default TestPage;
