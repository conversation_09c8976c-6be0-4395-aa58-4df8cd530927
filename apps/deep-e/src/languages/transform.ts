// import type { VoerkaI18nScope, VoerkaI18nTranslateVars,VoerkaI18nTranslateOptions } from "@voerkai18n/runtime";
// export transform = (scope:VoerkaI18nScope)=>{
//  return (message:string, vars?:VoerkaI18nTranslateVars, options?:VoerkaI18nTranslateOptions) => {
//         return
// }
// export type TransformResultType = any

export const transform = undefined;
export type TransformResultType = any;
