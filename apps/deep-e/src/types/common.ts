import { z } from 'zod';

const pageDataSchema = z.object({
  page: z.number(),
  pageSize: z.number(),
  total: z.number().optional(),
});
export type PageData = z.infer<typeof pageDataSchema>;

const LogTypeSchema = z.object({
  details: z.string().nullable(),
  error: z.string(),
  level: z.enum(['info', 'error', 'warning']),
  msg: z.string(),
  msgDetail: z.string(),
  seq: z.number(),
  taskUuid: z.string().uuid(),
  time: z.string().datetime(),
});

export type LogType = z.infer<typeof LogTypeSchema>;
