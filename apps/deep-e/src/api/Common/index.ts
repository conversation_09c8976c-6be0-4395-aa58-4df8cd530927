import request from '@/services';

const CommonApi = {
  /**
   *@description 修改用户偏好计划版本
   *@param
   *@return {Promise}
   *@auther liujinx
   */
  setUserPerferVersion: (data: any) =>
    request.post('/api/user/setUserPreference', data),
  /**
   *@description 修改用户偏好计划域
   *@param
   *@return {Promise}
   *@auther liujinxu
   */
  setUserPerferParea: (data: any) =>
    request.post('/api/base/setUserPreferenceParea', data),

  ///user/getUserListByCoId
  getUserListByCoId: () => request.get('/api/user/getUserListByCoId'),

  /**
   * @description 获取用户有权限的菜单
   *@api /menu/getTierMenus
   */
  getTierMenus: () => request.get('/api/menu/getTierMenus', {}),

  /**
   * @description 获取用户偏好表格版本
   * @api /filter/getTableConfigColumn
   */
  getTableConfigColumn: (data: { menuTag: string; schemeId: number }) =>
    request.get('/api/filter/getTableConfigColumn', { params: data }),

  /**
   * @description 保存用户偏好表格版本
   * @api /filter/saveTableConfigColumn
   */
  saveTableConfigColumn: (data: any) =>
    request.post('/api/filter/saveTableConfigColumn', data),

  /**
   * @description 获取时区列表
   * @api /baseInfo/getSystemTimeZoneList
   */
  getSystemTimeZoneList: () =>
    request.get('/api/baseInfo/getSystemTimeZoneList'),

  /**
   * @description 修改密码
   * @api /user/changePassword
   */
  changePassword: (data: { password: string; newPassword: string }) =>
    request.post('/api/user/changePassword', data),

  getModelTypeListForChat: () => {
    return request.get('/api/modelConfig/getModelTypeListForChat');
  },
};

export default CommonApi;
