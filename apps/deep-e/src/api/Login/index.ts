import request from '@/services';

const LoginApi = {
  // 获取AES密钥
  getAesKey: (randomString: string) =>
    request.get('/api/base/getPasswordCrypto', { params: { randomString } }),

  // 获取登陆验证码/base/captcha
  getCaptcha: () => request.post('/api/base/captcha'),
  // 登陆/base/login
  login: (data: any) => request.post('/api/base/login', data),
  // 预登陆接口
  loginPre: (data: any) => request.post('/api/base/loginPre', data),
  // 跳转int验证token
  scpJumpToInt: () => request.post('/jump/scpJumpToInt'),
  // token过期，再次登录
  loginAgain: (data: any) =>
    request.post('/api/base/getNewTokenForExpired', data),
  // 覆盖已登录用户登录
  loginConfirm: (data: any) => request.post('/api/base/loginConfirm', data),
  // 注销
  logout: () => request.post('/api/base/logout'),
  // 使用钉钉/企业微信授权
  ssoAuth: (data: any) =>
    request.get('/api/base/ssoAuth', {
      params: data,
    }),
  // 使用钉钉/企业微信预登录
  ssoLoginPre: (data: any) => request.post('/api/base/ssoLoginPre', data),
  // 使用钉钉/企业微信登录
  ssoLogin: (data: any) => request.post('/api/base/ssoLogin', data),
  // 钉钉/企业微信强制登录
  ssoLoginConfirm: (data: any) =>
    request.post('/api/base/ssoLoginConfirm', data),
};

export default LoginApi;
