import { useContext } from 'react';

export function useAlertDialog() {
  const context = useContext(AlertDialogContext);
  if (!context) {
    throw new Error(
      'useAlertDialog must be used within an AlertDialogProvider',
    );
  }
  return context;
}

import React, { createContext, useCallback, useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

type AlertDialogOptions = {
  title?: string;
  description?: string;
  confirmText?: string;
  onConfirm?: () => void;
};

type AlertDialogContextType = {
  show: (options: AlertDialogOptions) => void;
};

export const AlertDialogContext = createContext<AlertDialogContextType | null>(
  null,
);

export function AlertDialogProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState<AlertDialogOptions>({});

  const show = useCallback((options: AlertDialogOptions) => {
    setOptions(options);
    setOpen(true);
  }, []);

  const handleConfirm = () => {
    options.onConfirm?.();
    setOpen(false);
  };

  return (
    <AlertDialogContext.Provider value={{ show }}>
      {children}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{options.title || t('提示')}</AlertDialogTitle>
            <AlertDialogDescription>
              {options.description || ''}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={handleConfirm}
              className='bg-blue-500 hover:bg-blue-500/80'>
              {options.confirmText || t('确认')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AlertDialogContext.Provider>
  );
}
