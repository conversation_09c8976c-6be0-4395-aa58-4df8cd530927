import { PageData } from '@/types';
import { useState } from 'react';
import type { PaginationProps } from 'antd';
const usePageData = (): {
  pageData: PageData;
  setPageData: React.Dispatch<
    React.SetStateAction<{
      page: number;
      pageSize: number;
      total?: number | undefined;
    }>
  >;
  pagination: PaginationProps;
} => {
  const [pageData, setPageData] = useState<PageData>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const pagination: PaginationProps = {
    current: pageData.page,
    pageSize: pageData.pageSize,
    total: pageData.total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => t('共{total}条', total),
    onChange: (page, pageSize) => {
      setPageData({
        ...pageData,
        page: page,
        pageSize: pageSize,
      });
    },
    onShowSizeChange: (current, pageSize) => {
      setPageData({
        ...pageData,
        page: current,
        pageSize: pageSize,
      });
    },
  };
  return {
    pageData,
    setPageData,
    pagination,
  };
};

export default usePageData;
