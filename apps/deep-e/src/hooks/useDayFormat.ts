import { useAuthStore } from '@/store/features';
import { useInternationalStore } from '@/store/features/international';
import { useEffect, useState } from 'react';
const useDayFormat = () => {
  const { language } = useAuthStore();
  const { getDayFormat } = useInternationalStore();
  const [format, setFormat] = useState('YYYY-MM-DD HH:mm:ss');
  useEffect(() => {
    const dayFormat = getDayFormat(language as 'zh' | 'en' | 'de');
    setFormat(dayFormat);
  }, [language]);
  return format;
};
export default useDayFormat;
