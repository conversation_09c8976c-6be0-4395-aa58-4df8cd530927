/**
 *@description 转换BIG节点类型
 *@return string
 *@auther liujinxu
 * @param node_type
 */
function transformNodeType(node_type: string): string {
  switch (node_type) {
    case 'input':
      return 'raw';
    case 'output':
      return 'polymerization';
    case 'JPA':
    case 'FE':
    case 'PA':
      return 'production';
    case 'BES':
    case 'BAS':
    case 'JBS':
      return 'transport';
    case 'BAP':
    case 'BEP':
    case 'JBP':
    case 'LA':
      return 'purchase';
    case 'FCT':
    case 'VC':
    case 'VJ':
    case 'LD':
      return 'order';
    case 'STK':
      return 'stock';
    default:
      return 'raw';
  }
}

function checkoutNodeDeeper(node_type: string): number {
  switch (node_type) {
    case 'JPA':
    case 'JBS':
    case 'JBP':
      return 1;
    case 'BAS':
    case 'BAP':
    case 'PA':
      return 2;
    case 'FE':
    case 'LA':
    case 'BES':
    case 'BEP':
    case 'VC':
      return 3;
  }
  return 1;
}

function transformNodeName(node_type: string, isItemInput: boolean): string {
  switch (node_type) {
    case 'input':
      if (isItemInput) {
        return '销售订单条目';
      }
      return '物料消耗';
    case 'output':
      return '物料接收';
    case 'JPA':
    case 'FE':
    case 'PA':
      return '生产';
    case 'BES':
    case 'BAS':
    case 'JBS':
      return '运输';
    case 'BAP':
    case 'BEP':
    case 'JBP':
    case 'LA':
      return '采购';
    case 'FCT':
    case 'VC':
    case 'VJ':
      return '独立需求';
    case 'STK':
      return '库存';
    default:
      return '原料';
  }
}
function transformStatus(
  status: string[] | undefined,
): 'normal' | 'error' | 'warning' {
  if (!status) return 'normal';
  const returnStatus = status.map((item) => {
    switch (item) {
      case 'redundantRisk':
      case 'deliveryRisk':
      case 'riskCertificate':
        return 'error';
      case 'delayDelivery':
        return 'warning';
      default:
        return 'normal';
    }
  });
  for (let i = 0; i < returnStatus.length; i++) {
    if (returnStatus[i] === 'error') {
      return 'error';
    }
    if (returnStatus[i] === 'warning') {
      return 'warning';
    }
  }
  return 'normal';
}
function transformTypeToName(type: string): string {
  const typeMap = {
    JPA: 'JW计划订单',
    FE: 'ERP生产订单',
    PA: 'ERP计划订单',
    BES: 'ERP调拨订单',
    BAS: 'ERP调拨申请',
    JBS: 'JW调拨申请',
    BAP: 'ERP采购申请',
    BEP: 'ERP采购订单',
    JBP: 'JW采购申请',
    LA: '入库',
    VJ: '出库',
    STK: '库存',
    FCT: '预测订单',
    VC: '销售订单',
    LD: '时序需求',
  };
  const newMap = new Map<string, string>(Object.entries(typeMap));
  const nameMap = newMap.get(type);
  return nameMap ? nameMap : '未知类型';
}

export {
  transformNodeType,
  checkoutNodeDeeper,
  transformNodeName,
  transformStatus,
  transformTypeToName,
};
