import dayjs, { Dayjs } from 'dayjs';
type DateType = 'date' | 'week' | 'month' | 'quarter' | 'year';
function changeSvgColor(svgAddress: string, color: string): Promise<string> {
  // 使用fetch获取SVG文件内容
  return fetch(svgAddress)
    .then((response) => response.text())
    .then((svgString) => {
      // 使用DOMParser解析SVG字符串
      const parser = new DOMParser();
      const doc = parser.parseFromString(svgString, 'image/svg+xml');

      // 获取SVG的根元素
      const svgElement = doc.documentElement;

      // 遍历所有具有fill属性的元素，将其颜色修改为传入的颜色
      const elementsWithFill = svgElement.querySelectorAll('[fill]');
      elementsWithFill.forEach((element) => {
        element.setAttribute('fill', color);
      });

      // 将修改后的SVG转换回字符串
      const modifiedSvgString = new XMLSerializer().serializeToString(
        svgElement,
      );

      return modifiedSvgString;
    })
    .catch((error) => {
      console.error('Error fetching or modifying SVG:', error);
      return ''; // 返回空字符串或其他默认值
    });
}

/**
 * @description 判断对像是否为空对象
 * */
function isEmptyObject(obj: object): boolean {
  return Object.keys(obj).length === 0;
}
function formatDate(date: Dayjs, type: DateType): string {
  switch (type) {
    case 'date':
      return dayjs(date).format('YYYY-MM-DD');
    case 'month':
      return dayjs(date).startOf('month').format('YYYY-MM');
    case 'year':
      return dayjs(date).startOf('year').format('YYYY');
    default:
      return dayjs(date).format();
  }
}

/**
 * @description 移除对象中值为undefined的键值对
 * */
function removeUndefinedValues(obj: any) {
  return Object.fromEntries(
     
    Object.entries(obj).filter(([_, value]) => value !== undefined),
  );
}

// 判断是否为移动端方法
const isMobile = () => {
  const userAgentInfo = navigator.userAgent;
  return (
    /AppleWebKit.*Mobile/i.test(userAgentInfo) ||
    /MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(
      userAgentInfo,
    )
  );
};

export {
  changeSvgColor,
  isEmptyObject,
  formatDate,
  removeUndefinedValues,
  isMobile,
};
