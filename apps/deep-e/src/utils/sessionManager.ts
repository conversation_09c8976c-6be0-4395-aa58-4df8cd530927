import { v4 as uuidv4 } from 'uuid';

/**
 * Session Manager Utility
 *
 * Provides utilities for managing chat session IDs and related functionality.
 * This can be used across different components that need session management.
 */

export interface SessionInfo {
  sessionId: string;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
}

export class SessionManager {
  private static instance: SessionManager;
  private sessions: Map<string, SessionInfo> = new Map();

  private constructor() {}

  /**
   * Get singleton instance of SessionManager
   */
  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Generate a new session ID
   * @returns A new UUID v4 string
   */
  public generateSessionId(): string {
    return uuidv4();
  }

  /**
   * Create a new session with metadata
   * @returns SessionInfo object with new session details
   */
  public createSession(): SessionInfo {
    const sessionId = this.generateSessionId();
    const now = new Date();

    const sessionInfo: SessionInfo = {
      sessionId,
      createdAt: now,
      lastActivity: now,
      messageCount: 0,
    };

    this.sessions.set(sessionId, sessionInfo);
    return sessionInfo;
  }

  /**
   * Update session activity
   * @param sessionId - The session ID to update
   * @param incrementMessageCount - Whether to increment the message count
   */
  public updateSessionActivity(
    sessionId: string,
    incrementMessageCount: boolean = false,
  ): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivity = new Date();
      if (incrementMessageCount) {
        session.messageCount++;
      }
    }
  }

  /**
   * Get session information
   * @param sessionId - The session ID to retrieve
   * @returns SessionInfo object or undefined if not found
   */
  public getSession(sessionId: string): SessionInfo | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all active sessions
   * @returns Array of SessionInfo objects
   */
  public getAllSessions(): SessionInfo[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Remove a session
   * @param sessionId - The session ID to remove
   */
  public removeSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  /**
   * Clear all sessions
   */
  public clearAllSessions(): void {
    this.sessions.clear();
  }

  /**
   * Clean up old sessions (older than specified hours)
   * @param hoursOld - Sessions older than this many hours will be removed
   */
  public cleanupOldSessions(hoursOld: number = 24): void {
    const cutoffTime = new Date(Date.now() - hoursOld * 60 * 60 * 1000);

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.lastActivity < cutoffTime) {
        this.sessions.delete(sessionId);
      }
    }
  }

  /**
   * Get session duration in minutes
   * @param sessionId - The session ID
   * @returns Duration in minutes or -1 if session not found
   */
  public getSessionDuration(sessionId: string): number {
    const session = this.sessions.get(sessionId);
    if (!session) return -1;

    const durationMs =
      session.lastActivity.getTime() - session.createdAt.getTime();
    return Math.floor(durationMs / (1000 * 60));
  }
}

/**
 * React hook for session management
 *
 * @returns Object with session management functions
 */
export function useSessionManager() {
  const manager = SessionManager.getInstance();

  return {
    generateSessionId: () => manager.generateSessionId(),
    createSession: () => manager.createSession(),
    updateSessionActivity: (
      sessionId: string,
      incrementMessageCount?: boolean,
    ) => manager.updateSessionActivity(sessionId, incrementMessageCount),
    getSession: (sessionId: string) => manager.getSession(sessionId),
    getAllSessions: () => manager.getAllSessions(),
    removeSession: (sessionId: string) => manager.removeSession(sessionId),
    clearAllSessions: () => manager.clearAllSessions(),
    cleanupOldSessions: (hoursOld?: number) =>
      manager.cleanupOldSessions(hoursOld),
    getSessionDuration: (sessionId: string) =>
      manager.getSessionDuration(sessionId),
  };
}

/**
 * Simple session ID generator function for basic use cases
 * @returns A new UUID v4 string
 */
export const generateSessionId = (): string => {
  return uuidv4();
};

/**
 * Validate if a string is a valid UUID v4
 * @param uuid - The string to validate
 * @returns True if valid UUID v4, false otherwise
 */
export const isValidSessionId = (uuid: string): boolean => {
  const uuidV4Regex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidV4Regex.test(uuid);
};

export default SessionManager;
