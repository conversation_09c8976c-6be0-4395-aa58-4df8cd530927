# GEMINI.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React TypeScript application built with Vite for an AI/LLM platform called "Deep-E". The application provides a comprehensive dashboard for managing AI models, datasets, evaluations, and training processes.

## Development Commands

```bash
# Development
pnpm dev                # Start development server (localhost:8188)
pnpm dev:mock          # Start development server with MSW mocking enabled

# Building
pnpm build             # Build for production (includes TypeScript compilation)
pnpm build:dev         # Build for development environment 
pnpm build:prod        # Build for production environment

# Code Quality
pnpm eslint            # Run ESLint with auto-fix
pnpm lint              # Run lint-staged (used in pre-commit hooks)

# Testing
pnpm test              # Run Vitest tests

# Internationalization
pnpm translate         # Extract, translate, and compile i18n messages using Voerkai18n

# Preview
pnpm preview           # Preview production build
```

## Architecture Overview

### UI Library Stack
The application uses multiple UI libraries in combination:
- **Ant Design** (`antd`) - Primary component library for complex components
- **NextUI** (`@nextui-org/react`) - Modern React components
- **shadcn/ui** - Utility-first components in `src/components/ui/`
- **Tailwind CSS** - Utility-first CSS framework
- **Styled Components** - CSS-in-JS for custom styling

### State Management
- **Redux Toolkit** with Redux Persist for global state
- **Zustand** for lightweight state management (see `src/store/zustand_index.ts`)
- State persisted to localStorage with `redux-persist`

### Routing System
- Dynamic route generation based on user permissions via `authMenus`
- Routes defined in `src/router/index.tsx` with lazy loading
- Route info configuration in `route_info` array maps paths to icons
- Keep-alive functionality for certain routes using `react-activation`

### Internationalization (i18n)
- **Voerkai18n** framework for translations
- Supports English (en-US), Chinese (zh-CN), and German (de-DE)
- Messages in `src/languages/messages/`
- Paragraphs in `src/languages/paragraphs/`
- Auto-import `t` function available globally

### API & Services
- Custom `authFetch` wrapper in `src/services/fetch.ts` handles authentication
- API modules structured by feature in `src/api/`
- Axios for HTTP requests
- Proxy configuration in `vite.config.ts` for `/api`, `/mock`, and `/ai` endpoints

### Key Features
- **Dashboard**: Home page with charts and metrics
- **Model Management**: Deploy, train, and manage AI models
- **Datasets**: Upload and manage training datasets  
- **Knowledge Hub**: Document and knowledge base management
- **Evaluation**: Model performance testing and comparison
- **Fine-tuning**: Model training and parameter tuning

## Directory Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   └── Layout/         # App layout components
├── pages/              # Route-based page components
├── api/                # API service modules
├── store/              # Redux store and Zustand stores
├── hooks/              # Custom React hooks
├── services/           # Utility services (fetch, etc.)
├── languages/          # i18n configuration and messages
├── router/             # Route configuration
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Development Guidelines

### Component Patterns
- Use lazy loading for page components: `React.lazy(() => import('./Component'))`
- Wrap async components with `<Suspense fallback={<Loading />}>`
- Error boundaries implemented using `@ant-design/pro-components`

### State Patterns
- Use Redux for global application state (auth, layout)
- Use Zustand for feature-specific state
- Prefer custom hooks for component-level state logic

### Styling Approach
- Tailwind for utility classes
- Ant Design theme customization in `AntdConfigProvider`
- Component-specific styles with styled-components when needed
- CSS variables for theming support

### API Integration
- All API calls should use `authFetch` for authentication
- API modules organized by domain (Login, Common, TaskTemplate, etc.)
- Handle loading states and error boundaries consistently

### Testing
- Vitest + React Testing Library setup
- Test files should be colocated with components
- Global test setup in `src/test/setup.ts`

## Important Notes

- This is a monorepo setup using workspace dependencies (`@repo/*`)
- MSW (Mock Service Worker) available for API mocking in development
- Keep-alive routes configured to preserve state on navigation
- Docker configuration available for containerized deployment
- Husky + lint-staged for git hooks and code quality enforcement