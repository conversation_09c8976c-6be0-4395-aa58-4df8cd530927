/// <reference types="vitest" />
import { defineConfig } from 'vite';
// import { Plugin as importToCDN } from 'vite-plugin-cdn-import';
import react from '@vitejs/plugin-react';
import path from 'path';
import compress from 'vite-plugin-compress';
import svgr from 'vite-plugin-svgr';
// import Voerkai18nPlugin from "@voerkai18n/vite"
import i18nPlugin from '@voerkai18n/plugins/vite';
import tailwindcss from '@tailwindcss/vite';
import AutoImport from 'unplugin-auto-import/vite';
export default defineConfig({
  plugins: [
    AutoImport({
      dts: true,
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
      ],
      imports: [
        {
          '@/languages': ['t'],
        },
        {
          zod: ['z'],
        },
      ],
    }),
    i18nPlugin(),
    react(),
    tailwindcss(),
    svgr({
      include: '**/*.svg?react',
      // svgrOptions: {
      //   dimensions: false,
      // },
    }),
    compress as any,
  ],
  server: {
    host: '0.0.0.0',
    port: 8188,
    hmr: true,
    open: false,
    proxy: {
      '/api': {
        target: 'http://**************:50056',
        // target: 'http://localhost:50052',
        // target: 'http://*************:50056',
        changeOrigin: true,
        secure: false, // 添加此行，忽略 HTTPS 证书验证
        rewrite: (path) => path.replace(/^\/api/, ''),
        headers: {
          // 添加必要的请求头
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,PATCH,OPTIONS',
        },
      },
      '/mock': {
        target: 'http://127.0.0.1:4523/m1/5965047-0-default',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/mock\/api/, ''),
      },
      '/ai': {
        target: 'http://**************:11434',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/ai/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 设置请求头
            proxyReq.setHeader('X-Forwarded-For', req.socket.remoteAddress);
            proxyReq.setHeader('X-Real-IP', req.socket.remoteAddress);
            proxyReq.setHeader('Host', req.headers.host);
          });

          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 设置响应头
            proxyRes.headers['Access-Control-Allow-Origin'] = '*';
            proxyRes.headers['Access-Control-Allow-Methods'] =
              'GET, POST, OPTIONS';
            proxyRes.headers['Access-Control-Allow-Headers'] = '*';
            proxyRes.headers['Access-Control-Expose-Headers'] = '*';

            // OPTIONS 请求的特殊处理
            if (req.method === 'OPTIONS') {
              proxyRes.headers['Access-Control-Max-Age'] = '1728000';
              proxyRes.headers['Content-Type'] = 'text/plain charset=UTF-8';
              proxyRes.headers['Content-Length'] = '0';
              res.statusCode = 204;
            }
          });
        },
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,PATCH,OPTIONS',
          // 添加更多必要的头部
          'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'true',
        },
      },
    },
  },
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          antd: ['antd', '@ant-design/icons'],
        },
      },
    },
    chunkSizeWarningLimit: 3000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
    css: true,
  },
});
