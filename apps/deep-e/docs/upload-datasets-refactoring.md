# 数据集上传组件解耦重构

## 概述

本文档描述了将原本单一的数据集上传组件按照tab功能进行解耦重构的过程。

## 重构目标

1. **关注点分离**：将单文件上传和多模态上传的逻辑分离到不同的组件中
2. **代码可维护性**：减少单个文件的复杂度，提高代码的可读性和可维护性
3. **组件复用**：独立的组件可以在其他地方复用
4. **测试友好**：独立的组件更容易进行单元测试

## 重构后的文件结构

```
apps/deep-e/src/pages/Datasets/children/UploadDatasets/
├── index.tsx                          # 主入口组件
├── components/
│   ├── SingleFileUpload.tsx          # 单文件上传组件
│   └── MultiModalUpload.tsx          # 多模态上传组件
└── docs/
    ├── folder-compression-implementation.md
    └── upload-datasets-refactoring.md
```

## 组件职责划分

### 1. 主入口组件 (`index.tsx`)

**职责**：
- 管理tab切换状态
- 提供统一的页面布局和导航
- 管理表单实例并传递给子组件
- 处理tab切换时的表单重置

**核心功能**：
```typescript
- Tab状态管理 (activeTab)
- 表单实例管理 (form)
- 页面导航 (handleBack)
- Tab切换处理 (handleTabChange)
```

### 2. 单文件上传组件 (`SingleFileUpload.tsx`)

**职责**：
- 处理单文件上传的所有逻辑
- 管理文件选择和验证
- 调用单文件上传API
- 提供单文件上传的UI界面

**核心功能**：
```typescript
- 文件选择和验证
- 表单提交处理 (handleSubmit)
- API调用 (uploadDataset)
- 错误处理和用户反馈
```

**API端点**：`/api/dataset/uploadDataset`

### 3. 多模态上传组件 (`MultiModalUpload.tsx`)

**职责**：
- 处理文件夹选择和管理
- JSONL文件检测和选择
- 文件夹压缩功能
- 压缩文件下载功能
- 调用文件夹上传API

**核心功能**：
```typescript
- 文件夹选择 (handleFolderSelect)
- JSONL文件检测 (processAllFiles)
- 文件树构建 (buildFileTree)
- 文件夹压缩 (handleCompressFolder)
- 压缩文件下载 (handleDownloadZip)
- 表单提交处理 (handleSubmit)
```

**API端点**：`/api/file/uploadFolderForDataSet`

## 技术实现细节

### 状态管理

#### 主入口组件状态
```typescript
const [activeTab, setActiveTab] = useState('singleFile');
const [form] = Form.useForm<DatasetFormValues>();
```

#### 单文件上传组件状态
- 无额外状态，直接使用传入的form实例

#### 多模态上传组件状态
```typescript
const [jsonlFiles, setJsonlFiles] = useState<string[]>([]);
const [selectedJsonl, setSelectedJsonl] = useState<string | undefined>();
const [showJsonlSelectionSection, setShowJsonlSelectionSection] = useState(false);
const [fileTreeData, setFileTreeData] = useState<any[]>([]);
const [uppyInstance, setUppyInstance] = useState<any>(null);
const [compressedZipBlob, setCompressedZipBlob] = useState<Blob | null>(null);
const [zipFileName, setZipFileName] = useState<string>('');
```

### 组件通信

#### Props传递
```typescript
interface SingleFileUploadProps {
  form: any; // Ant Design Form实例
}

interface MultiModalUploadProps {
  form: any; // Ant Design Form实例
}
```

#### 数据流
1. 主入口组件创建form实例
2. 将form实例传递给对应的子组件
3. 子组件使用form实例进行表单操作
4. Tab切换时主入口组件重置表单

### 依赖管理

#### 共同依赖
- React, Ant Design, React Router
- API函数 (uploadDataset, uploadFolderForDataSet)
- 国际化 (t函数)

#### 单文件上传特有依赖
- 无特殊依赖

#### 多模态上传特有依赖
- Uppy (文件夹选择)
- JSZip (文件夹压缩)

## 重构优势

### 1. 代码组织
- **单一职责**：每个组件只负责一种上传方式
- **代码分离**：复杂的多模态逻辑不会影响简单的单文件上传
- **易于理解**：开发者可以专注于特定功能的实现

### 2. 维护性
- **独立修改**：修改一种上传方式不会影响另一种
- **错误隔离**：一个组件的问题不会影响其他组件
- **版本控制**：更清晰的代码变更历史

### 3. 可测试性
- **单元测试**：可以为每个组件编写独立的测试
- **模拟依赖**：更容易模拟API调用和外部依赖
- **测试覆盖**：可以针对不同场景编写专门的测试

### 4. 可复用性
- **组件复用**：单文件上传组件可以在其他页面使用
- **逻辑复用**：压缩功能可以提取为独立的hook
- **配置灵活**：通过props可以配置不同的行为

## 使用示例

### 基本使用
```typescript
import UploadDatasets from './UploadDatasets';

// 在路由中使用
<Route path="/datasets/upload" component={UploadDatasets} />
```

### 独立使用子组件
```typescript
import SingleFileUpload from './components/SingleFileUpload';
import { Form } from 'antd';

function MyComponent() {
  const [form] = Form.useForm();
  
  return (
    <div>
      <h1>上传文件</h1>
      <SingleFileUpload form={form} />
    </div>
  );
}
```

## 未来扩展

### 1. 功能扩展
- 添加新的上传方式（如URL导入）
- 支持更多文件格式
- 添加上传进度显示

### 2. 组件优化
- 提取共同逻辑为自定义hooks
- 添加更多的配置选项
- 优化性能和用户体验

### 3. 测试完善
- 添加单元测试
- 添加集成测试
- 添加端到端测试

## 总结

通过将原本复杂的单一组件按照功能进行解耦，我们获得了更好的代码组织、更高的可维护性和更强的可测试性。这种架构为未来的功能扩展和优化提供了良好的基础。
