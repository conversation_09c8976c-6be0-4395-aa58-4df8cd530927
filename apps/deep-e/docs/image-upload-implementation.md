# 图片上传功能实现文档

## 概述

在 `apps/deep-e/src/pages/PromptWords/page.tsx` 页面中成功添加了图片上传功能，支持在聊天对话中上传和显示图片。该功能包含本地存储、实时预览和图片放大查看等完整的用户体验。

## 实现的功能

### 1. 图片上传组件
- **支持格式**: PNG、JPG、JPEG 格式的图片文件
- **文件大小限制**: 最大 5MB
- **多图片上传**: 支持一次选择多张图片
- **实时预览**: 上传后立即显示本地图片预览，无需等待服务器处理
- **单独删除**: 每张图片都有独立的删除按钮
- **持续可用**: 上传按钮在有图片时仍然保持可见和可用

### 2. 本地存储和预览
- **本地 Blob URL**: 使用 `URL.createObjectURL()` 创建本地图片预览
- **即时显示**: 选择图片后立即显示，提升用户体验
- **内存管理**: 自动释放 blob URL，防止内存泄漏
- **优先显示**: 优先使用本地 URL，服务器 URL 作为备用

### 3. 图片预览功能
- **Ant Design Image 组件**: 使用 `Image` 组件替代普通 `<img>` 标签
- **点击放大**: 支持点击图片进行放大预览
- **缩略图显示**: 在上传区域显示 64x64px 的缩略图
- **聊天中预览**: 在聊天消息中显示 200x200px 的图片，支持放大查看

### 4. API 集成
- **上传接口**: `/file/uploadFileForDeepPrompt` (POST)
- **参数**:
  - `sessionId`: 当前会话ID
  - `images`: 图片文件数组
- **响应格式**:
  ```json
  {
    "code": 0,
    "data": {
      "list": ["70507078-e804-4c2b-9b95-be73267ffbb2_1751263767.png"]
    },
    "msg": "操作成功"
  }
  ```
- **服务函数**: 在 `api/index.ts` 中创建了 `uploadImagesForDeepPrompt` 函数

### 5. 会话管理
- **动态生成**: 调用 `/knowledge/createSession` API 动态生成会话ID
- **服务器端生成**: 从服务器获取唯一的会话标识符
- **降级处理**: API 调用失败时使用 `uuid` 库作为备用方案
- **会话重置**: 清除对话时调用 API 生成新的 sessionId
- **图片关联**: 图片与当前会话ID绑定

### 6. 聊天界面集成
- **图片显示**: 在聊天消息中渲染上传的图片
- **消息携带**: 发送消息时自动包含已上传的图片信息
- **响应式布局**: 图片以合适的尺寸显示在对话中
- **放大预览**: 聊天中的图片支持点击放大查看

## 代码结构

### 主要文件修改

#### 1. `apps/deep-e/src/pages/PromptWords/api/index.ts`
```typescript
// 创建新的会话ID
export const createSession = (): Promise<returnData<{ result: string }>> => {
  return request.post('/api/knowledge/createSession');
};

// 新增图片上传服务函数
export const uploadImagesForDeepPrompt = (sessionId: string, images: File[]): Promise<returnData<any[]>> => {
  const formData = new FormData();
  formData.append('sessionId', sessionId);

  images.forEach((file) => {
    formData.append('images', file);
  });

  return request.post('/api/file/uploadFileForDeepPrompt', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
```

#### 2. `apps/deep-e/src/pages/PromptWords/page.tsx`
主要添加的状态管理：
```typescript
const [sessionId, setSessionId] = useState(() => uuidv4());
const [images, setImages] = useState<string[]>([]);
const [uploading, setUploading] = useState(false);
const [uploadedImages, setUploadedImages] = useState<Array<{
  url: string;
  localUrl: string; // 本地 blob URL
  name: string;
  uid: string;
  fileName: string; // 服务器返回的文件名
  file: File; // 保存原始文件对象
}>>([]);
```

本地预览和上传处理逻辑：
```typescript
const uploadImages = useCallback(async (files: File[]) => {
  // 先创建本地预览图片
  const localImages = files.map((file, index) => ({
    url: '', // 服务器URL稍后填充
    localUrl: URL.createObjectURL(file), // 创建本地 blob URL
    name: file.name,
    uid: `local-${Date.now()}-${index}`,
    fileName: '', // 服务器文件名稍后填充
    file: file, // 保存原始文件对象
  }));

  // 立即显示本地预览
  setUploadedImages(prev => [...prev, ...localImages]);

  // 上传到服务器
  const res = await uploadImagesForDeepPrompt(sessionId, files);
  if (res.code === 0) {
    // 更新服务器信息
    const uploadedFileNames = res.data.list || [];
    setUploadedImages(prev => {
      const updatedImages = [...prev];
      localImages.forEach((localImg, index) => {
        const imgIndex = updatedImages.findIndex(img => img.uid === localImg.uid);
        if (imgIndex !== -1 && uploadedFileNames[index]) {
          updatedImages[imgIndex] = {
            ...updatedImages[imgIndex],
            url: `/file/image/${uploadedFileNames[index]}`,
            fileName: uploadedFileNames[index],
          };
        }
      });
      return updatedImages;
    });
  }
}, [sessionId]);
```

内存管理：
```typescript
// 删除图片时释放 blob URL
const removeImage = useCallback((uid: string) => {
  const imageToRemove = uploadedImages.find(img => img.uid === uid);
  if (imageToRemove?.localUrl) {
    URL.revokeObjectURL(imageToRemove.localUrl);
  }
  // ... 其他删除逻辑
}, [uploadedImages]);

// 组件卸载时清理所有 blob URL
useEffect(() => {
  return () => {
    uploadedImages.forEach(img => {
      if (img.localUrl) {
        URL.revokeObjectURL(img.localUrl);
      }
    });
  };
}, [uploadedImages]);
```

#### 3. `apps/deep-e/src/pages/PromptWords/components/ChartView.tsx`
扩展了 AgentMessage 类型以支持图片：
```typescript
type AgentUserMessage = {
  role: 'user';
  content: string;
  images?: string[];
  onComplete?: () => void;
};
```

聊天消息中的图片渲染：
```typescript
// 使用 Ant Design Image 组件进行渲染
<Image
  key={index}
  src={imageUrl}
  alt={`上传的图片 ${index + 1}`}
  width={200}
  height={200}
  className='object-cover rounded-lg border border-gray-200'
  style={{
    maxWidth: '200px',
    maxHeight: '200px',
    objectFit: 'cover'
  }}
  preview={{
    src: imageUrl,
  }}
/>
```

## 用户界面

### 图片上传区域
- 位置：在 Sender 组件上方
- 样式：使用 Ant Design Upload 组件
- 交互：点击按钮选择文件，支持多选

### 图片预览区域
- 显示：已上传图片的缩略图网格
- 尺寸：64x64px 的正方形缩略图
- 删除：每张图片右上角有删除按钮
- 预览：点击缩略图可以放大查看
- 即时显示：选择文件后立即显示本地预览

### 聊天消息中的图片
- 位置：在文本内容上方
- 尺寸：最大 200x200px，保持宽高比
- 样式：圆角边框，响应式布局
- 交互：点击图片可以放大预览
- 优化：使用 Ant Design Image 组件提供更好的用户体验

## 技术特点

### 1. 类型安全
- 使用 TypeScript 严格类型检查
- 扩展了消息类型以支持图片字段
- 完整的类型定义包含本地和服务器URL

### 2. 错误处理
- 文件格式验证
- 文件大小限制
- 上传失败提示
- 网络错误处理
- 上传失败时自动清理本地预览

### 3. 用户体验
- 即时本地预览，无需等待上传
- 上传进度指示
- 实时反馈
- 直观的删除操作
- 响应式设计
- 图片放大预览功能

### 4. 内存管理
- 自动创建和释放 blob URL
- 组件卸载时清理所有资源
- 删除图片时释放对应的内存
- 防止内存泄漏

### 5. 状态管理
- 图片状态与会话绑定
- 清除对话时自动清理图片和内存
- 状态同步更新
- 本地URL优先，服务器URL备用

## 使用方法

1. **上传图片**：
   - 点击"上传图片 (JPG/PNG)"按钮
   - 选择一张或多张图片文件
   - 等待上传完成

2. **预览图片**：
   - 上传成功后在上传区域查看缩略图
   - 可以继续上传更多图片

3. **删除图片**：
   - 鼠标悬停在图片上显示删除按钮
   - 点击删除按钮移除单张图片
   - 自动释放对应的本地 blob URL

4. **预览图片**：
   - 点击缩略图或聊天中的图片进行放大预览
   - 支持图片的详细查看
   - 使用 Ant Design Image 组件的预览功能

5. **发送消息**：
   - 输入文本消息
   - 已上传的图片会自动包含在消息中
   - 在聊天界面中查看图片和文本
   - 聊天中的图片也支持点击放大

6. **清除对话**：
   - 点击"清除对话"按钮
   - 自动清除所有图片和消息
   - 释放所有本地 blob URL
   - 生成新的会话ID

## API 响应处理

### 上传成功响应
```json
{
  "code": 0,
  "data": {
    "list": [
      "70507078-e804-4c2b-9b95-be73267ffbb2_1751263767.png"
    ]
  },
  "msg": "操作成功"
}
```

### 数据处理逻辑
1. **文件名提取**: 从 `res.data.list` 数组中获取上传后的文件名
2. **URL 构建**: 使用 `/file/image/${fileName}` 格式构建图片访问URL
3. **状态更新**:
   - `images` 数组存储文件名，用于API请求
   - `uploadedImages` 数组存储完整的图片信息，用于UI显示

## 注意事项

1. **文件限制**：
   - 仅支持 JPG、JPEG、PNG 格式
   - 单个文件最大 5MB
   - 无数量限制

2. **会话管理**：
   - 每次清除对话会生成新的 sessionId
   - 图片与特定会话绑定
   - 切换模型时会重置会话

3. **网络要求**：
   - 需要稳定的网络连接进行上传
   - 大文件上传可能需要较长时间

4. **图片访问**：
   - 图片URL格式: `/file/image/{fileName}`
   - 确保服务器端正确配置图片访问路径

## 后续优化建议

1. **性能优化**：
   - 图片压缩
   - 懒加载
   - 缓存机制

2. **功能扩展**：
   - 支持更多图片格式
   - 图片编辑功能
   - 批量操作

3. **用户体验**：
   - 拖拽上传
   - 上传进度条
   - 图片放大查看
