# 文件夹压缩上传功能实现

## 概述

本文档描述了在多模态数据集上传功能中实现文件夹自动压缩为zip文件的修改。

## 修改内容

### 1. 依赖添加

- 添加了 `jszip` 库用于在浏览器中创建zip文件
- 安装命令：`pnpm add jszip`

### 2. 核心功能修改

#### 文件位置
- `apps/deep-e/src/pages/Datasets/children/UploadDatasets/index.tsx` (主入口组件)
- `apps/deep-e/src/pages/Datasets/children/UploadDatasets/components/SingleFileUpload.tsx` (单文件上传组件)
- `apps/deep-e/src/pages/Datasets/children/UploadDatasets/components/MultiModalUpload.tsx` (多模态上传组件)
- `apps/deep-e/src/pages/Datasets/api/index.ts` (API函数)

#### 主要变更

1. **导入JSZip库**
   ```typescript
   import JSZip from 'jszip';
   ```

2. **新增API函数**
   ```typescript
   export const uploadFolderForDataSet = (
     params: FormData,
   ): Promise<returnData<UploadDatasetResponse>> => {
     return request.post('/api/file/uploadFolderForDataSet', params, {
       headers: {
         'Content-Type': 'multipart/form-data',
       },
     });
   };
   ```

3. **移除Uppy XHR上传插件**
   - 不再使用 `@uppy/xhr-upload` 插件
   - 移除了相关的上传事件监听器
   - Uppy现在仅用于文件夹选择和文件管理

4. **组件解耦重构**
   - 将原本单一的组件按照tab功能进行解耦
   - 主入口组件负责tab切换和表单管理
   - 单文件上传组件处理简单文件上传
   - 多模态上传组件处理复杂的文件夹压缩和上传

5. **实现文件夹压缩逻辑**
   - 在多模态上传组件的 `handleSubmit` 函数中实现
   - 遍历所有选择的文件
   - 使用FileReader读取文件内容
   - 将文件添加到JSZip实例中，保持原有的文件夹结构
   - 生成压缩的zip文件

4. **上传流程优化**
   - 显示压缩进度提示
   - 创建zip文件对象
   - 使用专门的 `uploadFolderForDataSet` API上传zip文件到 `/api/file/uploadFolderForDataSet`
   - 显示上传进度提示

### 3. 用户界面更新

1. **提示信息更新**
   - 添加了压缩提示：`📦 文件夹将自动压缩为 .zip 文件后上传`
   - 更新按钮文本：`压缩并上传数据集`

2. **进度反馈**
   - 压缩阶段：显示"正在压缩文件夹..."
   - 上传阶段：显示"正在上传数据集..."

### 4. 技术实现细节

#### 压缩配置
```typescript
const zipBlob = await zip.generateAsync({
  type: 'blob',
  compression: 'DEFLATE',
  compressionOptions: {
    level: 6
  }
});
```

#### 文件处理
- 保持原有的文件夹结构（使用relativePath）
- 支持所有文件类型
- 异步处理文件读取和压缩

#### 错误处理
- 压缩失败时显示错误信息
- 自动清理进度提示
- 保持原有的错误处理逻辑

## 功能特点

1. **保持原有功能**
   - 文件夹选择功能不变
   - JSONL文件检测和选择功能不变
   - 文件树显示功能不变
   - 单文件上传功能不变

2. **新增功能**
   - 自动文件夹压缩
   - 压缩进度显示
   - **压缩文件下载**：用户可以先压缩文件夹并下载预览
   - **智能上传**：支持使用已压缩的文件直接上传，避免重复压缩
   - 优化的用户体验

3. **性能优化**
   - 使用DEFLATE压缩算法
   - 压缩级别设置为6（平衡压缩率和速度）
   - 异步处理避免界面阻塞
   - 智能缓存压缩结果，避免重复压缩

## 使用流程

### 基本流程
1. 用户选择多模态数据集上传标签页
2. 点击"选择文件夹"按钮选择包含数据的文件夹
3. 系统自动检测JSONL文件并显示文件树
4. 用户选择要使用的JSONL文件

### 压缩和下载流程（新增）
5. 在"文件夹操作"区域，点击"压缩文件夹"按钮
6. 系统压缩文件夹为zip文件
7. 压缩完成后，显示"下载 [文件名].zip"按钮
8. 用户可以点击下载按钮预览压缩文件

### 上传流程
9. 点击"上传已压缩的数据集"或"压缩并上传数据集"按钮
   - 如果已压缩：直接使用缓存的zip文件上传
   - 如果未压缩：重新压缩后上传
10. 上传完成后跳转到数据集列表页面

## 新增功能详解

### 1. 压缩文件下载功能

#### 功能描述
- 用户可以在上传前先压缩文件夹并下载预览
- 支持本地保存压缩文件作为备份
- 提供压缩状态的可视化反馈

#### 实现细节
```typescript
// 下载压缩文件的核心逻辑
const handleDownloadZip = () => {
  const url = URL.createObjectURL(compressedZipBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = zipFileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
```

#### 用户界面
- "压缩文件夹"按钮：触发文件夹压缩
- "下载 [文件名].zip"按钮：下载压缩后的文件
- 压缩状态指示器：显示"✅ 文件夹已压缩完成"

### 2. 智能上传优化

#### 功能描述
- 检测是否已有压缩文件，避免重复压缩
- 动态更新按钮文本，提供清晰的操作指引
- 优化用户体验，减少等待时间

#### 实现逻辑
```typescript
// 智能判断是否需要重新压缩
if (compressedZipBlob && zipFileName) {
  // 使用已压缩的文件
  zipBlob = compressedZipBlob;
  fileName = zipFileName;
} else {
  // 重新压缩文件夹
  // ... 压缩逻辑
}
```

#### 按钮状态
- 未压缩：显示"压缩并上传数据集"
- 已压缩：显示"上传已压缩的数据集"

### 3. API集成优化

#### 新增API端点
- **端点**: `/api/file/uploadFolderForDataSet`
- **方法**: POST
- **用途**: 专门用于上传压缩后的文件夹zip文件

#### API使用示例
```typescript
// 创建FormData
const formData = new FormData();
formData.append('datasetName', values.datasetName);
formData.append('datasetDesc', values.datasetDesc);
formData.append('datasetType', 'multiModal');
formData.append('selectedJsonl', selectedJsonl);
formData.append('file', zipFile);

// 调用专门的文件夹上传API
const res = await uploadFolderForDataSet(formData);
```

#### API区分
- **单文件上传**: 使用 `uploadDataset` API → `/api/dataset/uploadDataset`
- **文件夹上传**: 使用 `uploadFolderForDataSet` API → `/api/file/uploadFolderForDataSet`

### 4. 状态管理优化

#### 状态变量
- `compressedZipBlob`: 存储压缩后的zip文件
- `zipFileName`: 存储zip文件名
- 在切换标签页和取消上传时自动清理状态

#### 内存管理
- 使用URL.revokeObjectURL()释放内存
- 在组件卸载时清理所有状态

## 兼容性

- 支持所有现代浏览器
- 保持与现有后端API的兼容性
- 不影响单文件上传功能
- 向后兼容，不影响现有用户工作流程
