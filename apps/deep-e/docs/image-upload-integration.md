# Image Upload Integration for Chat Conversations

## Overview

This document describes the integration of the `/file/uploadFileForDeepPrompt` API endpoint into the PromptWords chat conversation interface. The implementation provides seamless image upload functionality with session management integration.

## API Endpoint Details

- **URL**: `/file/uploadFileForDeepPrompt`
- **Method**: POST
- **Content-Type**: `multipart/form-data`
- **Authentication**: Requires `X-Token` header

### Request Parameters
- `sessionId`: The current chat session ID (string)
- `images`: Image files to upload (File objects)

### Response Format
```json
{
  "code": 0,
  "msg": "Success",
  "data": [
    {
      "id": "unique-image-id",
      "url": "https://example.com/path/to/image.jpg",
      "path": "/uploads/images/image.jpg",
      "name": "original-filename.jpg"
    }
  ]
}
```

## Implementation Features

### 1. File Upload UI Integration

#### Drag & Drop Support
- Users can drag JPG/PNG images directly into the chat interface
- Visual feedback during drag operations
- Automatic file type validation
- **No quantity limits** - users can upload unlimited images

#### File Selection
- Click-to-browse functionality for image selection
- File picker with JPG/PNG filter
- Multiple file selection support
- **Unlimited uploads** - no restrictions on number of images

#### Paste Support
- Paste images directly from clipboard
- Automatic file type validation for pasted content
- Immediate upload processing

#### Image Management
- **Individual image deletion** - remove specific images with close button
- **Bulk deletion** - clear all uploaded images at once
- **Visual image list** - see all uploaded images with names
- **Interactive UI** - hover effects and clear visual feedback

### 2. Session Management Integration

#### Automatic Session Association
```typescript
const uploadImages = useCallback(async (files: File[]) => {
  const formData = new FormData();
  formData.append('sessionId', sessionId); // Current session ID
  files.forEach((file) => {
    formData.append('images', file);
  });
  // ... upload logic
}, [sessionId, token]);
```

#### Session Cleanup
- Uploaded images are cleared when starting new sessions
- Session reset also clears image upload state
- Consistent state management across session changes

### 3. User Experience Features

#### Loading States
- Upload progress indicators
- Disabled state during upload operations
- Visual feedback for upload status

#### Error Handling
- File type validation with user feedback
- Network error handling with retry options
- Clear error messages for failed uploads

#### Success Feedback
- Success notifications with upload count
- Display of uploaded image names
- Visual confirmation of successful uploads
- **Interactive image preview area** - dedicated section showing all uploaded images
- **Individual delete buttons** - X button on each image for removal
- **Bulk delete option** - "Clear All" button for removing all images

### 4. Chat Integration

#### Message Enhancement
```typescript
// Messages include image information
let messageContent = message;
if (uploadedImages.length > 0) {
  const imageInfo = uploadedImages
    .map((img) => `[图片: ${img.name}]`)
    .join(' ');
  messageContent = `${message}\n\n${imageInfo}`;
}
```

#### Image Reference in Conversations
- Uploaded images are referenced in chat messages
- Image metadata is passed to the chat API
- Consistent image handling across conversation flow

## Code Implementation

### State Management
```typescript
// Upload-related state
const [uploadedImages, setUploadedImages] = useState<Array<{
  url: string;
  name: string;
  uid: string;
}>>([]);
const [uploading, setUploading] = useState(false);

// Image management functions
const removeImage = useCallback((uid: string) => {
  setUploadedImages(prev => prev.filter(img => img.uid !== uid));
}, []);

const clearAllImages = useCallback(() => {
  setUploadedImages([]);
}, []);
```

### Upload Function
```typescript
const uploadImages = useCallback(async (files: File[]) => {
  if (files.length === 0) return [];
  
  setUploading(true);
  try {
    const formData = new FormData();
    formData.append('sessionId', sessionId);
    files.forEach((file) => {
      formData.append('images', file);
    });

    const response = await fetch('/file/uploadFileForDeepPrompt', {
      method: 'POST',
      headers: { 'X-Token': token || '' },
      body: formData,
    });

    const result = await response.json();
    if (result.code !== 0) {
      throw new Error(result.msg || 'Upload failed');
    }

    // Process successful upload
    const newUploadedImages = result.data.map((img, index) => ({
      url: img.url || img.path || '',
      name: files[index]?.name || `image-${index}`,
      uid: img.id || `uploaded-${Date.now()}-${index}`,
    }));

    setUploadedImages(prev => [...prev, ...newUploadedImages]);
    return newUploadedImages;
  } catch (error) {
    // Handle upload errors
    console.error('Image upload error:', error);
    throw error;
  } finally {
    setUploading(false);
  }
}, [sessionId, token]);
```

### UI Integration

#### Upload Component
```typescript
<Attachments
  accept='.jpg,.png'
  beforeUpload={async (file) => {
    const validTypes = ['image/jpeg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      message.error('仅支持上传 JPG 或 PNG 格式的图片文件');
      return false;
    }
    await uploadImages([file]);
    return false; // Prevent default upload
  }}
  placeholder={(type) => ({
    title: uploading ? '正在上传...' : '上传文件',
    description: uploading
      ? '图片上传中，请稍候...'
      : '仅支持 JPG/PNG 图片，点击或拖拽文件到此区域上传',
  })}
  disabled={!hasValidModel() || uploading}
/>
```

#### Image Preview Area
```typescript
{/* 已上传图片预览区域 */}
{uploadedImages.length > 0 && (
  <div className='mb-3 p-3 bg-white rounded-lg border border-gray-200'>
    <div className='flex items-center justify-between mb-2'>
      <span className='text-sm text-gray-600'>
        已上传图片 ({uploadedImages.length} 张)
      </span>
      <Button
        type='text'
        size='small'
        danger
        icon={<DeleteOutlined />}
        onClick={clearAllImages}
        className='text-xs'>
        清除全部
      </Button>
    </div>
    <div className='flex flex-wrap gap-2'>
      {uploadedImages.map((img) => (
        <div
          key={img.uid}
          className='flex items-center gap-2 bg-blue-50 border border-blue-200 rounded px-3 py-2 text-sm'>
          <span className='text-blue-700 max-w-32 truncate' title={img.name}>
            {img.name}
          </span>
          <CloseOutlined
            className='text-blue-500 hover:text-red-500 cursor-pointer text-xs'
            onClick={() => removeImage(img.uid)}
          />
        </div>
      ))}
    </div>
  </div>
)}
```

## Security Considerations

### Authentication
- All upload requests include authentication tokens
- Session-based access control
- Secure file handling on the server side

### File Validation
- Client-side file type validation
- Server-side security checks (assumed)
- File size limitations (configurable)

### Session Isolation
- Images are associated with specific sessions
- No cross-session image access
- Automatic cleanup on session reset

## Error Handling

### Client-Side Validation
- File type checking (JPG/PNG only)
- File size validation (if configured)
- Network connectivity checks

### Server-Side Error Handling
- HTTP status code checking
- API response validation
- Graceful error recovery

### User Feedback
- Clear error messages for different failure scenarios
- Retry mechanisms for transient failures
- Progress indicators for long uploads

## Performance Considerations

### Upload Optimization
- Immediate upload on file selection
- Asynchronous upload processing
- Progress tracking for large files

### Memory Management
- Efficient file handling
- Cleanup of temporary objects
- Optimized state updates

### Network Efficiency
- FormData for efficient file transfer
- Proper HTTP headers for file uploads
- Connection reuse where possible

## Testing Recommendations

### Unit Tests
- Upload function testing with mocked API
- State management validation
- Error handling verification

### Integration Tests
- End-to-end upload workflow
- Session integration testing
- UI interaction testing

### Manual Testing
- Different file types and sizes
- Network failure scenarios
- Multiple upload scenarios

## Recent Updates

### ✅ Completed Features
1. **✅ Unlimited Image Uploads**: Removed quantity restrictions
2. **✅ Individual Image Deletion**: Added close button for each image
3. **✅ Bulk Image Deletion**: "Clear All" button for removing all images
4. **✅ Enhanced UI**: Dedicated image preview area with better visual design
5. **✅ Interactive Management**: Hover effects and clear visual feedback

## Future Enhancements

1. **Image Preview**: Display thumbnail previews of uploaded images
2. **Progress Tracking**: Real-time upload progress bars
3. **Batch Operations**: Multiple image upload with progress tracking
4. **Image Compression**: Client-side image optimization
5. **Drag & Drop Enhancements**: Better visual feedback and multiple drop zones
6. **Image Reordering**: Drag and drop to reorder uploaded images
