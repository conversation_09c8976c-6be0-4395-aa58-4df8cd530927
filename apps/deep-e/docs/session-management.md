# Chat Session Management Implementation

## Overview

This document describes the session management implementation for chat conversations in the PromptWords component. The implementation ensures each conversation maintains a unique identifier for proper backend tracking and message association.

## Features Implemented

### 1. Session ID Generation
- **UUID Generation**: Uses the `uuid` library (v4) to generate RFC-compliant UUIDs
- **Automatic Initialization**: Each new conversation starts with a unique sessionId
- **Library Used**: `uuid` package (already installed in the project)

### 2. Session ID Usage
- **API Integration**: The sessionId is included in all chat-related API calls
- **Request Payload**: Added to the request body sent to `/api/knowledge/queryByKnowledgeBase`
- **Consistent Threading**: Ensures proper message threading and conversation context

### 3. Session ID Storage
- **Component State**: Stored in the main component's state using React's `useState`
- **Persistence**: Maintained throughout the conversation lifecycle
- **Reactive Updates**: Automatically triggers re-renders when sessionId changes

### 4. Session Reset Behavior
Session ID is automatically reset (new UUID generated) in the following scenarios:

#### Automatic Reset:
- **Model Selection Change**: When switching between different models
- **Clear Conversation**: When using the "Clear Chat" button

#### Manual Reset:
- **New Session Button**: Dedicated "New Session" button for explicit session creation

## Implementation Details

### Code Changes Made

#### 1. Import UUID Library
```typescript
import { v4 as uuidv4 } from 'uuid';
```

#### 2. Session State Management
```typescript
const [sessionId, setSessionId] = useState(() => uuidv4());
```

#### 3. API Integration
```typescript
const requestChat = useCallback(
  (modelState: ChatViewProps['modelState'], messages: any) => {
    const abortController = new AbortController();
    return {
      request: fetch('/api/knowledge/queryByKnowledgeBase', {
        method: 'POST',
        headers: {
          'X-Token': token,
        } as any,
        body: JSON.stringify({
          ...modelState,
          isStream: true,
          messages: messages,
          sessionId: sessionId, // Include sessionId in request
        }),
        signal: abortController.signal,
      }),
      abort: abortController,
    };
  },
  [token, sessionId], // sessionId added to dependencies
);
```

#### 4. Session Reset Functions
```typescript
const clearHistory = () => {
  // Generate new session ID when clearing conversation
  setSessionId(uuidv4());
  clearRefs.current.forEach((clear) => {
    if (clear) clear();
  });
};
```

#### 5. UI Components Added
- **New Session Button**: Manual session reset capability
- **Session ID Display**: Debug information showing current session ID

### UI Components

#### New Session Button
```typescript
<Button
  type='text'
  className='hover:bg-blue-50'
  onClick={() => setSessionId(uuidv4())}
  disabled={!hasValidModel()}>
  {t('新会话')}
</Button>
```

#### Session ID Display (Debug)
```typescript
<div className='mb-2'>
  <span className='text-gray-400'>Session ID: </span>
  <span className='font-mono text-gray-600'>{sessionId}</span>
</div>
```

## Benefits

1. **Conversation Tracking**: Each conversation has a unique identifier
2. **Backend Integration**: Enables proper message threading on the server side
3. **User Control**: Users can manually start new sessions when needed
4. **Automatic Management**: Sessions reset automatically when context changes
5. **Debug Visibility**: Session ID is visible for debugging purposes

## Usage

### For Users
- **Automatic**: Session management works automatically in the background
- **Manual Control**: Use the "New Session" button to start fresh conversations
- **Clear Chat**: Clears messages and starts a new session automatically

### For Developers
- **API Calls**: All chat requests now include a `sessionId` parameter
- **Session Tracking**: Use the sessionId for conversation threading and analytics
- **Debug Information**: Session ID is displayed in the UI footer for debugging

## Image Upload Integration

### Overview
The chat interface now supports image uploads through the `/file/uploadFileForDeepPrompt` API endpoint, seamlessly integrated with the existing session management.

### Features
- **Drag & Drop Upload**: Users can drag images directly into the chat interface
- **File Selection**: Click to browse and select JPG/PNG images
- **Paste Support**: Paste images directly from clipboard
- **Session Integration**: All uploads are associated with the current sessionId
- **Real-time Feedback**: Loading states and success/error messages
- **Image Display**: Shows uploaded image count and names in the UI

### API Integration
```typescript
// Upload function
const uploadImages = useCallback(async (files: File[]) => {
  const formData = new FormData();
  formData.append('sessionId', sessionId);
  files.forEach((file) => {
    formData.append('images', file);
  });

  const response = await fetch('/file/uploadFileForDeepPrompt', {
    method: 'POST',
    headers: { 'X-Token': token || '' },
    body: formData,
  });
}, [sessionId, token]);
```

### User Experience
- **Automatic Upload**: Images are uploaded immediately when selected
- **Visual Feedback**: Upload progress and status indicators
- **Session Cleanup**: Uploaded images are cleared when starting new sessions
- **Message Integration**: Uploaded images are referenced in chat messages

## Future Enhancements

1. **Session Persistence**: Store sessions in localStorage for recovery after page refresh
2. **Session History**: Maintain a list of previous sessions for user navigation
3. **Session Metadata**: Add timestamps, message counts, and other session information
4. **Session Export**: Allow users to export conversation sessions
5. **Session Sharing**: Enable sharing of specific conversation sessions
6. **Image Preview**: Display thumbnail previews of uploaded images
7. **Image Management**: Allow users to remove individual uploaded images
8. **Batch Upload**: Support multiple image uploads in a single operation

## Technical Notes

- **UUID Format**: Uses UUID v4 (random) for maximum uniqueness
- **Performance**: Minimal performance impact as UUIDs are generated only when needed
- **Memory**: Session IDs are lightweight strings with minimal memory footprint
- **Compatibility**: Works with existing chat infrastructure without breaking changes
- **File Types**: Supports JPG and PNG image formats only
- **Upload Security**: Uses authentication tokens for secure uploads
