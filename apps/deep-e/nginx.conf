events {
    worker_connections  1024;
}

http {
    include       /usr/local/openresty/nginx/conf/mime.types;
   # 全局设置
    client_body_timeout 120;
    client_header_timeout 120;
    keepalive_timeout 120;
    send_timeout 120;
    client_max_body_size 100M;  # 设置上传文件大小限制为100M

     types {
        text/javascript js;
        application/javascript js;
        # 添加其他 MIME 类型...
     }
    server {
        listen 80;
        server_name  localhost;

        location / {
            client_max_body_size 100M;  # 设置上传文件大小限制为100M
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location ^~ /api {
            client_max_body_size 100M;  # 设置上传文件大小限制为100M
            rewrite ^/api/(.*)$ /$1 break;
            # proxy_pass http://*************:55051;
             proxy_pass http://${UI_SCP_GO_AI_HOST}:${UI_SCP_GO_AI_PORT};
            #proxy_pass *************:50051;
            proxy_read_timeout 120;
            proxy_connect_timeout 120;
            proxy_send_timeout 120;

            # 添加跨域头部
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }

        location ^~ /jump/scpJumpToInt {
            client_max_body_size 100M;  # 设置上传文件大小限制为100M
            # proxy_pass http://*************:55051;
            proxy_pass http://${UI_SCP_GO_AI_HOST}:${UI_SCP_GO_AI_PORT};
            #proxy_pass *************:50051;
            proxy_read_timeout 120;
            proxy_connect_timeout 120;
            proxy_send_timeout 120;
            # 添加跨域头部
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }

        location ^~ /ai {
            client_max_body_size 100M;  # 设置上传文件大小限制为100M
            rewrite ^/ai/(.*)$ /$1 break;
            proxy_pass http://**************:11434;
            
            # 添加或修改这些头部
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $http_host;
            
            # 开启这些超时设置
            proxy_read_timeout 120;
            proxy_connect_timeout 120;
            proxy_send_timeout 120;
            
            # 添加 OPTIONS 预检请求处理
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
            
            # 常规请求的跨域设置
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Expose-Headers' '*' always;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}