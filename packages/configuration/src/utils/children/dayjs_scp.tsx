import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import configurations from '../../configurations';
import updateLocale from 'dayjs/plugin/updateLocale';
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(updateLocale);

const configuration = configurations.scp;

dayjs.tz.setDefault(configuration.TimeZone || undefined);

function dayjs_scp(date?: any): any {
    return dayjs(date).tz(configuration.TimeZone || undefined);
}

namespace dayjs_scp {
    export const updateLocale = (locale: any, customConfig: any) => dayjs.updateLocale(locale, customConfig);
    export const locale = (locale: any) => dayjs.locale(locale);
    export const extend = (plugin: any, options?: any) => dayjs.extend(plugin, options);
    export const unix = (unix: number) => dayjs.tz(dayjs(unix * 1000), configuration.TimeZone || undefined);
}

export default dayjs_scp;