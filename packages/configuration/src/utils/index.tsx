import configurations from '../configurations';
import dayjs_scp from './children/dayjs_scp';
import dayjs_int from './children/dayjs_int';
import dayjs from 'dayjs';

const getDayjs = (app: any) => {
    switch (app) {
        case 'scp':
            return dayjs_scp;
        case 'int':
            return dayjs_int;
        default:
            return dayjs;
    };
};

const getTimeZone = (app: any) => {
    let TimeZone = null;
    switch (app) {
        case 'scp':
            TimeZone = configurations.scp.TimeZone;
            break;
        case 'int':
            TimeZone = configurations.int.TimeZone;
            break;
    };
    return TimeZone ? TimeZone : dayjs.tz.guess();
};

export default {
    getDayjs,
    getTimeZone,
}