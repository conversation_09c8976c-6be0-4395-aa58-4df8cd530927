{
  "extends": "@repo/typescript-config/react-library.json",
  "compilerOptions": {
    "allowImportingTsExtensions": true,
    "emitDeclarationOnly": true,
    "allowJs": true,
    "baseUrl": ".",
    "outDir": "dist",
    "declaration": true,
    "declarationDir": "dist/types",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "moduleResolution": "node",
    "paths": {
      "@/*": ["./src/*"]
    },
    "resolveJsonModule": true,
    "rootDir": "src",
    "skipLibCheck": true,
    "strict": true
  },
  "exclude": ["**/node_modules"],
  "include": ["src"],
}
