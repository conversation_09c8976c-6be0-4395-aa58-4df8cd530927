{"name": "@repo/configuration", "version": "0.1.0", "private": "true", "types": "./dist/types/index.d.ts", "type": "module", "main": "./dist/lib/index.js", "module": "./dist/es/index.js", "scripts": {"dev": "tsc --watch", "build": "modern build"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/es/index.js", "require": "./dist/lib/index.js"}}, "devDependencies": {"@modern-js/eslint-config": "2.56.2", "@modern-js/module-tools": "2.56.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.16.0", "typescript": "latest"}, "dependencies": {"dayjs": "^1.11.13", "esbuild": "0.19.2"}}