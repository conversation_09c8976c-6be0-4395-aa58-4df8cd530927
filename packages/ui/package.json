{"name": "@repo/ui", "version": "0.1.0", "types": "./dist/types/index.d.ts", "type": "module", "main": "./dist/lib/index.js", "module": "./dist/es/index.js", "exports": {".": {"import": "./dist/es/index.js", "require": "./dist/lib/index.js", "types": "./dist/types/index.d.ts"}, "./dev": {"import": "./src/index.ts", "require": "./src/index.ts", "types": "./src/index.d.ts"}}, "scripts": {"build-storybook": "storybook build", "storybook": "storybook dev -p 6006", "dev": "modern dev", "build": "modern build", "build:watch": "modern build -w", "reset": "rimraf ./**/node_modules", "lint": "eslint . --max-warnings 0 --fix", "change": "modern change", "bump": "modern bump", "pre": "modern pre", "change-status": "modern change-status", "gen-release-note": "modern gen-release-note", "release": "modern release", "new": "modern new", "upgrade": "modern upgrade", "translate": "voerkai18n extract && voerkai18n translate --appkey $KEY --appid $ID", "translate:compile": "voerkai18n compile", "translate:auto": "KEY=0cIMpshCtxohtlSJKlSM ID=20230302001582986 pnpm translate && pnpm translate:compile"}, "lint-staged": {"*.{js,jsx,ts,tsx,mjs,cjs}": ["node --max_old_space_size=8192 ./node_modules/eslint/bin/eslint.js --fix --color --cache --quiet"]}, "dependencies": {"@ant-design/icons": "^5.3.6", "@voerkai18n/formatters": "^3.0.10", "@voerkai18n/react": "^3.0.10", "@voerkai18n/runtime": "^3.0.10", "ahooks": "^3.8.4", "antd": "^5.22.3", "dayjs": "^1.11.13", "esbuild": "0.19.2", "framer-motion": "^11.13.1", "lodash": "^4.17.21", "react": "^18.2.0", "styled-components": "^6.1.12", "zustand": "^5.0.3"}, "devDependencies": {"@modern-js/eslint-config": "2.56.2", "@modern-js/module-tools": "2.56.2", "@modern-js/storybook": "2.56.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.17.13", "@types/node": "~16.11.7", "@types/react": "^18.2.61", "@voerkai18n/plugins": "^3.0.10", "autoprefixer": "^10.4.19", "cssnano": "^7.0.4", "eslint": "^9.16.0", "husky": "~8.0.1", "lint-staged": "~13.1.0", "postcss-assets": "^6.0.0", "postcss-cssnext": "^3.1.1", "prettier": "~2.8.1", "react-dom": "^18", "rimraf": "~3.0.2", "stylelint": "^16.8.1", "typescript": "~5.0.4"}, "sideEffects": [], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}