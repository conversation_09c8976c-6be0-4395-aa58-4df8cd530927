export default  {
    "95": "Execution Method",
    "96": "Scheduled Execution",
    "97": "Immediate Execution",
    "98": "User",
    "99": "Template Type",
    "100": "Task Execution Status",
    "103": "Remarks",
    "111": "Original Template",
    "112": "System Template",
    "113": "Shared Template",
    "114": "Custom Template",
    "118": "Run Task",
    "122": "Scheduled Parameters",
    "124": "Task Details",
    "125": "Data Details",
    "126": "Scheduled Task Template",
    "127": "Please Select a Scheduled Task Template",
    "128": "Plan Details",
    "129": "Copy Steps",
    "130": "Are you sure to delete this task?",
    "131": "Level",
    "132": "Message",
    "133": "Time",
    "134": "Operation",
    "135": "This log has no details yet",
    "136": "Details",
    "137": "Log",
    "138": "Re-execute",
    "139": "Skip",
    "140": "Task chain",
    "141": "Execution log",
    "142": "Log details",
    "155": "Please enter the template name",
    "160": "Add a row",
    "161": "Filter condition",
    "162": "Type",
    "163": "Please enter filter condition",
    "164": "Please enter type",
    "165": "Please enter value",
    "166": "Value",
    "167": "And",
    "168": "Or",
    "169": "Add filter condition",
    "170": "Please enter",
    "208": "Return {}",
    "210": "Task template",
    "211": "Please select a task template",
    "214": "Start time {}",
    "215": "{name} uploaded successfully",
    "216": "{name} upload failed",
    "217": "{name} is not a yml/yaml file",
    "218": "End time {}",
    "220": "Return",
    "223": "Upload {upload}",
    "224": "Task Details{name}",
    "227": "Select copy steps",
    "232": "Submission successful",
    "233": "Error:",
    "236": "Task Name{}",
    "237": "Planned Version{}",
    "238": "Task Status{}",
    "239": "Completed{}",
    "240": "In Progress{}",
    "241": "Task Interrupted{}",
    "242": "Pending Execution{}",
    "243": "Timeout{}",
    "244": "Time Range{}",
    "245": "Reset{}",
    "246": "Query{}",
    "247": "Run Task{}",
    "248": "Previous Page{}",
    "249": "Next Page{}",
    "250": "Jump to Page {}",
    "251": "Page{}",
    "252": "Jump{}"
}