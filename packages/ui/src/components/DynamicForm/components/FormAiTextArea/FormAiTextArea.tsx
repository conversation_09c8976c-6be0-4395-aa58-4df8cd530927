import React, { useRef, useState } from 'react';
import { Input, Form, Button } from 'antd';
import { StyledSelect } from '../FormSelect';
import { FormField } from '../../types';
import ShouldVisible from '../ShouldVisible';
import ShouldFetch from '../ShouldFetch';
interface FormAiTextAreaProps {
  field: FormField;
  formName: string;
}

const { TextArea } = Input;

const FormAiTextArea: React.FC<FormAiTextAreaProps> = ({ field, formName }) => {
  const dependencyFields = field.interface?.params || [];
  const options = field.options?.aiTextArea?.options || [];
  const formItem = (
    <Form.Item
      noStyle
      shouldUpdate={(prev: any, next: any) => {
        let flag = false;
        dependencyFields.forEach((field) => {
          const prevValue = prev[formName]?.[field.field];
          const nextValue = next[formName]?.[field.field];
          if (prevValue !== nextValue) {
            flag = true;
          }
        });
        return flag;
      }}>
      {({ getFieldValue }) => {
        const dependencyValues = dependencyFields.map((field) =>
          getFieldValue([formName, field.field]),
        );
        return (
          <Form.Item
            label={field.label}
            name={[formName, field.field]}
            initialValue={field.defaultValue}
            rules={[
              { required: field.required, message: `请输入${field.label}` },
            ]}>
            {field.interface ? (
              <ShouldFetch
                dependencies={dependencyValues}
                field={field}
                formName={formName}>
                <CustomTextArea options={options} field={field} />
              </ShouldFetch>
            ) : (
              <CustomTextArea options={options} field={field} />
            )}
          </Form.Item>
        );
      }}
    </Form.Item>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

type CustomTextAreaType = {
  onChange?: any;
  value?: string;
  options: { value: any; label: string }[];
  field: FormField;
  [key: string]: any;
};

const CustomTextArea: React.FC<CustomTextAreaType> = ({
  value,
  onChange,
  field,
  options,
}) => {
  const [selectedText, setSelectedText] = useState<string>('AI文本');
  const textAreaRef = useRef<any>(null);

  const handleInsertText = () => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = value || ''; // 确保value有值
    const newValue = `${currentValue.slice(0, start)}${selectedText}${currentValue.slice(end)}`;

    onChange?.(newValue);

    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + selectedText.length,
        start + selectedText.length,
      );
    }, 0);
  };

  return (
    <div className='flex flex-col'>
      <div className='flex gap-2 mb-2'>
        <div className='flex-1'>
          <StyledSelect
            field={field}
            options={options}
            onChange={(value) => {
              setSelectedText(`{{${value}}}`);
            }}
          />
        </div>
        <Button onClick={handleInsertText} style={{ height: '30px' }}>
          添加
        </Button>
      </div>
      <TextArea
        ref={textAreaRef}
        value={value}
        onChange={(value) => {
          onChange?.(value.target.value);
        }}
      />
    </div>
  );
};

export default FormAiTextArea;
