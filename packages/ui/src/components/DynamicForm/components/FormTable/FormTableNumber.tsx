import { Form, InputNumber } from 'antd';
import { t } from '@/languages';
interface FormTableInputProps {
  prefix: string;
  name: string;
  max?: number;
  min?: number;
  defaultValue?: string;
  disabled?: boolean;
  required?: boolean;
}

export default function FormTableInput({
  prefix,
  disabled,
  name,
  max,
  min,
  defaultValue,
  required = false,
}: FormTableInputProps) {
  return (
    <Form.Item
      name={[prefix, name]}
      initialValue={defaultValue}
      rules={[{ required, message: `${t('请输入')}${name}` }]}>
      <InputNumber disabled={disabled} min={min} max={max} />
    </Form.Item>
  );
}
