interface BaseFormTableColumnsDataProps {
  name: string;
  key: string;
}

interface InputFormTableColumnsDataProps extends BaseFormTableColumnsDataProps {
  valueType: 'input';
}

interface FilterFormTableColumnsDataProps
  extends BaseFormTableColumnsDataProps {
  valueType: 'filter';
}

interface TextFormTableColumnsDataProps extends BaseFormTableColumnsDataProps {
  valueType: 'text';
}

interface NumberFormTableColumnsDataProps
  extends BaseFormTableColumnsDataProps {
  valueType: 'number';
  min: number; // 当 valueType 为 'number' 时，必须有 min 字段
  max: number; // 当 valueType 为 'number' 时，必须有 max 字段
}

// 联合类型
export type FormTableColumnsDataProps =
  | InputFormTableColumnsDataProps
  | FilterFormTableColumnsDataProps
  | TextFormTableColumnsDataProps
  | NumberFormTableColumnsDataProps;
