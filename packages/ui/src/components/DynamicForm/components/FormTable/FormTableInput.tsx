import { Form, Input } from 'antd';

interface FormTableInputProps {
  prefix: string;
  name: string;
  defaultValue?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function FormTableInput({
  prefix,
  name,
  defaultValue,
  required = false,
  disabled = false,
}: FormTableInputProps) {
  return (
    <Form.Item
      name={[prefix, name]}
      initialValue={defaultValue}
      rules={[{ required, message: `请输入${name}` }]}>
      <Input disabled={disabled} />
    </Form.Item>
  );
}
