import { Button, Input, Form, Modal, Space, Select, App } from 'antd';
import { FormContext } from '../../Context';
import React, { useContext, useEffect, useState } from 'react';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import type { ModalProps } from 'antd';
import _ from 'lodash';
import { t } from '@/languages';
interface FromTableFilterProps {
  prefix: string;
  initValue: any;
  filterSchemeId: number;
  name: string;
  filterStr: string;
  filiterFieldOptions: Array<{
    id: number;
    label: string;
    value: string;
    type: string;
  }>;
  operatorOptions: Map<string, any[]>;
  required?: boolean;
}

export default function FormTableFilter({
  filterSchemeId,
  required = false,
  name,
  filiterFieldOptions,
  operatorOptions,
  prefix,
  filterStr,
}: FromTableFilterProps) {
  return (
    <FilterButton
      filterStr={filterStr}
      filterSchemeId={filterSchemeId}
      required={required}
      listName={[prefix, name]}
      filiterFieldOptions={filiterFieldOptions}
      operatorOptions={operatorOptions}
    />
  );
}

function FilterButton({
  listName,
  filterStr,
  filterSchemeId,
  filiterFieldOptions,
  operatorOptions,
}: {
  required?: boolean;
  filterSchemeId: number;
  listName: string[];
  filterStr: string;
  filiterFieldOptions: Array<{
    id: number;
    label: string;
    value: string;
    type: string;
  }>;
  operatorOptions: Map<string, any[]>;
}) {
  const { message } = App.useApp();
  const token = useContext(FormContext).token || '';
  const form = Form.useFormInstance();
  const [initValue, setInitValue] = useState([]);
  const [open, setOpen] = useState(false);
  const [filterString, setFilterString] = useState(filterStr);

  const modalProps: ModalProps = {
    open,
    onCancel: () => setOpen(false),
    onOk: () => {
      const values = form.getFieldValue([...listName]);
      try {
        const str = values.map((item: any) => {
          const field = filiterFieldOptions.find(
            (field) => field.id === item.conditionId,
          )?.label;
          const operator = operatorOptions
            .get(
              filiterFieldOptions.find((field) => field.id === item.conditionId)
                ?.type as string,
            )
            ?.find((oper) => oper.value === item.preferOperator)?.label;
          const connector = item.connector === 'and' ? '并且' : '或者';
          return `${item.leftType} ${field} ${operator} ${item.preferValue} ${item.rightType} ${connector}`;
        });
        setFilterString(str.join(' '));
        setOpen(false);
      } catch (e) {
        message.error('请补充过滤条件');
        console.log(e);
      }
    },
    forceRender: true,
    width: 1000,
    className: 'flex justify-center items-center',
  };

  const getListInitValue = async () => {
    const res = await fetch(
      `/api/filter/buildQueryItems?schemeId=${filterSchemeId}`,
      {
        method: 'get',
        headers: {
          'X-Token': token,
          'accept-language': 'zh',
        },
      },
    ).then((res) => res.json());
    if (!res.code) {
      const list = _.get(res, 'data.list', []) || [];
      form.setFieldValue([...listName], list);
      setInitValue(list);
    }
  };

  useEffect(() => {
    getListInitValue();
  }, []);

  // useEffect(() => {
  //   const values = form.getFieldValue([...listName]);
  //   console.log(values);
  //   form
  //     .validateFields([...listName], { validateOnly: false, recursive: true })
  //     .then(() => {
  //       setButtonErrorStatus(false);
  //     })
  //     .catch((error) => {
  //       if (
  //         error.errorFields.filter((item: any) => item !== undefined).length > 0
  //       ) {
  //         console.log(error.errorFields);
  //         setButtonErrorStatus(true);
  //       } else {
  //         setButtonErrorStatus(false);
  //       }
  //     });
  // }, [values, form]);

  return (
    <>
      <Button type='link' onClick={() => setOpen(true)}>
        <span
          style={{
            maxWidth: '200px',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          }}>
          {filterString === '' || filterString === undefined
            ? t('过滤条件')
            : filterString}
        </span>
      </Button>
      <Modal {...modalProps}>
        <div className='w-[800px] h-[200px] overflow-auto p-2'>
          <Form.List name={[...listName]} initialValue={initValue || []}>
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space
                    key={key}
                    style={{ display: 'flex', marginBottom: 8 }}
                    align='baseline'>
                    <Form.Item {...restField} name={[name, 'leftType']}>
                      <Select
                        placeholder={t('类型')}
                        style={{ width: 60 }}
                        allowClear
                        options={[
                          {
                            label: '(',
                            value: '(',
                          },
                          {
                            label: '((',
                            value: '((',
                          },
                          {
                            label: '(((',
                            value: '(((',
                          },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'conditionField']}
                      rules={[
                        { required: true, message: t('请输入过滤条件') },
                      ]}>
                      <Select
                        placeholder={t('过滤条件')}
                        options={filiterFieldOptions}
                        onChange={(_, option) => {
                          let selectedId: number;
                          if (Array.isArray(option)) {
                            selectedId = option[0]?.id;
                          } else {
                            selectedId = option?.id || -1;
                          }

                          form.setFieldValue(
                            [...listName, name, 'conditionId'],
                            selectedId,
                          );
                        }}
                      />
                    </Form.Item>
                    <Form.Item name={[name, 'conditionId']} hidden>
                      <Input />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prev, next) => {
                        const prevId = _.get(prev, [
                          ...listName,
                          name,
                          'conditionId',
                        ]);
                        const nextId = _.get(next, [
                          ...listName,
                          name,
                          'conditionId',
                        ]);
                        return prevId !== nextId;
                      }}>
                      {({ getFieldValue }) => {
                        const conditionId = getFieldValue([
                          ...listName,
                          name,
                          'conditionId',
                        ]);
                        const type = filiterFieldOptions.find(
                          (item) => item.id === conditionId,
                        )?.type;
                        const _operOptions: any = operatorOptions.get(
                          type as string,
                        );
                        return (
                          <Form.Item
                            {...restField}
                            name={[name, 'preferOperator']}
                            rules={[
                              { required: true, message: t('请输入类型') },
                            ]}>
                            <Select
                              placeholder={t('类型')}
                              style={{ width: 120 }}
                              options={_operOptions}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'preferValue']}
                      rules={[{ required: true, message: t('请输入值') }]}>
                      <Input placeholder={t('值')} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'rightType']}>
                      <Select
                        placeholder={t('类型')}
                        allowClear
                        style={{ width: 60 }}
                        options={[
                          {
                            label: ')',
                            value: ')',
                          },
                          {
                            label: '))',
                            value: '))',
                          },
                          {
                            label: ')))',
                            value: ')))',
                          },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'connector']}>
                      <Select
                        allowClear
                        placeholder={t('类型')}
                        style={{ width: 120 }}
                        options={[
                          {
                            label: t('并且'),
                            value: 'and',
                          },
                          {
                            label: t('或者'),
                            value: 'or',
                          },
                        ]}
                      />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type='dashed'
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}>
                    {t('添加过滤条件')}
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </div>
      </Modal>
    </>
  );
}
