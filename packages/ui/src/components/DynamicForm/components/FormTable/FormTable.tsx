import { Table, <PERSON><PERSON>, Config<PERSON><PERSON>ider, Space, Form } from 'antd';
import {
  FormTableFilter,
  FormTableInput,
  FormText,
  FormTableNumber,
  type FormTableColumnsDataProps,
} from './';
import { useState, useEffect, useContext } from 'react';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { FormContext } from '../../Context';
import { FormField } from '../../types';
import _ from 'lodash';
import { t } from '@/languages';
interface FormTableProps {
  field: FormField;
  formName: string;
}

export default function FormTable({ field, formName }: FormTableProps) {
  const { required } = field;
  const token = useContext(FormContext).token || '';
  const form = Form.useFormInstance();
  let _columns: FormTableColumnsDataProps[] = [];
  let _dataSource: any[] = [];
  if (field?.options?.table) {
    const { columns } = field?.options?.table || {};
    _columns = columns;
    _dataSource =
      field.defaultValue?.map((item: any) => ({
        ...item,
        id: item.proirity,
      })) || [];
  }

  const [filterFieldOptions, setFilterFieldOptions] = useState<
    Array<{
      label: string;
      value: string;
      type: string;
      id: number;
    }>
  >([]);
  const [operatorOptions, setOperatorOptions] = useState<Map<string, any[]>>(
    new Map(),
  );
  const [dataSource, setDataSource] = useState(_dataSource);

  const handleAddRow = () => {
    const newRow = {
      id: Date.now(),
    };
    const _dataSource = !dataSource ? [newRow] : [...dataSource, newRow];
    setDataSource(_dataSource);
  };

  const getFieldOptionsFetch = async () => {
    try {
      const res = await fetch(
        `/api/filter/getAllFilterConditionsByMenu?menuTag=${formName}`,
        {
          method: 'GET',
          headers: {
            'X-Token': token,
            'accept-language': 'zh',
          },
        },
      ).then((res) => res.json());
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];
        setFilterFieldOptions(
          list.map((item: any) => {
            return {
              label: item.conditionContent.label,
              value: item.conditionContent.field,
              type: item.conditionContent.type,
              id: item.id,
            };
          }),
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  //获取操作符
  const getOperatorOptionsFetch = async () => {
    try {
      const filterTypeSelect = new Map();
      const res = await fetch(`/api/filter/getFilterOperatorList`, {
        method: 'GET',
        headers: {
          'X-Token': token,
          'accept-language': 'zh',
        },
      }).then((res) => res.json());
      const list = _.get(res, 'data.list');
      for (const key in list) {
        list[key].forEach(
          (item: { opt: string; label: string; value: string }) => {
            item.value = item.opt;
          },
        );
        filterTypeSelect.set(key, list[key]);
        setOperatorOptions(filterTypeSelect);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getFieldOptionsFetch();
    getOperatorOptionsFetch();
  }, []);

  const handleDeleteRow = (priority: number) => {
    const _dataSourceKeys = dataSource
      .filter((item: any) => {
        console.log(item.priority, priority, 'item.priority, priority');
        return item.priority !== priority;
      })
      .map((item: any) => {
        return {
          ...item,
          id: item.priority,
          priority: item.priority,
          cost: item.cost,
        };
      });
    setDataSource(_dataSourceKeys);
    form.setFieldsValue(_dataSourceKeys);
  };

  const renderColumns = _columns.map((item: FormTableColumnsDataProps) => {
    let render = null;
    switch (item.valueType) {
      case 'input':
        render = (text: string, record: any, index: number) => (
          <FormTableInput
            required={required}
            prefix={index.toString()}
            name={item.key}
            disabled={record?.isDisabled?.includes(item.key) || false}
            defaultValue={text}
            key={item.key + index}
          />
        );
        break;
      case 'number':
        render = (text: string, record: any, index: number) => (
          <FormTableNumber
            max={item.max}
            min={item.min}
            required={required}
            prefix={index.toString()}
            disabled={record?.isDisabled?.includes(item.key) || false}
            name={item.key}
            defaultValue={text}
            key={item.key + index}
          />
        );
        break;
      case 'filter':
        render = (value: any, record: any, index: number) => {
          return (
            <FormTableFilter
              filterStr={record.conditionFieldStr}
              required={required}
              filterSchemeId={record.filterSchemeId}
              initValue={value}
              name={'conditionField'}
              prefix={index.toString()}
              key={item.key + index}
              filiterFieldOptions={filterFieldOptions}
              operatorOptions={operatorOptions}
            />
          );
        };
        break;
      case 'text':
        render = (text: string, _: any, index: number) => (
          <FormText text={text} key={item.key + index} />
        );
        break;
    }

    return {
      title: item.name,
      key: item.key,
      dataIndex: item.key,
      render: render,
    };
  });

  const actionColumn = {
    title: t('操作'),
    key: 'action',
    render: (_: string, record: any) => (
      <Space>
        <Button
          disabled={record?.isDisabled?.includes('action') || false}
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteRow(record.priority)}
        />
      </Space>
    ),
  };

  return (
    <div className='w-full'>
      <ConfigProvider
        theme={{
          components: {
            Form: {
              itemMarginBottom: 0,
            },
          },
        }}>
        <Table
          columns={[...renderColumns, actionColumn]}
          dataSource={dataSource}
          pagination={false}
          rowKey='id'
        />
        <Button
          type='dashed'
          onClick={handleAddRow}
          block
          icon={<PlusOutlined />}
          style={{ marginBottom: 16 }}>
          {t('添加一行')}
        </Button>
      </ConfigProvider>
    </div>
  );
}
