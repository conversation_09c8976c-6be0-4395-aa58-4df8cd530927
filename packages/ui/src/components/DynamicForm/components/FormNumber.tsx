import React from 'react';
import { InputNumber, Form, Col } from 'antd';
import ShouldVisible from './ShouldVisible';
import { FormField } from '../types';
interface FormNumberProps {
  field: FormField;
  formName: string;
}

const FormNumber: React.FC<FormNumberProps> = ({ field, formName }) => {
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        initialValue={field.defaultValue}
        rules={[{ required: field.required }]}>
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormNumber;
