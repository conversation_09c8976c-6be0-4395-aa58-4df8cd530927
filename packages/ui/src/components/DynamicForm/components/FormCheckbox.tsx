import React from 'react';
import { Checkbox, Form } from 'antd';
import { useEffect, useState, useContext } from 'react';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { FormContext } from '../Context';
import _ from 'lodash';
interface FormCheckboxProps {
  field: FormField;
  formName: string;
}

const FormCheckbox: React.FC<FormCheckboxProps> = ({ field, formName }) => {
  const token = useContext(FormContext).token;
  const form = Form.useFormInstance();
  const dependencyFields = field.interface?.params || [];
  const watch = Form.useWatch(
    dependencyFields.map((field) => [formName, field.field])[0],
    form,
  );
  // 将 options 定义为状态变量
  const [options, setOptions] = useState(field.options?.checkbox || []);

  const getOptions = () => {
    if (!field.interface) return;
    form.setFieldsValue({ [formName]: { [field.field]: undefined } });
    let params = {};
    if (field.interface.params) {
      params = field.interface.params.reduce((acc, cur) => {
        return {
          ...acc,
          [cur.field]: cur.isFromField
            ? form.getFieldValue([formName, cur.field])
            : cur.value,
        };
      }, {});
    }

    const queryString = new URLSearchParams(params).toString();
    try {
      fetch(`${field.interface.url}?${queryString}`, {
        method: field.interface.method,
        headers: {
          'X-Token': token,
          'Accept-Language': localStorage.getItem('language') || 'zh',
        } as any,
      })
        .then((res) => res.json())
        .then((res) => {
          const list = _.get(res, 'data.list', []) || [];

          // 更新 options 状态
          setOptions(
            list.map((item: any) => ({ value: item.value, label: item.label })),
          );
        });
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getOptions();
  }, [watch]);

  const formItem = (
    <Form.Item
      label={field.label}
      name={[formName, field.field]}
      rules={[{ required: field.required }]}
      initialValue={field.defaultValue}>
      <Checkbox.Group options={options} />
    </Form.Item>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormCheckbox;
