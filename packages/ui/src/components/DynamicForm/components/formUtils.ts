import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 将字符串格式的日期转换为 dayjs 对象
 * @param dateStr - 字符串格式的日期，例如 '2024-10-01'
 * @returns dayjs 对象，如果日期无效则返回 null
 */
export function stringToDayjs(dateStr: string): Dayjs | null {
  if (!dateStr) {
    return null;
  }

  const dateObj = dayjs(dateStr);
  if (!dateObj.isValid()) {
    // console.error('无效的日期字符串:', dateStr);
    return null;
  }

  // 设置为浏览器的时区
  const localTime = dateObj.tz(dayjs.tz.guess());

  console.log('localTime', localTime);

  return localTime;
}

/**
 * 将 dayjs 对象转换为字符串格式的日期
 * @param dateObj - dayjs 对象
 * @param format - 日期格式，默认为 'YYYY-MM-DD'
 * @returns 字符串格式的日期，如果 dayjs 对象无效则返回空字符串
 */
export function dayjsToString(
  dateObj: Dayjs,
  format: string = 'YYYY-MM-DD',
): string {
  if (!dateObj || !dateObj.isValid()) {
    // console.error('无效的 dayjs 对象');
    return '';
  }

  // 设置为浏览器的时区
  const localTime = dateObj.tz(dayjs.tz.guess());

  return localTime.format(format);
}
