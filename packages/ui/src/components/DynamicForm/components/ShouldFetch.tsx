import { FormField } from '../types';
import { FormContext } from '../Context';
import _ from 'lodash';
import { Form } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

type ShouldFetchProps = React.PropsWithChildren<{
  dependencies: string[];
  formName: string;
  field: FormField;
}>;

const ShouldFetch: React.FC<ShouldFetchProps> = ({
  field,
  children,
  formName,
  dependencies,
}) => {
  const form = Form.useFormInstance();
  const token = useContext(FormContext).token;
  const [options, setOptions] = useState(field.options?.select || []);

  type Language = 'en-US' | 'zh-CN' | 'de-DE';
  const switchLanguage = (language: Language) => {
    switch (language) {
      case 'en-US':
        return 'en';
      case 'zh-CN':
        return 'zh';
      case 'de-DE':
        return 'de';
      default:
        return 'en';
    }
  };
  const getOptions = () => {
    if (!field.interface) return;
    form.setFieldsValue({ [formName]: { [field.field]: undefined } });
    let params: any = {};
    if (field.interface.params) {
      params = field.interface.params.reduce((acc, cur) => {
        if (cur.isNeedLocalValue) {
          return {
            ...acc,
            [cur.value]: localStorage.getItem(cur.value) || '',
          };
        }
        return {
          ...acc,
          [cur.field]: cur.isFromField
            ? form.getFieldValue([formName, cur.field])
            : cur.value,
        };
      }, {});
    }
    if (Object.keys(params).some((key) => !params[key])) {
      return;
    }
    const queryString = new URLSearchParams(params).toString();
    try {
      fetch(`${field.interface.url}?${queryString}`, {
        method: field.interface.method,
        headers: {
          'X-Token': token,
          'Accept-Language': switchLanguage(
            (localStorage.getItem('language') as Language) || 'en-US',
          ),
        } as any,
      })
        .then((res) => res.json())
        .then((res) => {
          const list = _.get(res, 'data.list', []) || [];

          // 更新 options 状态
          const _options = list.map((item: any) => ({
            value: item.value,
            label: item.label,
          }));
          setOptions(_options);
          if (_options.length === 1) {
            form.setFieldValue([formName, field.field], _options[0].value);
          }
        });
      if (field.defaultValue !== null) {
        form.setFieldValue([formName, field.field], field.defaultValue);
      }
    } catch (error) {
      console.error(error);
    }
  };
  const onChange = (value: any) => {
    form.setFieldValue([formName, field.field], value);
  };

  useEffect(() => {
    if (dependencies !== undefined) {
      getOptions();
    }
  }, dependencies);

  return React.cloneElement(children as React.ReactElement, {
    options,
    value: form.getFieldValue([formName, field.field]),
    onChange,
  });
};

export default ShouldFetch;
