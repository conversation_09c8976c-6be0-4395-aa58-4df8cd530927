import React from 'react';
import { DatePicker, Form, Col } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { stringToDayjs } from './formUtils';
import { Dayjs } from 'dayjs';

interface FormRangeDateProps {
  field: FormField;
  formName: string;
}

const FormRangeDate: React.FC<FormRangeDateProps> = ({ field, formName }) => {
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        rules={[{ required: field.required }]}
        initialValue={
          field.defaultValue
            ? [field.defaultValue[0], field.defaultValue[1]]
            : [null, null]
        }>
        <StringRangePicker />
      </Form.Item>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }

  return formItem;
};

interface StringRangePickerProps {
  value?: [string, string];
  onChange?: (value: [string, string] | null) => void;
}

const StringRangePicker: React.FC<StringRangePickerProps> = ({
  value,
  onChange,
}) => {
  const handleChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      onChange?.([
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
      ]);
    } else {
      onChange?.(null);
    }
  };

  return (
    <DatePicker.RangePicker
      value={
        value
          ? [stringToDayjs(value[0]), stringToDayjs(value[1])]
          : [null, null]
      }
      onChange={(dates: any) => handleChange(dates as [Dayjs, Dayjs] | null)}
      className='w-full'
    />
  );
};

export default FormRangeDate;
