import React from 'react';
import {
  Upload,
  Form,
  Col,
  Button,
  type UploadProps,
  message,
  type UploadFile,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { useState, useContext } from 'react';
import { FormContext } from '../Context';
import { t } from '@/languages';

interface FormUploadProps {
  field: FormField;
  formName: string;
}

const SingleFileUpload: React.FC<
  UploadProps & { onChange?: (fileName: string) => void }
> = (props) => {
  const { onChange, ...restProps } = props;
  const [fileList, setFileList] = useState<UploadFile<any>[]>([]);

  return (
    <Upload
      {...restProps}
      fileList={fileList}
      onChange={(info: any) => {
        console.log(info);
        const newFileList = info.fileList.slice(-1); // 只保留最新上传的一个文件
        setFileList(newFileList);
        if (info.file.status === 'done' && info.file.response) {
          onChange && onChange(info.file.response.data.result);
          message.success(t('{name} 上传成功', { name: info.file.name }));
        } else if (info.file.status === 'error') {
          message.error(t('{name} 上传失败', { name: info.file.name }));
        }
      }}>
      {props.children}
    </Upload>
  );
};

const FormUpload: React.FC<FormUploadProps> = ({ field, formName }) => {
  const token = useContext(FormContext).token || '';
  const action = field.options?.upload
    ? field.options?.upload?.action
    : '/file/uploadFile';
  const form = Form.useFormInstance();
  const config: UploadProps = {
    capture: undefined,
    name: 'file',
    action: '/api' + action,
    beforeUpload: (file: any) => {
      const isYML =
        file.type === 'application/x-yaml' ||
        file.type === 'application/x-yml' ||
        file.type === 'application/x-jsonl' ||
        file.name.endsWith('.jsonl');
      if (!isYML) {
        message.error(t('{name} 不是yml/yaml文件', { name: file.name }));

        return Upload.LIST_IGNORE;
      }
      return true;
    },
    headers: {
      Authorization: token,
      'X-Token': token,
    },
  };

  const formItem = (
    <Col span={8}>
      <Form.Item
        label={t('上传{upload}', { upload: '' })}
        name={[formName, field.field]}
        rules={[{ required: field.required }]}>
        <SingleFileUpload
          {...config}
          onChange={(fileName: any) => {
            form.setFieldValue([formName, field.field], fileName);
          }}>
          <Button icon={<UploadOutlined />}>
            {t('上传{upload}', { upload: '' })}
          </Button>
        </SingleFileUpload>
      </Form.Item>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }

  return formItem;
};

export default FormUpload;
