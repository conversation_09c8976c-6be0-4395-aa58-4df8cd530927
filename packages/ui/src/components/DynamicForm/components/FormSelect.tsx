import React, { useEffect } from 'react';
import { Select, Form, Flex, ConfigProvider } from 'antd';
import { FormField, FieldAction } from '../types';
import ShouldVisible from './ShouldVisible';
import ShouldFetch from './ShouldFetch';
import useDynamicFormStore from '@/store/feature/DynamicForm/store';
interface FormSelectProps {
  field: FormField;
  formName: string;
}

const FormSelect: React.FC<FormSelectProps> = ({ field, formName }) => {
  let returnItem = null;
  const form = Form.useFormInstance();
  const dependencyFields = field.interface?.params || [];
  const selectValue = Form.useWatch([formName, field.field], form);

  const { changePeriod } = useDynamicFormStore();

  /**
   * @description 执行含有的函数
   */

  const actionsFuncs = (value: any) => {
    const funcs =
      (field.actions &&
        field.actions.map((item: FieldAction) => {
          switch (item.type) {
            case 'changeBasePeriod':
              return changePeriod;
            default:
              return () => {
                console.warn('未定义函数');
              };
          }
        })) ||
      [];
    funcs.map((func) => func(value));
  };

  const options = field.options?.select || [];

  const formItem = (
    <Form.Item
      noStyle
      shouldUpdate={(prev: any, next: any) => {
        let flag = false;
        dependencyFields.forEach((field) => {
          const prevValue = prev[formName]?.[field.field];
          const nextValue = next[formName]?.[field.field];
          console.log(prevValue, nextValue, 'prevValue, nextValue');
          if (prevValue !== nextValue) {
            flag = true;
          }
        });
        return flag;
      }}>
      {({ getFieldValue }) => {
        const dependencyValues = dependencyFields.map((field) =>
          getFieldValue([formName, field.field]),
        );
        return (
          <Form.Item
            label={field.label}
            name={[formName, field.field]}
            initialValue={field.defaultValue}
            rules={[
              { required: field.required, message: `请输入${field.label}` },
            ]}>
            {field.interface ? (
              <ShouldFetch
                dependencies={dependencyValues}
                field={field}
                formName={formName}>
                <StyledSelect field={field} options={options} />
              </ShouldFetch>
            ) : (
              <StyledSelect field={field} options={options} />
            )}
          </Form.Item>
        );
      }}
    </Form.Item>
  );
  returnItem = formItem;

  if (field.visibilityCondition) {
    returnItem = (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {returnItem}
      </ShouldVisible>
    );
  }

  useEffect(() => {
    actionsFuncs(selectValue);
  }, [selectValue]);
  return returnItem;
};

interface StyledSelectProps {
  field: FormField;
  value?: string;
  options: { value: string; label: string }[];
  onChange?: (value: string) => void;
  onSelect?: (value: string) => void;
}
export const StyledSelect: React.FC<StyledSelectProps> = ({
  // field,
  value,
  options,
  onChange,
  onSelect,
}) => {
  return (
    <Flex vertical={true}>
      <ConfigProvider
        theme={{
          components: {
            Select: {
              optionSelectedColor: '#0C40DE',
              optionFontSize: 16,
              optionSelectedFontWeight: 400,
              optionSelectedBg: 'none',
            },
          },
        }}>
        <Select
          value={value}
          onChange={onChange}
          onSelect={onSelect}
          options={options}
          style={{
            height: '30px',
          }}
          labelRender={(label: any) => {
            return (
              <span
                className='text-s3'
                style={{
                  color: '#848A99',
                }}>
                {label.label}
              </span>
            );
          }}
          dropdownStyle={{
            border: '1px solid #1890FF',
            color: '#848A99',
          }}
          optionRender={(option: any) => {
            return (
              <span
                className='text-s3'
                style={{
                  color: option.key === value ? '#0C40DE' : '#848A99',
                }}>
                {option.label}
              </span>
            );
          }}
        />
      </ConfigProvider>
    </Flex>
  );
};

export default FormSelect;
