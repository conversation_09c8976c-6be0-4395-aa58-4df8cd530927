import React from 'react';
import { Input, Select, Form, Col, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';

interface FormInputProps {
  field: FormField;
  formName: string;
}

interface DynamicFormListProps {
  fields: any[];
  add: () => void;
  remove: (name: number) => void;
  field: FormField;
}

const DynamicFormList: React.FC<DynamicFormListProps> = ({
  fields,
  add,
  remove,
  field,
}) => {
  // React.useEffect(() => {
  //   if (fields.length === 0) {
  //     add();
  //   }
  // }, [fields, add]);

  return (
    <>
      {fields.map((fieldRow, index) => {
        const { key: _key, ...restField } = fieldRow;
        console.log(`form-field-${restField.name}-${index}`);
        return (
          <div
            key={`form-field-${restField.name}-${index}`}
            style={{
              display: 'flex',
              gap: 8,
              alignItems: 'flex-start',
              padding: '8px',
            }}>
            <Form.Item
              key={restField.name}
              {...restField}
              name={[restField.name, 'select1']}
              rules={[{ required: field.required }]}>
              <Select
                placeholder='请选择选项1'
                style={{ width: 120 }}
                options={field.options?.filter?.fieldOptions}
              />
            </Form.Item>
            <Form.Item
              {...restField}
              name={[restField.name, 'select2']}
              rules={[{ required: field.required }]}>
              <Select
                placeholder='请选择选项2'
                style={{ width: 120 }}
                options={field.options?.filter?.optionsOptions}
              />
            </Form.Item>
            <Form.Item
              {...restField}
              name={[restField.name, 'input']}
              rules={[{ required: field.required }]}>
              <Input placeholder='请输入内容' style={{ width: 160 }} />
            </Form.Item>
            <Form.Item colon={false} wrapperCol={{ offset: 6 }}>
              <Button
                type='default'
                danger
                onClick={() => remove(restField.name)}>
                删除
              </Button>
            </Form.Item>
          </div>
        );
      })}
      <Button className='ml-2' icon={<PlusOutlined />} onClick={() => add()}>
        增加
      </Button>
    </>
  );
};
//TODO:需要进行初始化参数
const FormFilter: React.FC<FormInputProps> = ({ field, formName }) => {
  const defaultValue = JSON.parse(field.defaultValue || '[]');
  const formList = (
    <Col span={24}>
      <label>{field.label}</label>
      <Form.List name={[formName, field.field]} initialValue={defaultValue}>
        {(fields, methods) => (
          <DynamicFormList fields={fields} {...methods} field={field} />
        )}
      </Form.List>
    </Col>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formList}
      </ShouldVisible>
    );
  }
  return formList;
};

export default FormFilter;
