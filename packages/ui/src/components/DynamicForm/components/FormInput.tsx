import React from 'react';
import { Input, Form } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
interface FormInputProps {
  field: FormField;
  formName: string;
}

const FormInput: React.FC<FormInputProps> = ({ field, formName }) => {
  const formItem = (
    <Form.Item
      label={field.label}
      name={[formName, field.field]}
      rules={[{ required: field.required }]}
      initialValue={field.defaultValue}>
      <Input />
    </Form.Item>
  );

  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

export default FormInput;
