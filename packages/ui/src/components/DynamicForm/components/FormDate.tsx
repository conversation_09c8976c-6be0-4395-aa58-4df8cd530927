import React from 'react';
import { DatePicker, Form, Col } from 'antd';
import { FormField } from '../types';
import ShouldVisible from './ShouldVisible';
import { stringToDayjs, dayjsToString } from './formUtils';
import { BasePeriodName } from './types';
import { Dayjs } from 'dayjs';
import useDynamicFormStore from '@/store/feature/DynamicForm/store';
interface FormDateProps {
  field: FormField;
  formName: string;
}

const FormDate: React.FC<FormDateProps> = ({ field, formName }) => {
  const { basePeriod } = useDynamicFormStore();
  const formItem = (
    <Col span={8}>
      <Form.Item
        label={field.label}
        name={[formName, field.field]}
        rules={[{ required: field.required }]}
        initialValue={stringToDayjs(
          field.defaultValue === '' ? null : field.defaultValue,
        )}>
        <StringDatePicker basePeriod={basePeriod} />
      </Form.Item>
    </Col>
  );
  if (field.visibilityCondition) {
    return (
      <ShouldVisible
        fieldName={field.field}
        visibilityCondition={field.visibilityCondition}
        formName={formName}>
        {formItem}
      </ShouldVisible>
    );
  }
  return formItem;
};

interface StringRangePickerProps {
  value?: string;
  onChange?: (value: string | null) => void;
  basePeriod?: number;
}

const StringDatePicker: React.FC<StringRangePickerProps> = ({
  value,
  onChange,
  basePeriod,
}) => {
  const handleChange = (date: Dayjs | null) => {
    if (date) {
      onChange?.(dayjsToString(date));
    } else {
      onChange?.(null);
    }
  };

  const getDatePickerType = () => {
    console.log(basePeriod);
    switch (basePeriod) {
      case BasePeriodName.YEAR:
        return 'year';
      case BasePeriodName.QUARTER:
        return 'quarter';
      case BasePeriodName.MONTH:
        return 'month';
      case BasePeriodName.WEEK:
        return 'week';
      case BasePeriodName.DAY:
        return 'date';
      default:
        return 'date';
    }
  };

  React.useEffect(() => {
    if (value) {
      handleChange(stringToDayjs(value));
    }
  }, []);

  return (
    <DatePicker
      value={value ? stringToDayjs(value) : null}
      picker={getDatePickerType()}
      onChange={(date: any) => handleChange(date as Dayjs | null)}
      className='w-full'
    />
  );
};

export default FormDate;
