import React, { useEffect, useState } from 'react';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import deDE from 'antd/locale/de_DE';
import {
  FormInput,
  FormNumber,
  FormSelect,
  FormDate,
  FormCheckbox,
  FormRangeDate,
  FormUpload,
  FormTable,
  FormFilter,
  FormAiTextArea,
} from './components';
import { Row, Col, ConfigProvider } from 'antd';
import { FormField } from './types';
import { FormContext } from './Context';
import useDynamicFormStore from '@/store/feature/DynamicForm/store';

export interface DynamicFormProps {
  fields: FormField[];
  formName: string;
  token?: string;
}

interface DynamicFormType extends React.FC<DynamicFormProps> {
  FormTable: typeof FormTable;
}

const DynamicForm: DynamicFormType = ({ fields, formName, token }) => {
  const { basePeriod, changePeriod } = useDynamicFormStore();
  const language = localStorage.getItem('language') || 'en-US';
  const [antdLanguagePackage, setAntdLanguagePackage] = useState(zhCN);

  const initBasePeriod = () => {
    const _basePeriod = fields.map((field) => field.basePeriod)[0] || -1;
    if (basePeriod === -1) {
      changePeriod(_basePeriod);
    }
  };

  const renderFormItem = (field: FormField) => {
    switch (field.type) {
      case 'text':
        return (
          <Col span={8}>
            <FormInput field={field} formName={formName} key={field.field} />
          </Col>
        );
      case 'number':
        return (
          <FormNumber field={field} formName={formName} key={field.field} />
        );
      case 'select':
        return (
          <Col span={8}>
            <FormSelect field={field} formName={formName} key={field.field} />
          </Col>
        );
      case 'date':
        return <FormDate field={field} formName={formName} key={field.field} />;
      case 'rangeDate':
        return (
          <FormRangeDate field={field} formName={formName} key={field.field} />
        );
      case 'checkbox':
        return (
          <FormCheckbox field={field} formName={formName} key={field.field} />
        );
      case 'upload':
        return (
          <FormUpload field={field} formName={formName} key={field.field} />
        );
      case 'table':
        return (
          <FormTable field={field} key={field.field} formName={formName} />
        );
      case 'filter':
        return (
          <FormFilter field={field} formName={formName} key={field.field} />
        );
      case 'aiTextArea':
        return (
          <Col span={8}>
            <FormAiTextArea
              field={field}
              formName={formName}
              key={field.field}
            />
          </Col>
        );
    }
  };

  useEffect(() => {
    initBasePeriod();
  }, [fields]);

  useEffect(() => {
    if (language === 'en-US') {
      setAntdLanguagePackage(enUS);
    } else if (language === 'de-DE') {
      setAntdLanguagePackage(deDE);
    } else {
      setAntdLanguagePackage(zhCN);
    }
  }, [language]);

  return (
    <ConfigProvider locale={antdLanguagePackage}>
      <FormContext.Provider value={{ token, fields, formName }}>
        <Row gutter={[16, 16]}>{fields.map(renderFormItem)}</Row>
      </FormContext.Provider>
    </ConfigProvider>
  );
};

DynamicForm.FormTable = FormTable;
export {
  FormInput,
  FormNumber,
  FormSelect,
  FormDate,
  FormCheckbox,
  FormRangeDate,
  FormUpload,
  FormTable,
  FormFilter,
  FormAiTextArea,
};
export default DynamicForm;
