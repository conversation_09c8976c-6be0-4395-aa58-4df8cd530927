import { type FormTableColumnsDataProps } from './components/FormTable/types';

export interface ConditionalDisplay {
  field: string;
  value: any;
  component: string;
}

export interface FieldAction {
  type:
    | 'setValue'
    | 'clearField'
    | 'toggleComponent'
    | 'format'
    | 'changeBasePeriod';
  target: string;
  value?: any;
  toggleOptions?: {
    components: string[];
    conditions: { field: string; value: any }[];
  };
}

export interface FormFieldOptions {
  select?: { value: any; label: string }[];
  checkbox?: { value: string; label: string }[];
  date?: {
    disabledDate?: string | string[];
    showTime?: boolean;
    format?: string;
  };
  table?: {
    columns: FormTableColumnsDataProps[];
    initValue: any[];
  };
  filter?: {
    fieldOptions: { value: string; label: string }[];
    optionsOptions: { value: string; label: string }[];
  };
  upload?: {
    action: string;
  };
  aiTextArea?: {
    options: { value: any; label: string }[];
  };
  [key: string]: any; // This allows for additional custom options
}

export interface FormField {
  field: string;
  label: string;
  basePeriod?: number;
  type:
    | 'text'
    | 'number'
    | 'select'
    | 'date'
    | 'switch'
    | 'button'
    | 'checkbox'
    | 'upload'
    | 'rangeDate'
    | 'table'
    | 'filter'
    | 'aiTextArea';
  options?: FormFieldOptions;
  required?: boolean;
  visibilityCondition?: {
    field: string;
    operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
    value: any;
  };
  conditionalDisplay?: ConditionalDisplay[];
  actions?: FieldAction[];
  defaultValue?: any;
  events?: {
    [key: string]: FieldAction[];
  };
  interface?: {
    url: string;
    method: 'GET' | 'POST';
    params?: {
      field: string;
      value: any;
      isFromField: boolean;
      isNeedLocalValue: boolean;
    }[];
    transform?: (data: any) => any;
  };
}

export interface DynamicFormProps {
  fields: FormField[];
  onSubmit: (formId: string, values: any) => void;
  onChange?: (formId: string, values: any) => void;
  formId: string;
}

export interface MultipleDynamicFormsProps {
  formConfigs: {
    id: string;
    fields: FormField[];
  }[];
  onSubmit: (allFormData: Record<string, any>) => void;
}
