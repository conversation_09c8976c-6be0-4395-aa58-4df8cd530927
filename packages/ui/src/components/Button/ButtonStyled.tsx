import styled from 'styled-components';
import { ButtonProps } from './Button';

const commonButtonStyles = styled.button<ButtonProps>`
  bakcground-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  font-family: Roboto;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.72px;

  min-height: ${({ size }) => {
    switch (size) {
      case 'small':
        return '32px';
      case 'big':
        return '54px';
      case 'normal':
      default:
        return '42px';
    }
  }};

  min-width: ${({ size }) => {
    switch (size) {
      case 'small':
        return '32px';
      case 'big':
        return '54px';
      case 'normal':
      default:
        return '42px';
    }
  }};

  border-radius: 36px;

  padding: ${({ size }) => {
    switch (size) {
      case 'small':
        return '4px 21px';
      case 'big':
        return '15px 21px';
      case 'normal':
      default:
        return '9px 21px';
    }
  }};
`;

const PrimaryButton = styled(commonButtonStyles)<ButtonProps>`
  background-color: ${(props) => props.theme.colors.primary.default};
  &:hover {
    background-color: ${(props) => props.theme.colors.primary.hover};
  }
  &:active {
    background-color: ${(props) => props.theme.colors.primary.active};
  }
`;

const DashedButton = styled(commonButtonStyles)<ButtonProps>`
  border: 1px dashed ${(props) => props.theme.colors.Grey.level4};
  color: ${(props) => props.theme.colors.Grey.level1};
  &:hover {
    color: ${(props) => props.theme.colors.primary.default};
    border-color: ${(props) => props.theme.colors.primary.default};
  }
  &:active {
    color: ${(props) => props.theme.colors.primary.active};
    border-color: ${(props) => props.theme.colors.primary.active};
  }
`;

export { PrimaryButton, DashedButton };
