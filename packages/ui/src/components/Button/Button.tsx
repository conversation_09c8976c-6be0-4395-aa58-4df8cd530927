import React from 'react';
import { ThemeProvider } from 'styled-components';
import { commonTheme } from '../../common';
import { PrimaryButton, DashedButton } from './ButtonStyled';

export interface ButtonProps extends React.HTMLAttributes<HTMLButtonElement> {
  size?: 'small' | 'normal' | 'big';
  type?: 'primary' | 'secondary' | 'dashed' | 'link';
}

const Button: React.FC<ButtonProps> = ({
  size = 'normal',
  type = 'primary',
  ...props
}) => {
  let button = null;
  switch (type) {
    case 'primary':
      button = <PrimaryButton size={size} {...props} />;
      break;
    case 'dashed':
      button = <DashedButton size={size} {...props} />;
      break;
    default:
      button = <PrimaryButton size={size} {...props} />;
  }
  return <ThemeProvider theme={commonTheme}>{button}</ThemeProvider>;
};

export default Button;
