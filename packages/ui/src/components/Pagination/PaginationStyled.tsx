import styled from 'styled-components';
import { Flex } from 'antd';
import Button from '../Button';
import { motion } from 'framer-motion';

export const PaginationContainer = styled(Flex).attrs({
  justify: 'space-between',
})`
  width: 800px;
  height: 60px;
`;

export const PagesContainer = styled(motion.div)`
  display: flex;
  gap: 18px;
  align-items: center;
  background-color: white;
  border-radius: 25px;
  padding: 0 25px;
`;

export const PaginationButton = styled(motion.button)`
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  color: #1890FF;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  &:disabled {
    color: #white;
    cursor: not-allowed;
  }

  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.9px;
`;

export const PageNumberGroup = styled(motion.div)`
  display: flex;
  gap: 18px;
  align-items: center;
`;

export const PaginationEllipsisButton = styled(PaginationButton)`
  
`;

export const PaginationNormalButton = styled(PaginationButton)`

  font-family: Roboto;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.96px;
`;

export const PaginationCurrentButton = styled(PaginationButton)`
  background-color: #1890FF;
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;

  font-family: Roboto;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.96px;
`;

export const PaginationJumpContainer = styled.div`
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 25px;
  padding: 0 25px;
  gap: 18px;
`;

export const PaginationJumpLabelText = styled.span`
  color: #848A99;

  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
`;

export const PaginationInput = styled.input.attrs({
  type: 'number',
})`
  width: 40px;
  height: 40px;
  color: #0C40DE;
  box-sizing: border-box;
  border: 1px solid #0C40DE;
  border-radius: 8px;
  text-align: center;
  padding: 4px 8px;
  -moz-appearance: textfield;

  /* For Chrome, Safari, Edge, Opera */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* For Firefox */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -moz-appearance: none;
    margin: 0;
  }

  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; 
  letter-spacing: 0.56px;
`;

export const PaginationJumpButton = styled(Button).attrs({
  size: 'small',
  type: 'primary',
})`
  height: 45px;
  border-radius: 36px;
  width: 80px;
  color: white;

  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
`;