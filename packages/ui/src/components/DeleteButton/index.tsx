import React from 'react';
import { But<PERSON>, Popconfirm } from 'antd';
import { Translate } from '@/languages';
export interface DeleteButtonProps {
  onClick: () => void;
  deleteTitle: string;
  disabled?: boolean;
  [key: string]: any;
}
const DeleteButton: React.FC<DeleteButtonProps> = ({
  deleteTitle,
  onClick,
  disabled = false,
  ...rest
}) => {
  return (
    <Popconfirm
      title={deleteTitle}
      onConfirm={() => {
        onClick();
      }}
      okText={<Translate message={'确定'} />}
      cancelText={<Translate message={'取消'} />}>
      <Button type='primary' danger disabled={disabled} {...rest}>
        <Translate message={'删除'} />
      </Button>
    </Popconfirm>
  );
};
export default DeleteButton;
