import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Drawer, Table, Timeline, App, Flex } from 'antd';
import React from 'react';
import { t } from '@/languages';

export interface ExpandTableContentProps {
  record: any;
  getLogsScpLogger: any;
  getCronTaskDetail: any;
  detailProgressData: any;
  currentLogs: any;
  isInit: boolean;
  reloadStep?: (id: string, seq: number) => void;
  skipStep?: (id: string, seq: number) => void;
}

const ExpandTableContent: React.FC<ExpandTableContentProps> = ({
  record,
  getLogsScpLogger,
  getCronTaskDetail,
  reloadStep,
  skipStep,
  detailProgressData,
  currentLogs,
  isInit,
}: ExpandTableContentProps) => {
  const isGenSence = localStorage.getItem('scene') === 'gen-int';
  const { message } = App.useApp();
  const [detailLogMessage, setDetailLogMessage] = useState<string>('');

  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);

  const closeDrawer = () => {
    setDrawerOpen(false);
  };

  const innerColumns = [
    {
      title: t('级别'),
      key: 'level',
      dataIndex: 'level',
    },
    {
      title: t('消息'),
      key: 'msg',
      dataIndex: 'msg',
    },
    {
      title: t('时间'),
      key: 'time',
      dataIndex: 'time',
      render: (value: string) => {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: t('操作'),
      key: 'options',
      dataIndex: 'options',
      render: (_: any, record: any) => {
        return (
          <a
            onClick={() => {
              if (!record.details || record.details === '') {
                message.warning(t('该日志暂无详情'));
                return;
              }
              setDrawerOpen(true);
              setDetailLogMessage(record.details);
            }}
            style={{
              display:
                !record.details || record.details === '' ? 'none' : 'block',
            }}>
            {t('详情')}
          </a>
        );
      },
    },
  ];

  const renderTimeline = (item: any) => {
    const { seq, templateName, state } = item;
    let color = '';
    let icon = null;
    switch (state) {
      case 1:
        color = 'green';
        icon = <CheckCircleOutlined />;
        break;
      case 2:
        color = 'blue';
        icon = <UndoOutlined />;
        break;
      case 3:
        color = 'red';
        icon = <CloseCircleOutlined />;
        break;
      case 4:
        color = 'grey';
        break;
      default:
        break;
    }
    return {
      dot: icon,
      color,
      children: (
        <div>
          {templateName}
          {(state === 1 || state === 2 || state == 3) && (
            <span
              className='text-blue-500 ml-[10px] cursor-pointer'
              onClick={() => getLogsScpLogger(seq, record.taskUuid)}>
              {t('日志')}
            </span>
          )}
          {state === 3 && !isGenSence && (
            <>
              <span
                className='text-red-500 ml-[10px] cursor-pointer'
                onClick={() => reloadStep && reloadStep(record.taskUuid, 1)}>
                {t('重新执行')}
              </span>
              <span
                className='text-red-500 ml-[10px] cursor-pointer'
                onClick={() => skipStep && skipStep(record.taskUuid, 2)}>
                {t('跳过')}
              </span>
            </>
          )}
        </div>
      ),
    };
  };

  useEffect(() => {
    getCronTaskDetail(record.taskUuid);
  }, [record]);

  return (
    <Flex vertical={false}>
      <Flex
        vertical={true}
        style={{
          flex: 0.3,
        }}
        gap={'10px'}>
        <label className='text-h4'>{t('任务链')}</label>
        <Timeline items={detailProgressData.map(renderTimeline)} />
      </Flex>
      {!isInit && (
        <Flex
          vertical={true}
          style={{
            flex: 0.7,
          }}>
          <label className='text-h4'>{t('执行日志')}</label>
          <div>
            <Table
              rowKey={(record) => record.msg + record.time}
              columns={innerColumns}
              dataSource={currentLogs}
              pagination={false}
            />
          </div>
        </Flex>
      )}
      <Drawer
        title={t('日志详情')}
        width={800}
        open={drawerOpen}
        onClose={closeDrawer}
        getContainer={false}>
        {detailLogMessage}
      </Drawer>
    </Flex>
  );
};

export default ExpandTableContent;
