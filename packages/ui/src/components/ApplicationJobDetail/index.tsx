import React, { useEffect, useState, useCallback } from 'react';
import { Pagination } from '@/index';
import { RightOutlined, DownOutlined, CopyOutlined } from '@ant-design/icons';
import {
  Table,
  Tag,
  DatePicker,
  Input,
  Form,
  Select,
  Button,
  Flex,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { t } from '@/languages';
import dayjs from 'dayjs';

export interface ApplicationJobDetailProps {
  navigate: any;
  ExpandTableContent: any;
  pVersionList?: any;
  userList: any;
  total: any;
  pageData: any;
  dataSource: any;
  contextHolder: any;
  getCornJobTask: any;
  getUserListByCoIdContainsSelf: any;
  setFormData: any;
  setPageData: any;
}

const ApplicationJobDetail = ({
  navigate,
  ExpandTableContent,
  pVersionList = [],
  userList,
  total,
  pageData,
  dataSource,
  contextHolder,
  getCornJobTask,
  getUserListByCoIdContainsSelf,
  setFormData,
  setPageData,
}: ApplicationJobDetailProps) => {
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ColumnsType<any> = [
    {
      title: t('任务名称{}', ''),
      key: 'jobName',
      // width: 250,
      dataIndex: 'jobName',
      render: (text: string, record: any) => {
        return (
          <a
            className='text-black'
            onClick={() => {
              const params = {
                taskUuid: record.taskUuid,
                jobId: record.jobId,
                taskName: text,
                taskStateCode: record.taskStateCode,
              };
              const queryString = new URLSearchParams(params).toString();
              navigate(`/checkApplication?${queryString}`);
            }}>
            {text}
            <CopyOutlined className={'ml-[10px]'} />
          </a>
        );
      },
    },
    {
      title: t('执行方式'),
      key: 'jobType',
      // width: 200,
      dataIndex: 'jobType',
      render: (value: number) => {
        return value === 1 ? t('定时执行') : t('立即执行');
      },
    },
    {
      title: t('用户'),
      key: 'createdByName',
      // width: 200,
      dataIndex: 'createdByName',
    },
    {
      title: t('模版类型'),
      key: 'templateTypeName',
      // width: 200,
      dataIndex: 'templateTypeName',
    },
    {
      title: t('执行任务状态'),
      key: 'taskStateCode',
      // width: 200,
      dataIndex: 'taskStateCode',
      render: (value: number, record: any) => {
        let color: string = '';
        switch (value) {
          case 1:
            color = 'green';
            break;
          case 2:
            color = 'blue';
            break;
          case 3:
            color = 'red';
            break;
          case 4:
            color = 'grey';
            break;
          case 5:
            color = 'orange';
            break;
          default:
            break;
        }
        return <Tag color={color}>{record.taskStateName}</Tag>;
      },
    },
    {
      title: t('开始时间{}', ''),
      key: ' taskStartTime',
      // width: 200,
      dataIndex: 'taskStartTime',
      render: (value: number) =>
        dayjs(value * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: t('结束时间{}', ''),
      key: ' taskEndTime',
      // width: 200,
      dataIndex: 'taskEndTime',
      render: (value: number) => {
        if (!value || value === 0) {
          return '';
        } else return dayjs(value * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: t('备注信息'),
      key: 'message',
      dataIndex: 'message',
    },
  ].map((item) => ({ ...item, align: 'center', ellipsis: true }));

  const changePageData = useCallback((page: number, pageSize: number) => {
    setPageData({ page, pageSize });
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await getCornJobTask(); // 假设 getCornJobTask 是一个返回 Promise 的异步函数
      setLoading(false);
    };
    fetchData();
  }, [pageData]);

  return (
    <div>
      {contextHolder}
      <HeaderForm
        onSearch={(values: Omit<any, 'page' | 'pageSize'>) => {
          setFormData({ ...values, ...pageData, page: 1 });
          setPageData({ ...pageData, page: 1 });
        }}
        navigate={navigate}
        pVersionList={pVersionList}
        userList={userList}
        getUserListByCoIdContainsSelf={getUserListByCoIdContainsSelf}
      />
      <Table
        className='mt-[12px]'
        expandable={{
          expandedRowRender: (record) => {
            return record.taskStateCode !== 4 ? (
              <ExpandTableContent record={record} />
            ) : null;
          },
          expandRowByClick: true,
          expandIcon: ({ expanded, onExpand, record }) => {
            return record.taskStateCode !== 4 ? (
              expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              )
            ) : null;
          },
          rowExpandable: (record) => {
            return record.taskStateCode !== 4;
          },
        }}
        loading={loading}
        columns={columns}
        rowKey={(record) => record.jobId + record.taskStartTime}
        dataSource={dataSource}
        pagination={false}
      />
      <div className='flex items-center justify-center mt-8'>
        <Pagination
          current={pageData.page}
          pageSize={pageData.pageSize}
          total={total}
          onPageChange={changePageData}
        />
      </div>
    </div>
  );
};

type HeaderFormProps = {
  onSearch: (values: any) => void;
  navigate: any;
  pVersionList: any;
  userList: any;
  getUserListByCoIdContainsSelf: any;
};

const HeaderForm = ({
  onSearch,
  navigate,
  pVersionList,
  userList,
  getUserListByCoIdContainsSelf,
}: HeaderFormProps) => {
  type FilterType = {
    timeRange: [dayjs.Dayjs, dayjs.Dayjs];
    taskState?: Array<number>;
    taskTag?: string;
    jobName?: string;
    jobMode?: string;
  };
  const [form] = Form.useForm();

  const onFinish = (values: FilterType) => {
    const { timeRange, ...rest } = values;
    const startTime = timeRange[0].format();
    const endTime = timeRange[1].endOf('day').format();
    onSearch({ ...rest, startTime, endTime });
  };

  useEffect(() => {
    getUserListByCoIdContainsSelf();
  }, []);

  const styles = {
    formLabel: {
      flex: 1,
    },
  };

  return (
    <div>
      <Form<FilterType>
        form={form}
        layout={'vertical'}
        labelAlign={'right'}
        onFinish={onFinish}>
        <Form.Item noStyle>
          <Flex vertical={false} gap={8}>
            <Form.Item
              label={t('任务名称{}', '')}
              name='jobName'
              style={styles.formLabel}>
              <Input />
            </Form.Item>
            <Form.Item label={t('用户')} name='userId' style={styles.formLabel}>
              <Select
                allowClear
                mode='multiple'
                maxTagCount={'responsive'}
                options={userList}
              />
            </Form.Item>
            <Form.Item
              label={t('计划版本{}', '')}
              name='pvId'
              style={styles.formLabel}>
              <Select
                allowClear
                mode='multiple'
                maxTagCount={'responsive'}
                options={pVersionList}
              />
            </Form.Item>
            <Form.Item
              label={t('任务状态{}', '')}
              name='taskState'
              style={styles.formLabel}>
              <Select
                allowClear
                mode='multiple'
                maxTagCount={'responsive'}
                options={[
                  { label: t('已完成{}', ''), value: 1 },
                  { label: t('执行中{}', ''), value: 2 },
                  { label: t('任务中断{}', ''), value: 3 },
                  { label: t('待执行{}', ''), value: 4 },
                  { label: t('超时{}', ''), value: 5 },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={t('模版类型')}
              name='templateType'
              style={styles.formLabel}>
              <Select
                allowClear
                mode='multiple'
                maxTagCount={'responsive'}
                options={[
                  { label: t('原始模版'), value: 1 },
                  { label: t('系统模班'), value: 2 },
                  { label: t('分享模版'), value: 3 },
                  { label: t('自建模版'), value: 4 },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={t('时间范围{}', '')}
              name='timeRange'
              rules={[{ required: true }]}
              initialValue={[dayjs().startOf('day'), dayjs().endOf('day')]}
              style={{
                ...styles.formLabel,
                minWidth: 260,
              }}>
              <DatePicker.RangePicker className='w-full' />
            </Form.Item>
          </Flex>
        </Form.Item>
        <Form.Item noStyle>
          <Flex vertical={false} gap={10} justify='flex-end'>
            <Button htmlType='reset'>{t('重置{}', '')}</Button>
            <Button type='primary' htmlType='submit'>
              {t('查询{}', '')}
            </Button>
            <Button
              type='primary'
              onClick={() => {
                navigate('/runApplicationJob');
              }}>
              {t('运行任务{}', '')}
            </Button>
          </Flex>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ApplicationJobDetail;
