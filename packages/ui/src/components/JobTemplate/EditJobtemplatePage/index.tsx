import { PropsWithChildren, useEffect, useState } from 'react';
import {
  Flex,
  Typo<PERSON>,
  Config<PERSON><PERSON>ider,
  Badge,
  Button,
  Form,
  Input,
  Collapse,
  CollapseProps,
} from 'antd';
import DynamicForm from '@/components/DynamicForm';
import { FormField } from '@/components/DynamicForm/types';
import LeftArrow from '../../../assets/LeftArrow.svg';
import CollapseArrow from '../../../assets/CollapseArrow.svg';
import { Translate, t } from '@/languages';
import { motion } from 'framer-motion';

export interface TemplateFrameValue {
  key: string;
  title: string;
  remark?: string;
  sourceName: string;
  value: FormField[];
}

export interface EditJobTemplatePageProps extends PropsWithChildren {
  isShowTimeComponents?: boolean;
  isCreated: string;
  badgeCount?: number;
  pageType: string;
  templateUuid: string;
  templateName: string;
  templateFrameValue: TemplateFrameValue[];
  jobFrameValue: FormField[];
  stepModalData: {
    open: boolean;
    openStepModal: () => void;
  };
  token: string;
  openSaveAsModal: () => void;
  onFormFinish: (values?: any) => void;
  returnFunc?: () => void;
  initForm?: (form: any) => void;
  updateJobTemplate: (formData: any) => void;
  changeFormData: (formData: any) => void;
  getJobTemplateFrameAndValue: (templateUuid: string) => void;
  getJobTemplateFrame: () => void;
}

const { Title } = Typography;
const EditJobTemplatePage: React.FC<EditJobTemplatePageProps> = ({
  isShowTimeComponents = true,
  children,
  isCreated,
  badgeCount,
  returnFunc,
  templateUuid,
  stepModalData,
  templateFrameValue,
  jobFrameValue,
  pageType,
  templateName,
  onFormFinish,
  initForm,
  updateJobTemplate,
  token,
  openSaveAsModal,
  changeFormData,
  getJobTemplateFrameAndValue,
  getJobTemplateFrame,
}) => {
  const [form] = Form.useForm();
  const [createdEditable, setCreatedEditable] = useState(false);

  const items: CollapseProps['items'] = [
    ...(isShowTimeComponents
      ? [
          {
            key: '1',
            label: (
              <span className='text-s1' style={{ color: '#0D152D' }}>
                {<Translate message={'定时参数'} />}
              </span>
            ),
            forceRender: true,
            children: (
              <DynamicForm
                formName='steps'
                fields={[...jobFrameValue]}
                token={token}
              />
            ),
          },
        ]
      : []),
    ...(templateFrameValue === null ? [] : templateFrameValue).map(
      (item, index) => {
        item.value?.forEach((field) => {
          field.required = false;
        });
        return {
          key: item.key + index,
          forceRender: true,
          label: (
            <div className='text-s1' style={{ color: '#0D152D' }}>
              {item.title}
              {item.remark !== '' && (
                <span className='text-gray-400'>({item.remark})</span>
              )}
            </div>
          ),
          children: (
            <DynamicForm
              formName={item.key + '-' + index}
              fields={item.value}
              token={token}
            />
          ),
        };
      },
    ),
  ];

  const updateFormData = () => {
    const values = form.getFieldsValue();
    const _formData: any = {
      templateUuid: templateUuid || null,
      templateName: values.templateName,
      cronParam: values.steps,
      jobChain: templateFrameValue.map(
        (item: TemplateFrameValue, index: number) => {
          return {
            subTemplateName: item.title,
            seq: index + 1,
            subTemplateUuid: item.key,
            subTemplateParam: values[item.key + '-' + index],
            subTemplateRemark: item.remark,
          };
        },
      ),
    };
    changeFormData(_formData);
    return _formData;
  };

  useEffect(() => {
    updateFormData();
  }, [templateFrameValue, jobFrameValue]);

  useEffect(() => {
    form?.resetFields();
    // setJobFrameValue([]);
    // setTemplateFrameValue([]);
    if (templateUuid !== '' && templateUuid) {
      getJobTemplateFrameAndValue(templateUuid);
    } else {
      getJobTemplateFrame();
    }
  }, [templateUuid, templateName]);

  useEffect(() => {
    if (form) {
      initForm?.(form);
    }
  }, []);

  return (
    <div className='min-h-full h-full overflow-auto px-4'>
      <Flex
        vertical={false}
        gap={'30px'}
        align='center'
        style={{
          marginBottom: '22px',
          paddingTop: '10px',
        }}>
        <Flex vertical={false}>
          <img src={LeftArrow}></img>
          <div
            className='cursor-pointer text-s1'
            onClick={returnFunc}
            style={{
              color: '#2F6BFF',
            }}>
            {<Translate message={'返回'} />}
          </div>
        </Flex>
        <Flex
          vertical={false}
          justify='space-between'
          align='center'
          style={{
            flex: 1,
          }}>
          <Title
            level={4}
            className='mb-6 text-s1'
            style={{
              color: '#0D152D',
              margin: '0',
            }}>
            {<Translate message={'编辑计划模版'} />}
          </Title>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  defaultBg: stepModalData.open ? '#0C40DE' : '#1890FF',
                  defaultHoverBg: '#0C40DE',
                },
                Badge: {
                  indicatorHeight: '30px',
                  textFontSize: 18,
                  textFontWeight: 500,
                },
              },
            }}>
            <Badge
              count={badgeCount || 0}
              color={'#F25445'}
              className='text-s1'
              styles={{
                indicator: {
                  borderRadius: '20px',
                  lineHeight: '30px',
                },
              }}
              style={{
                display:
                  isCreated === 'true'
                    ? createdEditable
                      ? 'inline-block'
                      : 'none'
                    : pageType === 'edit'
                      ? 'inline-block'
                      : 'none',
              }}
              offset={[-5, 5]}>
              <Button
                onClick={stepModalData.openStepModal}
                className='text-s1'
                style={{
                  padding: '9px 21px',
                  borderRadius: '36px',
                  color: '#fff',
                  height: '42px',
                  width: '117px',
                  display:
                    isCreated === 'true'
                      ? createdEditable
                        ? 'inline-block'
                        : 'none'
                      : pageType === 'edit'
                        ? 'inline-block'
                        : 'none',
                }}>
                {<Translate message={'选择模版'} />}
              </Button>
            </Badge>
          </ConfigProvider>
        </Flex>
      </Flex>
      <div
        style={{
          backgroundColor: '#fff',
          borderRadius: '10px',
          padding: '23px 30px',
          marginBottom: '22px',
        }}>
        <Form
          form={form}
          disabled={
            isCreated === 'true' ? !createdEditable : pageType === 'view'
          }
          name='job_template'
          onFinish={onFormFinish}
          layout='vertical'
          className='w-full'>
          <Flex vertical={true} gap={'10px'}>
            <Flex vertical={false} justify='space-between' align='center'>
              <span
                className='text-s1'
                style={{
                  color: '#0D152D',
                }}>
                {<Translate message={'模版名称'} /> + ':'}
              </span>
            </Flex>
            <Form.Item
              name='templateName'
              initialValue={templateName}
              rules={[{ required: true, message: '请输入模版姓名' }]}
              style={{
                margin: '0',
              }}>
              <Input
                className='text-s3'
                disabled={!!templateName}
                placeholder={t('请输入模版名称')}
              />
            </Form.Item>
            <ConfigProvider
              theme={{
                components: {
                  Collapse: {
                    headerPadding: '10px 0',
                    colorBorder: 'none',
                    contentPadding: '0',
                  },
                },
              }}>
              <Collapse
                bordered={false}
                items={items}
                className='w-full'
                expandIcon={({ isActive }) => {
                  const Animation = {
                    open: {
                      rotate: 0,
                    },
                    close: {
                      rotate: -90,
                    },
                  };
                  return (
                    <motion.img
                      src={CollapseArrow}
                      variants={Animation}
                      animate={isActive ? 'open' : 'close'}
                      transition={{
                        duration: 0.08,
                      }}
                      style={{
                        transform: 'rotate(-90deg)',
                      }}></motion.img>
                  );
                }}
              />
            </ConfigProvider>
          </Flex>
        </Form>
      </div>
      <Flex
        vertical={false}
        gap={'12px'}
        justify='end'
        style={{
          position: 'relative',
        }}>
        <ConfigProvider
          theme={{
            components: {
              Button: {
                defaultBorderColor: '#848A99',
              },
            },
          }}>
          <Button
            className='text-s1'
            style={{
              color: '#0D152D',
              display: isCreated === 'true' ? 'inline-block' : 'none',
              position: 'absolute',
              left: '0',
            }}
            onClick={() => {
              setCreatedEditable(!createdEditable);
            }}>
            {<Translate message={'编辑'} />}
          </Button>
        </ConfigProvider>
        <ConfigProvider
          theme={{
            components: {
              Button: {
                defaultBg: '#1890FF',
                defaultHoverBg: '#4096ff',
              },
            },
          }}>
          <Button
            onClick={openSaveAsModal}
            className='text-s1'
            style={{
              color: '#fff',
              display: pageType === 'edit' ? 'none' : 'inline-block',
            }}>
            {<Translate message={'另存为'} />}
          </Button>
        </ConfigProvider>
        <ConfigProvider
          theme={{
            components: {
              Button: {
                defaultBorderColor: '#848A99',
              },
            },
          }}>
          <Button
            // disabled={
            //   isCreated === 'true'
            //     ? !createdEditable
            //     : templateFrameValue.length === 0
            // }
            className='text-s1'
            style={{
              color: '#0D152D',
              display: pageType === 'edit' ? 'inline-block' : 'none',
            }}
            onClick={() => {
              isCreated === 'true'
                ? updateJobTemplate(updateFormData())
                : // : onFormFinish(form.getFieldsValue());
                  console.log(form.getFieldsValue());
            }}>
            {isCreated === 'true' ? (
              <Translate message={'保存'} />
            ) : (
              <Translate message={'提交'} />
            )}
          </Button>
        </ConfigProvider>
      </Flex>
      {children}
    </div>
  );
};

export default EditJobTemplatePage;
