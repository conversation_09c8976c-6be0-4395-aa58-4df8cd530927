import React from 'react';
import { Button, Table, Modal, Select } from 'antd';
import InputForm from './InputForm';
import type { FormItemType } from './types';
import { Pagination } from '../../../index';
import { Translate } from '@/languages/index';
import { PlusOutlined } from '@ant-design/icons';

export interface JobTemplatePageProps extends React.PropsWithChildren {
  InputFormItems: FormItemType[];
  onFinish: (values: any) => void;
  onClickNewTemplate: () => void;
  mainTableData: {
    columns: any;
    dataSource: any;
    loading: boolean;
  };
  pageData: {
    current: number;
    total: number;
    pageSize: number;
    onPageChange: (page: number) => void;
  };
  modalData: {
    title: string;
    open: boolean;
    onFinish: () => void;
    onCancel: () => void;
    okButtonDisabled?: boolean;
    select: {
      placeholder: string;
      options: any;
      onChange: (value: any) => void;
    };
  };
}

const JobTemplatePage: React.FC<JobTemplatePageProps> = ({
  children,
  InputFormItems,
  onFinish,
  onClickNewTemplate,
  mainTableData,
  pageData,
  modalData,
}) => {
  return (
    <div className='w-full rounded-xl px-[10px]'>
      <header className={'flex justify-between'}>
        <InputForm
          className='w-full'
          data={InputFormItems}
          onFinish={onFinish}
        />
        <Button
          type='primary'
          className={'rounded-3xl bg-primary-400'}
          icon={<PlusOutlined />}
          onClick={onClickNewTemplate}>
          {<Translate message={'新增模版'} />}
        </Button>
      </header>
      <main className='mt-[10px]'>
        <Table {...mainTableData} pagination={false} rowKey='id' />
      </main>
      <footer className={'w-full flex justify-center mt-[10px]'}>
        <Pagination {...pageData} />
      </footer>
      {children}
      <Modal
        title={modalData.title}
        open={modalData.open}
        destroyOnClose={true}
        footer={false}
        onCancel={modalData.onCancel}
        okButtonProps={{ disabled: modalData.okButtonDisabled }}>
        <div>
          <div className=' flex flex-col gap-[20px]'>
            <Select
              placeholder={modalData.select.placeholder}
              style={{ width: '100%' }}
              options={modalData.select.options}
              onChange={modalData.select.onChange}
            />
          </div>
          <div className='flex mt-[20px]'>
            <Button
              className={'bg-blue-500 mx-auto text-white rounded-full'}
              onClick={modalData.onFinish}>
              {<Translate message={'完成'} />}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
export default JobTemplatePage;
