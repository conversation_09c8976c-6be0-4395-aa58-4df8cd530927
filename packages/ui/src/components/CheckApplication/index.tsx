import React, { useState, useEffect } from 'react';
import {
  Select,
  Collapse,
  Form,
  Button,
  Modal,
  Flex,
  Typography,
  ConfigProvider,
  CollapseProps,
} from 'antd';
import { t } from '@/languages';
import { motion } from 'framer-motion';
import { TemplateFrameValue, DeleteButton, DynamicForm } from '@/index';
import LeftArrow from '@/assets/LeftArrow.svg';
import CollapseArrow from '@/assets/CollapseArrow.svg';

const { Title } = Typography;

export interface CheckApplicationProps {
  navigate: any;
  token: string;
  getJobTemplateFrameAndValue: any;
  getCronTaskDetailContainsParent: any;
  deleteUnExecutedJob: any;
  jobFrameValue: any;
  templateFrameValue: any;
  copyTaskList: any;
  showHeader?: boolean;
  showCopy?: boolean;
  /**复制之后跳转的路径 */
  navigatePath?: string;
}

const CheckApplication = ({
  navigate,
  token,
  getJobTemplateFrameAndValue,
  getCronTaskDetailContainsParent,
  deleteUnExecutedJob,
  jobFrameValue,
  templateFrameValue,
  copyTaskList,
  showCopy = true,
  showHeader = true,
  navigatePath = '/runApplicationJob',
}: CheckApplicationProps) => {
  const [form] = Form.useForm();

  const taskUuid = new URLSearchParams(window.location.search).get('taskUuid');
  const jobId = new URLSearchParams(window.location.search).get('jobId') || '';
  const taskStateCode =
    new URLSearchParams(window.location.search).get('taskStateCode') || '';
  const [templateUuid, setTemplateUuid] = useState<string | null>(null);

  const taskName = new URLSearchParams(window.location.search).get('taskName');

  const [items, setItems] = useState<CollapseProps['items']>([]);

  const [activeKey, setActiveKey] = useState<string | string[]>([]);

  const [copySeq, setCopySeq] = useState<number | undefined>(undefined);

  const [modalData, setModalData] = useState({
    visible: false,
    title: t('选择复制步骤'),
  });
  const openModal = () => {
    setModalData((prev) => ({ ...prev, visible: true }));
  };
  const closeModal = () => {
    setModalData((prev) => ({ ...prev, visible: false }));
  };
  const okModal = () => {
    navigate(
      `${navigatePath}?taskUuid=${taskUuid}&seq=${copySeq}&jobId=${jobId}&templateName=${copyTaskList && copyTaskList[copySeq as number].label}&templateUuid=${templateUuid}`,
    );
    setModalData((prev) => ({ ...prev, visible: false }));
  };

  useEffect(() => {
    if (taskUuid === null) return;
    getJobTemplateFrameAndValue(taskUuid, jobId);
    getCronTaskDetailContainsParent(taskUuid, jobId);
  }, [taskUuid, taskName, jobId]);

  useEffect(() => {
    form.resetFields();
    if (jobFrameValue.length === 0) return;
    if (showHeader) {
      setItems([
        {
          key: '1',
          label: (
            <span className='text-s1' style={{ color: '#0D152D' }}>
              {t('定时参数')}
            </span>
          ),
          forceRender: true,
          children: (
            <DynamicForm
              formName='steps'
              fields={jobFrameValue}
              token={token}
            />
          ),
        },
        ...(templateFrameValue === null ? [] : templateFrameValue).map(
          (item: TemplateFrameValue, index: number) => {
            return {
              forceRender: true,
              key: item.key + index,
              label: (
                <div
                  className='text-s1'
                  style={{
                    color: '#0D152D',
                  }}>
                  {item.title}
                  {item.remark !== '' && (
                    <span className='text-gray-400'>({item.remark})</span>
                  )}
                </div>
              ),
              children: (
                <DynamicForm
                  formName={item.key + item.remark + index}
                  fields={item.value}
                  token={token}
                />
              ),
            };
          },
        ),
      ]);
    } else {
      setItems([
        ...(templateFrameValue === null ? [] : templateFrameValue).map(
          (item: TemplateFrameValue, index: number) => {
            return {
              forceRender: true,
              key: item.key + index,
              label: (
                <div
                  className='text-s1'
                  style={{
                    color: '#0D152D',
                  }}>
                  {item.title}
                  {item.remark !== '' && (
                    <span className='text-gray-400'>({item.remark})</span>
                  )}
                </div>
              ),
              children: (
                <DynamicForm
                  formName={item.key + item.remark + index}
                  fields={item.value}
                  token={token}
                />
              ),
            };
          },
        ),
      ]);
    }
  }, [jobFrameValue]);

  useEffect(() => {
    setActiveKey(
      items
        ?.map((item: any) => item.key)
        .filter((item: any): item is string => item !== undefined) || [],
    );
  }, [items]);

  useEffect(() => {
    if (Array.isArray(copyTaskList) && copyTaskList.length === 1) {
      setCopySeq(0);
      setTemplateUuid(copyTaskList[0].templateUuid);
    }
  }, [copyTaskList]);

  return (
    <div className='min-h-full p-4 sm:p-6 lg:p-8 h-full overflow-auto'>
      {showHeader && (
        <Flex
          vertical={false}
          gap={'30px'}
          align='center'
          style={{
            marginBottom: '22px',
            paddingTop: '10px',
          }}>
          <Flex vertical={false}>
            <img src={LeftArrow}></img>
            <div
              className='cursor-pointer text-s1'
              onClick={() => navigate(-1)}
              style={{
                color: '#2F6BFF',
              }}>
              {t('返回{}', '')}
            </div>
          </Flex>
          <Title
            level={4}
            className='mb-6 text-s1'
            style={{
              color: '#0D152D',
              margin: '0',
            }}>
            {t('任务详情')}
          </Title>
        </Flex>
      )}
      {showHeader && (
        <Flex
          vertical={true}
          style={{
            backgroundColor: '#fff',
            borderRadius: '10px',
            padding: '23px 30px',
            marginBottom: '22px',
          }}>
          <div
            className='text-s1'
            style={{
              color: '#0D152D',
              marginBottom: '10px',
            }}>
            {t('数据详情') + ':'}
          </div>
          <div className='mb-6'>
            <label
              htmlFor='planTemplate'
              className='block text-sm font-medium text-gray-700 mb-2'>
              {t('计划任务模板')}
            </label>
            <Select
              defaultValue={taskName}
              disabled={true}
              id='planTemplate'
              style={{ width: '100%' }}
              placeholder={t('请选择计划任务模板')}
              className='w-full text-s3'
            />
          </div>
          <div className='mt-6 pt-4 border-t border-gray-200'>
            <div
              className='text-s1'
              style={{
                color: '#0D152D',
                marginBottom: '10px',
              }}>
              {t('计划详情') + ':'}
            </div>
          </div>
        </Flex>
      )}
      <Form disabled={true} form={form} layout={'vertical'}>
        {items?.length !== 0 && (
          <ConfigProvider
            theme={{
              components: {
                Collapse: {
                  headerPadding: '10px 0',
                  colorBorder: 'none',
                  contentPadding: '0',
                },
              },
            }}>
            <Collapse
              activeKey={activeKey}
              bordered={false}
              items={items}
              onChange={(keys) => setActiveKey(keys)}
              expandIcon={({ isActive }) => {
                const Animation = {
                  open: {
                    rotate: 0,
                  },
                  close: {
                    rotate: -90,
                  },
                };
                return (
                  <motion.img
                    src={CollapseArrow}
                    variants={Animation}
                    animate={isActive ? 'open' : 'close'}
                    transition={{
                      duration: 0.08,
                    }}
                    style={{
                      transform: 'rotate(-90deg)',
                    }}></motion.img>
                );
              }}
            />
          </ConfigProvider>
        )}
      </Form>
      {showCopy && (
        <div className='flex gap-4'>
          <Button type={'primary'} onClick={openModal}>
            {t('复制步骤')}
          </Button>
          {taskStateCode === '4' && (
            <DeleteButton
              onClick={() => deleteUnExecutedJob(jobId)}
              deleteTitle={t('确定删除此任务吗')}
            />
          )}
        </div>
      )}
      <Modal
        title={modalData.title}
        open={modalData.visible}
        onOk={okModal}
        onCancel={closeModal}>
        <Select
          style={{ width: 300 }}
          value={copySeq}
          onChange={(value, options: any) => {
            setCopySeq(value);
            console.log(options);
            setTemplateUuid(options.templateUuid);
          }}
          options={copyTaskList}
        />
      </Modal>
    </div>
  );
};

export default CheckApplication;
