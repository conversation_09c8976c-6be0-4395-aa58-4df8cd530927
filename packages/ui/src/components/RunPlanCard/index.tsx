import { useEffect, useState } from 'react';
import {
  Select,
  Collapse,
  Form,
  Button,
  message,
  Flex,
  Typography,
  ConfigProvider,
} from 'antd';
import type { CollapseProps } from 'antd';
import { FormField, DynamicForm, TemplateFrameValue } from '@/index';
import { t } from '@/languages';
import { motion } from 'framer-motion';
import LeftArrow from '@/assets/LeftArrow.svg';
import CollapseArrow from '@/assets/CollapseArrow.svg';

const { Title } = Typography;
export interface RunPlanCardProps {
  startCronJob: any;
  getPlanTemplate: any;
  getJobTemplateFrameAndValue: any;
  getJobTemplateFrameAndValueByCopy: any;
  useNavigate: any;
  useUpdateEffect: any;
  token: string;
  planTemplateList: any;
  jobFrameValue: FormField[];
  templateFrameValue: TemplateFrameValue[];
  setPlanTemplateList: any;
  title?: string | React.ReactNode;
  showReturn?: boolean;
}

const RunPlanCard = ({
  startCronJob,
  getPlanTemplate,
  getJobTemplateFrameAndValue,
  getJobTemplateFrameAndValueByCopy,
  useNavigate,
  useUpdateEffect,
  token,
  planTemplateList,
  jobFrameValue,
  templateFrameValue,
  setPlanTemplateList,
  title = '运行计划任务',
  showReturn = true,
}: RunPlanCardProps) => {
  const [form] = Form.useForm();

  const navigate = useNavigate();

  const taskUuid = new URLSearchParams(window.location.search).get('taskUuid');
  const jobId = new URLSearchParams(window.location.search).get('jobId') || '';
  const seq = new URLSearchParams(window.location.search).get('seq') || null;
  const copyTemplateName = new URLSearchParams(window.location.search).get(
    'templateName',
  );
  const copyTemplateUuid = new URLSearchParams(window.location.search).get(
    'templateUuid',
  );

  const [isCopy, setIsCopy] = useState<boolean>(!!copyTemplateUuid);
  const [templateName, setTemplateName] = useState<string>('');
  const [items, setItems] = useState<CollapseProps['items']>([]);
  const [templateUuid, setTemplateUuid] = useState<string | null>(null);
  const [activeKey, setActiveKey] = useState<string | string[]>([]);

  const onFinish = async () => {
    try {
      const values = await form.validateFields();
      const formData = {
        templateUuid: templateUuid || null,
        templateName: templateName,
        cronParam: values.steps,
        jobChain: templateFrameValue.map(
          (item: TemplateFrameValue, index: number) => {
            return {
              subTemplateName: item.title,
              seq: index + 1,
              subTemplateUuid: item.key,
              subTemplateParam: values[item.key + item.remark + index],
            };
          },
        ),
      };
      const res = await startCronJob(formData);
      if (res.code === 0) {
        message.success(t('提交成功'));
        navigate(-1);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error(t('错误:'), error);
    }
  };

  useUpdateEffect(() => {
    if (templateUuid === null || seq !== null) return;
    getJobTemplateFrameAndValue(templateUuid);
  }, [templateUuid]);

  useEffect(() => {
    if (seq === null) return;
    getJobTemplateFrameAndValueByCopy(
      taskUuid || '',
      jobId,
      isCopy,
      Number(seq),
    );
  }, []);

  useEffect(() => {
    if (copyTemplateName === null || copyTemplateUuid === null) return;
    setPlanTemplateList((prev: any) => [
      ...prev,
      { label: copyTemplateName, value: copyTemplateUuid },
    ]);
    setTemplateName(copyTemplateName || '');
    setTemplateUuid(copyTemplateUuid || null);
    setIsCopy(true);
  }, [seq, copyTemplateName, copyTemplateUuid]);

  useEffect(() => {
    getPlanTemplate();
    if (jobFrameValue.length === 0) return;

    setItems(() => {
      const _items = [
        {
          key: '1',
          label: (
            <span className='text-s1' style={{ color: '#0D152D' }}>
              {t('定时参数')}
            </span>
          ),
          forceRender: true,
          children: (
            <DynamicForm
              formName='steps'
              fields={jobFrameValue}
              token={token}
            />
          ),
        },
        ...(templateFrameValue === null ? [] : templateFrameValue).map(
          (item: TemplateFrameValue, index: number) => {
            return {
              forceRender: true,
              key: item.key + item.remark + index,
              label: (
                <div
                  className='text-s1'
                  style={{
                    color: '#0D152D',
                  }}>
                  {item.title}
                  {item.remark !== '' && (
                    <span className='text-gray-400'>({item.remark})</span>
                  )}
                </div>
              ),
              children: (
                <DynamicForm
                  formName={item.key + item.remark + index}
                  fields={item.value}
                  token={token}
                />
              ),
            };
          },
        ),
      ];
      setActiveKey(_items.map((item) => item.key));
      return _items;
    });
  }, [jobFrameValue, templateFrameValue]);

  useEffect(() => {
    form.resetFields();
  }, [items]);

  useEffect(() => {
    if (planTemplateList.length === 1) {
      setTemplateUuid(planTemplateList[0].value);
    }
  }, [planTemplateList]);

  return (
    <div className='min-h-full h-full overflow-auto px-4'>
      <Flex
        vertical={false}
        gap={'30px'}
        align='center'
        style={{
          marginBottom: '22px',
          paddingTop: '10px',
        }}>
        {showReturn && (
          <Flex vertical={false}>
            <img src={LeftArrow}></img>
            <span
              onClick={() => navigate(-1)}
              className='cursor-pointer text-s1'
              style={{
                color: '#2F6BFF',
              }}>
              {t('返回 ')}
            </span>
          </Flex>
        )}

        <Title
          level={4}
          className='mb-6 text-s1'
          style={{
            color: '#0D152D',
            margin: '0',
          }}>
          {title}
        </Title>
      </Flex>
      <Flex
        vertical={true}
        style={{
          backgroundColor: '#fff',
          borderRadius: '10px',
          padding: '23px 30px',
          marginBottom: '22px',
        }}>
        <Flex vertical={true} gap={10}>
          <label
            htmlFor='planTemplate'
            className='text-s1'
            style={{
              color: '#0D152D',
            }}>
            {t('任务模板') + ':'}
          </label>
          <Select
            disabled={!!seq}
            options={planTemplateList}
            id='planTemplate'
            style={{ width: '100%' }}
            value={templateUuid}
            placeholder={t('请选择任务模板')}
            className='w-full'
            onChange={(value, options: any) => {
              setTemplateName(options?.label);
              setTemplateUuid(value);
            }}
          />
        </Flex>
        <div className='mt-6 pt-4 border-t border-gray-200'>
          <h2
            className='text-s1'
            style={{
              color: '#0D152D',
            }}>
            {t('任务详情{name}', { name: '' }) + ':'}
          </h2>
          <Form form={form} onFinish={onFinish} layout={'vertical'}>
            {items?.length !== 0 && (
              <ConfigProvider
                theme={{
                  components: {
                    Collapse: {
                      headerPadding: '10px 0',
                      colorBorder: 'none',
                      contentPadding: '0',
                    },
                  },
                }}>
                <Collapse
                  activeKey={activeKey}
                  bordered={false}
                  items={items}
                  onChange={(keys) => setActiveKey(keys)}
                  expandIcon={({ isActive }) => {
                    const Animation = {
                      open: {
                        rotate: 0,
                      },
                      close: {
                        rotate: -90,
                      },
                    };
                    return (
                      <motion.img
                        src={CollapseArrow}
                        variants={Animation}
                        animate={isActive ? 'open' : 'close'}
                        transition={{
                          duration: 0.08,
                        }}
                        style={{
                          transform: 'rotate(-90deg)',
                        }}></motion.img>
                    );
                  }}
                />
              </ConfigProvider>
            )}
          </Form>
        </div>
      </Flex>
      <Button
        type='primary'
        onClick={() => {
          onFinish();
        }}
        disabled={templateUuid === null}
        className='bg-blue-500 text-white px-4 py-2 rounded-md'>
        {t('运行任务')}
      </Button>
    </div>
  );
};

export default RunPlanCard;
