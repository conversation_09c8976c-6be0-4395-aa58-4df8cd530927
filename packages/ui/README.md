# Modern.js Package

## Setup

Install the dependencies:

```bash
pnpm run install
```

## Get Started

Run and debug the module:

```bash
pnpm run dev
```

Build the module for production:

```bash
pnpm run build
```

Enable optional features:

```bash
pnpm run new
```

Other commands:

```bash
pnpm run lint         # Lint and fix source files
pnpm run change       # Add a new changeset
pnpm run bump         # Update version and changelog via changeset
pnpm run release      # Release the package
```

For more information, see the [Modern.js Module documentation](https://modernjs.dev/module-tools/en).
