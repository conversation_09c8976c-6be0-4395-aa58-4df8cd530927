import { Button } from '../src';
export default {
  title: 'Button',
  component: Button,
  argTypes: {
    size: {
      control: {
        type: 'select',
        options: ['small', 'normal', 'big'],
      },
    },
    type: {
      control: {
        type: 'select',
        options: ['primary', 'secondary', 'dashed', 'link'],
      },
    },
  },
};

export const Primary = {
  args: {
    children: 'Button',
    size: 'normal',
    type: 'primary',
  },
};

// const Component = ({ backgroundColor, color }) => (
//   <button type="button" style={{ backgroundColor, color }}>
//     this is a Story Component
//   </button>
// );
//
// export default {
//   title: 'Example/Component',
//   component: Component,
//   argTypes: {
//     backgroundColor: { control: 'color' },
//     çolor: { control: 'color' },
//   },
// };
//
// export const Primary = {
//   args: {
//     backgroundColor: '#1ea7fd',
//     color: 'white',
//   },
// };
//
// export const Secondary = {
//   args: {
//     backgroundColor: 'transparent',
//     color: '#333',
//   },
// };
