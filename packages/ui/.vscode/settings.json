{
  "files.associations": {
    ".code-workspace": "jsonc",
    ".eslintrc": "jsonc",
    ".eslintrc*.json": "jsonc",
    ".stylelintrc": "jsonc",
    "stylelintrc": "jsonc",
    "README": "markdown"
  },
  "search.useIgnoreFiles": true,
  "search.exclude": {
    "**/dist": true,
    "**/*.log": true,
    "**/*.pid": true,
    "**/.git": true,
    "**/node_modules": true
  },
  //
  "editor.rulers": [80, 120],
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  //
  "cSpell.diagnosticLevel": "Hint",
  "eslint.run": "onType",
  "eslint.probe": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "javascript.validate.enable": false,
  "typescript.validate.enable": true,
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "[css]": {
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "[less]": {
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "javascript.format.enable": false,
  "typescript.format.enable": false,
  //
  "json.format.enable": false,
  "[json]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "emmet.triggerExpansionOnTab": true,
  "typescript.tsdk": "node_modules/typescript/lib"
}
