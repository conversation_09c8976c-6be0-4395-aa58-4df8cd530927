import { moduleTools, defineConfig } from '@modern-js/module-tools';
import Autoprefixer from 'autoprefixer';
import PostcssAssets from 'postcss-assets';
import CssNext from 'postcss-cssnext';
import StyleLint from 'stylelint';
import Cssnano from 'cssnano';

export default defineConfig({
  plugins: [moduleTools()],
  buildPreset: 'npm-component',
  buildConfig: {
    alias: {
      '@': './src',
    },
    style: {
      postcss: {
        plugins: [Autoprefixer, PostcssAssets, CssNext, StyleLint, Cssnano],
      },
    },
  },
});
