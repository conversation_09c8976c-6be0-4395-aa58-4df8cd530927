/** @type {import("tailwindcss").Config} */
import colors from "tailwindcss/colors";

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    colors: {
      ...colors,
      white: "#ffffff",
      //brand color
      brand: {
        DEFAULT: "#2F6BFF",
        dark: "#0C40DE",
        light: "#1890FF",
      },

      gray: {
        ...colors.gray,
        l7: "#E2E3E7",
        l5: "#EFF2F3",
      },

      //functional color
      success: "#26cc68",
      warning: "#ffd565",
      error: "#f25445",

      //label color/secondary color
      label: {
        red: "#ebaca2",
        pink: "#ffdfcb",
        yellow: "#f7d57b",
        green: "#8ec188",
        blue: "#8ec188",
      },
      //neutral color
      neutral: {
        5: "#eff2f3",
        10: "#e2e3e7",
        15: "#d5d7dd",
        25: "#b9bcc6",
        45: "#848a99",
        65: "#555e70",
        85: "#293147",
        100: "#0d152d",
      },
    },
    extend: {
      fontSize: {
       normal: [
          '14px',
          {
            fontStyle: 'normal',
            fontWeight: 400,
            lineHeight: '20px',
            letterSpacing: '0.56px',
            fontFamily: 'PingFang SC',
          },
        ],
        h1: [
          '24px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.96px',
            fontWeight: 600,
          },
        ],
        h2: [
          '22px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.88px',
            fontWeight: 600,
          },
        ],
        h3: [
          '20px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.64px',
            fontWeight: 600,
          },
        ],
        h4: [
          '18px',
          {
            lineHeight: '24px',
            letterSpacing: '0.72px',
            fontWeight: 500,
          },
        ],
        s1: [
          '18px',
          {
            fontWeight: 500,
            lineHeight: '24px',
            letterSpacing: '0.72px',
          },
        ],
        s2: [
          '16px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.64px',
            fontWeight: 500,
          },
        ],
        s3: [
          '16px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.64px',
            fontWeight: 400,
          },
        ],
        b1: [
          '14px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.56px',
            fontWeight: 600,
          },
        ],
        b2: [
          '14px',
          {
            lineHeight: '20px',
            letterSpacing: '0.56px',
            fontWeight: 400,
          },
        ],
        st1: [
          '12px',
          {
            lineHeight: 'normal',
            letterSpacing: '0.48px',
            fontWeight: 400,
          },
        ],
        st2: [
          '10px',
          {
            lineHeight: 'normal',
            fontWeight: 500,
          },
        ],
      },
    },
  },
};
