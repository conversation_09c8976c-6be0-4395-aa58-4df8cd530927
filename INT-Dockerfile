FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/node:18 AS build-stage
WORKDIR /app

COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY ./packages ./packages
COPY apps/int-ui/ ./apps/int-ui/

RUN echo public-hoist-pattern[]=*@nextui-org/*>>~/.npmrc
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm
# RUN pnpm store prune
RUN pnpm install 

COPY . .
RUN pnpm run build --filter=int-ui


# 定义生产阶段
FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/openresty/openresty:alpine AS production-stage
RUN echo "http://nl.alpinelinux.org/alpine/v3.10/main" > /etc/apk/repositories \
    && echo "http://nl.alpinelinux.org/alpine/v3.10/community" >> /etc/apk/repositories
# 更新 APK 仓库索引
RUN apk update

# 安装 gettext 软件包（包含 envsubst 命令）
RUN apk add --no-cache gettext
COPY --from=build-stage /app/apps/int-ui/dist /usr/share/nginx/html
# 设置工作目录
WORKDIR /usr/local/openresty/nginx/conf
# 复制你的Nginx配置文件到容器中
COPY apps/int-ui/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf.template
ENTRYPOINT envsubst '$UI_BFF_HOST $UI_BFF_PORT $UI_SCP_HOST $UI_SCP_PORT $INT_UI_START_PORT'  < nginx.conf.template > nginx.conf && cat nginx.conf && nginx -g 'daemon off;'

EXPOSE ${INT_UI_START_PORT}

